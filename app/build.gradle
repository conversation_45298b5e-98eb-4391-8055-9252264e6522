plugins {
    id 'com.android.application'
    id 'com.google.gms.google-services'
    id 'com.google.firebase.crashlytics'
}


android {
    namespace "com.rising.high.tech.bigultimatenavdraw"
    compileSdkVersion 34

    defaultConfig {
        applicationId "com.rising.high.tech.bigultimatenavdraw"
        minSdkVersion 24
        targetSdkVersion 34
        versionCode 1
        versionName "1.0"
        multiDexEnabled true
        ndk {
            abiFilters "armeabi-v7a", "arm64-v8a", "x86", "x86_64"
        }
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    buildFeatures {
        viewBinding true
    }
    sourceSets {
        main {
            jniLibs.srcDirs = ['libs']
        }
    }
}

dependencies {

    implementation fileTree(include: ['*.jar'], dir: 'libs')
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'com.google.android.material:material:1.10.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    implementation 'androidx.navigation:navigation-fragment:2.7.5'
    implementation 'androidx.navigation:navigation-ui:2.7.5'
    implementation 'androidx.lifecycle:lifecycle-livedata-ktx:2.6.2'
    implementation 'androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2'
    implementation 'androidx.legacy:legacy-support-v4:1.0.0'

    implementation project(path: ':NeptuneLiteApi_V3.24.00_20210519')
   // implementation fileTree(dir: 'C:\\androidCurrentProjects\\navpos\\app\\libs', include: ['*.aar', '*.jar'], exclude: [])
    implementation fileTree(dir: 'libs', include: ['*.aar', '*.jar'], exclude: [])

    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'


    implementation "androidx.recyclerview:recyclerview:1.3.2"
    implementation "androidx.cardview:cardview:1.0.0"
    implementation 'com.squareup.okhttp3:logging-interceptor:4.10.0'
    implementation 'com.squareup.okhttp3:okhttp:4.10.0'
    implementation 'com.squareup.picasso:picasso:2.8'
    //  retrofit library
    implementation 'com.squareup.retrofit2:retrofit:2.9.0'
    implementation 'com.google.code.gson:gson:2.10.1'
    implementation 'com.squareup.retrofit2:converter-gson:2.9.0'

    implementation 'androidx.swiperefreshlayout:swiperefreshlayout:1.1.0'

    // TPE SDKs
//    implementation files('libs/SDK4KeyManagerBinderV1.4.jar')
    //implementation files('libs/NeptuneLiteApi_V3.24.00_20210519.jar')
    // cardview dependency

    //scan lib camera - updated to newer versions
    implementation 'com.journeyapps:zxing-android-embedded:4.3.0'
    implementation 'com.google.zxing:core:3.5.1'

    implementation 'io.reactivex.rxjava3:rxandroid:3.0.2'
    // Because RxAndroid releases are few and far between, it is recommended you also
    // explicitly depend on RxJava's latest version for bug fixes and new features.
    // (see https://github.com/ReactiveX/RxJava/releases for latest 3.x.x version)
    implementation 'io.reactivex.rxjava3:rxjava:3.1.6'

    implementation 'com.squareup.retrofit2:adapter-rxjava3:2.9.0'
    implementation 'androidx.multidex:multidex:2.0.1'
    
    // Removed ButterKnife dependency as it's causing issues with Java 9+ modules
    // implementation 'com.jakewharton:butterknife:10.2.3'
    // annotationProcessor 'com.jakewharton:butterknife-compiler:10.2.3'
    
    // RecyclerView dependency already included above
    implementation 'com.github.ybq:Android-SpinKit:1.4.0'
    // Material design dependency already included above

    // Removed MultiSelectSpinner dependency as it's causing issues
    // implementation 'com.github.pratikbutani:MultiSelectSpinner:master-SNAPSHOT'
    
    // Removed iosdialog dependency that is no longer available
    // Removed debug-db dependencies that are no longer available
    
    implementation files('libs/GLPage_V1.01.00_20171222.jar')
    
    // Removed searchablespinnerlibrary dependency that is no longer available
    // Removed duplicate Picasso dependency
    
    // Declare the dependencies for the Crashlytics and Analytics libraries
    // When using the BoM, you don't specify versions in Firebase library dependencies
    implementation 'com.google.firebase:firebase-crashlytics:18.4.3'
    implementation platform('com.google.firebase:firebase-bom:32.3.1')
}

// Plugins are now applied at the top of the file
