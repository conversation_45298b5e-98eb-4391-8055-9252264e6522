package com.rising.high.tech.bigultimatenavdraw.api;

import android.content.Context;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.rising.high.tech.bigultimatenavdraw.model.Brand;
import com.rising.high.tech.bigultimatenavdraw.model.Business;
import com.rising.high.tech.bigultimatenavdraw.model.Business_location;
import com.rising.high.tech.bigultimatenavdraw.model.Category;
import com.rising.high.tech.bigultimatenavdraw.model.Contact;
import com.rising.high.tech.bigultimatenavdraw.model.Customer_groups;
import com.rising.high.tech.bigultimatenavdraw.model.Discount;
import com.rising.high.tech.bigultimatenavdraw.model.ProductStock;
import com.rising.high.tech.bigultimatenavdraw.model.ProfitLossReport;
import com.rising.high.tech.bigultimatenavdraw.model.ResponseUser;
import com.rising.high.tech.bigultimatenavdraw.model.Tax_rates;
import com.rising.high.tech.bigultimatenavdraw.model.Transaction;
import com.rising.high.tech.bigultimatenavdraw.model.Product;
import com.rising.high.tech.bigultimatenavdraw.model.Unit;
import com.rising.high.tech.bigultimatenavdraw.model.User;
import com.rising.high.tech.bigultimatenavdraw.model.Warranty;
import com.rising.high.tech.bigultimatenavdraw.util.NetworkConnectionInterceptor;
import com.rising.high.tech.bigultimatenavdraw.util.serverInteraction;

import java.util.ArrayList;
import java.util.HashMap;

import io.reactivex.rxjava3.core.Observable;
import okhttp3.OkHttpClient;
import retrofit2.Retrofit;
import retrofit2.adapter.rxjava3.RxJava3CallAdapterFactory;
import retrofit2.converter.gson.GsonConverterFactory;

public class APIClient {

    private ApiInterface apiInterface;
    private static APIClient INSTANCE;
    private static Context _context;

    public static Context get_context() {
        return _context;
    }

    public APIClient(Context mContext) {

        this._context = mContext;

        OkHttpClient.Builder oktHttpClient = new OkHttpClient.Builder()
                .addInterceptor(new NetworkConnectionInterceptor(get_context()));
        Gson gson = new GsonBuilder()
                .setLenient()
                .create();
        Retrofit retrofit = new Retrofit.Builder()
                .baseUrl(serverInteraction.BASE_URL)
                .addConverterFactory(GsonConverterFactory.create(gson))
                .addCallAdapterFactory(RxJava3CallAdapterFactory.create())
                .client(oktHttpClient.build())
                .build();
        apiInterface = retrofit.create(ApiInterface.class);
    }

    public static APIClient getINSTANCE(Context context) {
        if (INSTANCE == null) {
            INSTANCE = new APIClient(context);
        }
        return INSTANCE;
    }

    public Observable<ResponseUser<ArrayList<Category>>> getCategory() {
        return apiInterface.getCategory();
    }

    public Observable<ResponseUser<ArrayList<Product>>> getProduct() {
        return apiInterface.getProduct();
    }

    public Observable<ResponseUser<ArrayList<Brand>>> getBrand() {
        return apiInterface.getBrand();
    }
    public Observable<ResponseUser<ArrayList<Warranty>>> getWarranty() {
        return apiInterface.getWarranty();
    }

    public Observable<ResponseUser<ArrayList<Business_location>>> getBusinessLocation() {
        return apiInterface.getBusinessLocation();
    }

    public Observable<ResponseUser<ArrayList<Contact>>> getCustomer() {
        return apiInterface.getCustomer();
    }

    public Observable<ResponseUser<ArrayList<Transaction>>> getSells() {
        return apiInterface.getSells();
    }

    public Observable<ResponseUser<ArrayList<Transaction>>> getExpense() {
        return apiInterface.getExpense();
    }

    public Observable<ResponseUser<Contact>> postContact(Contact contact) {
        return apiInterface.postContact(contact);
    }

    public Observable<ResponseUser<ProfitLossReport>> getProfitLost() {
        return apiInterface.getProfitLost();
    }

    public Observable<ResponseUser<ArrayList<ProductStock>>> getProductStock() {
        return apiInterface.getProductStock();
    }


    public Observable<ResponseUser<ArrayList<Unit>>> getUnit() {
        return apiInterface.getUnit();
    }

    public Observable<ResponseUser<Business>> getBusinessSetting() {
        return apiInterface.getBusinessSetting();
    }

    public Observable<ResponseUser<ArrayList<Product>>> storeProduct(HashMap<Object, Object> hashMap) {
        return apiInterface.storeProduct(hashMap);
    }

    public Observable<ResponseUser<ArrayList<Contact>>> storeContact(HashMap<Object, Object> hashMap) {
        return apiInterface.storeContact(hashMap);
    }

    public Observable<ResponseUser<ArrayList<Category>>> storeCategories(HashMap<Object, Object> hashMap) {
        return apiInterface.storeCategories(hashMap);
    }

    public Observable<ResponseUser<String>> postSell(HashMap<Object, Object> hashMap) {
        return apiInterface.postSell(hashMap);
    }

    public Observable<ResponseUser<ArrayList<Unit>>> storeUnits(HashMap<Object, Object> hashMap) {
        return apiInterface.storeUnits(hashMap);
    }

    public Observable<ResponseUser<String>> storeOpeningStock(HashMap<Object, Object> hashMap ) {
        return apiInterface.storeOpeningStock(hashMap);
    }

    public Observable<ResponseUser<ArrayList<Customer_groups>>> storeCustomerGroups(HashMap<Object, Object> hashMap ) {
        return apiInterface.storeCustomerGroups(hashMap);
    }


    public Observable<ResponseUser<ArrayList<Tax_rates>>> storeTaxRates(HashMap<Object, Object> hashMap ) {
        return apiInterface.storeTaxRates(hashMap);
    }

    public Observable<ResponseUser<ArrayList<Discount>>> storeDiscounts(HashMap<Object, Object> hashMap ) {
        return apiInterface.storeDiscounts(hashMap);
    }


    public Observable<ResponseUser<String>> storeExpenses(HashMap<Object, Object> hashMap ) {
        return apiInterface.storeExpenses(hashMap);
    }


    public Observable<ResponseUser<ArrayList<Business_location>>> storeBusinessLocation(HashMap<Object, Object> hashMap ) {
        return apiInterface.storeBusinessLocation(hashMap);
    }

    public Observable<ResponseUser<ArrayList<User>>> storeUsers(HashMap<Object, Object> hashMap ) {
        return apiInterface.storeUsers(hashMap);
    }

    public Observable<ResponseUser<String>> storeBusiness(Business business) {
        return apiInterface.storeBusiness(business);
    }


}