package com.rising.high.tech.bigultimatenavdraw.api;
import com.rising.high.tech.bigultimatenavdraw.model.Brand;
import com.rising.high.tech.bigultimatenavdraw.model.Business;
import com.rising.high.tech.bigultimatenavdraw.model.Category;
import com.rising.high.tech.bigultimatenavdraw.model.Contact;
import com.rising.high.tech.bigultimatenavdraw.model.Customer_groups;
import com.rising.high.tech.bigultimatenavdraw.model.Discount;
import com.rising.high.tech.bigultimatenavdraw.model.ProductStock;
import com.rising.high.tech.bigultimatenavdraw.model.ProfitLossReport;
import com.rising.high.tech.bigultimatenavdraw.model.ResponseUser;
import com.rising.high.tech.bigultimatenavdraw.model.Tax_rates;
import com.rising.high.tech.bigultimatenavdraw.model.Transaction;
import com.rising.high.tech.bigultimatenavdraw.model.Product;
import com.rising.high.tech.bigultimatenavdraw.model.Business_location;
import com.rising.high.tech.bigultimatenavdraw.model.Unit;
import com.rising.high.tech.bigultimatenavdraw.model.User;
import com.rising.high.tech.bigultimatenavdraw.model.Warranty;

import java.util.ArrayList;
import java.util.HashMap;
import io.reactivex.rxjava3.core.Observable;
import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.POST;

public interface ApiInterface {

    @GET("taxonomy")
    public Observable<ResponseUser<ArrayList<Category>>> getCategory();

    @GET("product")
    public Observable<ResponseUser<ArrayList<Product>>> getProduct();

    @GET("brand")
    public Observable<ResponseUser<ArrayList<Brand>>> getBrand();

    @GET("warranties")
    public Observable<ResponseUser<ArrayList<Warranty>>> getWarranty();

    @GET("business-location")
    public Observable<ResponseUser<ArrayList<Business_location>>> getBusinessLocation();

    @GET("contactapi")
    public Observable<ResponseUser<ArrayList<Contact>>> getCustomer();

    @GET("sell")
    public Observable<ResponseUser<ArrayList<Transaction>>> getSells();

    @GET("expense")
    public Observable<ResponseUser<ArrayList<Transaction>>> getExpense();

    @GET("profit-loss-report")
    public Observable<ResponseUser<ProfitLossReport>> getProfitLost();

    @GET("product-stock-report")
    public Observable<ResponseUser<ArrayList<ProductStock>>> getProductStock();

    @GET("unit")
    public Observable<ResponseUser<ArrayList<Unit>>> getUnit();

    @POST("contactapi")
    public Observable<ResponseUser<Contact>> postContact(@Body Contact contact);

    @POST("sell")
    public Observable<ResponseUser<String>> postSell(@Body HashMap<Object, Object> hashMap);

    @POST("product-store")
    public Observable<ResponseUser<ArrayList<Product>>> storeProduct(@Body HashMap<Object, Object> hashMap);

    @POST("contacts-store")
    public Observable<ResponseUser<ArrayList<Contact>>> storeContact(@Body HashMap<Object, Object> hashMap);

    @POST("categories-store")
    public Observable<ResponseUser<ArrayList<Category>>> storeCategories(@Body HashMap<Object, Object> hashMap);

    @POST("add-openning-stock")
    public Observable<ResponseUser<String>> storeOpeningStock(@Body HashMap<Object, Object> hashMap);

    @POST("units-store")
    public Observable<ResponseUser<ArrayList<Unit>>> storeUnits(@Body HashMap<Object, Object> hashMap);

    @POST("customer-groups")
    public Observable<ResponseUser<ArrayList<Customer_groups>>> storeCustomerGroups(@Body HashMap<Object, Object> hashMap);

    @POST("tax-rates-store")
    public Observable<ResponseUser<ArrayList<Tax_rates>>> storeTaxRates(@Body HashMap<Object, Object> hashMap);

    @POST("discounts-store")
    public Observable<ResponseUser<ArrayList<Discount>>> storeDiscounts(@Body HashMap<Object, Object> hashMap);

    @POST("expenses-store")
    public Observable<ResponseUser<String>> storeExpenses(@Body HashMap<Object, Object> hashMap);

    @POST("store-business-locations")
    public Observable<ResponseUser<ArrayList<Business_location>>> storeBusinessLocation(@Body HashMap<Object, Object> hashMap);

    @POST("add-users")
    public Observable<ResponseUser<ArrayList<User>>> storeUsers(@Body HashMap<Object, Object> hashMap);

    @POST("post-business-settings")
    public Observable<ResponseUser<String>> storeBusiness(@Body Business business);

    @GET("get-business-settings")
    public Observable<ResponseUser<Business>> getBusinessSetting();

}
