package com.rising.high.tech.bigultimatenavdraw.db;

import android.content.ContentValues;
import android.content.Context;
import android.content.res.Resources;
import android.database.Cursor;

import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.model.Brand;

import java.util.ArrayList;

public class BrandDbController extends DBController {
    private Context _context;
    private Resources resources;
    public static final String BRAND_TABLE_NAME = "brands";

    public static final String BRAND_ID = "id"; //int
    public static final String BRAND_NAME = "name";
    public static final String BRAND_SHORT_DESC = "short_desc";
    public static final String BRAND_BUSINESS_ID = "business_id";

    public static final String BRAND_TABLE_CREATE =
            "CREATE TABLE " + BRAND_TABLE_NAME + " (" +
                    BRAND_ID + " INTEGER PRIMARY KEY AUTOINCREMENT, " +
                    BRAND_NAME + " TEXT, " +
                    BRAND_SHORT_DESC + " TEXT, " +
                    BRAND_BUSINESS_ID + " INTEGER) ;";
    public static final String BRAND_TABLE_DROP = "DROP TABLE IF EXISTS " + BRAND_TABLE_NAME + ";";

    public BrandDbController(Context context) {
        super(context);
        _context=context;
        resources=_context.getResources();
    }

    public int insert(Brand unit) {
        // Create a new map of values, where column names are the keys
        ContentValues pValues = new ContentValues();

        pValues.put(BRAND_ID, unit.getId());
        pValues.put(BRAND_NAME, unit.getName());
        pValues.put(BRAND_SHORT_DESC, unit.getShort_desc());
        pValues.put(BRAND_BUSINESS_ID, unit.getBusiness_id());
        return (int) mDb.insert(BRAND_TABLE_NAME, null, pValues);
    }

    public int insertLocal(Brand unit) {
        // Create a new map of values, where column names are the keys
        ContentValues pValues = new ContentValues();
        pValues.put(BRAND_NAME, unit.getName());
        pValues.put(BRAND_SHORT_DESC, unit.getShort_desc());
        pValues.put(BRAND_BUSINESS_ID, unit.getBusiness_id());

        return (int) mDb.insert(BRAND_TABLE_NAME, null, pValues);
    }
    public int updateLocal(Brand brand) {
        ContentValues pValues = new ContentValues();
        pValues.put(BRAND_NAME, brand.getName());
        pValues.put(BRAND_SHORT_DESC, brand.getShort_desc());
        return mDb.update(BRAND_TABLE_NAME, pValues, BRAND_ID + " = '" + brand.getId() + "'", null);

    }
    public void deleteItem(Integer id) {
        mDb.execSQL("delete from " + BRAND_TABLE_NAME  + " WHERE " + BRAND_ID + " = '" + id + "'");
    }
    public ArrayList<Brand> getAllBrands() {
        ArrayList<Brand> tempCompany = new ArrayList<>();
        String selectQuery = "SELECT  * FROM " + BRAND_TABLE_NAME;
        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {
            do {
                Brand unit = new Brand();
                unit.setId(cursor.getInt(0));
                unit.setName(cursor.getString(1));
                unit.setShort_desc(cursor.getString(2));
                unit.setBusiness_id(cursor.getInt(3));

                tempCompany.add(unit);

            } while (cursor.moveToNext());

        }
        return tempCompany;
    }

    public ArrayList<Brand> getAllBrandsLike(String name) {
        ArrayList<Brand> tempCompany = new ArrayList<>();
        String selectQuery = "SELECT  * FROM " + BRAND_TABLE_NAME + " WHERE " + BRAND_NAME + " LIKE '%" + name +  "%' ";

        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {
            do {
                Brand unit = new Brand();
                unit.setId(cursor.getInt(0));
                unit.setName(cursor.getString(1));
                unit.setShort_desc(cursor.getString(2));
                unit.setBusiness_id(cursor.getInt(3));
                tempCompany.add(unit);

            } while (cursor.moveToNext());

        }
        // mDb.close();

        return tempCompany;
    }

    public Brand getBrandById(Integer id) {
        Brand brand = new Brand();

        String selectQuery = "SELECT  * FROM " + BRAND_TABLE_NAME + " WHERE " + BRAND_ID + " = " + id;

        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {
                brand.setId(cursor.getInt(0));
                brand.setName(cursor.getString(1));
                brand.setShort_desc(cursor.getString(2));
                brand.setBusiness_id(cursor.getInt(3));
        }
        return brand;
    }

    public ArrayList<Brand> getAllBrandSpinner() {
        ArrayList<Brand> tempCompany = new ArrayList<>();
        String selectQuery = "SELECT  * FROM " + BRAND_TABLE_NAME;
        Cursor cursor = mDb.rawQuery(selectQuery, null);

        tempCompany.add(new Brand(0, resources.getString(R.string.label_choose_all), resources.getString(R.string.label_choose_all)));
        if (cursor.moveToFirst()) {
            do {
                Brand unit = new Brand();
                unit.setId(Integer.parseInt(cursor.getString(0)));
                unit.setName(cursor.getString(1));
                unit.setShort_desc(cursor.getString(2));
                unit.setBusiness_id(cursor.getInt(3));
                tempCompany.add(unit);

            } while (cursor.moveToNext());

        }

        return tempCompany;
    }

    // Insert all product
    public void fill(ArrayList<Brand> products) {
        if (!products.isEmpty()) {
            for (Brand product : products) {
                this.insert(product);
            }
        }
        //   mDb.close();
    }

    public void clear() {
        mDb.execSQL("DELETE FROM " + BRAND_TABLE_NAME);
    }

}
