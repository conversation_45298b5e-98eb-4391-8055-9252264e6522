package com.rising.high.tech.bigultimatenavdraw.db;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;

import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.model.Business;
import com.rising.high.tech.bigultimatenavdraw.model.Business_location;
import com.rising.high.tech.bigultimatenavdraw.model.Tax_rates;

import java.util.ArrayList;

public class BusinessLocationDbController extends DBController {

    private static final String TAG = "BusinessLocationDbContr";

    private static Context _context;
    // **********   Table "STATIONDbController" fields ********************************************************************

    public static final String STATION_TABLE_NAME = "business_locations";

    public static final String STATION_ID = "id"; //int
    public static final String STATION_BUSINESS_ID = "business_id";
    public static final String STATION_LOCATION_ID = "localtion_id";
    public static final String STATION_NAME = "name";
    public static final String STATION_COUNTRY = "country";
    public static final String STATION_CITY = "city";
    public static final String STATION_LANDMARK = "landmark";
    public static final String STATION_ZIP_CODE = "zip_code";
    public static final String STATION_MOBILE = "mobile";
    public static final String STATION_EMAIL = "email";
    public static final String STATION_IS_ACTIVE = "is_active";
    public static final String STATION_SYNC = "sync";
    public static final String STATION_LOCATION_SERVER_ID = "location_server_id";
    public static final String STATION_STATE = "state";

    public static final String STATION_TABLE_CREATE =
            "CREATE TABLE " + STATION_TABLE_NAME + " (" +
                    STATION_ID + " INTEGER PRIMARY KEY AUTOINCREMENT, " +
                    STATION_BUSINESS_ID + " INTEGER, " +
                    STATION_LOCATION_ID + " TEXT, " +
                    STATION_NAME + " TEXT, " +
                    STATION_COUNTRY + " TEXT, " +
                    STATION_CITY + " TEXT, " +
                    STATION_LANDMARK + " TEXT, " +
                    STATION_ZIP_CODE + " TEXT, " +
                    STATION_MOBILE + " TEXT, " +
                    STATION_EMAIL + " TEXT, " +
                    STATION_IS_ACTIVE + " INTEGER , " +
                    STATION_SYNC + " TEXT , " +
                    STATION_LOCATION_SERVER_ID + " INTEGER , " +
                    STATION_STATE + " TEXT) ;";

    public static final String STATION_TABLE_DROP = "DROP TABLE IF EXISTS " + STATION_TABLE_NAME + ";";


    public BusinessLocationDbController(Context context) {
        super(context);
        this._context = context;
    }


    public int insert(Business_location business_location) {
        // Create a new map of values, where column names are the keys
        ContentValues pValues = new ContentValues();

        pValues.put(STATION_ID, business_location.getId());
        pValues.put(STATION_BUSINESS_ID, business_location.getBusiness_id());
        pValues.put(STATION_LOCATION_ID, business_location.getLocaltion_id());
        pValues.put(STATION_NAME, business_location.getName());
        pValues.put(STATION_COUNTRY, business_location.getCountry());
        pValues.put(STATION_CITY, business_location.getCity());
        pValues.put(STATION_STATE, business_location.getState());

        pValues.put(STATION_LANDMARK, business_location.getLandmark());
        pValues.put(STATION_ZIP_CODE, business_location.getZip_code());
        pValues.put(STATION_MOBILE, business_location.getMobile());
        pValues.put(STATION_EMAIL, business_location.getEmail());
        pValues.put(STATION_IS_ACTIVE, business_location.getIs_active());
        pValues.put(STATION_SYNC, "yes");
        pValues.put(STATION_LOCATION_SERVER_ID, business_location.getLocation_server_id());

        // Insert the new row, returning the primary key value of the new row
        int newRowId = (int) mDb.insert(STATION_TABLE_NAME, null, pValues); //renvoie l'id de l'enregistrement créé

        // mDb.close();
        return newRowId;
    }

    public int insertLocal(Business_location business_location) {
        // Create a new map of values, where column names are the keys
        ContentValues pValues = new ContentValues();

        pValues.put(STATION_BUSINESS_ID, business_location.getBusiness_id());
        pValues.put(STATION_LOCATION_ID, business_location.getLocaltion_id());
        pValues.put(STATION_NAME, business_location.getName());
        pValues.put(STATION_COUNTRY, business_location.getCountry());
        pValues.put(STATION_CITY, business_location.getCity());
        pValues.put(STATION_LANDMARK, business_location.getLandmark());
        pValues.put(STATION_ZIP_CODE, business_location.getZip_code());
        pValues.put(STATION_MOBILE, business_location.getMobile());
        pValues.put(STATION_EMAIL, business_location.getEmail());
        pValues.put(STATION_IS_ACTIVE, business_location.getIs_active());
        pValues.put(STATION_SYNC,business_location.getSync());
        pValues.put(STATION_STATE, business_location.getState());

        // Insert the new row, returning the primary key value of the new row
        int newRowId = (int) mDb.insert(STATION_TABLE_NAME, null, pValues); //renvoie l'id de l'enregistrement créé

        // mDb.close();
        return newRowId;
    }


    public int update(Business_location business_location) {
        ContentValues pValues = new ContentValues();

        pValues.put(STATION_BUSINESS_ID, business_location.getBusiness_id());
        pValues.put(STATION_LOCATION_ID, business_location.getLocaltion_id());
        pValues.put(STATION_NAME, business_location.getName());
        pValues.put(STATION_COUNTRY, business_location.getCountry());
        pValues.put(STATION_CITY, business_location.getCity());
        pValues.put(STATION_LANDMARK, business_location.getLandmark());
        pValues.put(STATION_ZIP_CODE, business_location.getZip_code());
        pValues.put(STATION_MOBILE, business_location.getMobile());
        pValues.put(STATION_EMAIL, business_location.getEmail());
        pValues.put(STATION_IS_ACTIVE, business_location.getIs_active());
        pValues.put(STATION_SYNC,business_location.getSync());
        pValues.put(STATION_STATE,business_location.getState());
        return mDb.update(STATION_TABLE_NAME, pValues, STATION_ID + " = '" + business_location.getId() + "'", null);
    }
    
    public ArrayList<Business_location> getAllStation() {
        ArrayList<Business_location> tmpBusinesslocation = new ArrayList<>();
        String selectQuery = "SELECT  * FROM " + STATION_TABLE_NAME;
        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {
            do {
                Business_location business_location = new Business_location();
                business_location.setId(Integer.parseInt(cursor.getString(0)));
                business_location.setBusiness_id(Integer.parseInt(cursor.getString(1)));
                business_location.setLocaltion_id(cursor.getString(2));
                business_location.setName(cursor.getString(3));
                business_location.setCountry(cursor.getString(4));
                business_location.setCity(cursor.getString(5));
                business_location.setLandmark(cursor.getString(6));
                business_location.setZip_code(cursor.getString(7));
                business_location.setMobile(cursor.getString(8));
                business_location.setEmail(cursor.getString(9));
                business_location.setIs_active(cursor.getInt(10));
                business_location.setState(cursor.getString(13));
                tmpBusinesslocation.add(business_location);

            } while (cursor.moveToNext());

        }

        // mDb.close();

        return tmpBusinesslocation;
    }

    public int setSynBusinessLocation(Business_location business_location) {
        // Create a new map of values, where column names are the keys
        ContentValues pValues = new ContentValues();

        pValues.put(STATION_LOCATION_SERVER_ID, business_location.getLocation_server_id());
        pValues.put(STATION_SYNC, business_location.getSync());

        // Insert the new row, returning the primary key value of the new row
        int newRowId = mDb.update(STATION_TABLE_NAME, pValues, STATION_ID + " = '" + business_location.getId() + "'", null); //renvoie l'id de l'enregistrement créé

        // mDb.close();
        return newRowId;
    }
    
    public ArrayList<Business_location> getSyncStation(String sync) {
        ArrayList<Business_location> tmpBusinesslocation = new ArrayList<>();
        String selectQuery = "SELECT  * FROM " + STATION_TABLE_NAME + " WHERE " + STATION_SYNC + " = '" + sync  + "'";
        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {
            do {
                Business_location business_location = new Business_location();
                business_location.setId(Integer.parseInt(cursor.getString(0)));
                business_location.setBusiness_id(Integer.parseInt(cursor.getString(1)));
                business_location.setLocaltion_id(cursor.getString(2));
                business_location.setName(cursor.getString(3));
                business_location.setCountry(cursor.getString(4));
                business_location.setCity(cursor.getString(5));
                business_location.setLandmark(cursor.getString(6));
                business_location.setZip_code(cursor.getString(7));
                business_location.setMobile(cursor.getString(8));
                business_location.setEmail(cursor.getString(9));
                business_location.setIs_active(cursor.getInt(10));
                business_location.setState(cursor.getString(13));

                tmpBusinesslocation.add(business_location);

            } while (cursor.moveToNext());

        }

        // mDb.close();

        return tmpBusinesslocation;
    }

    public ArrayList<Business_location> getAllStationSpinner() {
        ArrayList<Business_location> tmpBusinesslocation = new ArrayList<>();
        String selectQuery = "SELECT  * FROM " + STATION_TABLE_NAME;
        Cursor cursor = mDb.rawQuery(selectQuery, null);
        Business_location businesslocation = new Business_location(0, _context.getResources().getString(R.string.lbl_please_select_station));
        tmpBusinesslocation.add(businesslocation);
        if (cursor.moveToFirst()) {
            do {
                Business_location business_location = new Business_location();
                business_location.setId(Integer.parseInt(cursor.getString(0)));
                business_location.setBusiness_id(Integer.parseInt(cursor.getString(1)));
                business_location.setLocaltion_id(cursor.getString(2));
                business_location.setName(cursor.getString(3));
                business_location.setCountry(cursor.getString(4));
                business_location.setCity(cursor.getString(5));

                business_location.setLandmark(cursor.getString(6));
                business_location.setZip_code(cursor.getString(7));
                business_location.setMobile(cursor.getString(8));
                business_location.setEmail(cursor.getString(9));
                business_location.setIs_active(cursor.getInt(10));
                business_location.setState(cursor.getString(13));

                tmpBusinesslocation.add(business_location);

            } while (cursor.moveToNext());
        }

        // mDb.close();

        return tmpBusinesslocation;
    }

    public ArrayList<Business_location> getStationsById(int id) {
        ArrayList<Business_location> tmpBusinesslocation = new ArrayList<>();
        String selectQuery = "SELECT  * FROM " + STATION_TABLE_NAME + " WHERE " + STATION_BUSINESS_ID + " = '" + id + "'";
        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {
            do {
                Business_location business_location = new Business_location();
                business_location.setId(Integer.parseInt(cursor.getString(0)));
                business_location.setBusiness_id(Integer.parseInt(cursor.getString(1)));
                business_location.setLocaltion_id(cursor.getString(2));
                business_location.setName(cursor.getString(3));
                business_location.setCountry(cursor.getString(4));
                business_location.setCity(cursor.getString(5));

                business_location.setLandmark(cursor.getString(6));
                business_location.setZip_code(cursor.getString(7));
                business_location.setMobile(cursor.getString(8));
                business_location.setEmail(cursor.getString(9));
                business_location.setIs_active(cursor.getInt(10));
                business_location.setState(cursor.getString(13));

                tmpBusinesslocation.add(business_location);

            } while (cursor.moveToNext());
        }

        // mDb.close();
        return tmpBusinesslocation;
    }

    public Business_location getStationById(int id) {
        Business_location business_location = new Business_location();

        String selectQuery = "SELECT  * FROM " + STATION_TABLE_NAME + " WHERE " + STATION_ID + " = '" + id + "'";
        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {
            business_location.setId(Integer.parseInt(cursor.getString(0)));
            business_location.setBusiness_id(Integer.parseInt(cursor.getString(1)));
            business_location.setLocaltion_id(cursor.getString(2));
            business_location.setName(cursor.getString(3));
            business_location.setCountry(cursor.getString(4));
            business_location.setCity(cursor.getString(5));
            business_location.setState(cursor.getString(5));

            business_location.setLandmark(cursor.getString(6));
            business_location.setZip_code(cursor.getString(7));
            business_location.setMobile(cursor.getString(8));
            business_location.setEmail(cursor.getString(9));
            business_location.setIs_active(cursor.getInt(10));
            business_location.setLocation_server_id(cursor.getInt(12));
            business_location.setState(cursor.getString(13));

        }

        // mDb.close();
        return business_location;
    }

    public ArrayList<Business_location> getCategoriesLike(String name) {
        ArrayList<Business_location> tmpBusinesslocation = new ArrayList<>();
        String selectQuery = "SELECT  * FROM " + STATION_TABLE_NAME + " WHERE " + STATION_NAME + " LIKE '%" + name + "%' ";


        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {
            do {
                Business_location business_location = new Business_location();
                business_location.setId(Integer.parseInt(cursor.getString(0)));
                business_location.setBusiness_id(Integer.parseInt(cursor.getString(1)));
                business_location.setLocaltion_id(cursor.getString(2));
                business_location.setName(cursor.getString(3));
                business_location.setCountry(cursor.getString(4));
                business_location.setCity(cursor.getString(5));

                business_location.setLandmark(cursor.getString(6));
                business_location.setZip_code(cursor.getString(7));
                business_location.setMobile(cursor.getString(8));
                business_location.setEmail(cursor.getString(9));
                business_location.setIs_active(cursor.getInt(10));
                business_location.setState(cursor.getString(13));

                tmpBusinesslocation.add(business_location);

            } while (cursor.moveToNext());
        }
        // mDb.close();
        return tmpBusinesslocation;
    }
    
    // Insert all product
    public void fill(ArrayList<Business_location> products) {
        if (!products.isEmpty()) {
            for (Business_location product : products) {
                this.insert(product);
            }
        }
        //   mDb.close();
    }


    public void deleteItem(Integer id) {
        mDb.execSQL("delete from " + STATION_TABLE_NAME + " WHERE " + STATION_ID + " = " + id);
    }
    
    public void clear() {
        mDb.execSQL("DELETE FROM " + STATION_TABLE_NAME);
    }
}
