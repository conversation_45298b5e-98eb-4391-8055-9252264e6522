package com.rising.high.tech.bigultimatenavdraw.db;

import android.content.ContentValues;
import android.content.Context;
import android.content.res.Resources;
import android.database.Cursor;
import android.util.Log;

import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.model.Category;


import java.util.ArrayList;

public class CategoryDbController extends DBController {

    Context _context;
    Resources resources;
    // **********   Table "CATERORY" fields ********************************************************************

    public static final String CATEGORY_TABLE_NAME = "categories";
    private static final String TAG = "CategoryDbController";

    public static final String CATEGORY_ID = "id"; //int
    public static final String CATEGORY_NAME = "name";
    public static final String CATEGORY_BUSINESS_ID = "business_id";
    public static final String CATEGORY_PARENT_ID = "parent_id";
    public static final String CATEGORY_CREATED_BY = "created_by";
    public static final String CATEGORY_CATEGORY_TYPE = "category_type";
    public static final String CATEGORY_DESCREPTION = "description";
    public static final String CATEGORY_SLUG = "slug";
    public static final String CATEGORY_SHORT_CODE = "short_code";
    public static final String CATEGORY_IS_SYNC = "is_sync";
    public static final String CATEGORY_SERVER_ID = "category_server_id";

    public static final String CATEGORY_TABLE_CREATE =
            "CREATE TABLE " + CATEGORY_TABLE_NAME + " (" +
                    CATEGORY_ID + " INTEGER PRIMARY KEY AUTOINCREMENT , " +
                    CATEGORY_NAME + " TEXT, " +
                    CATEGORY_BUSINESS_ID + " INTEGER, " +
                    CATEGORY_PARENT_ID + " INTEGER, " +
                    CATEGORY_CREATED_BY + " INTEGER, " +
                    CATEGORY_CATEGORY_TYPE + " TEXT, " +
                    CATEGORY_DESCREPTION + " TEXT, " +
                    CATEGORY_SLUG + " TEXT, " +
                    CATEGORY_SHORT_CODE + " TEXT, " +
                    CATEGORY_IS_SYNC + " TEXT, " +
                    CATEGORY_SERVER_ID + " INTEGER) ;";

    public static final String CATEGORY_TABLE_DROP = "DROP TABLE IF EXISTS " + CATEGORY_TABLE_NAME + ";";

    public CategoryDbController(Context context) {
        super(context);
        _context=context;
        resources=_context.getResources();
    }

    public int insert(Category category) {
        // Create a new map of values, where column names are the keys
        ContentValues pValues = new ContentValues();

        pValues.put(CATEGORY_ID, category.getId());
        pValues.put(CATEGORY_NAME, category.getName());
        pValues.put(CATEGORY_BUSINESS_ID, category.getBusiness_id());
        pValues.put(CATEGORY_SHORT_CODE, category.getShort_code());
        pValues.put(CATEGORY_PARENT_ID, category.getParent_id());
        pValues.put(CATEGORY_CREATED_BY, category.getCreated_by());
        pValues.put(CATEGORY_CATEGORY_TYPE, category.getCategory_type());
        pValues.put(CATEGORY_DESCREPTION, category.getDescription());
        pValues.put(CATEGORY_SLUG, category.getSlug());
        pValues.put(CATEGORY_IS_SYNC, category.getIs_sync());

        int newRowId = (int) mDb.insert(CATEGORY_TABLE_NAME, null, pValues); //renvoie l'id de l'enregistrement créé
        return newRowId;
    }

    public int insertLocal(Category category) {
        // Create a new map of values, where column names are the keys
        ContentValues pValues = new ContentValues();

        pValues.put(CATEGORY_NAME, category.getName());
        pValues.put(CATEGORY_BUSINESS_ID, category.getBusiness_id());
        pValues.put(CATEGORY_SHORT_CODE, category.getShort_code());
        pValues.put(CATEGORY_PARENT_ID, category.getParent_id() != null ? category.getParent_id() : 0);
        pValues.put(CATEGORY_CATEGORY_TYPE, category.getCategory_type());
        pValues.put(CATEGORY_CREATED_BY, category.getCreated_by());
        pValues.put(CATEGORY_CATEGORY_TYPE, category.getCategory_type());
        pValues.put(CATEGORY_DESCREPTION, category.getDescription());
        pValues.put(CATEGORY_SLUG, category.getSlug());
        pValues.put(CATEGORY_IS_SYNC, category.getIs_sync());

        int newRowId = (int) mDb.insert(CATEGORY_TABLE_NAME, null, pValues); //renvoie l'id de l'enregistrement créé
        return newRowId;
    }


    public int editCategory(Category category) {
        // Create a new map of values, where column names are the keys
        ContentValues pValues = new ContentValues();

        pValues.put(CATEGORY_NAME, category.getName());
        pValues.put(CATEGORY_BUSINESS_ID, category.getBusiness_id());
        pValues.put(CATEGORY_SHORT_CODE, category.getShort_code());
        pValues.put(CATEGORY_PARENT_ID, category.getParent_id());
        pValues.put(CATEGORY_CREATED_BY, category.getCreated_by());
        pValues.put(CATEGORY_CATEGORY_TYPE, category.getCategory_type());
        pValues.put(CATEGORY_DESCREPTION, category.getDescription());
        pValues.put(CATEGORY_SLUG, category.getSlug());

        int newRowId = mDb.update(CATEGORY_TABLE_NAME, pValues, CATEGORY_ID + " = '" + category.getId() + "'", null); //renvoie l'id de l'enregistrement créé

        return newRowId;
    }

    public int editServerCategory(Category category) {
        // Create a new map of values, where column names are the keys
        ContentValues pValues = new ContentValues();

        pValues.put(CATEGORY_IS_SYNC, category.getIs_sync());
        pValues.put(CATEGORY_SERVER_ID, category.getCategory_server_id());

        int newRowId = mDb.update(CATEGORY_TABLE_NAME, pValues, CATEGORY_ID + " = '" + category.getId() + "'", null); //renvoie l'id de l'enregistrement créé

        return newRowId;
    }

    public void fill(ArrayList<Category> categories) {
        if (!categories.isEmpty()) {
            for (Category product : categories) {
                this.insert(product);
            }
        }
    }


    public void deleteItem(int id) {
        mDb.execSQL("delete from " + CATEGORY_TABLE_NAME + " WHERE " + CATEGORY_ID + " = '" + id + "'");
    }

    public void deleteAll() {
        mDb.execSQL("delete from " + CATEGORY_TABLE_NAME);
    }

    public ArrayList<Category> getAllCategory() {
        ArrayList<Category> tmpCategory = new ArrayList<>();
        String selectQuery = "SELECT  * FROM " + CATEGORY_TABLE_NAME;
        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {
            do {
                Category category = new Category();
                category.setId(cursor.getInt(0));
                category.setName(cursor.getString(1));
                category.setBusiness_id(Integer.parseInt(cursor.getString(2)));
                category.setParent_id(cursor.getInt(3));
                category.setCreated_by(cursor.getInt(4));
                category.setCategory_type(cursor.getString(5));
                category.setDescription(cursor.getString(6));
                category.setSlug(cursor.getString(7));
                category.setShort_code(cursor.getString(8));

                tmpCategory.add(category);

            } while (cursor.moveToNext());
        }


        return tmpCategory;

    }

    public ArrayList<Category> getAllMainCategory() {
        ArrayList<Category> tmpCategory = new ArrayList<>();
        String selectQuery = "SELECT  * FROM " + CATEGORY_TABLE_NAME + " WHERE " + CATEGORY_PARENT_ID + " = " + '0';
        Cursor cursor = mDb.rawQuery(selectQuery, null);
        Category category_ = new Category(0, _context.getResources().getString(R.string.select_category_spin));
        tmpCategory.add(category_);
        if (cursor.moveToFirst()) {
            do {
                Category category = new Category();
                category.setId(cursor.getInt(0));
                category.setName(cursor.getString(1));
                category.setBusiness_id(Integer.parseInt(cursor.getString(2)));
                category.setParent_id(cursor.getInt(3));
                category.setCreated_by(cursor.getInt(4));
                category.setCategory_type(cursor.getString(5));
                category.setDescription(cursor.getString(6));
                category.setSlug(cursor.getString(7));
                category.setShort_code(cursor.getString(8));

                tmpCategory.add(category);

            } while (cursor.moveToNext());
        }


        return tmpCategory;

    }


    public ArrayList<Category> getSyncCategory(String status) {
        ArrayList<Category> tmpCategory = new ArrayList<>();
        String selectQuery = "SELECT  * FROM " + CATEGORY_TABLE_NAME + " WHERE " + CATEGORY_IS_SYNC + " = '" + status + "'" + " AND " + CATEGORY_PARENT_ID + " = " + 0 + " OR " +CATEGORY_PARENT_ID +" IS NULL " ;

        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {
            do {
                Category category = new Category();
                category.setId(cursor.getInt(0));
                category.setName(cursor.getString(1));
                category.setBusiness_id(Integer.parseInt(cursor.getString(2)));
                category.setParent_id(cursor.getInt(3) != 0 ? cursor.getInt(3) : null);
                category.setCreated_by(cursor.getInt(4));
                category.setCategory_type(cursor.getString(5));
                category.setDescription(cursor.getString(6));
                category.setSlug(cursor.getString(7));
                category.setShort_code(cursor.getString(8));

                tmpCategory.add(category);

            } while (cursor.moveToNext());
        }


        return tmpCategory;

    }


    public ArrayList<Category> getSyncSubCategory(String status) {
        ArrayList<Category> tmpCategory = new ArrayList<>();
        String selectQuery = "SELECT  * FROM " + CATEGORY_TABLE_NAME + " WHERE " + CATEGORY_IS_SYNC + " = '" + status + "'" + " AND " + CATEGORY_PARENT_ID + " != " + 0;

        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {
            do {
                Category category = new Category();
                category.setId(cursor.getInt(0));
                category.setName(cursor.getString(1));
                category.setBusiness_id(cursor.getInt(2));

                Category category1 = this.getCategoryById(cursor.getInt(3));
                category.setParent_id(category1.getCategory_server_id());

                category.setCreated_by(cursor.getInt(4));
                category.setCategory_type(cursor.getString(5));
                category.setDescription(cursor.getString(6));
                category.setSlug(cursor.getString(7));
                category.setShort_code(cursor.getString(8));

                tmpCategory.add(category);

            } while (cursor.moveToNext());
        }


        return tmpCategory;

    }

    public ArrayList<Category> getCategoriesLike(String name) {
        ArrayList<Category> tmpCategory = new ArrayList<>();
        String selectQuery = "SELECT  * FROM " + CATEGORY_TABLE_NAME + " WHERE " + CATEGORY_NAME + " LIKE '%" + name + "%' ";
        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {
            do {
                Category category = new Category();
                category.setId(cursor.getInt(0));
                category.setName(cursor.getString(1));
                category.setBusiness_id(Integer.parseInt(cursor.getString(2)));
                category.setParent_id(cursor.getInt(3));
                category.setCreated_by(cursor.getInt(4));
                category.setCategory_type(cursor.getString(5));
                category.setDescription(cursor.getString(6));
                category.setSlug(cursor.getString(7));
                category.setShort_code(cursor.getString(8));

                tmpCategory.add(category);

            } while (cursor.moveToNext());
        }


        return tmpCategory;

    }

    public Category getCategoryById(Integer id) {
        Category category = new Category();

        String selectQuery = "SELECT  * FROM " + CATEGORY_TABLE_NAME + " WHERE " + CATEGORY_ID + " = " + id;
        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {
            category.setId(cursor.getInt(0));
            category.setName(cursor.getString(1));
            category.setBusiness_id(cursor.getInt(2));
            category.setParent_id(cursor.getInt(3));
            category.setCreated_by(cursor.getInt(4));
            category.setCategory_type(cursor.getString(5));
            category.setDescription(cursor.getString(6));
            category.setSlug(cursor.getString(7));
            category.setShort_code(cursor.getString(8));
            category.setCategory_server_id(cursor.getInt(10));
        }
        return category;
    }

    public ArrayList<Category> getSubCategoryById(Integer id) {
        ArrayList<Category> tmpCategory = new ArrayList<>();

        String selectQuery = "SELECT  * FROM " + CATEGORY_TABLE_NAME + " WHERE " + CATEGORY_PARENT_ID + " = " + id;
        Cursor cursor = mDb.rawQuery(selectQuery, null);
        Category category_ = new Category(0, "Select Sub Category");
        tmpCategory.add(category_);
        if (cursor.moveToFirst()) {
            do {
                Category category = new Category();
                category.setId(cursor.getInt(0));
                category.setName(cursor.getString(1));
                category.setBusiness_id(Integer.parseInt(cursor.getString(2)));
                category.setParent_id(cursor.getInt(3));
                category.setCreated_by(cursor.getInt(4));
                category.setCategory_type(cursor.getString(5));
                category.setDescription(cursor.getString(6));
                category.setSlug(cursor.getString(7));
                category.setShort_code(cursor.getString(8));

                tmpCategory.add(category);

            } while (cursor.moveToNext());
        }
        return tmpCategory;
    }

    public Integer getCategoryServerId(Integer id) {
        Category category = new Category();

        String selectQuery = "SELECT  * FROM " + CATEGORY_TABLE_NAME + " WHERE " + CATEGORY_ID + " = " + id;
        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {
            category.setCategory_server_id(cursor.getInt(10));
        }

        return category.getCategory_server_id();
    }

    public ArrayList<Category> getAllCategorySpinner() {
        ArrayList<Category> tmpCategory = new ArrayList<>();
        String selectQuery = "SELECT  * FROM " + CATEGORY_TABLE_NAME;
        Cursor cursor = mDb.rawQuery(selectQuery, null);
        Category xcategory = new Category(0, resources.getString(R.string.label_choose_all));
        xcategory.setSelected(true); ;
        tmpCategory.add(xcategory);
        if (cursor.moveToFirst()) {
            do {
                Category category = new Category();
                category.setId(cursor.getInt(0));
                category.setName(cursor.getString(1));
                category.setBusiness_id(Integer.parseInt(cursor.getString(2)));
                category.setParent_id(cursor.getInt(3));
                category.setCreated_by(cursor.getInt(4));
                category.setCategory_type(cursor.getString(5));
                category.setDescription(cursor.getString(6));
                category.setSlug(cursor.getString(7));
                category.setShort_code(cursor.getString(8));

                tmpCategory.add(category);

            } while (cursor.moveToNext());
        }


        return tmpCategory;

    }
}
