package com.rising.high.tech.bigultimatenavdraw.db;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;

import com.rising.high.tech.bigultimatenavdraw.model.Business;
import com.rising.high.tech.bigultimatenavdraw.model.Transaction;
import com.rising.high.tech.bigultimatenavdraw.model.User;

import java.util.ArrayList;

public class CompanyDbController extends DBController {

    public static final String COMPANY_TABLE_NAME = "business";

    public static final String COMPANY_ID = "id"; //int
    public static final String COMPANY_NAME = "name";
    public static final String COMPANY_START_DATE = "start_date";
    public static final String COMPANY_FY_START_MONTH = "fy_start_month";
    public static final String COMPANY_CURRENCY_ID = "currency_id";
    public static final String COMPANY_CURRENCY_SYMBOL_PLACEMENT = "currency_symbol_placement";
    public static final String COMPANY_TIMEZONE = "time_zone";
    public static final String COMPANY_ACCOUNTING_METHOD = "accounting_method";
    public static final String COMPANY_TRANSACTION_EDIT_DAYS = "transaction_edit_days";
    public static final String COMPANY_DATE_FORMAT = "date_format";
    public static final String COMPANY_TIME_FORMAT = "time_format";
    public static final String COMPANY_DEFAULT_PROFIT_PERCENT = "default_profit_percent";
    public static final String COMPANY_LOGO = "logo";
    public static final String COMPANY_SYNC = "sync";
    public static final String COMPANY_SERVER_ID = "business_server_id";


    public static final String COMPANY_TABLE_CREATE =
            "CREATE TABLE " + COMPANY_TABLE_NAME + " (" +
                    COMPANY_ID + " INTEGER PRIMARY KEY AUTOINCREMENT, " +
                    COMPANY_NAME + " TEXT, " +
                    COMPANY_START_DATE + " TEXT, " +
                    COMPANY_FY_START_MONTH + " TEXT, " +
                    COMPANY_CURRENCY_ID + " INTEGER, " +
                    COMPANY_CURRENCY_SYMBOL_PLACEMENT + " TEXT, " +
                    COMPANY_TIMEZONE + " TEXT, " +
                    COMPANY_ACCOUNTING_METHOD + " TEXT, " +
                    COMPANY_TRANSACTION_EDIT_DAYS + " TEXT, " +
                    COMPANY_DATE_FORMAT + " TEXT, " +
                    COMPANY_TIME_FORMAT + " TEXT, " +
                    COMPANY_DEFAULT_PROFIT_PERCENT + " TEXT, " +
                    COMPANY_LOGO + " TEXT, " +
                    COMPANY_SYNC + " TEXT, " +
                    COMPANY_SERVER_ID + " TEXT) ;";

    public static final String COMPANY_TABLE_DROP = "DROP TABLE IF EXISTS " + COMPANY_TABLE_NAME + ";";

    public CompanyDbController(Context context) {
        super(context);
    }

    public int insert(Business business) {
        // Create a new map of values, where column names are the keys
        ContentValues pValues = new ContentValues();

        pValues.put(COMPANY_NAME, business.getName());
        pValues.put(COMPANY_START_DATE, business.getStart_date());
        pValues.put(COMPANY_FY_START_MONTH, business.getFy_start_month());
        pValues.put(COMPANY_CURRENCY_ID, business.getCurrency_id());
        pValues.put(COMPANY_CURRENCY_SYMBOL_PLACEMENT, business.getCurrency_symbol_placement());
        pValues.put(COMPANY_TIMEZONE, business.getTime_zone());
        pValues.put(COMPANY_ACCOUNTING_METHOD, business.getAccounting_method());
        pValues.put(COMPANY_TRANSACTION_EDIT_DAYS, business.getTransaction_edit_days());
        pValues.put(COMPANY_DATE_FORMAT, business.getDate_format());
        pValues.put(COMPANY_TIME_FORMAT, business.getTime_format());
        pValues.put(COMPANY_DEFAULT_PROFIT_PERCENT, business.getDefault_profit_percent());
        pValues.put(COMPANY_LOGO, business.getLogo());
        pValues.put(COMPANY_SYNC, business.getSync());

        return (int) mDb.insert(COMPANY_TABLE_NAME, null, pValues);
    }

    public int update(Business business) {
        ContentValues pValues = new ContentValues();
        pValues.put(COMPANY_NAME, business.getName());
        pValues.put(COMPANY_START_DATE, business.getStart_date());
        pValues.put(COMPANY_FY_START_MONTH, business.getFy_start_month());
        pValues.put(COMPANY_CURRENCY_ID, business.getCurrency_id());
        pValues.put(COMPANY_CURRENCY_SYMBOL_PLACEMENT, business.getCurrency_symbol_placement());
        pValues.put(COMPANY_TIMEZONE, business.getTime_zone());
        pValues.put(COMPANY_ACCOUNTING_METHOD, business.getAccounting_method());
        pValues.put(COMPANY_TRANSACTION_EDIT_DAYS, business.getTransaction_edit_days());
        pValues.put(COMPANY_DATE_FORMAT, business.getDate_format());
        pValues.put(COMPANY_TIME_FORMAT, business.getTime_format());
        pValues.put(COMPANY_DEFAULT_PROFIT_PERCENT, business.getDefault_profit_percent());
        pValues.put(COMPANY_LOGO, business.getLogo());
        return mDb.update(COMPANY_TABLE_NAME, pValues, COMPANY_ID + " = '" + business.getId() + "'", null);
    }

    public Business getCompanyData() {
        String selectQuery = "SELECT  * FROM " + COMPANY_TABLE_NAME;
        Cursor cursor = mDb.rawQuery(selectQuery, null);
        Business xCompany = new Business();
        if (cursor.moveToFirst()) {
            do {

                xCompany.setId(cursor.getInt(0));
                xCompany.setName(cursor.getString(1));
                xCompany.setStart_date(cursor.getString(2));
                xCompany.setFy_start_month(cursor.getString(3));
                xCompany.setCurrency_id(cursor.getInt(4));
                xCompany.setCurrency_symbol_placement(cursor.getString(5));
                xCompany.setTime_zone(cursor.getString(6));
                xCompany.setAccounting_method(cursor.getString(7));
                xCompany.setTransaction_edit_days(cursor.getString(8));
                xCompany.setDate_format(cursor.getString(9));
                xCompany.setTime_format(cursor.getString(10));
                xCompany.setDefault_profit_percent(cursor.getString(11));
                xCompany.setLogo(cursor.getString(12));

            } while (cursor.moveToNext());

        }
        return xCompany;
    }
    public Business getSyncCompanyData(String sync) {
        String selectQuery = "SELECT  * FROM " + COMPANY_TABLE_NAME + " WHERE " + COMPANY_SYNC  + " = '" + sync  + "'";
        Cursor cursor = mDb.rawQuery(selectQuery, null);
        Business xCompany = new Business();
        if (cursor.moveToFirst()) {
            do {

                xCompany.setId(cursor.getInt(0));
                xCompany.setName(cursor.getString(1));
                xCompany.setStart_date(cursor.getString(2));
                xCompany.setFy_start_month(cursor.getString(3));
                xCompany.setCurrency_id(cursor.getInt(4));
                xCompany.setCurrency_symbol_placement(cursor.getString(5));
                xCompany.setTime_zone(cursor.getString(6));
                xCompany.setAccounting_method(cursor.getString(7));
                xCompany.setTransaction_edit_days(cursor.getString(8));
                xCompany.setDate_format(cursor.getString(9));
                xCompany.setTime_format(cursor.getString(10));
                xCompany.setDefault_profit_percent(cursor.getString(11));
                xCompany.setLogo(cursor.getString(12));

            } while (cursor.moveToNext());

        }
        return xCompany;
    }

    public int setSyncCompany(Business business) {
        // Create a new map of values, where column names are the keys
        ContentValues pValues = new ContentValues();

        pValues.put(COMPANY_SYNC, business.getSync());

        // Insert the new row, returning the primary key value of the new row
        int newRowId = mDb.update(COMPANY_TABLE_NAME, pValues, COMPANY_ID + " = '" + business.getId() + "'", null); //renvoie l'id de l'enregistrement créé

        // mDb.close();
        return newRowId;
    }
    // Insert all product
    public void fill(ArrayList<Business> products) {
        if (!products.isEmpty()) {
            for (Business product : products) {
                this.insert(product);
            }
        }
        //   mDb.close();
    }

    public void clear() {
        mDb.execSQL("DELETE FROM " + COMPANY_TABLE_NAME);
    }

}
