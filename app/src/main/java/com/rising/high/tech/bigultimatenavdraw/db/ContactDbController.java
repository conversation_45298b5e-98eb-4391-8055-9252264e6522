package com.rising.high.tech.bigultimatenavdraw.db;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.util.Log;

import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.model.Business;
import com.rising.high.tech.bigultimatenavdraw.model.Contact;
import com.rising.high.tech.bigultimatenavdraw.model.Product;

import java.util.ArrayList;

public class ContactDbController extends DBController {

    private static final String TAG = "ContactDbController";
    private static Context _context;
    // **********   Table "CONTACT" fields ********************************************************************

    public static final String CONTACT_TABLE_NAME = "contacts";

    public static final String CONTACT_ID = "id"; //int
    public static final String CONTACT_NAME = "name";
    public static final String CONTACT_FIRST_NAME = "first_name";
    public static final String CONTACT_LAST_NAME = "last_name";
    public static final String CONTACT_DOB = "dob";
    public static final String CONTACT_SUPPLIER_NAME = "supplier_business_name";
    public static final String CONTACT_EMAIL = "email";
    public static final String CONTACT_MOBILE = "mobile";
    public static final String CONTACT_CITY = "city";
    public static final String CONTACT_STATE = "state";
    public static final String CONTACT_COUNTRY = "country";
    public static final String CONTACT_LAND_MARK = "land_mark";
    public static final String CONTACT_CONTACT = "customer";
    public static final String CONTACT_CRUSTOMER_ID = "customer_id";
    public static final String CONTACT_LOCATION = "location";
    public static final String CONTACT_CREADTED_BY = "created_by";
    public static final String CONTACT_BUSINESS_ID = "business_id";
    public static final String CONTACT_TYPE = "type";
    public static final String CONTACT_SYNC = "sync";
    public static final String CONTACT_ID_LOCATION = "id_location";
    public static final String CONTACT_ADDRESS_LINE = "address_line_1";
    public static final String CONTACT_CONTACT_ID = "contact_id";
    public static final String CONTACT_CONTACT_STATUS = "contact_status";
    public static final String CONTACT_CREATED_AT = "created_at";
    public static final String CONTACT_SHIPPING_ADRESSE = "shipping_address";
    public static final String CONTACT_ZIPE_CODE = "zip_code";
    public static final String CONTACT_TAX_NUMBER = "tax_number";
    public static final String CONTACT_BALANCE = "balance";
    public static final String CONTACT_PAY_TERM_NUMBER = "pay_term_number";
    public static final String CONTACT_PAY_TERM_TYPE = "pay_term_type";
    public static final String CONTACT_CREDIT_LIMIT = "credit_limit";
    public static final String CONTACT_PREFIX = "prefix";
    public static final String CONTACT_GROUP_ID = "customer_group_id";
    public static final String CONTACT_SERVER_ID = "contact_server_id";


    public static final String CONTACT_TABLE_CREATE =
            "CREATE TABLE " + CONTACT_TABLE_NAME + " (" +
                    CONTACT_ID + " INTEGER PRIMARY KEY AUTOINCREMENT , " +
                    CONTACT_NAME + " TEXT, " +
                    CONTACT_EMAIL + " TEXT, " +
                    CONTACT_MOBILE + " TEXT, " +
                    CONTACT_CITY + " TEXT, " +
                    CONTACT_STATE + " TEXT, " +
                    CONTACT_COUNTRY + " TEXT, " +
                    CONTACT_LAND_MARK + " TEXT, " +
                    CONTACT_CONTACT + " TEXT, " +
                    CONTACT_CRUSTOMER_ID + " TEXT, " +
                    CONTACT_LOCATION + " TEXT, " +
                    CONTACT_CREADTED_BY + " TEXT, " +
                    CONTACT_BUSINESS_ID + " INTEGER, " +
                    CONTACT_TYPE + " TEXT, " +
                    CONTACT_SYNC + " TEXT, " +
                    CONTACT_ID_LOCATION + " TEXT, " +
                    CONTACT_CONTACT_ID + " TEXT, " +
                    CONTACT_CONTACT_STATUS + " TEXT, " +
                    CONTACT_FIRST_NAME + " TEXT, " +
                    CONTACT_LAST_NAME + " TEXT, " +
                    CONTACT_SUPPLIER_NAME + " TEXT, " +
                    CONTACT_DOB + " TEXT, " +
                    CONTACT_ADDRESS_LINE + " TEXT, " +
                    CONTACT_CREATED_AT + " TEXT, " +
                    CONTACT_SHIPPING_ADRESSE + " TEXT, " +
                    CONTACT_ZIPE_CODE + " TEXT, " +
                    CONTACT_TAX_NUMBER + " TEXT, " +
                    CONTACT_BALANCE + " TEXT, " +
                    CONTACT_PAY_TERM_NUMBER + " TEXT, " +
                    CONTACT_PAY_TERM_TYPE + " INTEGER , " +
                    CONTACT_CREDIT_LIMIT + " TEXT , " +
                    CONTACT_PREFIX + " TEXT , " +
                    CONTACT_GROUP_ID + " INTEGER , " +
                    CONTACT_SERVER_ID + " INTEGER) ;";

    public static final String CONTACT_TABLE_DROP = "DROP TABLE IF EXISTS " + CONTACT_TABLE_NAME + ";";

    public ContactDbController(Context context) {
        super(context);
        _context = context;
    }

    public int insert(Contact contact) {
        // Create a new map of values, where column names are the keys
        ContentValues Values = new ContentValues();
        Values.put(CONTACT_ID, contact.getId());
        Values.put(CONTACT_NAME, contact.getName());
        Values.put(CONTACT_EMAIL, contact.getEmail());
        Values.put(CONTACT_MOBILE, contact.getMobile());
        Values.put(CONTACT_CITY, contact.getCity());
        Values.put(CONTACT_STATE, contact.getState());
        Values.put(CONTACT_COUNTRY, contact.getCountry());
        Values.put(CONTACT_LAND_MARK, contact.getLand_mark());
        Values.put(CONTACT_CONTACT, contact.getCustomer());
        Values.put(CONTACT_CRUSTOMER_ID, contact.getCustomer_id());
        Values.put(CONTACT_LOCATION, contact.getLocation());
        Values.put(CONTACT_CREADTED_BY, contact.getCreated_by());
        Values.put(CONTACT_BUSINESS_ID, contact.getBusiness_id());
        Values.put(CONTACT_TYPE, contact.getType());
        Values.put(CONTACT_ID_LOCATION, contact.getId_location());
        Values.put(CONTACT_CONTACT_ID, contact.getContact_id());
        Values.put(CONTACT_CONTACT_STATUS, contact.getContact_status());
        Values.put(CONTACT_FIRST_NAME, contact.getFirst_name());
        Values.put(CONTACT_LAST_NAME, contact.getLast_name());
        Values.put(CONTACT_SUPPLIER_NAME, contact.getSupplier_business_name());
        Values.put(CONTACT_DOB, contact.getDob());
        Values.put(CONTACT_ADDRESS_LINE, contact.getAddress_line_1());
        Values.put(CONTACT_SYNC, contact.getSync() != null ? contact.getSync() : "yes");
        Values.put(CONTACT_CREATED_AT, contact.getCreated_at());
        Values.put(CONTACT_SHIPPING_ADRESSE, contact.getShipping_address());
        Values.put(CONTACT_ZIPE_CODE, contact.getZip_code());

        Values.put(CONTACT_TAX_NUMBER, contact.getTax_number());
        Values.put(CONTACT_BALANCE, contact.getBalance());
        Values.put(CONTACT_PAY_TERM_NUMBER, contact.getPay_term_number());
        Values.put(CONTACT_PAY_TERM_TYPE, contact.getPay_term_type());
        Values.put(CONTACT_CREDIT_LIMIT, contact.getCredit_limit());
        Values.put(CONTACT_PREFIX, contact.getPrefix());
        Values.put(CONTACT_GROUP_ID, contact.getCustomer_group_id());

        int newRowId = (int) mDb.insert(CONTACT_TABLE_NAME, null, Values); //renvoie l'id de l'enregistrement créé
        return newRowId;
    }

    public int insertLocal(Contact contact) {
        // Create a new map of values, where column names are the keys
        ContentValues Values = new ContentValues();

        Values.put(CONTACT_NAME, contact.getName());
        Values.put(CONTACT_EMAIL, contact.getEmail());
        Values.put(CONTACT_MOBILE, contact.getMobile());
        Values.put(CONTACT_CITY, contact.getCity());
        Values.put(CONTACT_STATE, contact.getState());
        Values.put(CONTACT_COUNTRY, contact.getCountry());
        Values.put(CONTACT_LAND_MARK, contact.getLand_mark());
        Values.put(CONTACT_CONTACT, contact.getCustomer());
        Values.put(CONTACT_CRUSTOMER_ID, contact.getCustomer_id());
        Values.put(CONTACT_LOCATION, contact.getLocation());
        Values.put(CONTACT_CREADTED_BY, contact.getCreated_by());
        Values.put(CONTACT_BUSINESS_ID, contact.getBusiness_id());
        Values.put(CONTACT_TYPE, contact.getType());
        Values.put(CONTACT_ID_LOCATION, contact.getId_location());
        Values.put(CONTACT_CONTACT_ID, contact.getContact_id());
        Values.put(CONTACT_CONTACT_STATUS, contact.getContact_status());
        Values.put(CONTACT_FIRST_NAME, contact.getFirst_name());
        Values.put(CONTACT_LAST_NAME, contact.getLast_name());
        Values.put(CONTACT_SUPPLIER_NAME, contact.getSupplier_business_name());
        Values.put(CONTACT_DOB, contact.getDob());
        Values.put(CONTACT_ADDRESS_LINE, contact.getAddress_line_1());
        Values.put(CONTACT_SYNC, contact.getSync() != null ? contact.getSync() : "yes");
        Values.put(CONTACT_CREATED_AT, contact.getCreated_at());
        Values.put(CONTACT_SHIPPING_ADRESSE, contact.getShipping_address());
        Values.put(CONTACT_ZIPE_CODE, contact.getZip_code());

        Values.put(CONTACT_TAX_NUMBER, contact.getTax_number());
        Values.put(CONTACT_BALANCE, contact.getBalance());
        Values.put(CONTACT_PAY_TERM_NUMBER, contact.getPay_term_number());
        Values.put(CONTACT_PAY_TERM_TYPE, contact.getPay_term_type());
        Values.put(CONTACT_CREDIT_LIMIT, contact.getCredit_limit());
        Values.put(CONTACT_PREFIX, contact.getPrefix());
        Values.put(CONTACT_GROUP_ID, contact.getCustomer_group_id());
        Values.put(CONTACT_SERVER_ID, contact.getContact_server_id());

        int newRowId = (int) mDb.insert(CONTACT_TABLE_NAME, null, Values); //renvoie l'id de l'enregistrement créé
        return newRowId;
    }

    public int editContact(Contact contact) {
        // Create a new map of values, where column names are the keys
        ContentValues Values = new ContentValues();

        Values.put(CONTACT_NAME, contact.getName());
        Values.put(CONTACT_EMAIL, contact.getEmail());
        Values.put(CONTACT_MOBILE, contact.getMobile());
        Values.put(CONTACT_CITY, contact.getCity());
        Values.put(CONTACT_STATE, contact.getState());
        Values.put(CONTACT_COUNTRY, contact.getCountry());
        Values.put(CONTACT_LAND_MARK, contact.getLand_mark());
        Values.put(CONTACT_CONTACT, contact.getCustomer());
        Values.put(CONTACT_CRUSTOMER_ID, contact.getCustomer_id());
        Values.put(CONTACT_LOCATION, contact.getLocation());
        Values.put(CONTACT_CREADTED_BY, contact.getCreated_by());
        Values.put(CONTACT_BUSINESS_ID, contact.getBusiness_id());
        Values.put(CONTACT_TYPE, contact.getType());
        Values.put(CONTACT_ID_LOCATION, contact.getId_location());
        Values.put(CONTACT_CONTACT_ID, contact.getContact_id());
        Values.put(CONTACT_CONTACT_STATUS, contact.getContact_status());
        Values.put(CONTACT_FIRST_NAME, contact.getFirst_name());
        Values.put(CONTACT_LAST_NAME, contact.getLast_name());
        Values.put(CONTACT_SUPPLIER_NAME, contact.getSupplier_business_name());
        Values.put(CONTACT_DOB, contact.getDob());
        Values.put(CONTACT_ADDRESS_LINE, contact.getAddress_line_1());
        Values.put(CONTACT_SYNC, contact.getSync() != null ? contact.getSync() : "yes");
        Values.put(CONTACT_CREATED_AT, contact.getCreated_at());
        Values.put(CONTACT_SHIPPING_ADRESSE, contact.getShipping_address());
        Values.put(CONTACT_ZIPE_CODE, contact.getZip_code());

        Values.put(CONTACT_TAX_NUMBER, contact.getTax_number());
        Values.put(CONTACT_BALANCE, contact.getBalance());
        Values.put(CONTACT_PAY_TERM_NUMBER, contact.getPay_term_number());
        Values.put(CONTACT_PAY_TERM_TYPE, contact.getPay_term_type());
        Values.put(CONTACT_CREDIT_LIMIT, contact.getCredit_limit());
        Values.put(CONTACT_PREFIX, contact.getPrefix());
        Values.put(CONTACT_GROUP_ID, contact.getCustomer_group_id());

        int newRowId = mDb.update(CONTACT_TABLE_NAME, Values, CONTACT_ID + " = '" + contact.getId() + "'", null); //renvoie l'id de l'enregistrement créé
        return newRowId;
    }

    public void fill(ArrayList<Contact> contacts) {
        if (!contacts.isEmpty()) {
            for (Contact contact : contacts) {
                this.insert(contact);
            }
        }
    }

    public void deleteAll() {
        mDb.execSQL("delete from " + CONTACT_TABLE_NAME);
    }

    public void deleteCustomer(Integer id) {
        mDb.execSQL("delete from " + CONTACT_TABLE_NAME + " WHERE " + CONTACT_ID + " = " + id);
    }

    public ArrayList<String> getCutomerName() {
        ArrayList<String> name_customers = new ArrayList<>();
        String selectQuery = "SELECT  * FROM " + CONTACT_TABLE_NAME;
        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {
            do {
                name_customers.add(cursor.getString(1));
            } while (cursor.moveToNext());
        }
        // mDb.close();
        return name_customers;
    }

    public ArrayList<Contact> getAllContact() {
        ArrayList<Contact> tmpContact = new ArrayList<>();
        String selectQuery = "SELECT  * FROM " + CONTACT_TABLE_NAME;
        Cursor cursor = mDb.rawQuery(selectQuery, null);
        if (cursor.moveToFirst()) {
            do {
                Contact contact = new Contact();
                contact.setId(cursor.getInt(0));
                contact.setName(cursor.getString(1));
                contact.setEmail(cursor.getString(2));
                contact.setMobile(cursor.getString(3));
                contact.setCountry(cursor.getString(6));
                contact.setCity(cursor.getString(4));
                contact.setState(cursor.getString(5));
                contact.setCustomer(cursor.getString(8));
                contact.setCustomer_id(cursor.getString(9));
                contact.setLocation(cursor.getString(10));
                contact.setCreated_by(cursor.getString(11));
                contact.setBusiness_id(cursor.getInt(12));
                contact.setType(cursor.getString(13));
                contact.setSync(cursor.getString(14));
                contact.setId_location(cursor.getString(15));
                contact.setContact_id(cursor.getString(16));
                contact.setContact_status(cursor.getString(17));

                contact.setShipping_address(cursor.getString(24));
                contact.setZip_code(cursor.getString(25));
                contact.setTax_number(cursor.getString(26));
                contact.setBalance(cursor.getString(27));
                contact.setPay_term_number(cursor.getInt(28));
                contact.setPay_term_type(cursor.getString(29));
                contact.setCreated_at(cursor.getString(23));
                contact.setAddress_line_1(cursor.getString(22));
                contact.setCredit_limit(cursor.getString(30));
                contact.setDob(cursor.getString(21));
                contact.setPrefix(cursor.getString(31));
                contact.setCustomer_group_id(cursor.getInt(32));

                tmpContact.add(contact);
            } while (cursor.moveToNext());
        }

        // mDb.close();

        return tmpContact;
    }

    public ArrayList<Contact> getAllCustomers() {
        ArrayList<Contact> tmpContact = new ArrayList<>();
        String selectQuery = "SELECT  * FROM " + CONTACT_TABLE_NAME + " WHERE " + CONTACT_TYPE + " IN ('customer', 'both')" ;
        Cursor cursor = mDb.rawQuery(selectQuery, null);
        if (cursor.moveToFirst()) {
            do {
                Contact contact = new Contact();
                contact.setId(cursor.getInt(0));
                contact.setName(cursor.getString(1));
                contact.setEmail(cursor.getString(2));
                contact.setMobile(cursor.getString(3));
                contact.setCountry(cursor.getString(6));
                contact.setCity(cursor.getString(4));
                contact.setState(cursor.getString(5));
                contact.setCustomer(cursor.getString(8));
                contact.setCustomer_id(cursor.getString(9));
                contact.setLocation(cursor.getString(10));
                contact.setCreated_by(cursor.getString(11));
                contact.setBusiness_id(cursor.getInt(12));
                contact.setType(cursor.getString(13));
                contact.setSync(cursor.getString(14));
                contact.setId_location(cursor.getString(15));
                contact.setContact_id(cursor.getString(16));
                contact.setContact_status(cursor.getString(17));

                contact.setShipping_address(cursor.getString(24));
                contact.setZip_code(cursor.getString(25));
                contact.setTax_number(cursor.getString(26));
                contact.setBalance(cursor.getString(27));
                contact.setPay_term_number(cursor.getInt(28));
                contact.setPay_term_type(cursor.getString(29));
                contact.setCreated_at(cursor.getString(23));
                contact.setAddress_line_1(cursor.getString(22));
                contact.setCredit_limit(cursor.getString(30));
                contact.setDob(cursor.getString(21));
                contact.setPrefix(cursor.getString(31));
                contact.setCustomer_group_id(cursor.getInt(32));

                tmpContact.add(contact);
            } while (cursor.moveToNext());
        }

        // mDb.close();

        return tmpContact;
    }

    public ArrayList<Contact> getAllSupplier() {
        ArrayList<Contact> tmpContact = new ArrayList<>();
        String selectQuery = "SELECT  * FROM " + CONTACT_TABLE_NAME + " WHERE " + CONTACT_TYPE + " = 'supplier'";
        Cursor cursor = mDb.rawQuery(selectQuery, null);
        if (cursor.moveToFirst()) {
            do {
                Contact contact = new Contact();
                contact.setId(cursor.getInt(0));
                contact.setName(cursor.getString(1));
                contact.setEmail(cursor.getString(2));
                contact.setMobile(cursor.getString(3));
                contact.setCountry(cursor.getString(6));
                contact.setCity(cursor.getString(4));
                contact.setState(cursor.getString(5));
                contact.setCustomer(cursor.getString(8));
                contact.setCustomer_id(cursor.getString(9));
                contact.setLocation(cursor.getString(10));
                contact.setCreated_by(cursor.getString(11));
                contact.setBusiness_id(cursor.getInt(12));
                contact.setType(cursor.getString(13));
                contact.setSync(cursor.getString(14));
                contact.setId_location(cursor.getString(15));
                contact.setContact_id(cursor.getString(16));
                contact.setContact_status(cursor.getString(17));
                contact.setShipping_address(cursor.getString(24));
                contact.setZip_code(cursor.getString(25));
                contact.setTax_number(cursor.getString(26));
                contact.setBalance(cursor.getString(27));
                contact.setPay_term_number(cursor.getInt(28));
                contact.setPay_term_type(cursor.getString(29));
                contact.setCreated_at(cursor.getString(23));

                contact.setAddress_line_1(cursor.getString(22));
                contact.setCredit_limit(cursor.getString(30));
                contact.setDob(cursor.getString(21));
                contact.setPrefix(cursor.getString(31));
                contact.setCustomer_group_id(cursor.getInt(32));

                tmpContact.add(contact);
            } while (cursor.moveToNext());
        }

        // mDb.close();

        return tmpContact;
    }

    public ArrayList<Contact> getSpinSupplier() {
        ArrayList<Contact> tmpContact = new ArrayList<>();
        tmpContact.add(new Contact(0, _context.getResources().getString(R.string.lbl_please_select_contact)));
        String selectQuery = "SELECT  * FROM " + CONTACT_TABLE_NAME + " WHERE " + CONTACT_TYPE+ " IN ('supplier', 'both')" + " AND " + CONTACT_CONTACT_STATUS + " = " + "'active'";


        Cursor cursor = mDb.rawQuery(selectQuery, null);
        if (cursor.moveToFirst()) {
            do {
                Contact contact = new Contact();
                contact.setId(cursor.getInt(0));
                contact.setName(cursor.getString(1));
                contact.setEmail(cursor.getString(2));
                contact.setMobile(cursor.getString(3));
                contact.setCountry(cursor.getString(6));
                contact.setCity(cursor.getString(4));
                contact.setState(cursor.getString(5));
                contact.setCustomer(cursor.getString(8));
                contact.setCustomer_id(cursor.getString(9));
                contact.setLocation(cursor.getString(10));
                contact.setCreated_by(cursor.getString(11));
                contact.setBusiness_id(cursor.getInt(12));
                contact.setType(cursor.getString(13));
                contact.setSync(cursor.getString(14));
                contact.setId_location(cursor.getString(15));
                contact.setContact_id(cursor.getString(16));
                contact.setContact_status(cursor.getString(17));
                contact.setCreated_at(cursor.getString(23));
                contact.setShipping_address(cursor.getString(24));
                contact.setZip_code(cursor.getString(25));
                contact.setTax_number(cursor.getString(26));
                contact.setBalance(cursor.getString(27));
                contact.setPay_term_number(cursor.getInt(28));
                contact.setPay_term_type(cursor.getString(29));
                contact.setCreated_at(cursor.getString(23));
                contact.setPrefix(cursor.getString(31));

                contact.setAddress_line_1(cursor.getString(22));
                contact.setCredit_limit(cursor.getString(30));
                contact.setDob(cursor.getString(21));
                contact.setCustomer_group_id(cursor.getInt(32));

                tmpContact.add(contact);
            } while (cursor.moveToNext());
        }

        // mDb.close();

        return tmpContact;
    }


    public ArrayList<Contact> getSpinContacts() {
        ArrayList<Contact> tmpContact = new ArrayList<>();
        tmpContact.add(new Contact(0, _context.getResources().getString(R.string.lbl_please_select_contact)));
        String selectQuery = "SELECT * FROM " + CONTACT_TABLE_NAME + " WHERE " + CONTACT_CONTACT_STATUS + " = " + "'active'";


        Cursor cursor = mDb.rawQuery(selectQuery, null);
        if (cursor.moveToFirst()) {
            do {
                Contact contact = new Contact();
                contact.setId(cursor.getInt(0));
                contact.setName(cursor.getString(1));
                contact.setEmail(cursor.getString(2));
                contact.setMobile(cursor.getString(3));
                contact.setCountry(cursor.getString(6));
                contact.setCity(cursor.getString(4));
                contact.setState(cursor.getString(5));
                contact.setCustomer(cursor.getString(8));
                contact.setCustomer_id(cursor.getString(9));
                contact.setLocation(cursor.getString(10));
                contact.setCreated_by(cursor.getString(11));
                contact.setBusiness_id(cursor.getInt(12));
                contact.setType(cursor.getString(13));
                contact.setSync(cursor.getString(14));
                contact.setId_location(cursor.getString(15));
                contact.setContact_id(cursor.getString(16));
                contact.setContact_status(cursor.getString(17));
                contact.setCreated_at(cursor.getString(23));
                contact.setShipping_address(cursor.getString(24));
                contact.setZip_code(cursor.getString(25));
                contact.setTax_number(cursor.getString(26));
                contact.setBalance(cursor.getString(27));
                contact.setPay_term_number(cursor.getInt(28));
                contact.setPay_term_type(cursor.getString(29));
                contact.setCreated_at(cursor.getString(23));
                contact.setPrefix(cursor.getString(31));
                contact.setAddress_line_1(cursor.getString(22));
                contact.setCredit_limit(cursor.getString(30));
                contact.setDob(cursor.getString(21));
                contact.setCustomer_group_id(cursor.getInt(32));

                tmpContact.add(contact);
            } while (cursor.moveToNext());
        }

        // mDb.close();

        return tmpContact;
    }

    public ArrayList<Contact> getSpinnerCustomer() {
        ArrayList<Contact> tmpContact = new ArrayList<>();
        //  String selectQuery = "SELECT  * FROM " + CONTACT_TABLE_NAME + " WHERE " + CONTACT_TYPE + " = " + "'customer'";
        String selectQuery = "SELECT  * FROM " + CONTACT_TABLE_NAME + " WHERE " + CONTACT_TYPE + " IN ('customer', 'both')" + " AND " + CONTACT_CONTACT_STATUS + " = " + "'active'";

        tmpContact.add(new Contact(0, _context.getResources().getString(R.string.lbl_please_select_customer)));
        Cursor cursor = mDb.rawQuery(selectQuery, null);
        if (cursor.moveToFirst()) {
            do {
                Contact contact = new Contact();
                contact.setId(cursor.getInt(0));
                contact.setName(cursor.getString(1));
                contact.setEmail(cursor.getString(2));
                contact.setMobile(cursor.getString(3));
                contact.setCountry(cursor.getString(6));
                contact.setCity(cursor.getString(4));
                contact.setState(cursor.getString(5));
                contact.setCustomer(cursor.getString(8));
                contact.setCustomer_id(cursor.getString(9));
                contact.setLocation(cursor.getString(10));
                contact.setCreated_by(cursor.getString(11));
                contact.setBusiness_id(cursor.getInt(12));
                contact.setType(cursor.getString(13));
                contact.setSync(cursor.getString(14));
                contact.setId_location(cursor.getString(15));
                contact.setContact_id(cursor.getString(16));
                contact.setContact_status(cursor.getString(17));
                contact.setCreated_at(cursor.getString(23));
                contact.setShipping_address(cursor.getString(24));
                contact.setZip_code(cursor.getString(25));
                contact.setTax_number(cursor.getString(26));
                contact.setBalance(cursor.getString(27));
                contact.setPay_term_number(cursor.getInt(28));
                contact.setPay_term_type(cursor.getString(29));
                contact.setPrefix(cursor.getString(31));
                contact.setAddress_line_1(cursor.getString(22));
                contact.setCredit_limit(cursor.getString(30));
                contact.setDob(cursor.getString(21));
                contact.setCustomer_group_id(cursor.getInt(32));

                tmpContact.add(contact);
            } while (cursor.moveToNext());
        }

        // mDb.close();

        return tmpContact;
    }

    public ArrayList<Contact> getSpinnerActiveCustomer() {
        ArrayList<Contact> tmpContact = new ArrayList<>();
        String selectQuery = "SELECT  * FROM " + CONTACT_TABLE_NAME + " WHERE " + CONTACT_TYPE + " IN ('customer', 'both')" + " AND " + CONTACT_CONTACT_STATUS + " = " + "'active'";

        tmpContact.add(new Contact(0, _context.getResources().getString(R.string.lbl_please_select_customer)));
        Cursor cursor = mDb.rawQuery(selectQuery, null);
        if (cursor.moveToFirst()) {
            do {
                Contact contact = new Contact();
                contact.setId(cursor.getInt(0));
                contact.setName(cursor.getString(1));
                contact.setEmail(cursor.getString(2));
                contact.setMobile(cursor.getString(3));
                contact.setCountry(cursor.getString(6));
                contact.setCity(cursor.getString(4));
                contact.setState(cursor.getString(5));
                contact.setCustomer(cursor.getString(8));
                contact.setCustomer_id(cursor.getString(9));
                contact.setLocation(cursor.getString(10));
                contact.setCreated_by(cursor.getString(11));
                contact.setBusiness_id(cursor.getInt(12));
                contact.setType(cursor.getString(13));
                contact.setSync(cursor.getString(14));
                contact.setId_location(cursor.getString(15));
                contact.setContact_id(cursor.getString(16));
                contact.setContact_status(cursor.getString(17));
                contact.setCreated_at(cursor.getString(23));
                contact.setShipping_address(cursor.getString(24));
                contact.setZip_code(cursor.getString(25));
                contact.setTax_number(cursor.getString(26));
                contact.setBalance(cursor.getString(27));
                contact.setPay_term_number(cursor.getInt(28));
                contact.setPay_term_type(cursor.getString(29));
                contact.setPrefix(cursor.getString(31));
                contact.setAddress_line_1(cursor.getString(22));
                contact.setCredit_limit(cursor.getString(30));
                contact.setDob(cursor.getString(21));
                contact.setCustomer_group_id(cursor.getInt(32));

                tmpContact.add(contact);
            } while (cursor.moveToNext());
        }

        // mDb.close();

        return tmpContact;
    }

    public Contact getCustomerById(Integer id_customer) {
        Contact contact = new Contact();

        String selectQuery = "SELECT  * FROM " + CONTACT_TABLE_NAME + " WHERE " + CONTACT_ID + " = " + id_customer;
        Cursor cursor = mDb.rawQuery(selectQuery, null);
        if (cursor.moveToFirst()) {
            contact.setId(cursor.getInt(0));
            contact.setName(cursor.getString(1));
            contact.setEmail(cursor.getString(2));
            contact.setMobile(cursor.getString(3));
            contact.setCountry(cursor.getString(6));
            contact.setCity(cursor.getString(4));
            contact.setState(cursor.getString(5));
            contact.setCustomer(cursor.getString(8));
            contact.setCustomer_id(cursor.getString(9));
            contact.setLocation(cursor.getString(10));
            contact.setCreated_by(cursor.getString(11));
            contact.setBusiness_id(cursor.getInt(12));
            contact.setType(cursor.getString(13));
            contact.setSync(cursor.getString(14));
            contact.setId_location(cursor.getString(15));
            contact.setContact_id(cursor.getString(16));
            contact.setContact_status(cursor.getString(17));
            contact.setFirst_name(cursor.getString(18));
            contact.setLast_name(cursor.getString(19));
            contact.setCreated_at(cursor.getString(23));
            contact.setDob(cursor.getString(21));
            contact.setShipping_address(cursor.getString(24));
            contact.setZip_code(cursor.getString(25));
            contact.setTax_number(cursor.getString(26));
            contact.setBalance(cursor.getString(27));
            contact.setPay_term_number(cursor.getInt(28));
            contact.setPay_term_type(cursor.getString(29));
            contact.setPrefix(cursor.getString(31));
            contact.setAddress_line_1(cursor.getString(22));
            contact.setCredit_limit(cursor.getString(30));
            contact.setCustomer_group_id(cursor.getInt(32));
            contact.setSupplier_business_name(cursor.getString(20));

            contact.setDob(cursor.getString(21));
        }

        // mDb.close();

        return contact;
    }

    public int updateContactServer(Contact contact) {
        // Create a new map of values, where column names are the keys
        ContentValues pValues = new ContentValues();

        //   pValues.put(PRODUCT_ID, product.getId());
        pValues.put(CONTACT_SERVER_ID, contact.getContact_server_id());
        pValues.put(CONTACT_SYNC, "yes");

        // Insert the new row, returning the primary key value of the new row
        int newRowId = mDb.update(CONTACT_TABLE_NAME, pValues, CONTACT_ID + " = '" + contact.getId() + "'", null); //renvoie l'id de l'enregistrement créé

        // mDb.close();
        return newRowId;
    }


    public void setUpdateYes(int id, int id_user) {
        // mDb.execSQL("UPDATE " + CONTACT_TABLE_NAME + " SET " + CONTACT_SYNC + " = 'yes'" + " AND " + CONTACT_CRUSTOMER_ID + " = " + id_user + " WHERE " + CONTACT_ID + " = " + id);
        mDb.execSQL("UPDATE " + CONTACT_TABLE_NAME + " SET " + CONTACT_SYNC + " = 'yes'" + " WHERE " + CONTACT_ID + " = " + id);
        mDb.execSQL("UPDATE " + CONTACT_TABLE_NAME + " SET " + CONTACT_CRUSTOMER_ID + " = " + id_user + " WHERE " + CONTACT_ID + " = " + id);
    }

    public ArrayList<Contact> getSyncCustomer(String sync) {
        ArrayList<Contact> tmpContact = new ArrayList<>();
        String selectQuery = "SELECT  * FROM " + CONTACT_TABLE_NAME + " WHERE " + CONTACT_SYNC + " = '" + sync + "'";
        Cursor cursor = mDb.rawQuery(selectQuery, null);
        if (cursor.moveToFirst()) {
            do {
                Contact contact = new Contact();

                contact.setId(cursor.getInt(0));
                contact.setName(cursor.getString(1));
                contact.setMobile(cursor.getString(3));
                contact.setCountry(cursor.getString(6));
                contact.setCity(cursor.getString(4));
                contact.setState(cursor.getString(5));
                contact.setCustomer(cursor.getString(8));
                contact.setCustomer_id(cursor.getString(9));
                contact.setLocation(cursor.getString(10));
                contact.setCreated_by(cursor.getString(11));
                contact.setBusiness_id(cursor.getInt(12));
                contact.setType(cursor.getString(13));
                // customer.setSync(cursor.getString(14));
                contact.setId_location(cursor.getString(15));
                contact.setContact_status(cursor.getString(17));
                contact.setContact_id(cursor.getString(16));
                contact.setFirst_name(cursor.getString(18));
                contact.setLast_name(cursor.getString(19));
                contact.setSupplier_business_name(cursor.getString(20));
                contact.setDob(cursor.getString(21));
                contact.setAddress_line_1(cursor.getString(22));
                contact.setCreated_at(cursor.getString(23));
                contact.setShipping_address(cursor.getString(24));
                contact.setZip_code(cursor.getString(25));
                contact.setTax_number(cursor.getString(26));
                contact.setBalance(cursor.getString(27));
                contact.setPay_term_number(cursor.getInt(28));
                contact.setPay_term_type(cursor.getString(29));
                contact.setPrefix(cursor.getString(31));
                contact.setAddress_line_1(cursor.getString(22));
                contact.setCredit_limit(cursor.getString(30));
                contact.setDob(cursor.getString(21));
                contact.setCustomer_group_id(cursor.getInt(32));

                tmpContact.add(contact);
            } while (cursor.moveToNext());
        }

        // mDb.close();

        return tmpContact;
    }

    public ArrayList<Contact> getCustomerLike(String name) {
        ArrayList<Contact> tmpContact = new ArrayList<>();
        //String selectQuery = "SELECT  * FROM " + CONTACT_TABLE_NAME + " WHERE " + CONTACT_SYNC + " = '" + sync + "'";
        String type = "customer";
        String selectQuery = "SELECT  * FROM " + CONTACT_TABLE_NAME + " WHERE " + CONTACT_NAME + " LIKE '%" + name + "%' " + " AND " + CONTACT_TYPE + " = '" + type + "'";


        Cursor cursor = mDb.rawQuery(selectQuery, null);
        if (cursor.moveToFirst()) {
            do {

                Contact contact = new Contact();
                contact.setId(cursor.getInt(0));
                contact.setName(cursor.getString(1));
                contact.setEmail(cursor.getString(2));
                contact.setMobile(cursor.getString(3));
                contact.setCountry(cursor.getString(6));
                contact.setCity(cursor.getString(4));
                contact.setState(cursor.getString(5));
                contact.setCustomer(cursor.getString(8));
                contact.setCustomer_id(cursor.getString(9));
                contact.setLocation(cursor.getString(10));
                contact.setCreated_by(cursor.getString(11));
                contact.setBusiness_id(cursor.getInt(12));
                contact.setType(cursor.getString(13));
                contact.setSync(cursor.getString(14));
                contact.setId_location(cursor.getString(15));
                contact.setContact_id(cursor.getString(16));
                contact.setContact_status(cursor.getString(17));
                contact.setFirst_name(cursor.getString(18));
                contact.setLast_name(cursor.getString(19));
                contact.setSupplier_business_name(cursor.getString(20));
                contact.setDob(cursor.getString(21));
                contact.setAddress_line_1(cursor.getString(22));
                contact.setCreated_at(cursor.getString(23));
                contact.setShipping_address(cursor.getString(24));
                contact.setZip_code(cursor.getString(25));
                contact.setTax_number(cursor.getString(26));
                contact.setBalance(cursor.getString(27));
                contact.setPay_term_number(cursor.getInt(28));
                contact.setPay_term_type(cursor.getString(29));
                contact.setAddress_line_1(cursor.getString(22));
                contact.setCredit_limit(cursor.getString(30));
                contact.setDob(cursor.getString(21));
                contact.setPrefix(cursor.getString(31));
                contact.setCustomer_group_id(cursor.getInt(32));

                tmpContact.add(contact);
            } while (cursor.moveToNext());
        }

        // mDb.close();

        return tmpContact;
    }

    public ArrayList<Contact> getCustomerType(String name, Integer type) {
        String type_name = "";
        if (type == 0) {
            type_name = "";
        } else if (type == 1) {
            type_name = "both";
        } else if (type == 2) {
            type_name = "customer";
        }else if (type == 3){
            type_name = "supplier";
        }
        ArrayList<Contact> tmpContact = new ArrayList<>();

        String selectQuery = "";

        selectQuery = "SELECT * FROM " + CONTACT_TABLE_NAME + " WHERE ";
        String nameQuery = (name.equals("")) ? (CONTACT_NAME + " IS NOT NULL") : (CONTACT_NAME + " LIKE '%" + name + "%' ");
         String unitQuery = " AND " + ((type_name.matches("")) ? (CONTACT_TYPE + " IS NOT NULL") : (CONTACT_TYPE + " = '" + type_name + "'"));
        selectQuery += nameQuery + unitQuery;
        //   }

        Cursor cursor = mDb.rawQuery(selectQuery, null);
        if (cursor.moveToFirst()) {
            do {

                Contact contact = new Contact();
                contact.setId(cursor.getInt(0));
                contact.setName(cursor.getString(1));
                contact.setEmail(cursor.getString(2));
                contact.setMobile(cursor.getString(3));
                contact.setCountry(cursor.getString(6));
                contact.setCity(cursor.getString(4));
                contact.setState(cursor.getString(5));
                contact.setCustomer(cursor.getString(8));
                contact.setCustomer_id(cursor.getString(9));
                contact.setLocation(cursor.getString(10));
                contact.setCreated_by(cursor.getString(11));
                contact.setBusiness_id(cursor.getInt(12));
                contact.setType(cursor.getString(13));
                contact.setSync(cursor.getString(14));
                contact.setId_location(cursor.getString(15));
                contact.setContact_id(cursor.getString(16));
                contact.setContact_status(cursor.getString(17));
                contact.setFirst_name(cursor.getString(18));
                contact.setLast_name(cursor.getString(19));
                contact.setSupplier_business_name(cursor.getString(20));
                contact.setDob(cursor.getString(21));
                contact.setAddress_line_1(cursor.getString(22));
                contact.setCreated_at(cursor.getString(23));
                contact.setShipping_address(cursor.getString(24));
                contact.setZip_code(cursor.getString(25));
                contact.setTax_number(cursor.getString(26));
                contact.setBalance(cursor.getString(27));
                contact.setPay_term_number(cursor.getInt(28));
                contact.setPay_term_type(cursor.getString(29));
                contact.setPrefix(cursor.getString(31));
                contact.setAddress_line_1(cursor.getString(22));
                contact.setCredit_limit(cursor.getString(30));
                contact.setDob(cursor.getString(21));
                contact.setCustomer_group_id(cursor.getInt(32));

                tmpContact.add(contact);
            } while (cursor.moveToNext());
        }

        // mDb.close();

        return tmpContact;
    }

    public ArrayList<Contact> getSupplierLike(String name) {
        ArrayList<Contact> tmpContact = new ArrayList<>();
        String type = "supplier";
        String selectQuery = "SELECT  * FROM " + CONTACT_TABLE_NAME + " WHERE " + CONTACT_NAME + " LIKE '%" + name + "%' " + " AND " + CONTACT_TYPE + " = '" + type + "'";
        ;

        Cursor cursor = mDb.rawQuery(selectQuery, null);
        if (cursor.moveToFirst()) {
            do {
                Contact contact = new Contact();
                contact.setId(cursor.getInt(0));
                contact.setName(cursor.getString(1));
                contact.setEmail(cursor.getString(2));
                contact.setMobile(cursor.getString(3));
                contact.setCustomer(cursor.getString(8));
                contact.setCustomer_id(cursor.getString(9));
                contact.setLocation(cursor.getString(10));
                contact.setCreated_by(cursor.getString(11));
                contact.setBusiness_id(cursor.getInt(12));
                contact.setType(cursor.getString(13));
                contact.setSync(cursor.getString(14));
                contact.setId_location(cursor.getString(15));
                contact.setContact_id(cursor.getString(16));
                contact.setContact_status(cursor.getString(17));
                contact.setFirst_name(cursor.getString(18));
                contact.setLast_name(cursor.getString(19));
                contact.setSupplier_business_name(cursor.getString(20));
                contact.setDob(cursor.getString(21));
                contact.setAddress_line_1(cursor.getString(22));
                contact.setCreated_at(cursor.getString(23));
                contact.setShipping_address(cursor.getString(24));
                contact.setZip_code(cursor.getString(25));
                contact.setTax_number(cursor.getString(26));
                contact.setBalance(cursor.getString(27));
                contact.setPay_term_number(cursor.getInt(28));
                contact.setPay_term_type(cursor.getString(29));
                contact.setPrefix(cursor.getString(31));
                contact.setAddress_line_1(cursor.getString(22));
                contact.setCredit_limit(cursor.getString(30));
                contact.setDob(cursor.getString(21));
                contact.setCustomer_group_id(cursor.getInt(32));

                tmpContact.add(contact);
            } while (cursor.moveToNext());
        }

        // mDb.close();

        return tmpContact;
    }


}
