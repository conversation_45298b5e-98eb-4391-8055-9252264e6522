package com.rising.high.tech.bigultimatenavdraw.db;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;

import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.model.Category;
import com.rising.high.tech.bigultimatenavdraw.model.Customer_groups;
import com.rising.high.tech.bigultimatenavdraw.model.Customer_groups;

import java.util.ArrayList;

import static com.rising.high.tech.bigultimatenavdraw.util.Constant.YES;

public class CustomerGroupsDbController extends DBController {

    private final Context _context;
    public static final String CUSTOMER_GROUPS_TABLE_NAME = "customer_groups";

    public static final String CUSTOMER_GROUPS_ID = "id"; //int
    public static final String CUSTOMER_GROUPS_BUSINESS_ID = "customer_groups_id";
    public static final String CUSTOMER_GROUPS_NAME = "name";
    public static final String CUSTOMER_GROUPS_AMOUNT = "amount";
    public static final String CUSTOMER_GROUPS_CREATED_BY = "created_by";
    public static final String CUSTOMER_GROUPS_SYNC = "sync";
    public static final String CUSTOMER_GROUPS_SERVER_ID = "cg_server_id";

    public static final String CUSTOMER_GROUPS_TABLE_CREATE =
            "CREATE TABLE " + CUSTOMER_GROUPS_TABLE_NAME + " (" +
                    CUSTOMER_GROUPS_ID + " INTEGER PRIMARY KEY AUTOINCREMENT, " +
                    CUSTOMER_GROUPS_BUSINESS_ID + " INTEGER, " +
                    CUSTOMER_GROUPS_NAME + " TEXT, " +
                    CUSTOMER_GROUPS_AMOUNT + " TEXT, " +
                    CUSTOMER_GROUPS_CREATED_BY + " INTEGER , " +
                    CUSTOMER_GROUPS_SYNC + " TEXT , " +
                    CUSTOMER_GROUPS_SERVER_ID + " INTEGER) ;";

    public static final String CUSTOMER_GROUPS_TABLE_DROP = "DROP TABLE IF EXISTS " + CUSTOMER_GROUPS_TABLE_NAME + ";";

    public CustomerGroupsDbController(Context context) {
        super(context);
        _context = context;
    }

    public int insert(Customer_groups customer_groups) {
        // Create a new map of values, where column names are the keys
        ContentValues pValues = new ContentValues();

        pValues.put(CUSTOMER_GROUPS_ID, customer_groups.getId());
        pValues.put(CUSTOMER_GROUPS_BUSINESS_ID, customer_groups.getBusiness_id());
        pValues.put(CUSTOMER_GROUPS_NAME, customer_groups.getName());
        pValues.put(CUSTOMER_GROUPS_AMOUNT, customer_groups.getAmount());
        pValues.put(CUSTOMER_GROUPS_CREATED_BY, customer_groups.getCreated_by());

        // Insert the new row, returning the primary key value of the new row
        int newRowId = (int) mDb.insert(CUSTOMER_GROUPS_TABLE_NAME, null, pValues); //renvoie l'id de l'enregistrement créé

        // mDb.close();
        return newRowId;
    }

    public int insertLocal(Customer_groups customer_groups) {
        // Create a new map of values, where column names are the keys
        ContentValues pValues = new ContentValues();

        pValues.put(CUSTOMER_GROUPS_BUSINESS_ID, customer_groups.getBusiness_id());
        pValues.put(CUSTOMER_GROUPS_NAME, customer_groups.getName());
        pValues.put(CUSTOMER_GROUPS_AMOUNT, customer_groups.getAmount());
        pValues.put(CUSTOMER_GROUPS_CREATED_BY, customer_groups.getCreated_by());
        pValues.put(CUSTOMER_GROUPS_SYNC, customer_groups.getSync());
        pValues.put(CUSTOMER_GROUPS_SERVER_ID, customer_groups.getCg_server_id());

        // Insert the new row, returning the primary key value of the new row
        int newRowId = (int) mDb.insert(CUSTOMER_GROUPS_TABLE_NAME, null, pValues); //renvoie l'id de l'enregistrement créé

        // mDb.close();
        return newRowId;
    }

    public ArrayList<Customer_groups> getAllCustomerGroups() {
        ArrayList<Customer_groups> customer_groups = new ArrayList<>();
        String selectQuery = "SELECT  * FROM " + CUSTOMER_GROUPS_TABLE_NAME;
        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {
            do {
                Customer_groups customer_groups1 = new Customer_groups();
                customer_groups1.setId(cursor.getInt(0));
                customer_groups1.setBusiness_id(cursor.getInt(1));
                customer_groups1.setName(cursor.getString(2));
                customer_groups1.setAmount(cursor.getString(3));
                customer_groups1.setCreated_by(cursor.getInt(4));
                customer_groups.add(customer_groups1);

            } while (cursor.moveToNext());

        }

        // mDb.close();

        return customer_groups;
    }

    public Customer_groups getCustomerGroupsById(Integer customer_groups_id) {
        Customer_groups customer_groups1 = new Customer_groups();

        String selectQuery = "SELECT  * FROM " + CUSTOMER_GROUPS_TABLE_NAME + " WHERE " + CUSTOMER_GROUPS_ID + " = " + customer_groups_id;
        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {
            do {
                customer_groups1.setId(cursor.getInt(0));
                customer_groups1.setBusiness_id(cursor.getInt(1));
                customer_groups1.setName(cursor.getString(2));
                customer_groups1.setAmount(cursor.getString(3));
                customer_groups1.setCreated_by(cursor.getInt(4));

            } while (cursor.moveToNext());

        }

        // mDb.close();

        return customer_groups1;
    }
    public ArrayList<Customer_groups> getSyncCustomerGroups(String sync) {
        ArrayList<Customer_groups> customer_groups = new ArrayList<>();
        String selectQuery = "SELECT  * FROM " + CUSTOMER_GROUPS_TABLE_NAME  + " WHERE " + CUSTOMER_GROUPS_SYNC + " ='" + sync + "'";;
        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {
            do {
                Customer_groups customer_groups1 = new Customer_groups();
                customer_groups1.setId(cursor.getInt(0));
                customer_groups1.setBusiness_id(cursor.getInt(1));
                customer_groups1.setName(cursor.getString(2));
                customer_groups1.setAmount(cursor.getString(3));
                customer_groups1.setCreated_by(cursor.getInt(4));
                customer_groups1.setSync(cursor.getString(5));
                customer_groups1.setCg_server_id(cursor.getInt(6));
                customer_groups.add(customer_groups1);

            } while (cursor.moveToNext());

        }

        // mDb.close();

        return customer_groups;
    }

    public ArrayList<Customer_groups> getAllCustomerGroupsSpin() {
        ArrayList<Customer_groups> customer_groups = new ArrayList<>();
        customer_groups.add(new Customer_groups(0, _context.getResources().getString(R.string.label_none)));
        String selectQuery = "SELECT  * FROM " + CUSTOMER_GROUPS_TABLE_NAME;
        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {
            do {
                Customer_groups customer_groups1 = new Customer_groups();
                customer_groups1.setId(cursor.getInt(0));
                customer_groups1.setBusiness_id(cursor.getInt(1));
                customer_groups1.setName(cursor.getString(2));
                customer_groups1.setAmount(cursor.getString(3));
                customer_groups1.setCreated_by(cursor.getInt(4));
                customer_groups.add(customer_groups1);

            } while (cursor.moveToNext());

        }

        // mDb.close();

        return customer_groups;
    }

    public int editCustomer_groups(Customer_groups customer_groups) {
        // Create a new map of values, where column names are the keys
        ContentValues pValues = new ContentValues();
        pValues.put(CUSTOMER_GROUPS_BUSINESS_ID, customer_groups.getBusiness_id());
        pValues.put(CUSTOMER_GROUPS_NAME, customer_groups.getName());
        pValues.put(CUSTOMER_GROUPS_AMOUNT, customer_groups.getAmount());
        pValues.put(CUSTOMER_GROUPS_CREATED_BY, customer_groups.getCreated_by());
        int newRowId = mDb.update(CUSTOMER_GROUPS_TABLE_NAME, pValues, CUSTOMER_GROUPS_ID + " = '" + customer_groups.getId() + "'", null); //renvoie l'id de l'enregistrement créé
        return newRowId;
    }


    public int syncCustomerGroups(Customer_groups customer_groups) {
        // Create a new map of values, where column names are the keys
        ContentValues pValues = new ContentValues();

        pValues.put(CUSTOMER_GROUPS_SYNC, YES);
        pValues.put(CUSTOMER_GROUPS_SERVER_ID, customer_groups.getCg_server_id());

        int newRowId = mDb.update(CUSTOMER_GROUPS_TABLE_NAME, pValues, CUSTOMER_GROUPS_ID + " = '" + customer_groups.getId() + "'", null); //renvoie l'id de l'enregistrement créé
        return newRowId;
    }

    public ArrayList<Customer_groups> getCsutomerGroupsLike(String name) {
        ArrayList<Customer_groups> customer_groups = new ArrayList<>();
        String selectQuery = "SELECT  * FROM " + CUSTOMER_GROUPS_TABLE_NAME + " WHERE " + CUSTOMER_GROUPS_NAME + " LIKE '%" + name + "%' ";
        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {
            do {
                Customer_groups customer_groups1 = new Customer_groups();
                customer_groups1.setId(cursor.getInt(0));
                customer_groups1.setBusiness_id(cursor.getInt(1));
                customer_groups1.setName(cursor.getString(2));
                customer_groups1.setAmount(cursor.getString(3));
                customer_groups1.setCreated_by(cursor.getInt(4));
                customer_groups.add(customer_groups1);

            } while (cursor.moveToNext());
        }


        return customer_groups;

    }

    // Insert all product
    public void fill(ArrayList<Customer_groups> products) {
        if (!products.isEmpty()) {
            for (Customer_groups product : products) {
                this.insert(product);
            }
        }
        //   mDb.close();
    }

    public void clear() {
        mDb.execSQL("DELETE FROM " + CUSTOMER_GROUPS_TABLE_NAME);
    }

    public void deleteCustomerGroups(Integer id) {
        mDb.execSQL("delete from " + CUSTOMER_GROUPS_TABLE_NAME + " WHERE " + CUSTOMER_GROUPS_ID + " = " + id);
    }

}
