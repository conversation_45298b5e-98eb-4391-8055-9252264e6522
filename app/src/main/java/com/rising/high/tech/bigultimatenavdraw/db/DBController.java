package com.rising.high.tech.bigultimatenavdraw.db;

import android.content.Context;
import android.database.sqlite.SQLiteDatabase;
import android.util.Log;

public abstract class DBController {

    private static final String TAG = "DBController";
    // Nous sommes à la première version de la base
    // Si je décide de la mettre à jour, il faudra changer cet attribut
    protected final static int VERSION = 1;

    // name of database
    protected final static String NOM = "fbsMartdb.db";

    protected static SQLiteDatabase mDb;

    protected DatabaseHandler mHandler = null;

    public DBController(Context pContext) {
        this.mHandler = new DatabaseHandler(pContext, NOM, null, VERSION);
    }

    //public SQLiteDatabase open() {
    public synchronized SQLiteDatabase open() {
        // Pas besoin de fermer la dernière base puisque getWritableDatabase s'en charge
        if (mDb == null) {
            //sInstance = new DatabaseHelper(context.getApplicationContext());
            mDb = mHandler.getWritableDatabase();
        } else {
            mDb.close();
            mDb = mHandler.getWritableDatabase();
        }

        //return sInstance;
        return mDb;
    }

    public synchronized void close() {
        mDb.close();
    }

    public SQLiteDatabase getDb() {
        return mDb;
    }

}
