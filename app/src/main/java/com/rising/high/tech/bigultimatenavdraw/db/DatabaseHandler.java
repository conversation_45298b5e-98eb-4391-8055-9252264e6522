package com.rising.high.tech.bigultimatenavdraw.db;

import android.content.Context;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteOpenHelper;


public class DatabaseHandler extends SQLiteOpenHelper {

    //Methodes  DatabaseHandler *********

    public DatabaseHandler(Context context, String name, SQLiteDatabase.CursorFactory factory, int version) {
        //super(context, name, factory, version);
        super(context, name, null, version);
    }

    @Override
    public void onCreate(SQLiteDatabase db) {
        db.execSQL(CategoryDbController.CATEGORY_TABLE_CREATE);
        db.execSQL(ProductDbController.PRODUCT_TABLE_CREATE);
        db.execSQL(PayementDbController.PAYEMENT_TABLE_CREATE);
        db.execSQL(TransactionDbController.TRANSACTION_TABLE_CREATE);
        db.execSQL(BusinessLocationDbController.STATION_TABLE_CREATE);
        db.execSQL(ContactDbController.CONTACT_TABLE_CREATE);
        db.execSQL(TransactionSellLineDbController.SELL_LINE_TABLE_CREATE);
        db.execSQL(ProfitLossDbController.PROFITLOSS_TABLE_CREATE);
        db.execSQL(UnitDbController.UNIT_TABLE_CREATE);
        db.execSQL(VariationTemplateDbController.VARIATION_TEMPLATE_TABLE_CREATE);
        db.execSQL(WarrantyDbController.WARRANTY_TABLE_CREATE);
        db.execSQL(BrandDbController.BRAND_TABLE_CREATE);
        db.execSQL(TransactionPayementDbController.TRANSACTION_PAY_TABLE_CREATE);
        db.execSQL(TransactionSellLinePurchaseLineDbController.TRANSACTION_SL_PL_TABLE_CREATE);
        db.execSQL(PurchaseLineDbController.PURCHASE_LINE_TABLE_CREATE);
        db.execSQL(DiscountDbController.DISCOUNT_TABLE_CREATE);
        db.execSQL(DiscountVariationDbController.DISCOUNT_VAR_TABLE_CREATE);
        db.execSQL(ExpenseCategoriesDbController.EXPENSE_CATEGORY_TABLE_CREATE);
        db.execSQL(ProductLocationDbController.PRODUCT_LOCATION_TABLE_CREATE);
        db.execSQL(VariationLocationDetailDbController.VARIATION_LOCATION_DETAIL_TABLE_CREATE);
        db.execSQL(StockAdjustementLineDbController.STOCK_ADJUSTMENT_LINE_TABLE_CREATE);
        db.execSQL(VariationsDbController.VARIATION_TABLE_CREATE);
        db.execSQL(TaxRatesDbController.TAX_RATES_TABLE_CREATE);
        db.execSQL(CompanyDbController.COMPANY_TABLE_CREATE);
        db.execSQL(CustomerGroupsDbController.CUSTOMER_GROUPS_TABLE_CREATE);
        db.execSQL(VariationTemplateValuesDbController.VARIATION_TEMPLATE_TABLE_CREATE);
        db.execSQL(UserDbController.USER_TABLE_CREATE);
        db.execSQL(PermissionDbController.PERMISSION_TABLE_CREATE);
        db.execSQL(ModelHasRolesDbController.MODEL_HAS_ROLES_TABLE_CREATE);
        db.execSQL(RoleDbController.ROLE_TABLE_CREATE);
        db.execSQL(RoleHasPermissionDbController.ROLE_HAS_PERMISSION_TABLE_CREATE);
        db.execSQL(UserHasRolesDbController.USERHASROLE_TABLE_CREATE);
    }

    @Override
    public void onUpgrade(SQLiteDatabase db, int oldVersion, int newVersion) {
        db.execSQL(CategoryDbController.CATEGORY_TABLE_DROP);
        db.execSQL(ProductDbController.PRODUCT_TABLE_DROP);
        db.execSQL(PayementDbController.PAYEMENT_TABLE_DROP);
        db.execSQL(TransactionDbController.TRANSACTION_TABLE_DROP);
        db.execSQL(BusinessLocationDbController.STATION_TABLE_DROP);
        db.execSQL(ContactDbController.CONTACT_TABLE_DROP);
        db.execSQL(TransactionSellLineDbController.SELL_LINE_TABLE_DROP);
        db.execSQL(ProfitLossDbController.PROFITLOSS_TABLE_DROP);
        db.execSQL(UnitDbController.UNIT_TABLE_DROP);
        db.execSQL(VariationTemplateDbController.VARIATION_TEMPLATE_TABLE_DROP);
        db.execSQL(WarrantyDbController.WARRANTY_TABLE_DROP);
        db.execSQL(BrandDbController.BRAND_TABLE_DROP);
        db.execSQL(TransactionPayementDbController.TRANSACTION_PAY_TABLE_DROP);
        db.execSQL(TransactionSellLinePurchaseLineDbController.TRANSACTION_SL_PL_TABLE_DROP);
        db.execSQL(PurchaseLineDbController.PURCHASE_LINE_TABLE_DROP);
        db.execSQL(DiscountDbController.DISCOUNT_TABLE_DROP);
        db.execSQL(DiscountVariationDbController.DISCOUNT_VAR_TABLE_DROP);
        db.execSQL(ExpenseCategoriesDbController.EXPENSE_CATEGORY_TABLE_DROP);
        db.execSQL(ProductLocationDbController.PRODUCT_LOCATION_TABLE_DROP);
        db.execSQL(VariationLocationDetailDbController.VARIATION_LOCATION_DETAIL_TABLE_DROP);
        db.execSQL(StockAdjustementLineDbController.STOCK_ADJUSTMENT_LINE_TABLE_DROP);
        db.execSQL(VariationsDbController.VARIATION_TABLE_DROP);
        db.execSQL(TaxRatesDbController.TAX_RATES_TABLE_DROP);
        db.execSQL(CompanyDbController.COMPANY_TABLE_DROP);
        db.execSQL(CustomerGroupsDbController.CUSTOMER_GROUPS_TABLE_DROP);
        db.execSQL(VariationTemplateValuesDbController.VARIATION_TEMPLATE_TABLE_DROP);
        db.execSQL(UserDbController.USER_TABLE_DROP);
        db.execSQL(PermissionDbController.PERMISSION_TABLE_DROP);
        db.execSQL(ModelHasRolesDbController.MODEL_HAS_ROLES_TABLE_DROP);
        db.execSQL(RoleDbController.ROLE_TABLE_DROP);
        db.execSQL(RoleHasPermissionDbController.ROLE_HAS_PERMISSION_TABLE_DROP);
        db.execSQL(UserHasRolesDbController.USERHASROLE_TABLE_DROP);
        onCreate(db);
    }
}

