package com.rising.high.tech.bigultimatenavdraw.db;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.util.Log;

import com.google.gson.Gson;
import com.rising.high.tech.bigultimatenavdraw.model.Category;
import com.rising.high.tech.bigultimatenavdraw.model.Discount;
import com.rising.high.tech.bigultimatenavdraw.model.Unit;

import java.sql.Date;
import java.util.ArrayList;

public class DiscountDbController extends DBController {
    private static final String TAG = "DiscountDbController";
    // **********   Table "CATERORY" fields ********************************************************************

    private CategoryDbController categoryDbController;
    public static final String DISCOUNT_TABLE_NAME = "discounts";

    public static final String DISCOUNT_ID = "id"; //int
    public static final String DISCOUNT_NAME = "name";
    public static final String DISCOUNT_BUSINESS_ID = "business_id";
    public static final String DISCOUNT_BRAND_ID = "brand_id";
    public static final String DISCOUNT_CATEGORY_ID = "category_id";
    public static final String DISCOUNT_LOCATION_ID = "location_id";
    public static final String DISCOUNT_PRIORITY = "priority";
    public static final String DISCOUNT_DISCOUNT_TYPE = "discount_type";
    public static final String DISCOUNT_DISCOUNT_AMOUNT = "discount_amount";
    public static final String DISCOUNT_STARTS_AT = "starts_at";
    public static final String DISCOUNT_ENDS_AT = "ends_at";
    public static final String DISCOUNT_IS_ACTIVE = "is_active";
    public static final String DISCOUNT_APPLICABLE_IN_SPG = "applicable_in_spg";
    public static final String DISCOUNT_APPLICABLE_IN_CG = "applicable_in_cg";
    public static final String DISCOUNT_SYNC= "sync";
    public static final String DISCOUNT_SERVER_ID= "discount_server_id";

    public static final String DISCOUNT_TABLE_CREATE =
            "CREATE TABLE " + DISCOUNT_TABLE_NAME + " (" +
                    DISCOUNT_ID + " INTEGER PRIMARY KEY AUTOINCREMENT , " +
                    DISCOUNT_NAME + " TEXT, " +
                    DISCOUNT_BUSINESS_ID + " INTEGER, " +
                    DISCOUNT_BRAND_ID + " INTEGER, " +
                    DISCOUNT_CATEGORY_ID + " INTEGER, " +
                    DISCOUNT_LOCATION_ID + " INTEGER, " +
                    DISCOUNT_PRIORITY + " INTEGER, " +
                    DISCOUNT_DISCOUNT_TYPE + " TEXT, " +
                    DISCOUNT_DISCOUNT_AMOUNT + " TEXT, " +
                    DISCOUNT_STARTS_AT + " TEXT, " +
                    DISCOUNT_ENDS_AT + " TEXT, " +
                    DISCOUNT_IS_ACTIVE + " INTEGER, " +
                    DISCOUNT_APPLICABLE_IN_SPG + " INTEGER, " +
                    DISCOUNT_APPLICABLE_IN_CG + " INTEGER, " +
                    DISCOUNT_SYNC + " TEXT, " +
                    DISCOUNT_SERVER_ID+ " INTEGER) ;";

    public static final String DISCOUNT_TABLE_DROP = "DROP TABLE IF EXISTS " + DISCOUNT_TABLE_NAME + ";";

    public DiscountDbController(Context context) {
        super(context);

        categoryDbController=new CategoryDbController(context);
        categoryDbController.open();
    }

    public int insert(Discount discount) {
        // Create a new map of values, where column names are the keys
        ContentValues pValues = new ContentValues();

        pValues.put(DISCOUNT_ID, discount.getId());
        pValues.put(DISCOUNT_NAME, discount.getName());
        pValues.put(DISCOUNT_BUSINESS_ID, discount.getBusiness_id());
        pValues.put(DISCOUNT_BRAND_ID, discount.getBrand_id());
        pValues.put(DISCOUNT_CATEGORY_ID, discount.getCategory_id());
        pValues.put(DISCOUNT_LOCATION_ID, discount.getLocation_id());
        pValues.put(DISCOUNT_PRIORITY, discount.getPriority());
        pValues.put(DISCOUNT_DISCOUNT_TYPE, discount.getDiscount_type());
        pValues.put(DISCOUNT_DISCOUNT_AMOUNT, discount.getDiscount_amount());
        pValues.put(DISCOUNT_STARTS_AT, discount.getStarts_at());
        pValues.put(DISCOUNT_ENDS_AT, discount.getEnds_at());
        pValues.put(DISCOUNT_IS_ACTIVE, discount.getIs_active());
        pValues.put(DISCOUNT_APPLICABLE_IN_SPG, discount.getApplicable_in_spg());
        pValues.put(DISCOUNT_APPLICABLE_IN_CG, discount.getApplicable_in_cg());

        int newRowId = (int) mDb.insert(DISCOUNT_TABLE_NAME, null, pValues); //renvoie l'id de l'enregistrement créé
        return newRowId;
    }

    public int update(Discount discount) {
        // Create a new map of values, where column names are the keys
        ContentValues pValues = new ContentValues();

        //    pValues.put(DISCOUNT_ID, discount.getId());
        pValues.put(DISCOUNT_NAME, discount.getName());
        pValues.put(DISCOUNT_BUSINESS_ID, discount.getBusiness_id());
        pValues.put(DISCOUNT_BRAND_ID, discount.getBrand_id());
        pValues.put(DISCOUNT_CATEGORY_ID, discount.getCategory_id());
        pValues.put(DISCOUNT_LOCATION_ID, discount.getLocation_id());
        pValues.put(DISCOUNT_PRIORITY, discount.getPriority());
        pValues.put(DISCOUNT_DISCOUNT_TYPE, discount.getDiscount_type());
        pValues.put(DISCOUNT_DISCOUNT_AMOUNT, discount.getDiscount_amount());
        pValues.put(DISCOUNT_STARTS_AT, discount.getStarts_at());
        pValues.put(DISCOUNT_ENDS_AT, discount.getEnds_at());
        pValues.put(DISCOUNT_IS_ACTIVE, discount.getIs_active());
        pValues.put(DISCOUNT_APPLICABLE_IN_SPG, discount.getApplicable_in_spg());
        pValues.put(DISCOUNT_APPLICABLE_IN_CG, discount.getApplicable_in_cg());

        // Insert the new row, returning the primary key value of the new row
     //   int newRowId = (int) mDb.update(DISCOUNT_TABLE_NAME, pValues, where, null); //renvoie l'id de l'enregistrement créé

        int newRowId= mDb.update(DISCOUNT_TABLE_NAME, pValues,DISCOUNT_ID +" = '"+ discount.getId() + "'", null);

        return newRowId;
    }

    public int insertLocal(Discount discount) {
        // Create a new map of values, where column names are the keys
        ContentValues pValues = new ContentValues();

        pValues.put(DISCOUNT_NAME, discount.getName());
        pValues.put(DISCOUNT_BUSINESS_ID, discount.getBusiness_id());
        pValues.put(DISCOUNT_BRAND_ID, discount.getBrand_id());
        pValues.put(DISCOUNT_CATEGORY_ID, discount.getCategory_id());
        pValues.put(DISCOUNT_LOCATION_ID, discount.getLocation_id());
        pValues.put(DISCOUNT_PRIORITY, discount.getPriority());
        pValues.put(DISCOUNT_DISCOUNT_TYPE, discount.getDiscount_type());
        pValues.put(DISCOUNT_DISCOUNT_AMOUNT, discount.getDiscount_amount());
        pValues.put(DISCOUNT_STARTS_AT, discount.getStarts_at());
        pValues.put(DISCOUNT_ENDS_AT, discount.getEnds_at());
        pValues.put(DISCOUNT_IS_ACTIVE, discount.getIs_active());
        pValues.put(DISCOUNT_APPLICABLE_IN_SPG, discount.getApplicable_in_spg());
        pValues.put(DISCOUNT_APPLICABLE_IN_CG, discount.getApplicable_in_cg());
        pValues.put(DISCOUNT_SYNC, discount.getSync());
        pValues.put(DISCOUNT_SERVER_ID, discount.getDiscount_server_id());


        int newRowId = (int) mDb.insert(DISCOUNT_TABLE_NAME, null, pValues); //renvoie l'id de l'enregistrement créé
        return newRowId;
    }

    public void fill(ArrayList<Discount> categories) {
        if (!categories.isEmpty()) {
            for (Discount product : categories) {
                this.insert(product);
            }
        }
    }


    public void deleteItem(Integer id) {
        mDb.execSQL("delete from " + DISCOUNT_TABLE_NAME + " WHERE " + DISCOUNT_ID + " = '" + id + "'");
    }

    public void deleteAll() {
        mDb.execSQL("delete from " + DISCOUNT_TABLE_NAME);
    }

    public ArrayList<Discount> getAllDiscount() {
        ArrayList<Discount> discountList = new ArrayList<>();
        String selectQuery = "SELECT  * FROM " + DISCOUNT_TABLE_NAME;
        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {
            do {
                Discount discount = new Discount();

                discount.setId(cursor.getInt(0));
                discount.setName(cursor.getString(1));
                discount.setBusiness_id(cursor.getInt(2));
                discount.setBrand_id(cursor.getInt(3));
                discount.setCategory_id(cursor.getInt(4));
                discount.setLocation_id(cursor.getInt(5));
                discount.setPriority(cursor.getInt(6));
                discount.setDiscount_type(cursor.getString(7));
                discount.setDiscount_amount(cursor.getString(8));
                discount.setStarts_at(cursor.getString(9));
                discount.setEnds_at(cursor.getString(10));
                discount.setIs_active(cursor.getInt(11));
                discount.setApplicable_in_spg(cursor.getInt(12));
                discount.setApplicable_in_cg(cursor.getInt(13));


                discountList.add(discount);

            } while (cursor.moveToNext());
        }


        return discountList;

    }


    public int editServerDiscounts(Discount discount) {
        // Create a new map of values, where column names are the keys
        ContentValues pValues = new ContentValues();

        pValues.put(DISCOUNT_SYNC, discount.getSync());
        pValues.put(DISCOUNT_SERVER_ID, discount.getDiscount_server_id());

        int newRowId = mDb.update(DISCOUNT_TABLE_NAME, pValues, DISCOUNT_ID + " = '" + discount.getId() + "'", null); //renvoie l'id de l'enregistrement créé

        return newRowId;
    }



    public ArrayList<Discount> getSyncDiscount(String sync) {
        ArrayList<Discount> discountList = new ArrayList<>();
        String selectQuery = "SELECT  * FROM " + DISCOUNT_TABLE_NAME + " WHERE " + DISCOUNT_SYNC + " = '" + sync +"'";
        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {
            do {
                Discount discount = new Discount();

                discount.setId(cursor.getInt(0));
                discount.setName(cursor.getString(1));
                discount.setBusiness_id(cursor.getInt(2));
                discount.setBrand_id(cursor.getInt(3));
                Category category = categoryDbController.getCategoryById(cursor.getInt(4));
                discount.setCategory_id(category.getCategory_server_id());
                discount.setLocation_id(cursor.getInt(5));
                discount.setPriority(cursor.getInt(6));
                discount.setDiscount_type(cursor.getString(7));
                discount.setDiscount_amount(cursor.getString(8));
                discount.setStarts_at(cursor.getString(9));
                discount.setEnds_at(cursor.getString(10));
                discount.setIs_active(cursor.getInt(11));
                discount.setApplicable_in_spg(cursor.getInt(12));
                discount.setApplicable_in_cg(cursor.getInt(13));
                discount.setSync(cursor.getString(14));


                discountList.add(discount);

            } while (cursor.moveToNext());
        }


        return discountList;

    }

    public ArrayList<Discount> getDiscountLike(String name) {
        ArrayList<Discount> discountList = new ArrayList<>();
        String selectQuery = "SELECT  * FROM " + DISCOUNT_TABLE_NAME + " WHERE " + DISCOUNT_NAME + " LIKE '%" + name +  "%' ";

        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {
            do {
                Discount discount = new Discount();

                discount.setId(cursor.getInt(0));
                discount.setName(cursor.getString(1));
                discount.setBusiness_id(cursor.getInt(2));
                discount.setBrand_id(cursor.getInt(3));
                discount.setCategory_id(cursor.getInt(4));
                discount.setLocation_id(cursor.getInt(5));
                discount.setPriority(cursor.getInt(6));
                discount.setDiscount_type(cursor.getString(7));
                discount.setDiscount_amount(cursor.getString(8));
                discount.setStarts_at(cursor.getString(9));
                discount.setEnds_at(cursor.getString(10));
                discount.setIs_active(cursor.getInt(11));
                discount.setApplicable_in_spg(cursor.getInt(12));
                discount.setApplicable_in_cg(cursor.getInt(13));


                discountList.add(discount);

            } while (cursor.moveToNext());
        }


        return discountList;

    }

    public ArrayList<Discount> getDiscountByCategory(Integer id_category) {
        ArrayList<Discount> discountList = new ArrayList<>();
        String selectQuery = "SELECT  * FROM " + DISCOUNT_TABLE_NAME + " WHERE " + DISCOUNT_CATEGORY_ID + " = " + id_category;
        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {
            do {
                Discount discount = new Discount();

                discount.setId(cursor.getInt(0));
                discount.setName(cursor.getString(1));
                discount.setBusiness_id(cursor.getInt(2));
                discount.setBrand_id(cursor.getInt(3));
                discount.setCategory_id(cursor.getInt(4));
                discount.setLocation_id(cursor.getInt(5));
                discount.setPriority(cursor.getInt(6));
                discount.setDiscount_type(cursor.getString(7));
                discount.setDiscount_amount(cursor.getString(8));
                discount.setStarts_at(cursor.getString(9));
                discount.setEnds_at(cursor.getString(10));
                discount.setIs_active(cursor.getInt(11));
                discount.setApplicable_in_spg(cursor.getInt(12));
                discount.setApplicable_in_cg(cursor.getInt(13));


                discountList.add(discount);

            } while (cursor.moveToNext());
        }


        return discountList;

    }

    public ArrayList<Discount> getDiscountByPriority(Integer priority, String currentDate) {
        ArrayList<Discount> discountList = new ArrayList<>();
        String selectQuery = "SELECT  * FROM " + DISCOUNT_TABLE_NAME + " WHERE " + DISCOUNT_PRIORITY + " = " + priority +
                " AND " + DISCOUNT_IS_ACTIVE + " = " + 1 + " AND " + DISCOUNT_APPLICABLE_IN_SPG + " = " + 1 ;
        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {
            do {
                Discount discount = new Discount();

                discount.setId(cursor.getInt(0));
                discount.setName(cursor.getString(1));
                discount.setBusiness_id(cursor.getInt(2));
                discount.setBrand_id(cursor.getInt(3));
                discount.setCategory_id(cursor.getInt(4));
                discount.setLocation_id(cursor.getInt(5));
                discount.setPriority(cursor.getInt(6));
                discount.setDiscount_type(cursor.getString(7));
                discount.setDiscount_amount(cursor.getString(8));
                discount.setStarts_at(cursor.getString(9));
                discount.setEnds_at(cursor.getString(10));
                discount.setIs_active(cursor.getInt(11));
                discount.setApplicable_in_spg(cursor.getInt(12));
                discount.setApplicable_in_cg(cursor.getInt(13));

                discountList.add(discount);

            } while (cursor.moveToNext());
        }
        
        return discountList;

    }

    public Discount getDiscountById(int id) {
        Discount discount = new Discount();

        String selectQuery = "SELECT  * FROM " + DISCOUNT_TABLE_NAME + " WHERE " + DISCOUNT_ID + " = " + id;
        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {
            discount.setId(cursor.getInt(0));
            discount.setName(cursor.getString(1));
            discount.setBusiness_id(cursor.getInt(2));
            discount.setBrand_id(cursor.getInt(3));
            discount.setCategory_id(cursor.getInt(4));
            discount.setLocation_id(cursor.getInt(5));
            discount.setPriority(cursor.getInt(6));
            discount.setDiscount_type(cursor.getString(7));
            discount.setDiscount_amount(cursor.getString(8));
            discount.setStarts_at(cursor.getString(9));
            discount.setEnds_at(cursor.getString(10));
            discount.setIs_active(cursor.getInt(11));
            discount.setApplicable_in_spg(cursor.getInt(12));
            discount.setApplicable_in_cg(cursor.getInt(13));
        }


        return discount;

    }

}
