package com.rising.high.tech.bigultimatenavdraw.db;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;

import com.rising.high.tech.bigultimatenavdraw.model.Discount_variation;
import com.rising.high.tech.bigultimatenavdraw.model.Discount_variation;

import java.util.ArrayList;

public class DiscountVariationDbController extends DBController {

    // **********   Table "CATERORY" fields ********************************************************************

    public static final String DISCOUNT_VAR_TABLE_NAME = "discount_variations";

    public static final String DISCOUNT_VAR_ID = "id"; //int
    public static final String DISCOUNT_VAR_DISCOUNT_ID = "discount_id";
    public static final String DISCOUNT_VAR_VARIATION_ID = "variation_id";

    public static final String DISCOUNT_VAR_TABLE_CREATE =
            "CREATE TABLE " + DISCOUNT_VAR_TABLE_NAME + " (" +
                    DISCOUNT_VAR_ID + " INTEGER PRIMARY KEY AUTOINCREMENT , " +
                    DISCOUNT_VAR_DISCOUNT_ID + " INTEGER, " +
                    DISCOUNT_VAR_VARIATION_ID + " INTEGER) ;";

    public static final String DISCOUNT_VAR_TABLE_DROP = "DROP TABLE IF EXISTS " + DISCOUNT_VAR_TABLE_NAME + ";";

    public DiscountVariationDbController(Context context) {
        super(context);
    }

    public int insert(Discount_variation discount_variation) {
        // Create a new map of values, where column names are the keys
        ContentValues pValues = new ContentValues();

        pValues.put(DISCOUNT_VAR_ID, discount_variation.getId());
        pValues.put(DISCOUNT_VAR_DISCOUNT_ID, discount_variation.getDiscount_id());
        pValues.put(DISCOUNT_VAR_VARIATION_ID, discount_variation.getVariation_id());

        int newRowId = (int) mDb.insert(DISCOUNT_VAR_TABLE_NAME, null, pValues); //renvoie l'id de l'enregistrement créé
        return newRowId;
    }


    public int insertLocal(Discount_variation discount_variation) {
        // Create a new map of values, where column names are the keys
        ContentValues pValues = new ContentValues();

        pValues.put(DISCOUNT_VAR_DISCOUNT_ID, discount_variation.getDiscount_id());
        pValues.put(DISCOUNT_VAR_VARIATION_ID, discount_variation.getVariation_id());


        int newRowId = (int) mDb.insert(DISCOUNT_VAR_TABLE_NAME, null, pValues); //renvoie l'id de l'enregistrement créé
        return newRowId;
    }

    public ArrayList<Integer> getDiscountIds(Integer id_product) {
        // Create a new map of values, where column names are the keys
        ArrayList<Integer> idArray = new ArrayList<>();
        String selectQuery = "SELECT  * FROM " + DISCOUNT_VAR_TABLE_NAME + " WHERE " + DISCOUNT_VAR_VARIATION_ID + " = " + id_product;
        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {
            do {

                idArray.add(cursor.getInt(1));


            } while (cursor.moveToNext());
        }


        return idArray;

    }

    public ArrayList<Discount_variation> getDiscountVarByDiscountId(Integer discount_id) {
        // Create a new map of values, where column names are the keys
        ArrayList<Discount_variation> discount_variationsArray = new ArrayList<>();
        String selectQuery = "SELECT  * FROM " + DISCOUNT_VAR_TABLE_NAME + " WHERE " + DISCOUNT_VAR_DISCOUNT_ID + " = " + discount_id;
        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {
            do {
                Discount_variation discount_variation=new Discount_variation();
                discount_variation.setId(cursor.getInt(0));
                discount_variation.setDiscount_id(cursor.getInt(1));
                discount_variation.setVariation_id(cursor.getInt(2));
                discount_variationsArray.add(discount_variation);
            } while (cursor.moveToNext());
        }


        return discount_variationsArray;

    }

    public void fill(ArrayList<Discount_variation> categories) {
        if (!categories.isEmpty()) {
            for (Discount_variation product : categories) {
                this.insert(product);
            }
        }
    }


    public void deleteAll() {
        mDb.execSQL("delete from " + DISCOUNT_VAR_TABLE_NAME);
    }

    public void deleteByDiscountId(Integer discount_id) {
        mDb.execSQL("delete from " + DISCOUNT_VAR_TABLE_NAME + " WHERE " + DISCOUNT_VAR_DISCOUNT_ID + " = '" + discount_id + "'");
    }

    public ArrayList<Discount_variation> getAllDiscount_variation() {
        ArrayList<Discount_variation> tmpDiscount_variation = new ArrayList<>();
        String selectQuery = "SELECT  * FROM " + DISCOUNT_VAR_TABLE_NAME;
        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {
            do {
                Discount_variation discount_variation = new Discount_variation();
                discount_variation.setId(cursor.getInt(0));
                discount_variation.setDiscount_id(cursor.getInt(1));
                discount_variation.setVariation_id(cursor.getInt(2));
                tmpDiscount_variation.add(discount_variation);

            } while (cursor.moveToNext());
        }


        return tmpDiscount_variation;

    }



    public Discount_variation getDiscount_variationById(Integer id) {
        Discount_variation discount_variation = new Discount_variation();

        String selectQuery = "SELECT  * FROM " + DISCOUNT_VAR_TABLE_NAME + " WHERE " + DISCOUNT_VAR_ID + " = " + id;
        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {
                discount_variation.setId(cursor.getInt(0));
                discount_variation.setDiscount_id(cursor.getInt(1));
                discount_variation.setVariation_id(cursor.getInt(2));
        }
        return discount_variation;
    }


}
