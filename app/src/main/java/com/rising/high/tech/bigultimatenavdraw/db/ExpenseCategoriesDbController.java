package com.rising.high.tech.bigultimatenavdraw.db;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;

import com.rising.high.tech.bigultimatenavdraw.model.Expense_category;
import com.rising.high.tech.bigultimatenavdraw.model.Unit;

import java.util.ArrayList;

public class ExpenseCategoriesDbController extends DBController {

    public static final String EXPENSE_CATEGORY_TABLE_NAME = "expense_categories";

    public static final String EXPENSE_CATEGORY_ID = "id"; //int
    public static final String EXPENSE_CATEGORY_NAME = "name";
    public static final String EXPENSE_CATEGORY_BUSINESS_ID = "business_id";
    public static final String EXPENSE_CATEGORY_CODE = "code";

    public static final String EXPENSE_CATEGORY_TABLE_CREATE =
            "CREATE TABLE " + EXPENSE_CATEGORY_TABLE_NAME + " (" +
                    EXPENSE_CATEGORY_ID + " INTEGER PRIMARY KEY AUTOINCREMENT, " +
                    EXPENSE_CATEGORY_NAME + " TEXT, " +
                    EXPENSE_CATEGORY_BUSINESS_ID + " INTEGER, " +
                     EXPENSE_CATEGORY_CODE+ " TEXT) ;";

    public static final String EXPENSE_CATEGORY_TABLE_DROP = "DROP TABLE IF EXISTS " + EXPENSE_CATEGORY_TABLE_NAME + ";";

    public ExpenseCategoriesDbController(Context context) {
        super(context);
    }

    public int insert(Expense_category expense_category) {
        // Create a new map of values, where column names are the keys
        ContentValues pValues = new ContentValues();

        pValues.put(EXPENSE_CATEGORY_ID, expense_category.getId());
        pValues.put(EXPENSE_CATEGORY_NAME, expense_category.getName());
        pValues.put(EXPENSE_CATEGORY_BUSINESS_ID, expense_category.getBusiness_id());
        pValues.put(EXPENSE_CATEGORY_CODE, expense_category.getCode());

        // Insert the new row, returning the primary key value of the new row
        int newRowId = (int) mDb.insert(EXPENSE_CATEGORY_TABLE_NAME, null, pValues); //renvoie l'id de l'enregistrement créé

        // mDb.close();
        return newRowId;
    }

    public int insertLocal(Expense_category expense_category) {
        // Create a new map of values, where column names are the keys
        ContentValues pValues = new ContentValues();
        pValues.put(EXPENSE_CATEGORY_NAME, expense_category.getName());
        pValues.put(EXPENSE_CATEGORY_NAME, expense_category.getName());
        pValues.put(EXPENSE_CATEGORY_BUSINESS_ID, expense_category.getBusiness_id());
        pValues.put(EXPENSE_CATEGORY_CODE, expense_category.getCode());
        // Insert the new row, returning the primary key value of the new row
        int newRowId = (int) mDb.insert(EXPENSE_CATEGORY_TABLE_NAME, null, pValues); //renvoie l'id de l'enregistrement créé

        // mDb.close();
        return newRowId;
    }

    public void deleteItem(Integer id) {
        mDb.execSQL("delete from " + EXPENSE_CATEGORY_TABLE_NAME  + " WHERE " + EXPENSE_CATEGORY_ID + " = '" + id + "'");
    }

    public int editExpenseCategory(Expense_category expense_category) {
        // Create a new map of values, where column names are the keys
        ContentValues pValues = new ContentValues();

        pValues.put(EXPENSE_CATEGORY_NAME, expense_category.getName());
        pValues.put(EXPENSE_CATEGORY_CODE, expense_category.getCode());

        // Insert the new row, returning the primary key value of the new row
        int newRowId = mDb.update(EXPENSE_CATEGORY_TABLE_NAME, pValues, EXPENSE_CATEGORY_ID + " = '" + expense_category.getId() + "'", null); //renvoie l'id de l'enregistrement créé

        // mDb.close();
        return newRowId;
    }

    public ArrayList<Expense_category> getAllExpense_categoriesLike(String string) {
        ArrayList<Expense_category> tempCompany = new ArrayList<>();
        String selectQuery = "SELECT  * FROM " + EXPENSE_CATEGORY_TABLE_NAME+ " WHERE " + EXPENSE_CATEGORY_NAME + " like '%" + string + "%'";
        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {
            do {
                Expense_category expense_category = new Expense_category();
                expense_category.setId(cursor.getInt(0));
                expense_category.setName(cursor.getString(1));
                expense_category.setBusiness_id(cursor.getInt(2));
                expense_category.setCode(cursor.getString(3));

                tempCompany.add(expense_category);

            } while (cursor.moveToNext());

        }

        // mDb.close();

        return tempCompany;
    }
    public ArrayList<Expense_category> getAllExpense_categories() {
        ArrayList<Expense_category> tempCompany = new ArrayList<>();
        String selectQuery = "SELECT  * FROM " + EXPENSE_CATEGORY_TABLE_NAME;
        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {
            do {
                Expense_category expense_category = new Expense_category();
                expense_category.setId(cursor.getInt(0));
                expense_category.setName(cursor.getString(1));
                expense_category.setBusiness_id(cursor.getInt(2));
                expense_category.setCode(cursor.getString(3));

                tempCompany.add(expense_category);

            } while (cursor.moveToNext());

        }

        // mDb.close();

        return tempCompany;
    }

    public ArrayList<Expense_category> getSpinExpenseCategories() {
        ArrayList<Expense_category> tempCompany = new ArrayList<>();
        Expense_category expense_category1= new Expense_category(0, "Please Select");
        tempCompany.add(expense_category1);
        String selectQuery = "SELECT  * FROM " + EXPENSE_CATEGORY_TABLE_NAME;
        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {
            do {
                Expense_category expense_category = new Expense_category();
                expense_category.setId(cursor.getInt(0));
                expense_category.setName(cursor.getString(1));
                expense_category.setBusiness_id(cursor.getInt(2));
                expense_category.setCode(cursor.getString(3));

                tempCompany.add(expense_category);

            } while (cursor.moveToNext());

        }

        // mDb.close();

        return tempCompany;
    }

    public Expense_category getExpense_categoryById(Integer id) {
        Expense_category brand = new Expense_category();

        String selectQuery = "SELECT  * FROM " + EXPENSE_CATEGORY_TABLE_NAME + " WHERE " + EXPENSE_CATEGORY_ID + " = " + id;

        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {
                brand.setId(cursor.getInt(0));
                brand.setName(cursor.getString(1));
                brand.setCode(cursor.getString(3));


        }

        // mDb.close();

        return brand;
    }

    public ArrayList<Expense_category> getAllExpense_categorySpinner() {
        ArrayList<Expense_category> tempCompany = new ArrayList<>();
        String selectQuery = "SELECT  * FROM " + EXPENSE_CATEGORY_TABLE_NAME;
        Cursor cursor = mDb.rawQuery(selectQuery, null);

        tempCompany.add(new Expense_category(0,  "All"));
        if (cursor.moveToFirst()) {
            do {
                Expense_category expense_category = new Expense_category();
                expense_category.setId(cursor.getInt(0));
                expense_category.setName(cursor.getString(1));
                expense_category.setBusiness_id(cursor.getInt(2));
                expense_category.setCode(cursor.getString(3));

                tempCompany.add(expense_category);

            } while (cursor.moveToNext());

        }

        // mDb.close();

        return tempCompany;
    }

    // Insert all product
    public void fill(ArrayList<Expense_category> products) {
        if (!products.isEmpty()) {
            for (Expense_category product : products) {
                this.insert(product);
            }
        }
        //   mDb.close();
    }

    public void clear() {
        mDb.execSQL("DELETE FROM " + EXPENSE_CATEGORY_TABLE_NAME);
    }

}
