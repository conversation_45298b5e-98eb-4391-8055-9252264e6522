package com.rising.high.tech.bigultimatenavdraw.db;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;

import com.rising.high.tech.bigultimatenavdraw.model.Model_has_roles;
import com.rising.high.tech.bigultimatenavdraw.model.Model_has_roles;

import java.util.ArrayList;

public class ModelHasRolesDbController extends DBController {

    public static final String MODEL_HAS_ROLES_TABLE_NAME = "modelhasroles";

    public static final String MODEL_HAS_ROLES_ID = "role_id"; //int
    public static final String MODEL_HAS_MODEL_ID = "model_id";
    public static final String MODEL_HAS_MODEL_TYPE = "model_type";

    public static final String MODEL_HAS_ROLES_TABLE_CREATE =
            "CREATE TABLE " + MODEL_HAS_ROLES_TABLE_NAME + " (" +
                    MODEL_HAS_ROLES_ID + " INTEGER , " +
                    MODEL_HAS_MODEL_ID + " INTEGER , " +
                    MOD<PERSON>_HAS_MODEL_TYPE + " TEXT ) ;";
    public static final String MODEL_HAS_ROLES_TABLE_DROP = "DROP TABLE IF EXISTS " + MODEL_HAS_ROLES_TABLE_NAME + ";";

    public ModelHasRolesDbController(Context context) {
        super(context);
    }

    public int insert(Model_has_roles model_has_roles) {
        // Create a new map of values, where column names are the keys
        ContentValues pValues = new ContentValues();

        pValues.put(MODEL_HAS_ROLES_ID, model_has_roles.getRole_id());
        pValues.put(MODEL_HAS_MODEL_ID, model_has_roles.getModel_id());
        pValues.put(MODEL_HAS_MODEL_TYPE, model_has_roles.getModel_type());

        return (int) mDb.insert(MODEL_HAS_ROLES_TABLE_NAME, null, pValues);
    }

    public int insertLocal(Model_has_roles model_has_roles) {
        // Create a new map of values, where column names are the keys
        ContentValues pValues = new ContentValues();
        pValues.put(MODEL_HAS_ROLES_ID, model_has_roles.getRole_id());
        pValues.put(MODEL_HAS_MODEL_ID, model_has_roles.getModel_id());
        pValues.put(MODEL_HAS_MODEL_TYPE, model_has_roles.getModel_type());

        return (int) mDb.insert(MODEL_HAS_ROLES_TABLE_NAME, null, pValues);
    }
    public int updateLocal(Model_has_roles brand) {
        ContentValues pValues = new ContentValues();
        pValues.put(MODEL_HAS_ROLES_ID, brand.getRole_id());
        pValues.put(MODEL_HAS_MODEL_ID, brand.getModel_id());
        pValues.put(MODEL_HAS_MODEL_TYPE, brand.getModel_type());
        return mDb.update(MODEL_HAS_ROLES_TABLE_NAME, pValues, MODEL_HAS_ROLES_ID + " = '" + brand.getRole_id() + "'", null);

    }
    public void deleteItem(Integer id) {
        mDb.execSQL("delete from " + MODEL_HAS_ROLES_TABLE_NAME  + " WHERE " + MODEL_HAS_ROLES_ID + " = '" + id + "'");
    }
    public ArrayList<Model_has_roles> getAllModel_has_roless() {
        ArrayList<Model_has_roles> tempCompany = new ArrayList<>();
        String selectQuery = "SELECT  * FROM " + MODEL_HAS_ROLES_TABLE_NAME;
        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {
            do {

                Model_has_roles model_has_roles = new Model_has_roles();
                model_has_roles.setRole_id(cursor.getInt(0));
                model_has_roles.setModel_id(cursor.getInt(1));
                model_has_roles.setModel_type(cursor.getString(2));
                tempCompany.add(model_has_roles);

            } while (cursor.moveToNext());

        }
        return tempCompany;
    }


    // Insert all product
    public void fill(ArrayList<Model_has_roles> products) {
        if (!products.isEmpty()) {
            for (Model_has_roles product : products) {
                this.insert(product);
            }
        }
        //   mDb.close();
    }

    public void clear() {
        mDb.execSQL("DELETE FROM " + MODEL_HAS_ROLES_TABLE_NAME);
    }

}
