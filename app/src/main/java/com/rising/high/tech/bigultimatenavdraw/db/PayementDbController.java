package com.rising.high.tech.bigultimatenavdraw.db;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;

import com.rising.high.tech.bigultimatenavdraw.model.Payement;

import java.util.ArrayList;

public class PayementDbController extends DBController {

    public static final String PAYEMENT_TABLE_NAME = "payement";

    public static final String PAYEMENT_ID = "id"; //int
    public static final String PAYEMENT_AMOUNT = "amount";
    public static final String PAYEMENT_METHOD = "method";
    public static final String PAYEMENT_CARD_NUMBER = "card_number";
    public static final String PAYEMENT_CARD_HOLDER_NAME = "card_holder_name";
    public static final String PAYEMENT_CARD_TRANSACTION_NUMBER = "card_transaction_number";
    public static final String PAYEMENT_CARD_TYPE = "card_type";
    public static final String PAYEMENT_MONTH = "card_month";
    public static final String PAYEMENT_CARD_YEAR = "card_year";
    public static final String PAYEMENT_CARD_SECURITY = "card_security";
    public static final String PAYEMENT_CHEQUE_NUMBER = "cheque_number";
    public static final String PAYEMENT_BANK_ACCOUNT_NUMBER = "bank_account_number";
    public static final String PAYEMENT_TRNSACTION_NO_1 = "transaction_no_1";
    public static final String PAYEMENT_TRANSACTION_NO_2 = "transaction_no_2";
    public static final String PAYEMENT_TRANSACTION_NO_3 = "transaction_no_3";
    public static final String PAYEMENT_NOTE = "note";
    public static final String PAYEMENT_TTRANSACTION_ID = "transaction_id";


    public static final String PAYEMENT_TABLE_CREATE =
            "CREATE TABLE " + PAYEMENT_TABLE_NAME + " (" +
                    PAYEMENT_ID + " INTEGER PRIMARY KEY AUTOINCREMENT, " +
                    PAYEMENT_AMOUNT + " TEXT, " +
                    PAYEMENT_METHOD + " TEXT, " +
                    PAYEMENT_CARD_NUMBER + " TEXT, " +
                    PAYEMENT_CARD_HOLDER_NAME + " TEXT, " +
                    PAYEMENT_CARD_TRANSACTION_NUMBER + " TEXT, " +
                    PAYEMENT_CARD_TYPE + " TEXT, " +
                    PAYEMENT_MONTH + " TEXT, " +
                    PAYEMENT_CARD_YEAR + " TEXT, " +
                    PAYEMENT_CARD_SECURITY + " TEXT, " +
                    PAYEMENT_CHEQUE_NUMBER + " TEXT, " +
                    PAYEMENT_BANK_ACCOUNT_NUMBER + " TEXT, " +
                    PAYEMENT_TRNSACTION_NO_1 + " TEXT, " +
                    PAYEMENT_TRANSACTION_NO_2 + " TEXT, " +
                    PAYEMENT_TRANSACTION_NO_3 + " TEXT, " +
                    PAYEMENT_NOTE + " TEXT, " +
                    PAYEMENT_TTRANSACTION_ID + " INTEGER) ;";

    public static final String PAYEMENT_TABLE_DROP = "DROP TABLE IF EXISTS " + PAYEMENT_TABLE_NAME + ";";

    public PayementDbController(Context context) {
        super(context);
    }

    public int insert(Payement payement, int transaction_id) {
        // Create a new map of values, where column names are the keys
        ContentValues pValues = new ContentValues();

        pValues.put(PAYEMENT_AMOUNT, payement.getAmount());
        pValues.put(PAYEMENT_METHOD, payement.getMethod());
        pValues.put(PAYEMENT_CARD_NUMBER, payement.getCard_number() != null ? payement.getCard_number() : null);
        pValues.put(PAYEMENT_CARD_HOLDER_NAME, payement.getCard_holder_name() != null ? payement.getCard_holder_name() : null);
        pValues.put(PAYEMENT_CARD_TRANSACTION_NUMBER, payement.getCard_transaction_number() != null ? payement.getCard_transaction_number() : null);
        pValues.put(PAYEMENT_CARD_TYPE, payement.getCard_type() != null ? payement.getCard_type() : null);
        pValues.put(PAYEMENT_NOTE, payement.getNote() != null ? payement.getNote() : null);
        pValues.put(PAYEMENT_TTRANSACTION_ID, transaction_id);


        int newRowId = (int) mDb.insert(PAYEMENT_TABLE_NAME, null, pValues); //renvoie l'id de l'enregistrement créé
        return newRowId;
    }


    public ArrayList<Payement> getPayement(int Transaction_id) {
        String selectQuery = "SELECT  * FROM " + PAYEMENT_TABLE_NAME + " WHERE " + PAYEMENT_TTRANSACTION_ID + " = '" + Transaction_id + "'";
        Cursor cursor = mDb.rawQuery(selectQuery, null);


        ArrayList<Payement> tmpPayement= new ArrayList<>();
        if (cursor.moveToFirst()) {

            do {
                Payement payement = new Payement();

                payement.setAmount(cursor.getString(1));
                payement.setMethod(cursor.getString(2));



                // product.setImage_product(cursor.getString(28));

                tmpPayement.add(payement);

            } while (cursor.moveToNext());

        }

        // mDb.close();

        return tmpPayement;

    }


    public void fill(ArrayList<Payement> payements, int transaction_id) {
        if (!payements.isEmpty()) {
            for (Payement payement : payements) {
                this.insert(payement, transaction_id);
            }
        }
    }

}
