package com.rising.high.tech.bigultimatenavdraw.db;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;

import com.rising.high.tech.bigultimatenavdraw.model.Permission;
import com.rising.high.tech.bigultimatenavdraw.model.Permission;

import java.util.ArrayList;

public class PermissionDbController extends DBController {

    public static final String PERMISSION_TABLE_NAME = "permission";

    public static final String PERMISSION_ID = "id"; //int
    public static final String PERMISSION_NAME = "name";
    public static final String PERMISSION_GUARD_NAME = "guard_name";

    public static final String PERMISSION_TABLE_CREATE =
            "CREATE TABLE " + PERMISSION_TABLE_NAME + " (" +
                    PERMISSION_ID + " INTEGER PRIMARY KEY AUTOINCREMENT, " +
                    PERMISSION_NAME + " TEXT, " +
                    PERMISSION_GUARD_NAME + " TEXT) ;";

    public static final String PERMISSION_TABLE_DROP = "DROP TABLE IF EXISTS " + PERMISSION_TABLE_NAME + ";";

    public PermissionDbController(Context context) {
        super(context);
    }

    public int insert(Permission permission) {
        // Create a new map of values, where column names are the keys
        ContentValues pValues = new ContentValues();

        pValues.put(PERMISSION_ID, permission.getId());
        pValues.put(PERMISSION_NAME, permission.getName());
        pValues.put(PERMISSION_GUARD_NAME, permission.getGuard_name());

        // Insert the new row, returning the primary key value of the new row
        int newRowId = (int) mDb.insert(PERMISSION_TABLE_NAME, null, pValues); //renvoie l'id de l'enregistrement créé

        // mDb.close();
        return newRowId;
    }

    public int insertLocal(Permission permission) {
        // Create a new map of values, where column names are the keys
        ContentValues pValues = new ContentValues();

     //   pValues.put(PERMISSION_ID, permission.getId());
        pValues.put(PERMISSION_NAME, permission.getName());
        pValues.put(PERMISSION_GUARD_NAME, permission.getGuard_name());

        // Insert the new row, returning the primary key value of the new row
        int newRowId = (int) mDb.insert(PERMISSION_TABLE_NAME, null, pValues); //renvoie l'id de l'enregistrement créé

        // mDb.close();
        return newRowId;
    }

    public int editPermission(Permission permission) {
        // Create a new map of values, where column names are the keys
        ContentValues pValues = new ContentValues();
        pValues.put(PERMISSION_ID, permission.getId());
        pValues.put(PERMISSION_NAME, permission.getName());
        pValues.put(PERMISSION_GUARD_NAME, permission.getGuard_name());

        // Insert the new row, returning the primary key value of the new row
        int newRowId = mDb.update(PERMISSION_TABLE_NAME, pValues, PERMISSION_ID + " = '" + permission.getId() + "'", null); //renvoie l'id de l'enregistrement créé

        // mDb.close();
        return newRowId;
    }

    public void deleteItem(Integer id) {
        mDb.execSQL("delete from " + PERMISSION_TABLE_NAME + " WHERE " + PERMISSION_ID + " = '" + id + "'");
    }

    // get count of non sync products
    public int dbSyncCount() {

        int count = 0;
        String selectQuery = "SELECT  * FROM " + PERMISSION_TABLE_NAME ;
        SQLiteDatabase database = this.mHandler.getWritableDatabase();
        Cursor cursor = database.rawQuery(selectQuery, null);
        count = cursor.getCount();
        return count;

    }

    public ArrayList<Permission> getAllPermissions() {
        ArrayList<Permission> tempCompany = new ArrayList<>();
        String selectQuery = "SELECT  * FROM " + PERMISSION_TABLE_NAME;
        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {
            do {
                Permission permission = new Permission();
                permission.setId(cursor.getInt(0));
                permission.setName(cursor.getString(1));
                permission.setGuard_name(cursor.getString(2));
                tempCompany.add(permission);

            } while (cursor.moveToNext());

        }

        // mDb.close();

        return tempCompany;
    }

    public ArrayList<Integer> getPermissionIds(String name) {
        ArrayList<Integer> tempCompany = new ArrayList<>();
        String selectQuery = "SELECT  * FROM " + PERMISSION_TABLE_NAME + " WHERE " + PERMISSION_NAME + " like '%" + name + "%'";

        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {
            do {

                tempCompany.add(cursor.getInt(0));

            } while (cursor.moveToNext());

        }

        // mDb.close();

        return tempCompany;
    }



    public Integer getPermissionId(String name) {
        Integer tempCompany = 0 ;
        String selectQuery = "SELECT  * FROM " + PERMISSION_TABLE_NAME + " WHERE " + PERMISSION_NAME + " = '" + name + "'";

        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {
                tempCompany=cursor.getInt(0);
        }

        // mDb.close();

        return tempCompany;
    }

    public int getPermissionsId(String name) {
        int id = 0;
        String selectQuery = "SELECT  * FROM " + PERMISSION_TABLE_NAME + " WHERE " + PERMISSION_NAME + " = '" + name + "'";
        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) { id=cursor.getInt(0); }
        // mDb.close();
        return id;
    }

    /*
    public ArrayList<Permission> getSyncPermissions(String sync) {
        ArrayList<Permission> tempCompany = new ArrayList<>();
        String selectQuery = "SELECT  * FROM " + PERMISSION_TABLE_NAME + " WHERE " + PERMISSION_SYNC + " ='" + sync + "'";
        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {
            do {
                Permission permission = new Permission();
                permission.setId(cursor.getInt(0));
                permission.setActual_name(cursor.getString(1));
                permission.setShort_name(cursor.getString(2));
                permission.setAllow_decimal(cursor.getInt(3));
                permission.setBusiness_id(cursor.getInt(4));
                permission.setSync(cursor.getString(5));
                permission.setPermission_server_id(cursor.getInt(6));
                permission.setCreated_by(1);

                tempCompany.add(permission);

            } while (cursor.moveToNext());

        }

        // mDb.close();

        return tempCompany;
    }
*/

    public ArrayList<Permission> getAllPermissionSpinner() {
        ArrayList<Permission> tempCompany = new ArrayList<>();
//        String selectQuery = "SELECT  * FROM " + PERMISSION_TABLE_NAME;
//        Cursor cursor = mDb.rawQuery(selectQuery, null);
//
//        tempCompany.add(new Permission(0, "All", "All"));
//        if (cursor.moveToFirst()) {
//            do {
//                Permission permission = new Permission();
//                permission.setId(Integer.parseInt(cursor.getString(0)));
//                permission.setActual_name(cursor.getString(1));
//                permission.setShort_name(cursor.getString(2));
//                permission.setAllow_decimal(cursor.getInt(3));
//                permission.setBusiness_id(cursor.getInt(4));
//
//                tempCompany.add(permission);
//
//            } while (cursor.moveToNext());
//
//        }

        // mDb.close();

        return tempCompany;
    }


    // Insert all product
    public void fill(ArrayList<Permission> products) {
        if (!products.isEmpty()) {
            for (Permission product : products) {
                this.insertLocal(product);
            }
        }
        //   mDb.close();
    }

    public void clear() {
        mDb.execSQL("DELETE FROM " + PERMISSION_TABLE_NAME);
    }

}
