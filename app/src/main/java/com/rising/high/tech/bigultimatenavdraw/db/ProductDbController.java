package com.rising.high.tech.bigultimatenavdraw.db;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.util.Log;


import com.google.gson.Gson;
import com.rising.high.tech.bigultimatenavdraw.model.Product;
import com.rising.high.tech.bigultimatenavdraw.model.Product_location;
import com.rising.high.tech.bigultimatenavdraw.model.Unit;
import com.rising.high.tech.bigultimatenavdraw.model.Variation;
import com.rising.high.tech.bigultimatenavdraw.model.Variation_location_details;

import java.io.ByteArrayOutputStream;
import java.util.ArrayList;

public class ProductDbController extends DBController {

    private ProductLocationDbController productLocationDbController;
    private VariationLocationDetailDbController variationLocationDetailDbController;
    private VariationsDbController variationsDbController;
    private CategoryDbController categoryDbController;
    private UnitDbController unitDbController;
    private final String TAG = getClass().getSimpleName();

    // **********   Table "PRODUCT" fields ********************************************************************

    public static final String PRODUCT_TABLE_NAME = "products";
    public static final String PRODUCT_ID = "id"; //int
    public static final String PRODUCT_NAME = "name";
    public static final String PRODUCT_BUSINESS_ID = "business_id";
    public static final String PRODUCT_TYPE = "type";
    public static final String PRODUCT_UNIT_ID = "unit_id";
    public static final String PRODUCT_SUB_UNIT_IDS = "sub_unit_ids";
    public static final String PRODUCT_BRAND_ID = "brand_id";
    public static final String PRODUCT_CATEGORY_ID = "category_id";
    public static final String PRODUCT_SUB_CATEGORY_ID = "sub_category_id";
    public static final String PRODUCT_TAX = "tax";
    public static final String PRODUCT_TAX_TYPE = "tax_type";
    public static final String PRODUCT_ENABLE_STOCK = "enable_stock";
    public static final String PRODUCT_ALERT_QUANTITY = "alert_quantity";
    public static final String PRODUCT_SKU = "sku";
    public static final String PRODUCT_BARCODE_TYPE = "barcode_type";
    public static final String PRODUCT_SRC_NO = "enable_sr_no";
    public static final String PRODUCT_EXPIRED_PERIOD = "expiry_period";//===>
    public static final String PRODUCT_WEIGHT = "weight";
    public static final String PRODUCT_MAX_PRICE = "max_price";
    public static final String PRODUCT_SINGLE_DPP = "single_dpp";
    public static final String PRODUCT_SINGLE_DPP_INC_TAX = "single_dpp_inc_tax";
    public static final String PRODUCT_PROFIT_PERCENT = "profit_percent";
    public static final String PRODUCT_SINGLE_DSP = "single_dsp";
    public static final String PRODUCT_CATEGORY = "category";
    public static final String PRODUCT_SELL_QTE = "sell_qte";
    public static final String PRODUCT_IMAGE = "image";
    public static final String PRODUCT_PRODUCT_DESCREPTION = "product_description";
    public static final String PRODUCT_CREATED_BY = "created_by";
    public static final String PRODUCT_WARRANTY_ID = "warranty_id";
    public static final String PRODUCT_IS_INACTIVE = "is_inactive";
    public static final String PRODUCT_NOT_FOR_SELLING = "not_for_selling";
    public static final String PRODUCT_IMAGE_PRODUCT = "image_product";
    public static final String PRODUCT_IS_SYNC = "is_sync";
    public static final String PRODUCT_VARIATION_ID = "variation_id";
    public static final String PRODUCT_ID_PRODUCT = "product_id";
    public static final String PRODUCT_USER_ID = "user_id";
    public static final String PRODUCT_PRODUCT = "product";
    public static final String PRODUCT_UNIT_PRICE_INC_TAX = "unit_price_inc_tax";
    public static final String PRODUCT_SINGLE_DSP_INC_TAX = "single_dsp_inc_tax";
    public static final String PRODUCT_CURRENT_STOCK = "current_stock";
    public static final String PRODUCT_FAVORIS = "favoris";
    public static final String PRODUCT_IMAGE_URL = "image_url";
    public static final String PRODUCT_DEFAULT_SELL_PRICE = "default_sell_price";
    public static final String PRODUCT_QTY_AVAILABLE = "qty_available";
    public static final String PRODUCT_CATEGORY_NAME = "category_name";
    public static final String PRODUCT_UNIT_ACTUAL_NAME = "unit_actual_name";
    public static final String PRODUCT_UNIT_SHORT_NAME = "short_name";
    public static final String PRODUCT_LOCATION_ID = "location_id";
    public static final String PRODUCT_ID_PROD_SERVER_ID = "product_server_id";

    public static final String PRODUCT_TABLE_CREATE =
            "CREATE TABLE " + PRODUCT_TABLE_NAME + " (" +
                    PRODUCT_ID + " INTEGER PRIMARY KEY AUTOINCREMENT , " +
                    PRODUCT_NAME + " TEXT, " +
                    PRODUCT_BUSINESS_ID + " INTEGER, " +
                    PRODUCT_TYPE + " TEXT, " +
                    PRODUCT_UNIT_ID + " INTEGER, " +
                    PRODUCT_SUB_UNIT_IDS + " TEXT, " +
                    PRODUCT_BRAND_ID + " INTEGER, " +
                    PRODUCT_CATEGORY_ID + " INTEGER, " +
                    PRODUCT_SUB_CATEGORY_ID + " INTEGER, " +
                    PRODUCT_TAX + " TEXT, " +
                    PRODUCT_TAX_TYPE + " TEXT, " +
                    PRODUCT_MAX_PRICE + " TEXT, " +
                    PRODUCT_SINGLE_DPP + " TEXT, " +
                    PRODUCT_SINGLE_DPP_INC_TAX + " TEXT, " +
                    PRODUCT_PROFIT_PERCENT + " TEXT, " +
                    PRODUCT_SINGLE_DSP + " TEXT, " +
                    PRODUCT_CATEGORY + " TEXT, " +
                    PRODUCT_ENABLE_STOCK + " TEXT, " +
                    PRODUCT_ALERT_QUANTITY + " INTEGER, " +
                    PRODUCT_SKU + " TEXT, " +
                    PRODUCT_BARCODE_TYPE + " TEXT, " +
                    PRODUCT_SRC_NO + " INTEGER, " +
                    PRODUCT_EXPIRED_PERIOD + " TEXT, " +
                    PRODUCT_WEIGHT + " TEXT, " +
                    PRODUCT_IMAGE + " TEXT, " +
                    PRODUCT_IMAGE_PRODUCT + " BLOB, " +
                    PRODUCT_PRODUCT_DESCREPTION + " TEXT, " +
                    PRODUCT_CREATED_BY + " INTEGER, " +
                    PRODUCT_WARRANTY_ID + " INTEGER, " +
                    PRODUCT_IS_INACTIVE + " INTEGER, " +
                    PRODUCT_IS_SYNC + " TEXT, " +
                    PRODUCT_SELL_QTE + " INTEGER, " +
                    PRODUCT_NOT_FOR_SELLING + " INTEGER, " +
                    PRODUCT_VARIATION_ID + " INTEGER, " +
                    PRODUCT_ID_PRODUCT + " INTEGER, " +
                    PRODUCT_USER_ID + " INTEGER, " +
                    PRODUCT_PRODUCT + " INTEGER, " +
                    PRODUCT_UNIT_PRICE_INC_TAX + " TEXT, " +
                    PRODUCT_SINGLE_DSP_INC_TAX + " TEXT, " +
                    PRODUCT_CURRENT_STOCK + " TEXT, " +
                    PRODUCT_FAVORIS + " TEXT, " +
                    PRODUCT_IMAGE_URL + " TEXT, " +
                    PRODUCT_DEFAULT_SELL_PRICE + " TEXT, " +
                    PRODUCT_QTY_AVAILABLE + " TEXT, " +
                    PRODUCT_CATEGORY_NAME + " TEXT, " +
                    PRODUCT_UNIT_ACTUAL_NAME + " TEXT, " +
                    PRODUCT_UNIT_SHORT_NAME + " TEXT, " +
                    PRODUCT_LOCATION_ID + " INTEGER, " +
                    PRODUCT_ID_PROD_SERVER_ID + " INTEGER) ;";

    public static final String PRODUCT_TABLE_DROP = "DROP TABLE IF EXISTS " + PRODUCT_TABLE_NAME + ";";

    public ProductDbController(Context context) {
        super(context);
        productLocationDbController = new ProductLocationDbController(context);
        productLocationDbController.open();
        variationLocationDetailDbController = new VariationLocationDetailDbController(context);
        variationLocationDetailDbController.open();
        variationsDbController = new VariationsDbController(context);
        variationsDbController.open();
        categoryDbController = new CategoryDbController(context);
        categoryDbController.open();
        unitDbController = new UnitDbController(context);
        unitDbController.open();
    }

    public int insert(Product product) {
        // Create a new map of values, where column names are the keys
        ContentValues pValues = new ContentValues();

        pValues.put(PRODUCT_ID, product.getId());
        pValues.put(PRODUCT_NAME, product.getName());
        pValues.put(PRODUCT_UNIT_ID, product.getUnit() != null ? product.getUnit().getId() : 0);
        pValues.put(PRODUCT_BARCODE_TYPE, product.getBarcode_type());
        pValues.put(PRODUCT_TAX_TYPE, product.getTax_type());
        pValues.put(PRODUCT_TAX, product.getTax()!= null ? product.getTax():0);
        pValues.put(PRODUCT_TYPE, product.getType());
        pValues.put(PRODUCT_ALERT_QUANTITY, product.getAlert_quantity());
        pValues.put(PRODUCT_SUB_UNIT_IDS, product.getSub_unit_ids());
        pValues.put(PRODUCT_ENABLE_STOCK, product.getEnable_stock());
        pValues.put(PRODUCT_EXPIRED_PERIOD, product.getExpiry_period());
        pValues.put(PRODUCT_WEIGHT, product.getWeight());
        pValues.put(PRODUCT_SKU, product.getSku());
        pValues.put(PRODUCT_NOT_FOR_SELLING, product.getNot_for_selling());
        pValues.put(PRODUCT_BUSINESS_ID, product.getBusiness_id());
        pValues.put(PRODUCT_IMAGE_URL, product.getImage_url());
        pValues.put(PRODUCT_IS_INACTIVE, product.getIs_inactive());
        pValues.put(PRODUCT_CATEGORY_ID, product.getCategory() != null ? product.getCategory().getId() : 0);
        pValues.put(PRODUCT_CATEGORY_NAME, product.getCategory() != null ? product.getCategory().getName() : null);
        pValues.put(PRODUCT_BRAND_ID, product.getBrand() !=null? product.getBrand().getId() : 0);

        pValues.put(PRODUCT_FAVORIS, "no");
        pValues.put(PRODUCT_IS_SYNC, product.getIs_sync() != null ? product.getIs_sync() : "yes");
        pValues.put(PRODUCT_SELL_QTE, product.getSell_qte());
        pValues.put(PRODUCT_UNIT_ACTUAL_NAME, product.getUnit() != null ? product.getUnit().getActual_name() : null);
        pValues.put(PRODUCT_UNIT_SHORT_NAME, product.getUnit() != null ? product.getUnit().getShort_name() : null);
        if (product.getProduct_variations() != null && product.getProduct_variations().size() > 0) {
            if (product.getProduct_variations().get(0).getVariations() != null && product.getProduct_variations().get(0).getVariations().size() > 0) {

//                pValues.put(PRODUCT_DEFAULT_SELL_PRICE, product.getProduct_variations() != null ? product.getProduct_variations().get(0).getVariations().get(0).getDefault_sell_price() : "0");
//                pValues.put(PRODUCT_SINGLE_DPP, product.getProduct_variations() != null ? product.getProduct_variations().get(0).getVariations().get(0).getDefault_purchase_price() : "0");
//                pValues.put(PRODUCT_SINGLE_DPP_INC_TAX, product.getProduct_variations() != null ? product.getProduct_variations().get(0).getVariations().get(0).getDpp_inc_tax() : "0");
//                pValues.put(PRODUCT_PROFIT_PERCENT, product.getProduct_variations() != null ? product.getProduct_variations().get(0).getVariations().get(0).getProfit_percent() : "0");
//                pValues.put(PRODUCT_SINGLE_DSP, product.getProduct_variations() != null ? product.getProduct_variations().get(0).getVariations().get(0).getDefault_sell_price() : "0");
//                pValues.put(PRODUCT_SINGLE_DSP_INC_TAX, product.getProduct_variations() != null ? product.getProduct_variations().get(0).getVariations().get(0).getSell_price_inc_tax() : "0");

                //  insert variations containings prices
                Variation variation = product.getProduct_variations().get(0).getVariations().get(0);
                variationsDbController.insert(variation);

                if (product.getProduct_variations().get(0).getVariations().get(0).getVariation_location_details() != null && product.getProduct_variations().get(0).getVariations().get(0).getVariation_location_details().size() > 0) {
                    for (Variation_location_details variation_location_details : product.getProduct_variations().get(0).getVariations().get(0).getVariation_location_details()) {
                        Variation_location_details variation_location_details1 = variation_location_details;
                        variationLocationDetailDbController.insert(variation_location_details1);
                    }
                    //   pValues.put(PRODUCT_QTY_AVAILABLE, product.getProduct_variations().get(0).getVariations().get(0).getVariation_location_details().get(0).getQty_available());
                }

            } else {
                pValues.put(PRODUCT_DEFAULT_SELL_PRICE, 0);
            }
        } else {
            Variation variation = new Variation();
            variation.setSell_price_inc_tax("00.0");
            variation.setDefault_sell_price("00.0");
            variation.setProfit_percent("00.0");
            variation.setDpp_inc_tax("00.0");
            variation.setDefault_purchase_price("00.0");
            variation.setVariation_server_id(0);
            variation.setProduct_id(product.getId());
            variationsDbController.insertLocal(variation);
        }

        //  pValues.put(PRODUCT_LOCATION_ID, product.getProduct_locations() != null && product.getProduct_locations().size() > 0 ? product.getProduct_locations().get(0).getId() : null);

        if (product.getProduct_locations() != null && product.getProduct_locations().size() > 0) {
            for (Product_location product_location : product.getProduct_locations()) {
                product_location.setProduct_id(product.getId());
                product_location.setLocation_id(product_location.getId()+"");
                productLocationDbController.insertLocal(product_location);
            }
        }

        pValues.put(PRODUCT_CREATED_BY, product.getCreated_by());
        pValues.put(PRODUCT_SRC_NO, product.getEnable_sr_no());
        pValues.put(PRODUCT_SUB_CATEGORY_ID, product.getSub_category_id());

        // Insert the new row, returning the primary key value of the new row
        int newRowId = (int) mDb.insert(PRODUCT_TABLE_NAME, null, pValues); //renvoie l'id de l'enregistrement créé

        // mDb.close();
        return newRowId;
    }

    public int insertLocal(Product product) {
        // Create a new map of values, where column names are the keys
        ContentValues pValues = new ContentValues();

        pValues.put(PRODUCT_NAME, product.getName());
        pValues.put(PRODUCT_UNIT_ID, product.getUnit() != null ? product.getUnit().getId() : product.getUnit_id());
        pValues.put(PRODUCT_BARCODE_TYPE, product.getBarcode_type());
        pValues.put(PRODUCT_TAX_TYPE, product.getTax_type());
        pValues.put(PRODUCT_TAX, product.getTax());
        pValues.put(PRODUCT_TYPE, product.getType());
        pValues.put(PRODUCT_ALERT_QUANTITY, product.getAlert_quantity());
        pValues.put(PRODUCT_SUB_UNIT_IDS, product.getSub_unit_ids());
        pValues.put(PRODUCT_ENABLE_STOCK, product.getEnable_stock());
        pValues.put(PRODUCT_EXPIRED_PERIOD, product.getExpiry_period());
        pValues.put(PRODUCT_WEIGHT, product.getWeight());
        pValues.put(PRODUCT_SKU, product.getSku());
        pValues.put(PRODUCT_NOT_FOR_SELLING, product.getNot_for_selling());
        pValues.put(PRODUCT_BUSINESS_ID, product.getBusiness_id());
        pValues.put(PRODUCT_IMAGE_URL, product.getImage_url());
        pValues.put(PRODUCT_IS_INACTIVE, product.getIs_inactive());
        pValues.put(PRODUCT_CATEGORY_ID, product.getCategory_id());
        pValues.put(PRODUCT_CATEGORY_NAME, product.getCategory() != null ? product.getCategory().getName() : null);
        pValues.put(PRODUCT_IMAGE_URL, product.getImage_url());
        pValues.put(PRODUCT_DEFAULT_SELL_PRICE, product.getProduct_variations() != null ? product.getProduct_variations().get(0).getVariations().get(0).getDefault_sell_price() : null);
        pValues.put(PRODUCT_FAVORIS, "no");
        pValues.put(PRODUCT_IS_SYNC, product.getIs_sync() != null ? product.getIs_sync() : "yes");
        pValues.put(PRODUCT_SELL_QTE, product.getSell_qte());
        pValues.put(PRODUCT_UNIT_ACTUAL_NAME, product.getUnit_actualname());
        pValues.put(PRODUCT_UNIT_SHORT_NAME, product.getUnit_shortname());

        pValues.put(PRODUCT_LOCATION_ID, product.getLcoation_id());
        pValues.put(PRODUCT_CREATED_BY, product.getCreated_by());
        pValues.put(PRODUCT_SRC_NO, product.getEnable_sr_no());
        pValues.put(PRODUCT_BRAND_ID, product.getBrand_id() !=null? product.getBrand_id() : 0);
        pValues.put(PRODUCT_PRODUCT_DESCREPTION, product.getProduct_description());
        pValues.put(PRODUCT_SUB_CATEGORY_ID, product.getSub_category_id());

        if (product.getImage_product() != null)
            pValues.put(PRODUCT_IMAGE_PRODUCT, getBitmapAsByteArray(product.getImage_product()));


        // Insert the new row, returning the primary key value of the new row
        int newRowId = (int) mDb.insert(PRODUCT_TABLE_NAME, null, pValues); //renvoie l'id de l'enregistrement créé

        // mDb.close();
        return newRowId;
    }

    public byte[] getBitmapAsByteArray(Bitmap bitmap) {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        bitmap.compress(Bitmap.CompressFormat.JPEG, 0, outputStream);
        return outputStream.toByteArray();
    }

    public int updateProduct(Product product) {
        // Create a new map of values, where column names are the keys
        ContentValues pValues = new ContentValues();

        //   pValues.put(PRODUCT_ID, product.getId());
        pValues.put(PRODUCT_NAME, product.getName());
        pValues.put(PRODUCT_UNIT_ID, product.getUnit() != null ? product.getUnit().getId() : product.getUnit_id());
        pValues.put(PRODUCT_BARCODE_TYPE, product.getBarcode_type());
        pValues.put(PRODUCT_TAX_TYPE, product.getTax_type());
        pValues.put(PRODUCT_TAX, product.getTax());
        pValues.put(PRODUCT_BRAND_ID, product.getBrand_id());
        pValues.put(PRODUCT_TYPE, product.getType());
        pValues.put(PRODUCT_ALERT_QUANTITY, product.getAlert_quantity());
        pValues.put(PRODUCT_SUB_UNIT_IDS, product.getSub_unit_ids());
        pValues.put(PRODUCT_ENABLE_STOCK, product.getEnable_stock());
        pValues.put(PRODUCT_EXPIRED_PERIOD, product.getExpiry_period());
        pValues.put(PRODUCT_WEIGHT, product.getWeight());
        pValues.put(PRODUCT_SKU, product.getSku());
        pValues.put(PRODUCT_NOT_FOR_SELLING, product.getNot_for_selling());
        pValues.put(PRODUCT_BUSINESS_ID, product.getBusiness_id());
        pValues.put(PRODUCT_IMAGE_URL, product.getImage_url());
        pValues.put(PRODUCT_IS_INACTIVE, product.getIs_inactive());
        pValues.put(PRODUCT_CATEGORY_ID, product.getCategory_id());
        pValues.put(PRODUCT_CATEGORY_NAME, product.getCategory() != null ? product.getCategory().getName() : product.getCategory_name());
        pValues.put(PRODUCT_FAVORIS, "no");
        pValues.put(PRODUCT_IS_SYNC, product.getIs_sync() != null ? product.getIs_sync() : "yes");
        pValues.put(PRODUCT_SELL_QTE, product.getSell_qte());
        pValues.put(PRODUCT_UNIT_ACTUAL_NAME, product.getUnit() != null ? product.getUnit().getActual_name() : product.getUnit_actualname());
        pValues.put(PRODUCT_UNIT_SHORT_NAME, product.getUnit() != null ? product.getUnit().getShort_name() : product.getUnit_shortname());
        if(product.getImage_product()!=null)pValues.put(PRODUCT_IMAGE_PRODUCT, getBitmapAsByteArray(product.getImage_product()));
        if (product.getProduct_variations() != null && product.getProduct_variations().size() > 0) {
            if (product.getProduct_variations().get(0).getVariations().get(0).getVariation_location_details() != null && product.getProduct_variations().get(0).getVariations().get(0).getVariation_location_details().size() > 0) {
                //    pValues.put(PRODUCT_QTY_AVAILABLE, product.getProduct_variations().get(0).getVariations().get(0).getVariation_location_details().get(0).getQty_available());
            }
        }
        // pValues.put(PRODUCT_QTY_AVAILABLE, product.getProduct_variations() != null && product.getProduct_variations().size() > 0 ? product.getProduct_variations().get(0).getVariations().get(0).getVariation_location_details().get(0).getQty_available() : null);
        pValues.put(PRODUCT_LOCATION_ID, product.getLcoation_id());
        pValues.put(PRODUCT_CREATED_BY, product.getCreated_by());
        pValues.put(PRODUCT_SRC_NO, product.getEnable_sr_no());
        if (product.getImage_product() != null)
            pValues.put(PRODUCT_IMAGE_PRODUCT, getBitmapAsByteArray(product.getImage_product()));

        // Insert the new row, returning the primary key value of the new row
        int newRowId = mDb.update(PRODUCT_TABLE_NAME, pValues, PRODUCT_ID + " = '" + product.getId() + "'", null); //renvoie l'id de l'enregistrement créé

        // mDb.close();
        return newRowId;
    }

    public int updateProductServer(Product product) {
        // Create a new map of values, where column names are the keys
        ContentValues pValues = new ContentValues();

        //   pValues.put(PRODUCT_ID, product.getId());
        pValues.put(PRODUCT_ID_PROD_SERVER_ID, product.getProduct_server_id());
        pValues.put(PRODUCT_IS_SYNC, product.getIs_sync());
        pValues.put(PRODUCT_IMAGE_URL, product.getImage_url());

        // Insert the new row, returning the primary key value of the new row
        int newRowId = mDb.update(PRODUCT_TABLE_NAME, pValues, PRODUCT_ID + " = '" + product.getId() + "'", null); //renvoie l'id de l'enregistrement créé

        // mDb.close();
        return newRowId;
    }


    // get all products on db
    public ArrayList<Product> getSyncProduct(String status) {
        ArrayList<Product> tmpProducts = new ArrayList<>();
        String selectQuery = "SELECT  * FROM " + PRODUCT_TABLE_NAME + " WHERE " + PRODUCT_IS_SYNC + " = '" + status + "'";

        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst())
        {

            do {
                Product product = new Product();
                product.setId(cursor.getInt(0));
                product.setName(cursor.getString(1));
                Integer category_id_server = categoryDbController.getCategoryServerId(cursor.getInt(0));
                product.setCategory_id(category_id_server);
                product.setSku(cursor.getString(19));
                product.setType(cursor.getString(3));
                product.setNot_for_selling(cursor.getInt(35));
                product.setTax_type(cursor.getString(10));
                product.setTax(cursor.getInt(9) != 0 ? cursor.getInt(9) : null);

                Unit unit=unitDbController.getUnitsById(cursor.getInt(4));
                product.setUnit_id(unit.getUnit_server_id());
                product.setAlert_quantity(cursor.getInt(18));
                product.setBusiness_id(cursor.getInt(2));
                product.setImage_url(cursor.getString(41));

                product.setSell_qte(cursor.getInt(31));
                product.setQty_available(cursor.getString(43));
                product.setCategory_name(cursor.getString(44));
                product.setUnit_actualname(cursor.getString(45));
                product.setUnit_shortname(cursor.getString(46));
                product.setEnable_stock(cursor.getInt(17));
                product.setIs_sync(cursor.getString(30));

                product.setCreated_by(cursor.getInt(27));
                product.setBrand_id(cursor.getInt(6));
                product.setSub_category_id(cursor.getInt(8)!=0? cursor.getInt(8): null);
                product.setSub_unit_ids(null);

                Variation variations = variationsDbController.getVariationByProductId(cursor.getInt(0));
                product.setVariations(variations);

                product.setProduct_locations(productLocationDbController.getProductLocationByProductId(cursor.getInt(0)));

                if (cursor.getBlob(25) != null)
                    product.setImage_product(BitmapFactory.decodeByteArray(cursor.getBlob(25), 0, cursor.getBlob(25).length));

                tmpProducts.add(product);

            } while (cursor.moveToNext());

        }

        // mDb.close();

        return tmpProducts;
    }


    // get all products on db
    public Product getEditProduct(Integer id) {
        String selectQuery = "SELECT  * FROM " + PRODUCT_TABLE_NAME + " WHERE " + PRODUCT_ID + " = '" + id + "'";
        Product product = new Product();

        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {

            product.setId(cursor.getInt(0));
            product.setName(cursor.getString(1));
            product.setCategory_id(cursor.getInt(7));
            product.setSub_category_id(cursor.getInt(8));
            product.setSku(cursor.getString(19));
            if (cursor.getBlob(25) != null)
                product.setImage_product(BitmapFactory.decodeByteArray(cursor.getBlob(25), 0, cursor.getBlob(25).length));
            product.setType(cursor.getString(3));
            product.setNot_for_selling(cursor.getInt(32));
            product.setTax_type(cursor.getString(10));
            product.setTax(cursor.getInt(9) != 0 ? cursor.getInt(9) : 0);
            product.setUnit_id(cursor.getInt(4));
            product.setAlert_quantity(cursor.getInt(18));
            product.setBusiness_id(cursor.getInt(2));
            product.setImage_url(cursor.getString(41));

            product.setSell_qte(cursor.getInt(31));
            product.setQty_available(cursor.getString(43));
            product.setCategory_name(cursor.getString(44));
            product.setUnit_actualname(cursor.getString(45));
            product.setUnit_shortname(cursor.getString(46));
            product.setEnable_stock(cursor.getInt(17));
            product.setIs_sync(cursor.getString(30));

            product.setCreated_by(cursor.getInt(27));
            product.setTax_type(cursor.getString(10));
            product.setBrand_id(cursor.getInt(6));
            product.setProduct_description(cursor.getString(26));
            product.setWeight(cursor.getString(23));
            product.setSub_unit_ids(null);

        }

        // mDb.close();

        return product;
    }


    // get all products on db
    public ArrayList<Product> getProductByCategory(int id) {

        ArrayList<Product> tmpProducts = new ArrayList<>();
        String selectQuery = "SELECT  * FROM " + PRODUCT_TABLE_NAME + " WHERE " + PRODUCT_CATEGORY_ID + " = '" + id + "'" + " AND " + PRODUCT_NOT_FOR_SELLING + " = " + 0;
        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {

            do {
                Product product = new Product();

                product.setId(cursor.getInt(0));
                product.setName(cursor.getString(1));
                product.setCategory_id(cursor.getInt(7));
                product.setSku(cursor.getString(19));
                if (cursor.getBlob(25) != null)
                    product.setImage_product(BitmapFactory.decodeByteArray(cursor.getBlob(25), 0, cursor.getBlob(25).length));
                product.setType(cursor.getString(3));
                product.setNot_for_selling(cursor.getInt(35));
                product.setTax_type(cursor.getString(10));
                product.setTax(cursor.getInt(9));
                product.setUnit_id(cursor.getInt(4));
                product.setAlert_quantity(cursor.getInt(18));
                product.setBusiness_id(Integer.parseInt(cursor.getString(2)));
                product.setImage_url(cursor.getString(41));

                product.setSell_qte(cursor.getInt(31));
                product.setQty_available(cursor.getString(43));
                product.setCategory_name(cursor.getString(44));
                product.setUnit_actualname(cursor.getString(45));
                product.setUnit_shortname(cursor.getString(46));
                product.setCreated_by(cursor.getInt(27));
                product.setTax_type(cursor.getString(10));
                product.setLcoation_id(cursor.getInt(47));
                product.setIs_sync(cursor.getString(30));

                tmpProducts.add(product);

            } while (cursor.moveToNext());

        }

        // mDb.close();

        return tmpProducts;
    }


 // get all products on db
    public ArrayList<Integer> getProductCategory(int category_id) {

        ArrayList<Integer> tmpProducts = new ArrayList<>();
        String selectQuery = "SELECT  * FROM " + PRODUCT_TABLE_NAME + " WHERE " + PRODUCT_CATEGORY_ID + " = '" + category_id + "'" + " AND " + PRODUCT_NOT_FOR_SELLING + " = " + 0;
        Cursor cursor = mDb.rawQuery(selectQuery, null);
        if (cursor.moveToFirst()) {
            do {
                tmpProducts.add(cursor.getInt(0));
            } while (cursor.moveToNext());

        }
        // mDb.close();
        return tmpProducts;
    }


    // get all products on db
    public ArrayList<Product> getProductByName(String name) {
        ArrayList<Product> tmpProducts = new ArrayList<>();
        String selectQuery = "SELECT  * FROM " + PRODUCT_TABLE_NAME + " WHERE " + PRODUCT_NAME + " like '%" + name + "%'" + " AND " + PRODUCT_NOT_FOR_SELLING + " = " + 0;

        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {

            do {
                Product product = new Product();

                product.setId(cursor.getInt(0));
                product.setName(cursor.getString(1));
                product.setCategory_id(cursor.getInt(7));
                product.setSku(cursor.getString(19));
                if (cursor.getBlob(25) != null)
                    product.setImage_product(BitmapFactory.decodeByteArray(cursor.getBlob(25), 0, cursor.getBlob(25).length));
                product.setType(cursor.getString(3));
                product.setNot_for_selling(cursor.getInt(35));
                product.setTax_type(cursor.getString(10));
                product.setTax(cursor.getInt(9));
                product.setUnit_id(cursor.getInt(4));
                product.setAlert_quantity(cursor.getInt(18));
                product.setBusiness_id(Integer.parseInt(cursor.getString(2)));
                product.setImage_url(cursor.getString(41));

                product.setSell_qte(cursor.getInt(31));
                product.setQty_available(cursor.getString(43));
                product.setCategory_name(cursor.getString(44));
                product.setUnit_actualname(cursor.getString(45));
                product.setUnit_shortname(cursor.getString(46));
                product.setCreated_by(cursor.getInt(27));
                product.setTax_type(cursor.getString(10));
                product.setLcoation_id(cursor.getInt(47));
                product.setIs_sync(cursor.getString(30));


                tmpProducts.add(product);

            } while (cursor.moveToNext());

        }

        // mDb.close();

        return tmpProducts;
    }


    // get all products on db
    public Product getProductById(int id) {
        Product product = new Product();
        String selectQuery = "SELECT  * FROM " + PRODUCT_TABLE_NAME + " WHERE " + PRODUCT_ID + " = '" + id + "'";

        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {


            product.setId(Integer.parseInt(cursor.getString(0)));
            product.setName(cursor.getString(1));
            product.setCategory_id(cursor.getInt(7));
            product.setSku(cursor.getString(19));
            if (cursor.getBlob(25) != null)
                product.setImage_product(BitmapFactory.decodeByteArray(cursor.getBlob(25), 0, cursor.getBlob(25).length));
            product.setType(cursor.getString(3));
            product.setNot_for_selling(cursor.getInt(32));
            product.setTax_type(cursor.getString(10));
            product.setTax(cursor.getInt(9));
            product.setUnit_id(cursor.getInt(4));
            product.setAlert_quantity(cursor.getInt(18));
            product.setBusiness_id(Integer.parseInt(cursor.getString(2)));
            product.setImage_url(cursor.getString(41));
            product.setSell_qte(cursor.getInt(31));
            product.setCategory_name(cursor.getString(44));

            product.setUnit_actualname(cursor.getString(45));
            product.setUnit_shortname(cursor.getString(46));
            product.setIs_sync(cursor.getString(30));
            product.setBrand_id(cursor.getInt(6));
            product.setProduct_description(cursor.getString(26));
            product.setWeight(cursor.getString(23));
            product.setProduct_server_id(cursor.getInt(48));
        }

        // mDb.close();

        return product;
    }


    // Insert all product
    public void fill(ArrayList<Product> products) {
        if (products != null) {
            for (Product product : products) {
                this.insert(product);
            }
        }
        //   mDb.close();
    }

    // get all products on db
    public ArrayList<Product> getAllProduct() {
        ArrayList<Product> tmpProducts = new ArrayList<>();
        String selectQuery = "SELECT  * FROM " + PRODUCT_TABLE_NAME;
        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {
            do {
                Product product = new Product();
                product.setId(cursor.getInt(0));
                product.setName(cursor.getString(1));
                product.setCategory_id(cursor.getInt(7));
                product.setSku(cursor.getString(19));
                if (cursor.getBlob(25) != null)
                    product.setImage_product(BitmapFactory.decodeByteArray(cursor.getBlob(25), 0, cursor.getBlob(25).length));
                product.setType(cursor.getString(3));
                product.setNot_for_selling(cursor.getInt(35));
                product.setTax_type(cursor.getString(10));
                product.setTax(cursor.getInt(9));
                product.setUnit_id(cursor.getInt(4));
                product.setAlert_quantity(cursor.getInt(18));
                product.setBusiness_id(Integer.parseInt(cursor.getString(2)));
                product.setImage_url(cursor.getString(41));
                product.setSell_qte(cursor.getInt(31));
                product.setQty_available(cursor.getString(43));
                product.setCategory_name(cursor.getString(44));
                product.setUnit_actualname(cursor.getString(45));
                product.setUnit_shortname(cursor.getString(46));
                product.setCreated_by(cursor.getInt(27));
                product.setTax_type(cursor.getString(10));
                product.setLcoation_id(cursor.getInt(47));
                product.setIs_sync(cursor.getString(30));
                product.setEnable_stock(cursor.getInt(17));

                tmpProducts.add(product);

            } while (cursor.moveToNext());

        }

        // mDb.close();

        return tmpProducts;
    }

    // get all products on db
    public ArrayList<Product> getHomeProduct() {
        ArrayList<Product> tmpProducts = new ArrayList<>();
        String selectQuery = "SELECT  * FROM " + PRODUCT_TABLE_NAME + " WHERE " + PRODUCT_NOT_FOR_SELLING + " = " + 0;
        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {
            do {
                Product product = new Product();
                product.setId(cursor.getInt(0));
                product.setName(cursor.getString(1));
                product.setCategory_id(cursor.getInt(7));
                product.setSku(cursor.getString(19));
                if (cursor.getBlob(25) != null)
                    product.setImage_product(BitmapFactory.decodeByteArray(cursor.getBlob(25), 0, cursor.getBlob(25).length));
                product.setType(cursor.getString(3));
                product.setNot_for_selling(cursor.getInt(35));
                product.setTax_type(cursor.getString(10));
                product.setTax(cursor.getInt(9));
                product.setUnit_id(cursor.getInt(4));
                product.setAlert_quantity(cursor.getInt(18));
                product.setBusiness_id(Integer.parseInt(cursor.getString(2)));
                product.setImage_url(cursor.getString(41));
                product.setSell_qte(cursor.getInt(31));
                product.setQty_available(cursor.getString(43));
                product.setCategory_name(cursor.getString(44));
                product.setUnit_actualname(cursor.getString(45));
                product.setUnit_shortname(cursor.getString(46));
                product.setCreated_by(cursor.getInt(27));
                product.setTax_type(cursor.getString(10));
                product.setLcoation_id(cursor.getInt(47));
                product.setIs_sync(cursor.getString(30));
                product.setEnable_stock(cursor.getInt(17));

                tmpProducts.add(product);

            } while (cursor.moveToNext());

        }

        // mDb.close();

        return tmpProducts;
    }

    // get all products on db
    public ArrayList<Product> getFilter(Integer categoryId, Integer unitId, Integer locationId) {
        ArrayList<Product> tmpProducts = new ArrayList<>();
        String selectQuery = "";
        if ((categoryId.equals(0)) && unitId.equals(0) && locationId.equals(0)) {
            selectQuery = "SELECT * FROM " + PRODUCT_TABLE_NAME;
        } else {
            selectQuery = "SELECT * FROM " + PRODUCT_TABLE_NAME + " WHERE ";
            String categoryQuery = (categoryId.equals(0)) ? (PRODUCT_CATEGORY_ID + " IS NOT NULL") : (PRODUCT_CATEGORY_ID + " = " + categoryId);
            String unitQuery = " AND " + ((unitId.equals(0)) ? (PRODUCT_UNIT_ID + " IS NOT NULL") : (PRODUCT_UNIT_ID + " = '" + unitId + "'"));
            //   String locationQuery = " AND " + ((locationId.equals(0)) ? (PRODUCT_LOCATION_ID + " IS NOT NULL") : (PRODUCT_LOCATION_ID + " = '" + locationId + "'"));
            selectQuery += categoryQuery + unitQuery;
        }
        Cursor cursor = mDb.rawQuery(selectQuery, null);
        if (cursor.moveToFirst()) {
            do {
                Product product = new Product();
                product.setId(cursor.getInt(0));
                product.setName(cursor.getString(1));
                product.setCategory_id(cursor.getInt(7));
                product.setSku(cursor.getString(19));
                if (cursor.getBlob(25) != null)
                    product.setImage_product(BitmapFactory.decodeByteArray(cursor.getBlob(25), 0, cursor.getBlob(25).length));
                product.setType(cursor.getString(3));
                product.setNot_for_selling(cursor.getInt(35));
                product.setTax_type(cursor.getString(10));
                product.setTax(cursor.getInt(9));
                product.setUnit_id(cursor.getInt(4));
                product.setAlert_quantity(cursor.getInt(18));
                product.setBusiness_id(Integer.parseInt(cursor.getString(2)));
                product.setImage_url(cursor.getString(41));

                product.setSell_qte(cursor.getInt(31));
                product.setQty_available(cursor.getString(43));
                product.setCategory_name(cursor.getString(44));
                product.setUnit_actualname(cursor.getString(45));
                product.setUnit_shortname(cursor.getString(46));

                product.setCreated_by(cursor.getInt(27));
                product.setTax_type(cursor.getString(10));
                product.setLcoation_id(cursor.getInt(47));

                product.setIs_sync(cursor.getString(30));

                tmpProducts.add(product);

            } while (cursor.moveToNext());

        }

        // mDb.close();

        return tmpProducts;
    }

    public void setUpdateYes(int id, int id_product) {
        mDb.execSQL("UPDATE " + PRODUCT_TABLE_NAME + " SET " + PRODUCT_ID_PRODUCT + " = " + id_product + " WHERE " + PRODUCT_ID + " = " + id);
        mDb.execSQL("UPDATE " + PRODUCT_TABLE_NAME + " SET " + PRODUCT_VARIATION_ID + " = " + id_product + " WHERE " + PRODUCT_ID + " = " + id);
    }

    public void setUpdateStock(int id_product, String price, String quantity) {
        mDb.execSQL("UPDATE " + PRODUCT_TABLE_NAME + " SET " + PRODUCT_DEFAULT_SELL_PRICE + " = " + price + " WHERE " + PRODUCT_ID + " = " + id_product);
        mDb.execSQL("UPDATE " + PRODUCT_TABLE_NAME + " SET " + PRODUCT_QTY_AVAILABLE + " = " + quantity + " WHERE " + PRODUCT_ID + " = " + id_product);
    }

    public void setFavoris(int id, String status) {
        mDb.execSQL("UPDATE " + PRODUCT_TABLE_NAME + " SET " + PRODUCT_FAVORIS + " = '" + status + "' WHERE " + PRODUCT_ID_PRODUCT + " = " + id);
    }


    // get all products on db
    public ArrayList<Product> getFavorisProduct(String status) {
        ArrayList<Product> tmpProducts = new ArrayList<>();
        String selectQuery = "SELECT  * FROM " + PRODUCT_TABLE_NAME + " WHERE " + PRODUCT_FAVORIS + " = '" + status + "'";

        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {

            do {
                Product product = new Product();

                product.setId(Integer.parseInt(cursor.getString(0)));
                product.setName(cursor.getString(1));
                product.setCategory_id(Integer.parseInt(cursor.getString(7)));
                product.setSku(cursor.getString(19));
                if (cursor.getBlob(25) != null)
                    product.setImage_product(BitmapFactory.decodeByteArray(cursor.getBlob(25), 0, cursor.getBlob(25).length));
                product.setType(cursor.getString(3));
                product.setNot_for_selling(cursor.getInt(35));
                product.setTax_type(cursor.getString(10));
                product.setTax(cursor.getInt(9));
                product.setUnit_id(cursor.getInt(4));
                product.setAlert_quantity(cursor.getInt(18));
                product.setBusiness_id(Integer.parseInt(cursor.getString(2)));

                tmpProducts.add(product);

            } while (cursor.moveToNext());

        }

        // mDb.close();

        return tmpProducts;
    }

    public void deleteAll() {
        mDb.execSQL("delete from " + PRODUCT_TABLE_NAME);
    }

    public void deleteProduct(int id) {
        mDb.execSQL("delete from " + PRODUCT_TABLE_NAME + " WHERE " + PRODUCT_ID + " = " + id);
    }

    public void updateProductStock(ArrayList<Product> products) {
        for (Product product : products) {
            mDb.execSQL("UPDATE " + PRODUCT_TABLE_NAME + " SET " + PRODUCT_QTY_AVAILABLE + "= " + product.getQty_available() + " WHERE " + PRODUCT_ID + " = " + product.getId());
        }
    }

    // get non sync prodcut status
    public String getSyncStatus() {

        String msg = null;
        if (this.dbSyncCount() == 0) {
            msg = "SQLite and Remote MySQL DBs are in Sync!";
        } else {
            msg = "DB Sync needed";
        }
        return msg;

    }


    // get count of non sync products
    public int dbSyncCount() {

        int count = 0;
        String selectQuery = "SELECT  * FROM " + PRODUCT_TABLE_NAME + " WHERE " + PRODUCT_IS_SYNC + " = '" + "no" + "'";
        SQLiteDatabase database = this.mHandler.getWritableDatabase();
        Cursor cursor = database.rawQuery(selectQuery, null);
        count = cursor.getCount();
        return count;

    }
    public int getLastProductID() {
        int _id = 0;
        String selectQuery = "SELECT * FROM  " + PRODUCT_TABLE_NAME + " ORDER BY " + PRODUCT_ID + " DESC LIMIT 1";
        Cursor cursor = mDb.rawQuery(selectQuery, null);
        if (cursor.moveToLast()) {
            _id = cursor.getInt(0);
        }
        cursor.close();
        return _id;
    }
    public ArrayList<Product> searchProductList(int categoryId, int unitId, int locationId,int taxId,int brandId) {
        ArrayList<Product> tmpProducts = new ArrayList<>();
        String selectQuery = "";
        if ((categoryId == 0) && unitId == 0 && locationId == 0 && taxId == 0 && brandId == 0) {
            selectQuery = "SELECT * FROM " + PRODUCT_TABLE_NAME;
        } else {
            selectQuery = "SELECT * FROM " + PRODUCT_TABLE_NAME + " WHERE ";
            String categoryQuery = (categoryId == 0) ? (PRODUCT_CATEGORY_ID + " IS NOT NULL") : (PRODUCT_CATEGORY_ID + " = '" + categoryId + "'");
            String unitQuery = " AND " + ((unitId == 0) ? (PRODUCT_UNIT_ID + " IS NOT NULL") : (PRODUCT_UNIT_ID + " = '" + unitId + "'"));
          //  String locationQuery = " AND " + ((locationId == 0) ? (PRODUCT_LOCATION_ID + " IS NOT NULL") : (PRODUCT_LOCATION_ID + " = '" + locationId + "'"));
            String taxQuery = " AND " + ((taxId == 0) ? (PRODUCT_TAX + " IS NOT NULL") : (PRODUCT_TAX + " = '" + taxId + "'"));
            String brandQuery = " AND " + ((brandId == 0) ? (PRODUCT_BRAND_ID + " IS NOT NULL") : (PRODUCT_BRAND_ID + " = '" + brandId + "'"));
            selectQuery += categoryQuery + unitQuery  + taxQuery + brandQuery;
        }
        Cursor cursor = mDb.rawQuery(selectQuery, null);
        if (cursor.moveToFirst()) {
            do {
                Product product = new Product();
                product.setId(cursor.getInt(0));
                product.setName(cursor.getString(1));
                product.setCategory_id(cursor.getInt(7));
                product.setSku(cursor.getString(19));
                if(cursor.getBlob(25)!=null)product.setImage_product(BitmapFactory.decodeByteArray(cursor.getBlob(25), 0, cursor.getBlob(25).length));
                product.setType(cursor.getString(3));
                product.setNot_for_selling(cursor.getInt(35));
                product.setTax_type(cursor.getString(10));
                product.setTax(cursor.getInt(9));
                product.setUnit_id(cursor.getInt(4));
                product.setAlert_quantity(cursor.getInt(18));
                product.setBusiness_id(Integer.parseInt(cursor.getString(2)));
                product.setImage_url(cursor.getString(41));

                product.setSell_qte(cursor.getInt(31));
                product.setQty_available(cursor.getString(43));
                product.setCategory_name(cursor.getString(44));
                product.setUnit_actualname(cursor.getString(45));
                product.setUnit_shortname(cursor.getString(46));

                product.setCreated_by(cursor.getInt(27));
                product.setTax_type(cursor.getString(10));
                product.setLcoation_id(cursor.getInt(47));

                product.setIs_sync(cursor.getString(30));

    // get count of non sync products
                tmpProducts.add(product);

            } while (cursor.moveToNext());

        }

        // mDb.close();

        return tmpProducts;
    }
    public int dbIsEmptyCount() {
        int count = 0;
        String selectQuery = "SELECT  * FROM " + PRODUCT_TABLE_NAME;
        SQLiteDatabase database = this.mHandler.getWritableDatabase();
        Cursor cursor = database.rawQuery(selectQuery, null);
        count = cursor.getCount();
        //database.close();
        return count;

    }
}
