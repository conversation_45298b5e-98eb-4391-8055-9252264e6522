package com.rising.high.tech.bigultimatenavdraw.db;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.util.Log;

import com.google.gson.Gson;
import com.rising.high.tech.bigultimatenavdraw.model.Product_location;
import com.rising.high.tech.bigultimatenavdraw.model.Variation_location_details;

import java.util.ArrayList;

public class ProductLocationDbController extends DBController {

    private static final String TAG = "ProductLocationDbController";
    // **********   Table "CATERORY" fields ********************************************************************

    private VariationLocationDetailDbController variationLocationDetailDbController;
    public static final String PRODUCT_LOCATION_TABLE_NAME = "product_locations";

    public static final String PRODUCT_LOCATION_PRODUCT_ID = "product_id";
    public static final String PRODUCT_LOCATION_LOCATION_ID = "location_id";

    public static final String PRODUCT_LOCATION_TABLE_CREATE =
            "CREATE TABLE " + PRODUCT_LOCATION_TABLE_NAME + " (" +
                    PRODUCT_LOCATION_PRODUCT_ID + " INTEGER, " +
                    PRODUCT_LOCATION_LOCATION_ID + " INTEGER) ;";

    public static final String PRODUCT_LOCATION_TABLE_DROP = "DROP TABLE IF EXISTS " + PRODUCT_LOCATION_TABLE_NAME + ";";

    public ProductLocationDbController(Context context) {
        super(context);
        variationLocationDetailDbController= new VariationLocationDetailDbController(context);
        variationLocationDetailDbController.open();
    }

    public int insert(Product_location product_location) {
        // Create a new map of values, where column names are the keys
        ContentValues pValues = new ContentValues();

        pValues.put(PRODUCT_LOCATION_PRODUCT_ID, product_location.getProduct_id());
        pValues.put(PRODUCT_LOCATION_LOCATION_ID, product_location.getLocation_id());

        int newRowId = (int) mDb.insert(PRODUCT_LOCATION_TABLE_NAME, null, pValues); //renvoie l'id de l'enregistrement créé
        return newRowId;
    }

    public int insertLocal(Product_location product_location) {
        // Create a new map of values, where column names are the keys
        ContentValues pValues = new ContentValues();

        pValues.put(PRODUCT_LOCATION_PRODUCT_ID, product_location.getProduct_id());
        pValues.put(PRODUCT_LOCATION_LOCATION_ID, product_location.getLocation_id());


        int newRowId = (int) mDb.insert(PRODUCT_LOCATION_TABLE_NAME, null, pValues); //renvoie l'id de l'enregistrement créé
        return newRowId;
    }


    public void fill(ArrayList<Product_location> product_locations) {
        if (!product_locations.isEmpty()) {
            for (Product_location product : product_locations) {
                this.insert(product);
            }
        }
    }


    public void deleteAll() {
        mDb.execSQL("delete from " + PRODUCT_LOCATION_TABLE_NAME);
    }

    public ArrayList<Product_location> getAllProductLocations() {
        ArrayList<Product_location> tmpPRODUCT_LOCATION = new ArrayList<>();
        String selectQuery = "SELECT  * FROM " + PRODUCT_LOCATION_TABLE_NAME;
        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {
            do {
                Product_location product_location1 = new Product_location();
                product_location1.setProduct_id(cursor.getInt(0));
                product_location1.setLocation_id(cursor.getString(1));

                tmpPRODUCT_LOCATION.add(product_location1);

            } while (cursor.moveToNext());
        }


        return tmpPRODUCT_LOCATION;

    }


        public ArrayList<Product_location> getProductLocationByProductId(Integer product_id) {
        ArrayList<Product_location> tmpPRODUCT_LOCATION = new ArrayList<>();
        String selectQuery = "SELECT  * FROM " + PRODUCT_LOCATION_TABLE_NAME + " WHERE " + PRODUCT_LOCATION_PRODUCT_ID + " = " + product_id;
        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {
            do {
                Product_location product_location1 = new Product_location();
                product_location1.setProduct_id(cursor.getInt(0));
                product_location1.setLocation_id(cursor.getString(1));

                Variation_location_details variation_location_details=variationLocationDetailDbController.getVariationLocationDetailsByStationIdProductId(cursor.getInt(1), cursor.getInt(0));
                product_location1.setQty_available(variation_location_details.getQty_available());

                tmpPRODUCT_LOCATION.add(product_location1);

            } while (cursor.moveToNext());
        }

        return tmpPRODUCT_LOCATION;
    }


    public Boolean ifExist(Integer product_id, Integer location_id) {

        ArrayList<Variation_location_details> variation_location_details = new ArrayList<>();
        String selectQuery = "SELECT  * FROM " + PRODUCT_LOCATION_TABLE_NAME + " WHERE " + PRODUCT_LOCATION_PRODUCT_ID + " = " + product_id
                + " AND " + PRODUCT_LOCATION_LOCATION_ID + " = " + location_id;
        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {
            do {
                Variation_location_details variation_location_details1 = new Variation_location_details();
                variation_location_details1.setProduct_id(cursor.getInt(1));

                variation_location_details.add(variation_location_details1);

            } while (cursor.moveToNext());

        }
        if (variation_location_details.size() > 0) {
            return true;
        } else {
            return false;
        }

        // mDb.close();
    }


    public void deleteItem(Integer product_id, Integer location_id) {
        mDb.execSQL("delete from " + PRODUCT_LOCATION_TABLE_NAME + " WHERE " + PRODUCT_LOCATION_PRODUCT_ID + " = " + product_id + " AND " + PRODUCT_LOCATION_LOCATION_ID + " = " + location_id);
    }

    public void deleteItemByProductId(Integer product_id) {
        mDb.execSQL("delete from " + PRODUCT_LOCATION_TABLE_NAME + " WHERE " + PRODUCT_LOCATION_PRODUCT_ID + " = " + product_id);
    }


}
