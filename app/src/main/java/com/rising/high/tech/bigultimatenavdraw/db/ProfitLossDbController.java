package com.rising.high.tech.bigultimatenavdraw.db;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;

import com.rising.high.tech.bigultimatenavdraw.model.ProfitLossReport;

import java.util.ArrayList;

public class ProfitLossDbController extends DBController {

    // **********   Table "CATERORY" fields ********************************************************************

    public static final String PROFITLOSS_TABLE_NAME = "profitloss";
    public static final String PROFITLOSS_ID = "id"; //int
    public static final String PROFITLOSS_opening_stock = "opening_stock";
    public static final String PROFITLOSS_closing_stock = "closing_stock";
    public static final String PROFITLOSS_opening_stock_by_sp = "opening_stock_by_sp";
    public static final String PROFITLOSS_closing_stock_by_sp = "closing_stock_by_sp";
    public static final String PROFITLOSS_total_purchase_discount = "total_purchase_discount";
    public static final String PROFITLOSS_total_sell = "total_sell";
    public static final String PROFITLOSS_total_sell_discount= "total_sell_discount";
    public static final String PROFITLOSS_total_expense= "total_expense";
    public static final String PROFITLOSS_net_profit= "net_profit";
    public static final String PROFITLOSS_gross_profit= "gross_profit";
    public static final String PROFITLOSS_total_sell_return= "total_sell_return";

    public static final String PROFITLOSS_TABLE_CREATE =
            "CREATE TABLE " + PROFITLOSS_TABLE_NAME + " (" +
                    PROFITLOSS_ID + " TEXT , " +
                    PROFITLOSS_opening_stock + " TEXT, " +
                    PROFITLOSS_closing_stock + " TEXT, " +
                    PROFITLOSS_opening_stock_by_sp + " TEXT, " +
                    PROFITLOSS_closing_stock_by_sp + " TEXT, " +
                    PROFITLOSS_total_purchase_discount + " TEXT, " +
                    PROFITLOSS_total_sell + " TEXT, " +
                    PROFITLOSS_total_sell_discount + " TEXT, " +
                    PROFITLOSS_total_expense + " TEXT, " +
                    PROFITLOSS_net_profit + " TEXT, " +
                    PROFITLOSS_gross_profit + " , " +
                    PROFITLOSS_total_sell_return + " TEXT) ;";

    public static final String PROFITLOSS_TABLE_DROP = "DROP TABLE IF EXISTS " + PROFITLOSS_TABLE_NAME + ";";

    public ProfitLossDbController(Context context) {
        super(context);
    }

    public int insert(ProfitLossReport profitLossReport) {
        // Create a new map of values, where column names are the keys
        ContentValues pValues = new ContentValues();

        pValues.put(PROFITLOSS_opening_stock, profitLossReport.getOpening_stock());
        pValues.put(PROFITLOSS_closing_stock, profitLossReport.getClosing_stock());
        pValues.put(PROFITLOSS_opening_stock_by_sp, profitLossReport.getOpening_stock_by_sp());
        pValues.put(PROFITLOSS_closing_stock_by_sp, profitLossReport.getClosing_stock_by_sp());
        pValues.put(PROFITLOSS_total_purchase_discount, profitLossReport.getTotal_purchase_discount());
        pValues.put(PROFITLOSS_total_sell, profitLossReport.getTotal_sell());
        pValues.put(PROFITLOSS_total_sell_discount, profitLossReport.getTotal_sell_discount());
        pValues.put(PROFITLOSS_total_expense, profitLossReport.getTotal_expense());
        pValues.put(PROFITLOSS_net_profit, profitLossReport.getNet_profit());
        pValues.put(PROFITLOSS_gross_profit, profitLossReport.getGross_profit());
        pValues.put(PROFITLOSS_total_sell_return, profitLossReport.getTotal_sell_return());

        int newRowId = (int) mDb.insert(PROFITLOSS_TABLE_NAME, null, pValues); //renvoie l'id de l'enregistrement créé
        return newRowId;
    }

    public void fill(ArrayList<ProfitLossReport> categories) {
        if (!categories.isEmpty()) {
            for (ProfitLossReport product : categories) {
                this.insert(product);
            }
        }
    }

    public void deleteAll() {
        mDb.execSQL("delete from " + PROFITLOSS_TABLE_NAME);
    }

    public ProfitLossReport getAllProfitLossReport() {
        ProfitLossReport tmpProfitLossReport = new ProfitLossReport();
        String selectQuery = "SELECT  * FROM " + PROFITLOSS_TABLE_NAME;
        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {
           // do {
            tmpProfitLossReport.setOpening_stock(cursor.getString(1));
            tmpProfitLossReport.setClosing_stock(cursor.getString(2));
            tmpProfitLossReport.setOpening_stock_by_sp(cursor.getString(3));
            tmpProfitLossReport.setClosing_stock_by_sp(cursor.getString(4));
            tmpProfitLossReport.setTotal_purchase_discount(cursor.getString(5));
            tmpProfitLossReport.setTotal_sell(cursor.getString(6));
            tmpProfitLossReport.setTotal_sell_discount(cursor.getString(7));
            tmpProfitLossReport.setTotal_expense(cursor.getString(8));
            tmpProfitLossReport.setNet_profit(cursor.getString(9));
            tmpProfitLossReport.setGross_profit(cursor.getString(10));
            tmpProfitLossReport.setTotal_sell_return(cursor.getString(11));

             //   tmpProfitLossReport.add(profitLossReport);

          //  } while (cursor.moveToNext());
        }


        return tmpProfitLossReport;

    }

//    public ArrayList<ProfitLossReport> getAllProfitLossReportSpinner() {
//        ArrayList<ProfitLossReport> tmpProfitLossReport = new ArrayList<>();
//        String selectQuery = "SELECT  * FROM " + PROFITLOSS_TABLE_NAME;
//        Cursor cursor = mDb.rawQuery(selectQuery, null);
//        ProfitLossReport xprofitLossReport = new ProfitLossReport(0, "Tout");
//        tmpProfitLossReport.add(xprofitLossReport);
//        if (cursor.moveToFirst()) {
//            do {
//                ProfitLossReport profitLossReport = new ProfitLossReport();
//                profitLossReport.setId(Integer.parseInt(cursor.getString(0)));
//                profitLossReport.setName(cursor.getString(1));
//                profitLossReport.setBusiness_id(Integer.parseInt(cursor.getString(2)));
//                profitLossReport.setParent_id(cursor.getInt(3));
//                profitLossReport.setCreated_by(cursor.getInt(4));
//                profitLossReport.setProfitLossReport_type(cursor.getString(5));
//                profitLossReport.setDescription(cursor.getString(6));
//                profitLossReport.setSlug(cursor.getString(7));
//                profitLossReport.setShort_code(cursor.getString(8));
//
//                tmpProfitLossReport.add(profitLossReport);
//
//            } while (cursor.moveToNext());
//        }
//
//
//        return tmpProfitLossReport;
//
//    }
}
