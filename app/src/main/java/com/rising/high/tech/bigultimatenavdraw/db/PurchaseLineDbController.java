package com.rising.high.tech.bigultimatenavdraw.db;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;

import com.rising.high.tech.bigultimatenavdraw.model.Purchase_line;
import com.rising.high.tech.bigultimatenavdraw.model.Transaction;
import com.rising.high.tech.bigultimatenavdraw.model.Variation;

import java.util.ArrayList;

public class PurchaseLineDbController extends DBController {

    // **********   Table "CATERORY" fields ********************************************************************

    private ProductDbController productDbController;
    private VariationsDbController variationsDbController;
    public static final String PURCHASE_LINE_TABLE_NAME = "purchase_lines";

    public static final String PURCHASE_LINE_ID = "id"; //int
    public static final String PURCHASE_LINE_TRANSACTION_ID = "transaction_id";
    public static final String PURCHASE_LINE_PRODUCT_ID = "product_id";
    public static final String PURCHASE_LINE_QUANTITY = "quantity";
    public static final String PURCHASE_LINE_PP_WITHOUT_DISCOUNT = "pp_without_discount";
    public static final String PURCHASE_LINE_PURCHASE_PRICE = "purchase_price";
    public static final String PURCHASE_LINE_PURCHASE_LINE_PRICE_INC_TAX = "purchase_price_inc_tax";
    public static final String PURCHASE_LINE_ITEM_TAX = "item_tax";
    public static final String PURCHASE_LINE_VARIATION_ID = "variation_id";
    public static final String PURCHASE_LINE_TAX_ID = "tax_id";
    public static final String PURCHASE_LINE_QUANTITY_SOLD = "quantity_sold";
    public static final String PURCHASE_LINE_QUANTITY_ADJUSTED = "quantity_adjusted";
    public static final String PURCHASE_LINE_QUANTITY_RETURNED = "quantity_returned";

    public static final String PURCHASE_LINE_TABLE_CREATE =
            "CREATE TABLE " + PURCHASE_LINE_TABLE_NAME + " (" +
                    PURCHASE_LINE_ID + " INTEGER PRIMARY KEY AUTOINCREMENT , " +
                    PURCHASE_LINE_TRANSACTION_ID + " INTEGER, " +
                    PURCHASE_LINE_PRODUCT_ID + " INTEGER, " +
                    PURCHASE_LINE_QUANTITY + " INTEGER, " +
                    PURCHASE_LINE_PP_WITHOUT_DISCOUNT + " TEXT, " +
                    PURCHASE_LINE_PURCHASE_PRICE + " TEXT, " +
                    PURCHASE_LINE_PURCHASE_LINE_PRICE_INC_TAX + " TEXT, " +
                    PURCHASE_LINE_ITEM_TAX + " TEXT, " +
                    PURCHASE_LINE_VARIATION_ID + " INTEGER, " +
                    PURCHASE_LINE_TAX_ID + " INTEGER, " +
                    PURCHASE_LINE_QUANTITY_SOLD + " INTEGER, " +
                    PURCHASE_LINE_QUANTITY_ADJUSTED + " INTEGER, " +
                    PURCHASE_LINE_QUANTITY_RETURNED + " INTEGER) ;";

    public static final String PURCHASE_LINE_TABLE_DROP = "DROP TABLE IF EXISTS " + PURCHASE_LINE_TABLE_NAME + ";";

    public PurchaseLineDbController(Context context) {
        super(context);
        productDbController = new ProductDbController(context);
        productDbController.open();

        variationsDbController = new VariationsDbController(context);
        variationsDbController.open();
    }

    public int insert(Purchase_line sell_lines) {
        // Create a new map of values, where column names are the keys
        ContentValues pValues = new ContentValues();

        pValues.put(PURCHASE_LINE_ID, sell_lines.getId());
        pValues.put(PURCHASE_LINE_TRANSACTION_ID, sell_lines.getTransaction_id());
        pValues.put(PURCHASE_LINE_PRODUCT_ID, sell_lines.getProduct_id());
        pValues.put(PURCHASE_LINE_QUANTITY, sell_lines.getQuantity());
        pValues.put(PURCHASE_LINE_PP_WITHOUT_DISCOUNT, sell_lines.getPp_without_discount());
        pValues.put(PURCHASE_LINE_PURCHASE_PRICE, sell_lines.getPurchase_price());
        pValues.put(PURCHASE_LINE_PURCHASE_LINE_PRICE_INC_TAX, sell_lines.getPurchase_price_inc_tax());
        pValues.put(PURCHASE_LINE_ITEM_TAX, sell_lines.getItem_tax());
        pValues.put(PURCHASE_LINE_VARIATION_ID, sell_lines.getVariation_id());
        pValues.put(PURCHASE_LINE_TAX_ID, sell_lines.getTax_id());
        pValues.put(PURCHASE_LINE_QUANTITY_SOLD, sell_lines.getQuantity_sold());
        pValues.put(PURCHASE_LINE_QUANTITY_ADJUSTED, sell_lines.getQuantity_adjusted());
        pValues.put(PURCHASE_LINE_QUANTITY_RETURNED, sell_lines.getQuantity_returned());

        int newRowId = (int) mDb.insert(PURCHASE_LINE_TABLE_NAME, null, pValues); //renvoie l'id de l'enregistrement créé
        return newRowId;
    }

    public int insertLocal(Purchase_line sell_lines) {
        // Create a new map of values, where column names are the keys
        ContentValues pValues = new ContentValues();

        pValues.put(PURCHASE_LINE_TRANSACTION_ID, sell_lines.getTransaction_id());
        pValues.put(PURCHASE_LINE_PRODUCT_ID, sell_lines.getProduct_id());
        pValues.put(PURCHASE_LINE_QUANTITY, sell_lines.getQuantity());
        pValues.put(PURCHASE_LINE_PP_WITHOUT_DISCOUNT, sell_lines.getPp_without_discount());
        pValues.put(PURCHASE_LINE_PURCHASE_PRICE, sell_lines.getPurchase_price());
        pValues.put(PURCHASE_LINE_PURCHASE_LINE_PRICE_INC_TAX, sell_lines.getPurchase_price_inc_tax());
        pValues.put(PURCHASE_LINE_ITEM_TAX, sell_lines.getItem_tax());
        pValues.put(PURCHASE_LINE_VARIATION_ID, sell_lines.getVariation_id());
        pValues.put(PURCHASE_LINE_TAX_ID, sell_lines.getTax_id());
        pValues.put(PURCHASE_LINE_QUANTITY_SOLD, sell_lines.getQuantity_sold());
        pValues.put(PURCHASE_LINE_QUANTITY_ADJUSTED, sell_lines.getQuantity_adjusted());
        pValues.put(PURCHASE_LINE_QUANTITY_RETURNED, sell_lines.getQuantity_returned());

        int newRowId = (int) mDb.insert(PURCHASE_LINE_TABLE_NAME, null, pValues); //renvoie l'id de l'enregistrement créé
        return newRowId;
    }

    public void fill(ArrayList<Purchase_line> sell_lines) {
        if (!sell_lines.isEmpty()) {
            for (Purchase_line sell_lines1 : sell_lines) {
                this.insert(sell_lines1);
            }
        }
    }

    public void fillLocal(ArrayList<Purchase_line> sell_lines) {
        if (!sell_lines.isEmpty()) {
            for (Purchase_line sell_lines1 : sell_lines) {
                this.insertLocal(sell_lines1);
            }
        }
    }

    public void deleteAll() {
        mDb.execSQL("delete from " + PURCHASE_LINE_TABLE_NAME);
    }

    public void deletePurchase(int id) {
        mDb.execSQL("delete from " + PURCHASE_LINE_TABLE_NAME + " WHERE " + PURCHASE_LINE_ID + " = " + id);
    }

    public void deletePurchaseByTransaction(int transaction_id) {
        mDb.execSQL("delete from " + PURCHASE_LINE_TABLE_NAME + " WHERE " + PURCHASE_LINE_TRANSACTION_ID + " = " + transaction_id);
    }


    public int updatePurchaseLine(Purchase_line purchase_line) {
        // Create a new map of values, where column names are the keys
        ContentValues pValues = new ContentValues();


        pValues.put(PURCHASE_LINE_TRANSACTION_ID, purchase_line.getTransaction_id());
        pValues.put(PURCHASE_LINE_PRODUCT_ID, purchase_line.getProduct_id());
        pValues.put(PURCHASE_LINE_QUANTITY, purchase_line.getQuantity());
        pValues.put(PURCHASE_LINE_PP_WITHOUT_DISCOUNT, purchase_line.getPp_without_discount());
        pValues.put(PURCHASE_LINE_PURCHASE_PRICE, purchase_line.getPurchase_price());
        pValues.put(PURCHASE_LINE_PURCHASE_LINE_PRICE_INC_TAX, purchase_line.getPurchase_price_inc_tax());
        pValues.put(PURCHASE_LINE_ITEM_TAX, purchase_line.getItem_tax());
        pValues.put(PURCHASE_LINE_VARIATION_ID, purchase_line.getVariation_id());
        pValues.put(PURCHASE_LINE_TAX_ID, purchase_line.getTax_id());
        pValues.put(PURCHASE_LINE_QUANTITY_SOLD, purchase_line.getQuantity_sold());
        pValues.put(PURCHASE_LINE_QUANTITY_ADJUSTED, purchase_line.getQuantity_adjusted());
        pValues.put(PURCHASE_LINE_QUANTITY_RETURNED, purchase_line.getQuantity_returned());

        int newRowId = mDb.update(PURCHASE_LINE_TABLE_NAME, pValues, PURCHASE_LINE_ID + " = '" + purchase_line.getId() + "'", null); //renvoie l'id de l'enregistrement créé

        return newRowId;
    }



    public ArrayList<Purchase_line> getAllPurchaseLine() {
        ArrayList<Purchase_line> purchaseLines = new ArrayList<>();
        String selectQuery = "SELECT  * FROM " + PURCHASE_LINE_TABLE_NAME;
        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {
            do {
                Purchase_line purchaseLine = new Purchase_line();

                purchaseLine.setId(cursor.getInt(0));
                purchaseLine.setTransaction_id(cursor.getInt(1));
                purchaseLine.setProduct_id(cursor.getInt(2));
                purchaseLine.setQuantity(cursor.getInt(3));
                purchaseLine.setPp_without_discount(cursor.getString(4));
                purchaseLine.setPurchase_price(cursor.getString(5));
                purchaseLine.setPurchase_price_inc_tax(cursor.getString(6));
                purchaseLine.setItem_tax(cursor.getString(7));
                purchaseLine.setVariation_id(cursor.getInt(8));
                purchaseLine.setTax_id(cursor.getInt(9));
                purchaseLine.setQuantity_sold(cursor.getInt(10));
                purchaseLine.setQuantity_adjusted(cursor.getInt(11));
                purchaseLine.setQuantity_returned(cursor.getInt(12));


                purchaseLines.add(purchaseLine);


            } while (cursor.moveToNext());
        }

        return purchaseLines;
    }

    public ArrayList<Purchase_line> getPurchaseLineByTransaction(int transaction_id) {
        ArrayList<Purchase_line> purchaseLines = new ArrayList<>();
        String selectQuery = "SELECT  * FROM " + PURCHASE_LINE_TABLE_NAME + " WHERE " + PURCHASE_LINE_TRANSACTION_ID + " = " + transaction_id;
        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {
            do {
                Purchase_line purchaseLine = new Purchase_line();

                purchaseLine.setId(cursor.getInt(0));
                purchaseLine.setTransaction_id(cursor.getInt(1));
                purchaseLine.setProduct_id(cursor.getInt(2));
                purchaseLine.setQuantity(cursor.getInt(3));
                purchaseLine.setPp_without_discount(cursor.getString(4));
                purchaseLine.setPurchase_price(cursor.getString(5));
                purchaseLine.setPurchase_price_inc_tax(cursor.getString(6));
                purchaseLine.setItem_tax(cursor.getString(7));
                purchaseLine.setVariation_id(cursor.getInt(8));
                purchaseLine.setTax_id(cursor.getInt(9));
                purchaseLine.setQuantity_sold(cursor.getInt(10));
                purchaseLine.setQuantity_adjusted(cursor.getInt(11));
                purchaseLine.setQuantity_returned(cursor.getInt(12));
                purchaseLine.setProduct_name(productDbController.getProductById(cursor.getInt(2)).getName());
                Variation variation = variationsDbController.getVariationByProductId(cursor.getInt(2));
                purchaseLine.setSellingPrice(variation.getDefault_sell_price());
                purchaseLines.add(purchaseLine);

            } while (cursor.moveToNext());
        }

        return purchaseLines;
    }


    public Purchase_line getPurchaseLineTransaction(int transaction_id) {
        Purchase_line purchaseLine = new Purchase_line();

        String selectQuery = "SELECT  * FROM " + PURCHASE_LINE_TABLE_NAME + " WHERE " + PURCHASE_LINE_TRANSACTION_ID + " = " + transaction_id;
        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {

                purchaseLine.setId(cursor.getInt(0));
                purchaseLine.setTransaction_id(cursor.getInt(1));
                purchaseLine.setProduct_id(cursor.getInt(2));
                purchaseLine.setQuantity(cursor.getInt(3));
                purchaseLine.setPp_without_discount(cursor.getString(4));
                purchaseLine.setPurchase_price(cursor.getString(5));
                purchaseLine.setPurchase_price_inc_tax(cursor.getString(6));
                purchaseLine.setItem_tax(cursor.getString(7));
                purchaseLine.setVariation_id(cursor.getInt(8));
                purchaseLine.setTax_id(cursor.getInt(9));
                purchaseLine.setQuantity_sold(cursor.getInt(10));
                purchaseLine.setQuantity_adjusted(cursor.getInt(11));
                purchaseLine.setQuantity_returned(cursor.getInt(12));
                purchaseLine.setProduct_name(productDbController.getProductById(cursor.getInt(2)).getName());
                Variation variation = variationsDbController.getVariationByProductId(cursor.getInt(2));
                purchaseLine.setSellingPrice(variation.getDefault_sell_price());

        }

        return purchaseLine;
    }
    public Purchase_line getPurchaseLineByVariationProduct(int variation_id,int product_id) {
        Purchase_line purchaseLine = new Purchase_line();

        String selectQuery = "SELECT  * FROM " + PURCHASE_LINE_TABLE_NAME + " WHERE " + PURCHASE_LINE_VARIATION_ID + " = " + variation_id + " AND " + PURCHASE_LINE_PRODUCT_ID+ " = " + product_id;
        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {

            purchaseLine.setId(cursor.getInt(0));
            purchaseLine.setTransaction_id(cursor.getInt(1));
            purchaseLine.setProduct_id(cursor.getInt(2));
            purchaseLine.setQuantity(cursor.getInt(3));
            purchaseLine.setPp_without_discount(cursor.getString(4));
            purchaseLine.setPurchase_price(cursor.getString(5));
            purchaseLine.setPurchase_price_inc_tax(cursor.getString(6));
            purchaseLine.setItem_tax(cursor.getString(7));
            purchaseLine.setVariation_id(cursor.getInt(8));
            purchaseLine.setTax_id(cursor.getInt(9));
            purchaseLine.setQuantity_sold(cursor.getInt(10));
            purchaseLine.setQuantity_adjusted(cursor.getInt(11));
            purchaseLine.setQuantity_returned(cursor.getInt(12));
            purchaseLine.setProduct_name(productDbController.getProductById(cursor.getInt(2)).getName());
            Variation variation = variationsDbController.getVariationByProductId(cursor.getInt(2));
            purchaseLine.setSellingPrice(variation.getDefault_sell_price());

        }

        return purchaseLine;
    }


}
