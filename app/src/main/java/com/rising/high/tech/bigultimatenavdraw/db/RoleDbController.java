package com.rising.high.tech.bigultimatenavdraw.db;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;

import com.rising.high.tech.bigultimatenavdraw.model.Role;
import com.rising.high.tech.bigultimatenavdraw.model.Role;

import java.util.ArrayList;

public class RoleDbController extends DBController {

    public static final String ROLE_TABLE_NAME = "roles";

    public static final String ROLE_ID = "id"; //int
    public static final String ROLE_NAME = "name";
    public static final String ROLE_GUARD_NAME = "guard_name";
    public static final String ROLE_BUSINESS_ID = "business_id";
    public static final String ROLE_IS_DEFAULT = "is_default";
    public static final String ROLE_IS_SERVICE_STAFF= "is_service_staff";

    public static final String ROLE_TABLE_CREATE =
            "CREATE TABLE " + ROLE_TABLE_NAME + " (" +
                    ROLE_ID + " INTEGER PRIMARY KEY AUTOINCREMENT , " +
                    ROLE_NAME + " TEXT , " +
                    R<PERSON>E_GUARD_NAME + " TEXT , " +
                    ROLE_BUSINESS_ID + " INTEGER , " +
                    ROLE_IS_DEFAULT + " INTEGER , " +
                    ROLE_IS_SERVICE_STAFF + " INTEGER ) ;";
    public static final String ROLE_TABLE_DROP = "DROP TABLE IF EXISTS " + ROLE_TABLE_NAME + ";";

    public RoleDbController(Context context) {
        super(context);
    }

    public int insert(Role role) {
        // Create a new map of values, where column names are the keys
        ContentValues pValues = new ContentValues();

        pValues.put(ROLE_ID, role.getId());
        pValues.put(ROLE_NAME, role.getName());
        pValues.put(ROLE_GUARD_NAME, role.getGuard_name());
        pValues.put(ROLE_BUSINESS_ID, role.getBusiness_id());
        pValues.put(ROLE_IS_DEFAULT, role.getIs_default());
        pValues.put(ROLE_IS_SERVICE_STAFF, role.getIs_service_staff());

        return (int) mDb.insert(ROLE_TABLE_NAME, null, pValues);
    }

    public int insertLocal(Role role) {
        // Create a new map of values, where column names are the keys
        ContentValues pValues = new ContentValues();
        pValues.put(ROLE_NAME, role.getName());
        pValues.put(ROLE_GUARD_NAME, role.getGuard_name());
        pValues.put(ROLE_BUSINESS_ID, role.getBusiness_id());
        pValues.put(ROLE_IS_DEFAULT, role.getIs_default());
        pValues.put(ROLE_IS_SERVICE_STAFF, role.getIs_service_staff());

        return (int) mDb.insert(ROLE_TABLE_NAME, null, pValues);
    }
    public int updateLocal(Role role) {
        ContentValues pValues = new ContentValues();
        pValues.put(ROLE_NAME, role.getName());
        pValues.put(ROLE_GUARD_NAME, role.getGuard_name());
        pValues.put(ROLE_BUSINESS_ID, role.getBusiness_id());
        pValues.put(ROLE_IS_DEFAULT, role.getIs_default());
        pValues.put(ROLE_IS_SERVICE_STAFF, role.getIs_service_staff());

        return mDb.update(ROLE_TABLE_NAME, pValues, ROLE_ID + " = '" + role.getId() + "'", null);

    }
    public void deleteItem(Integer id) {
        mDb.execSQL("delete from " + ROLE_TABLE_NAME  + " WHERE " + ROLE_ID + " = '" + id + "'");
    }
    public ArrayList<Role> getAllRoles() {
        ArrayList<Role> tempCompany = new ArrayList<>();
        String selectQuery = "SELECT  * FROM " + ROLE_TABLE_NAME;
        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {
            do {

                Role role = new Role();
                role.setId(cursor.getInt(0));
                role.setName(cursor.getString(1));
                role.setBusiness_id(cursor.getInt(2));
                role.setIs_default(cursor.getInt(3));
                role.setIs_service_staff(cursor.getInt(4));
                tempCompany.add(role);

            } while (cursor.moveToNext());

        }
        return tempCompany;
    }

    public Role getRoleById(Integer role_id) {
        Role role = new Role();
        String selectQuery = "SELECT  * FROM " + ROLE_TABLE_NAME + " WHERE " + ROLE_ID +" = " + role_id;
        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {
            do {

                role.setId(cursor.getInt(0));
                role.setName(cursor.getString(1));
                role.setBusiness_id(cursor.getInt(2));
                role.setIs_default(cursor.getInt(3));
                role.setIs_service_staff(cursor.getInt(4));

            } while (cursor.moveToNext());

        }
        return role;
    }

    public ArrayList<Role> getRolesLike(String name) {
        ArrayList<Role> tempCompany = new ArrayList<>();
        String selectQuery = "SELECT  * FROM " + ROLE_TABLE_NAME + " WHERE " + ROLE_NAME + " LIKE '%" + name +  "%' ";

        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {
            do {

                Role role = new Role();
                role.setId(cursor.getInt(0));
                role.setName(cursor.getString(1));
                role.setBusiness_id(cursor.getInt(2));
                role.setIs_default(cursor.getInt(3));
                role.setIs_service_staff(cursor.getInt(4));
                tempCompany.add(role);

            } while (cursor.moveToNext());

        }
        return tempCompany;
    }


    // Insert all product
    public void fill(ArrayList<Role> products) {
        if (!products.isEmpty()) {
            for (Role product : products) {
                this.insert(product);
            }
        }
        //   mDb.close();
    }

    public void clear() {
        mDb.execSQL("DELETE FROM " + ROLE_TABLE_NAME);
    }

}
