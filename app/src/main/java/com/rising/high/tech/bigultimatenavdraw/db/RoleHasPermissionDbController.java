package com.rising.high.tech.bigultimatenavdraw.db;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;

import com.rising.high.tech.bigultimatenavdraw.model.Permission;
import com.rising.high.tech.bigultimatenavdraw.model.Role;

import java.util.ArrayList;

public class RoleHasPermissionDbController extends DBController {

    public static final String ROLE_HAS_PERMISSION_TABLE_NAME = "roles_has_permission";

    public static final String ROLE_HAS_PERMISSION_ID = "permission_id"; //int
    public static final String ROLE_HAS_PERMISSION_ROLE_ID = "role_id";

    public static final String ROLE_HAS_PERMISSION_TABLE_CREATE =
            "CREATE TABLE " + ROLE_HAS_PERMISSION_TABLE_NAME + " (" +
                    ROLE_HAS_PERMISSION_ID + " INTEGER , " +
                    ROLE_HAS_PERMISSION_ROLE_ID + " INTEGER ) ;";
    public static final String ROLE_HAS_PERMISSION_TABLE_DROP = "DROP TABLE IF EXISTS " + ROLE_HAS_PERMISSION_TABLE_NAME + ";";

    public RoleHasPermissionDbController(Context context) {
        super(context);
    }

    public int insert(Role role) {
        // Create a new map of values, where column names are the keys
        ContentValues pValues = new ContentValues();

        pValues.put(ROLE_HAS_PERMISSION_ID, role.getId());
        pValues.put(ROLE_HAS_PERMISSION_ROLE_ID, role.getName());

        return (int) mDb.insert(ROLE_HAS_PERMISSION_TABLE_NAME, null, pValues);
    }

    public int insertLocal(Role role) {
        // Create a new map of values, where column names are the keys
        ContentValues pValues = new ContentValues();
        pValues.put(ROLE_HAS_PERMISSION_ID, role.getPermission_id());
        pValues.put(ROLE_HAS_PERMISSION_ROLE_ID, role.getRole_id());


        return (int) mDb.insert(ROLE_HAS_PERMISSION_TABLE_NAME, null, pValues);
    }
    public int updateLocal(Role role) {
        ContentValues pValues = new ContentValues();
        pValues.put(ROLE_HAS_PERMISSION_ID, role.getPermission_id());
        pValues.put(ROLE_HAS_PERMISSION_ROLE_ID, role.getRole_id());

        return mDb.update(ROLE_HAS_PERMISSION_TABLE_NAME, pValues, ROLE_HAS_PERMISSION_ID + " = '" + role.getId() + "'", null);

    }
    public void deleteItem(Integer id) {
        mDb.execSQL("delete from " + ROLE_HAS_PERMISSION_TABLE_NAME  + " WHERE " + ROLE_HAS_PERMISSION_ID + " = '" + id + "'");
    }
    public ArrayList<Role> getAllRoles() {
        ArrayList<Role> tempCompany = new ArrayList<>();
        String selectQuery = "SELECT  * FROM " + ROLE_HAS_PERMISSION_TABLE_NAME;
        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {
            do {

                Role role = new Role();
                role.setPermission_id(cursor.getInt(0));
                role.setRole_id(cursor.getInt(1));
                tempCompany.add(role);

            } while (cursor.moveToNext());

        }
        return tempCompany;
    }


    public ArrayList<Integer> getRolesPermissionById(int role_id) {
        ArrayList<Integer> tempCompany = new ArrayList<>();
        String selectQuery = "SELECT  * FROM " + ROLE_HAS_PERMISSION_TABLE_NAME + " WHERE " + ROLE_HAS_PERMISSION_ROLE_ID + " = " + role_id;
        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {
            do {

                tempCompany.add(cursor.getInt(0));

            } while (cursor.moveToNext());

        }
        return tempCompany;
    }

    public boolean checkModulePermission(ArrayList<Permission> roles, Integer role_id) {
        ArrayList<Role> tempCompany = new ArrayList<>();
        String selectQuery = "SELECT  * FROM " + ROLE_HAS_PERMISSION_TABLE_NAME + " WHERE " + ROLE_HAS_PERMISSION_ROLE_ID + " ";
        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {
            do {

                Role role = new Role();
                role.setPermission_id(cursor.getInt(0));
                role.setRole_id(cursor.getInt(1));
                tempCompany.add(role);

            } while (cursor.moveToNext());

        }
        return true;
    }


    // Insert all product
    public void fill(ArrayList<Role> products) {
        if (!products.isEmpty()) {
            for (Role product : products) {
                this.insertLocal(product);
            }
        }
        //   mDb.close();
    }
    // Insert all product
    public void updateAll(ArrayList<Role> products) {
        mDb.execSQL("DELETE FROM " + ROLE_HAS_PERMISSION_TABLE_NAME + " WHERE " + ROLE_HAS_PERMISSION_ROLE_ID +" = " + products.get(0).getRole_id());
        if (!products.isEmpty()) {
            for (Role product : products) {
                this.insertLocal(product);
            }
        }
        //   mDb.close();
    }

    public void clear() {
        mDb.execSQL("DELETE FROM " + ROLE_HAS_PERMISSION_TABLE_NAME);
    }

}
