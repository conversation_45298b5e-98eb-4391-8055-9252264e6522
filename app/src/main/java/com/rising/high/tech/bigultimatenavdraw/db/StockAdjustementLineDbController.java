package com.rising.high.tech.bigultimatenavdraw.db;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.util.Log;

import com.rising.high.tech.bigultimatenavdraw.model.Sell_lines;

import java.util.ArrayList;

import static com.rising.high.tech.bigultimatenavdraw.db.TransactionDbController.TRANSACTION_ID;
import static com.rising.high.tech.bigultimatenavdraw.db.TransactionDbController.TRANSACTION_LOCATION_ID;
import static com.rising.high.tech.bigultimatenavdraw.db.TransactionDbController.TRANSACTION_STATUS;
import static com.rising.high.tech.bigultimatenavdraw.db.TransactionDbController.TRANSACTION_TABLE_NAME;
import static com.rising.high.tech.bigultimatenavdraw.db.TransactionDbController.TRANSACTION_TYPE;

public class StockAdjustementLineDbController extends DBController {
    private static final String TAG = "StockAdjustementLineDbC";
    // **********   Table "CATERORY" fields ********************************************************************

    public static final String STOCK_ADJUSTMENT_LINE_TABLE_NAME = "stock_adjustment_line";

    public static final String STOCK_ADJUSTMENT_LINE_ID = "id"; //int
    public static final String STOCK_ADJUSTMENT_LINE_TRANSACTION_ID = "transaction_id";
    public static final String STOCK_ADJUSTMENT_LINE_PRODUCT_ID = "product_id";
    public static final String STOCK_ADJUSTMENT_LINE_VARIATION_ID = "variation_id";
    public static final String STOCK_ADJUSTMENT_LINE_QUANTITY = "quantity";
    public static final String STOCK_ADJUSTMENT_UNIT_PRICE = "unit_price";
    public static final String STOCK_ADJUSTMENT_REMOVED_PURCHSE_LINE = "removed_purchase_line";
    public static final String STOCK_ADJUSTMENT_LOT_NO_LINE_ID = "lot_no_line_id";

    public static final String STOCK_ADJUSTMENT_LINE_TABLE_CREATE =
            "CREATE TABLE " + STOCK_ADJUSTMENT_LINE_TABLE_NAME + " (" +
                    STOCK_ADJUSTMENT_LINE_ID + " INTEGER PRIMARY KEY AUTOINCREMENT , " +
                    STOCK_ADJUSTMENT_LINE_TRANSACTION_ID + " INTEGER, " +
                    STOCK_ADJUSTMENT_LINE_PRODUCT_ID + " INTEGER, " +
                    STOCK_ADJUSTMENT_LINE_VARIATION_ID + " INTEGER, " +
                    STOCK_ADJUSTMENT_LINE_QUANTITY + " INTEGER, " +
                    STOCK_ADJUSTMENT_UNIT_PRICE + " TEXT, " +
                    STOCK_ADJUSTMENT_REMOVED_PURCHSE_LINE + " INTEGER, " +
                    STOCK_ADJUSTMENT_LOT_NO_LINE_ID + " INTEGER) ;";

    public static final String STOCK_ADJUSTMENT_LINE_TABLE_DROP = "DROP TABLE IF EXISTS " + STOCK_ADJUSTMENT_LINE_TABLE_NAME + ";";

    public StockAdjustementLineDbController(Context context) {
        super(context);
    }

    public int insert(Sell_lines sell_lines) {
        // Create a new map of values, where column names are the keys
        ContentValues pValues = new ContentValues();

        pValues.put(STOCK_ADJUSTMENT_LINE_ID, sell_lines.getId());
        pValues.put(STOCK_ADJUSTMENT_LINE_TRANSACTION_ID, sell_lines.getTransaction_id());
        pValues.put(STOCK_ADJUSTMENT_LINE_PRODUCT_ID, sell_lines.getProduct_id());
        pValues.put(STOCK_ADJUSTMENT_LINE_VARIATION_ID, sell_lines.getVariation_id());
        pValues.put(STOCK_ADJUSTMENT_LINE_QUANTITY, sell_lines.getQuantity());
        pValues.put(STOCK_ADJUSTMENT_UNIT_PRICE, sell_lines.getUnit_price());
        pValues.put(STOCK_ADJUSTMENT_REMOVED_PURCHSE_LINE, sell_lines.getRemoved_purchase_line());
        pValues.put(STOCK_ADJUSTMENT_LOT_NO_LINE_ID, sell_lines.getQty_returned());

        int newRowId = (int) mDb.insert(STOCK_ADJUSTMENT_LINE_TABLE_NAME, null, pValues); //renvoie l'id de l'enregistrement créé
        return newRowId;
    }


    public int insertLocal(Sell_lines sell_lines) {
        // Create a new map of values, where column names are the keys
        ContentValues pValues = new ContentValues();

        pValues.put(STOCK_ADJUSTMENT_LINE_TRANSACTION_ID, sell_lines.getTransaction_id());
        pValues.put(STOCK_ADJUSTMENT_LINE_PRODUCT_ID, sell_lines.getProduct_id());
        pValues.put(STOCK_ADJUSTMENT_LINE_VARIATION_ID, sell_lines.getVariation_id());
        pValues.put(STOCK_ADJUSTMENT_LINE_QUANTITY, sell_lines.getQuantity());
        pValues.put(STOCK_ADJUSTMENT_UNIT_PRICE, sell_lines.getUnit_price());
        pValues.put(STOCK_ADJUSTMENT_REMOVED_PURCHSE_LINE, sell_lines.getRemoved_purchase_line());
        pValues.put(STOCK_ADJUSTMENT_LOT_NO_LINE_ID, sell_lines.getQty_returned());

        int newRowId = (int) mDb.insert(STOCK_ADJUSTMENT_LINE_TABLE_NAME, null, pValues); //renvoie l'id de l'enregistrement créé
        return newRowId;
    }

    public void fill(ArrayList<Sell_lines> sell_lines) {
        if (!sell_lines.isEmpty()) {
            for (Sell_lines sell_lines1 : sell_lines) {
                this.insert(sell_lines1);
            }
        }
    }

    public void fillLocal(ArrayList<Sell_lines> sell_lines) {
        if (!sell_lines.isEmpty()) {
            for (Sell_lines sell_lines1 : sell_lines) {
                this.insertLocal(sell_lines1);
            }
        }
    }

    public void deleteAll() {
        mDb.execSQL("delete from " + STOCK_ADJUSTMENT_LINE_TABLE_NAME);
    }


    public void deleteRow(int id) {
        mDb.execSQL("delete from " + STOCK_ADJUSTMENT_LINE_TABLE_NAME + " WHERE " + STOCK_ADJUSTMENT_LINE_ID + " = " + id);
    }


    public ArrayList<Sell_lines> getStockAdjustment() {
        ArrayList<Sell_lines> sell_lines = new ArrayList<>();
        String selectQuery = "SELECT  * FROM " + STOCK_ADJUSTMENT_LINE_TABLE_NAME;
        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {
            do {
                Sell_lines sell_lines1 = new Sell_lines();

                sell_lines1.setId(cursor.getInt(0));
                sell_lines1.setTransaction_id(cursor.getInt(1));
                sell_lines1.setProduct_id(cursor.getInt(2));
                sell_lines1.setVariation_id(cursor.getInt(3));
                sell_lines1.setQuantity(cursor.getInt(4));
                sell_lines1.setUnit_price(cursor.getString(5));
                sell_lines1.setRemoved_purchase_line(cursor.getInt(6));
                sell_lines1.setQty_returned(cursor.getInt(7));

                sell_lines.add(sell_lines1);

            } while (cursor.moveToNext());
        }

        return sell_lines;
    }


    public ArrayList<Sell_lines> getStockAdjustmentByTransaction(Integer id_transaction) {
        ArrayList<Sell_lines> sell_lines = new ArrayList<>();
        String selectQuery = "SELECT  * FROM " + STOCK_ADJUSTMENT_LINE_TABLE_NAME + " WHERE " + STOCK_ADJUSTMENT_LINE_TRANSACTION_ID + " = " + id_transaction;

        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {
            do {
                Sell_lines sell_lines1 = new Sell_lines();

                sell_lines1.setId(cursor.getInt(0));
                sell_lines1.setTransaction_id(cursor.getInt(1));
                sell_lines1.setProduct_id(cursor.getInt(2));
                sell_lines1.setVariation_id(cursor.getInt(3));
                sell_lines1.setQuantity(cursor.getInt(4));
                sell_lines1.setUnit_price(cursor.getString(5));
                sell_lines1.setRemoved_purchase_line(cursor.getInt(6));
                sell_lines1.setQty_returned(cursor.getInt(7));

                sell_lines.add(sell_lines1);

            } while (cursor.moveToNext());
        }

        return sell_lines;
    }

    public String getTotalAdjusted(Integer id_product, Integer id_location) {
        String sell_lines = "0";
        String selectQuery = "SELECT SUM(" + STOCK_ADJUSTMENT_LINE_QUANTITY + ") FROM " + STOCK_ADJUSTMENT_LINE_TABLE_NAME + " INNER JOIN "
                + TRANSACTION_TABLE_NAME + " ON " + STOCK_ADJUSTMENT_LINE_TABLE_NAME + "." + STOCK_ADJUSTMENT_LINE_TRANSACTION_ID + " = " + TRANSACTION_TABLE_NAME + "." + TRANSACTION_ID
                + " WHERE " + TRANSACTION_TABLE_NAME + "." + TRANSACTION_TYPE + " = " + "'stock_adjustment'"
                + " AND " + STOCK_ADJUSTMENT_LINE_TABLE_NAME + "." + STOCK_ADJUSTMENT_LINE_PRODUCT_ID + " = " + id_product
                + " AND " + TRANSACTION_TABLE_NAME + "." + TRANSACTION_LOCATION_ID + " = " + id_location;


        Cursor cursor = mDb.rawQuery(selectQuery, null);
        if (cursor.moveToFirst()) {
            sell_lines = cursor.getInt(0) + "";
        }
        return sell_lines;
    }


}
