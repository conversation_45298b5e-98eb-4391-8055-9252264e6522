package com.rising.high.tech.bigultimatenavdraw.db;

import android.content.ContentValues;
import android.content.Context;
import android.content.res.Resources;
import android.database.Cursor;

import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.model.Tax_rates;
import com.rising.high.tech.bigultimatenavdraw.model.Tax_rates;
import com.rising.high.tech.bigultimatenavdraw.model.Unit;

import java.util.ArrayList;

public class TaxRatesDbController extends DBController {

    public static final String TAX_RATES_TABLE_NAME = "tax_rates";
    private static Resources resources;

    public static final String TAX_RATES_ID = "id"; //int
    public static final String TAX_RATES_BUSINESS_ID = "business_id";
    public static final String TAX_RATES_NAME = "name";
    public static final String TAX_RATES_AMOUNT = "amount";
    public static final String TAX_RATES_IS_TAX_GROUP = "is_tax_group";
    public static final String TAX_RATES_FOR_TAX_GROUP = "for_tax_group";
    public static final String TAX_RATES_CREATED_BY = "created_by";
    public static final String TAX_RATES_SYNC = "sync";
    public static final String TAX_RATES_SERVER_ID = "tr_server_id";

    public static final String TAX_RATES_TABLE_CREATE =
            "CREATE TABLE " + TAX_RATES_TABLE_NAME + " (" +
                    TAX_RATES_ID + " INTEGER PRIMARY KEY AUTOINCREMENT, " +
                    TAX_RATES_BUSINESS_ID + " INTEGER, " +
                    TAX_RATES_NAME + " TEXT, " +
                    TAX_RATES_AMOUNT + " TEXT, " +
                    TAX_RATES_IS_TAX_GROUP + " INTEGER, " +
                    TAX_RATES_FOR_TAX_GROUP + " INTEGER, " +
                    TAX_RATES_CREATED_BY + " INTEGER, " +
                    TAX_RATES_SYNC + " TEXT, " +
                    TAX_RATES_SERVER_ID + " INTEGER) ;";

    public static final String TAX_RATES_TABLE_DROP = "DROP TABLE IF EXISTS " + TAX_RATES_TABLE_NAME + ";";

    public TaxRatesDbController(Context context) {
        super(context);
        resources = context.getResources();
    }

    public int insert(Tax_rates tax_rates) {
        // Create a new map of values, where column names are the keys
        ContentValues pValues = new ContentValues();
        pValues.put(TAX_RATES_ID, tax_rates.getId());
        pValues.put(TAX_RATES_BUSINESS_ID, tax_rates.getBusiness_id());
        pValues.put(TAX_RATES_NAME, tax_rates.getName());
        pValues.put(TAX_RATES_AMOUNT, tax_rates.getAmount());
        pValues.put(TAX_RATES_IS_TAX_GROUP, tax_rates.getIs_tax_group());
        pValues.put(TAX_RATES_FOR_TAX_GROUP, tax_rates.getFor_tax_group());
        pValues.put(TAX_RATES_CREATED_BY, tax_rates.getCreated_by());

        // Insert the new row, returning the primary key value of the new row
        int newRowId = (int) mDb.insert(TAX_RATES_TABLE_NAME, null, pValues); //renvoie l'id de l'enregistrement créé

        // mDb.close();
        return newRowId;
    }

    public int insertLocal(Tax_rates tax_rates) {
        // Create a new map of values, where column names are the keys
        ContentValues pValues = new ContentValues();

        pValues.put(TAX_RATES_BUSINESS_ID, tax_rates.getBusiness_id());
        pValues.put(TAX_RATES_NAME, tax_rates.getName());
        pValues.put(TAX_RATES_AMOUNT, tax_rates.getAmount());
        pValues.put(TAX_RATES_IS_TAX_GROUP, tax_rates.getIs_tax_group());
        pValues.put(TAX_RATES_FOR_TAX_GROUP, tax_rates.getFor_tax_group());
        pValues.put(TAX_RATES_CREATED_BY, tax_rates.getCreated_by());
        pValues.put(TAX_RATES_SYNC, tax_rates.getSync());
        pValues.put(TAX_RATES_SERVER_ID, tax_rates.getCreated_by());

        // Insert the new row, returning the primary key value of the new row
        int newRowId = (int) mDb.insert(TAX_RATES_TABLE_NAME, null, pValues); //renvoie l'id de l'enregistrement créé

        // mDb.close();
        return newRowId;
    }

    public int editTax_rates(Tax_rates tax_rates) {
        // Create a new map of values, where column names are the keys
        ContentValues pValues = new ContentValues();

        pValues.put(TAX_RATES_ID, tax_rates.getId());
        pValues.put(TAX_RATES_BUSINESS_ID, tax_rates.getBusiness_id());
        pValues.put(TAX_RATES_NAME, tax_rates.getName());
        pValues.put(TAX_RATES_AMOUNT, tax_rates.getAmount());
        pValues.put(TAX_RATES_IS_TAX_GROUP, tax_rates.getIs_tax_group());
        pValues.put(TAX_RATES_FOR_TAX_GROUP, tax_rates.getFor_tax_group());
        pValues.put(TAX_RATES_CREATED_BY, tax_rates.getCreated_by());

        // Insert the new row, returning the primary key value of the new row
        int newRowId = mDb.update(TAX_RATES_TABLE_NAME, pValues, TAX_RATES_ID + " = '" + tax_rates.getId() + "'", null); //renvoie l'id de l'enregistrement créé

        // mDb.close();
        return newRowId;
    }

    public int setSynTax_rates(Tax_rates tax_rates) {
        // Create a new map of values, where column names are the keys
        ContentValues pValues = new ContentValues();

        pValues.put(TAX_RATES_SERVER_ID, tax_rates.getTr_server_id());
        pValues.put(TAX_RATES_SYNC, tax_rates.getSync());

        // Insert the new row, returning the primary key value of the new row
        int newRowId = mDb.update(TAX_RATES_TABLE_NAME, pValues, TAX_RATES_ID + " = '" + tax_rates.getId() + "'", null); //renvoie l'id de l'enregistrement créé

        // mDb.close();
        return newRowId;
    }


    public void deleteItem(Integer id_tax) {
        mDb.execSQL("delete from " + TAX_RATES_TABLE_NAME + " WHERE " + TAX_RATES_ID + " = '" + id_tax + "'");
    }

    public ArrayList<Tax_rates> getAllTax_rates() {
        ArrayList<Tax_rates> tempTax = new ArrayList<>();
        String selectQuery = "SELECT  * FROM " + TAX_RATES_TABLE_NAME;
        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {
            do {
                Tax_rates tax_rates = new Tax_rates();
                tax_rates.setId(cursor.getInt(0));
                tax_rates.setBusiness_id(cursor.getInt(1));
                tax_rates.setName(cursor.getString(2));
                tax_rates.setAmount(cursor.getString(3));
                tax_rates.setIs_tax_group(cursor.getInt(4));
                tax_rates.setFor_tax_group(cursor.getInt(5));
                tax_rates.setCreated_by(cursor.getInt(6));

                tempTax.add(tax_rates);

            } while (cursor.moveToNext());

        }

        // mDb.close();

        return tempTax;
    }


    public ArrayList<Tax_rates> getSyncTax_rates(String sync) {
        ArrayList<Tax_rates> tempTax = new ArrayList<>();
        String selectQuery = "SELECT  * FROM " + TAX_RATES_TABLE_NAME + " WHERE " + TAX_RATES_SYNC + " ='" + sync + "'";
        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {
            do {
                Tax_rates tax_rates = new Tax_rates();
                tax_rates.setId(cursor.getInt(0));
                tax_rates.setBusiness_id(cursor.getInt(1));
                tax_rates.setName(cursor.getString(2));
                tax_rates.setAmount(cursor.getString(3));
                tax_rates.setIs_tax_group(cursor.getInt(4));
                tax_rates.setFor_tax_group(cursor.getInt(5));
                tax_rates.setCreated_by(cursor.getInt(6));

                tempTax.add(tax_rates);

            } while (cursor.moveToNext());

        }

        // mDb.close();

        return tempTax;
    }

    public Tax_rates getTax_ratesById(int id_tax) {
        Tax_rates tax_rates = new Tax_rates();
        String selectQuery = "SELECT  * FROM " + TAX_RATES_TABLE_NAME +  " WHERE " + TAX_RATES_ID + " = " + id_tax;;

        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {
            tax_rates.setId(cursor.getInt(0));
            tax_rates.setBusiness_id(cursor.getInt(1));
            tax_rates.setName(cursor.getString(2));
            tax_rates.setAmount(cursor.getString(3));
            tax_rates.setIs_tax_group(cursor.getInt(4));
            tax_rates.setFor_tax_group(cursor.getInt(5));
            tax_rates.setCreated_by(cursor.getInt(6));

        }

        // mDb.close();

        return tax_rates;
    }

    public ArrayList<Tax_rates> getTaxRatesLike(String name) {
        ArrayList<Tax_rates> tempTax = new ArrayList<>();
        String selectQuery = "SELECT  * FROM " + TAX_RATES_TABLE_NAME + " WHERE " + TAX_RATES_NAME + " LIKE '%" + name +  "%' ";

        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {
            do {
                Tax_rates tax_rates = new Tax_rates();
                tax_rates.setId(cursor.getInt(0));
                tax_rates.setBusiness_id(cursor.getInt(1));
                tax_rates.setName(cursor.getString(2));
                tax_rates.setAmount(cursor.getString(3));
                tax_rates.setIs_tax_group(cursor.getInt(4));
                tax_rates.setFor_tax_group(cursor.getInt(5));
                tax_rates.setCreated_by(cursor.getInt(6));

                tempTax.add(tax_rates);

            } while (cursor.moveToNext());

        }

        return tempTax;
    }

    public ArrayList<Tax_rates> getAllTax_ratesSpinner() {
        ArrayList<Tax_rates> tempTax = new ArrayList<>();
        String selectQuery = "SELECT  * FROM " + TAX_RATES_TABLE_NAME;
        Cursor cursor = mDb.rawQuery(selectQuery, null);

        tempTax.add(new Tax_rates(0, resources.getString(R.string.label_none), "0"));
        if (cursor.moveToFirst()) {
            do {
                Tax_rates tax_rates = new Tax_rates();
                tax_rates.setId(cursor.getInt(0));
                tax_rates.setBusiness_id(cursor.getInt(1));
                tax_rates.setName(cursor.getString(2));
                tax_rates.setAmount(cursor.getString(3));
                tax_rates.setIs_tax_group(cursor.getInt(4));
                tax_rates.setFor_tax_group(cursor.getInt(5));
                tax_rates.setCreated_by(cursor.getInt(6));


                tempTax.add(tax_rates);

            } while (cursor.moveToNext());

        }

        // mDb.close();

        return tempTax;
    }

    // Insert all product
    public void fill(ArrayList<Tax_rates> products) {
        if (!products.isEmpty()) {
            for (Tax_rates product : products) {
                this.insert(product);
            }
        }
        //   mDb.close();
    }

    public void clear() {
        mDb.execSQL("DELETE FROM " + TAX_RATES_TABLE_NAME);
    }

}
