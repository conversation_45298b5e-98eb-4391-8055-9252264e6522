package com.rising.high.tech.bigultimatenavdraw.db;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.util.Log;

import com.google.gson.Gson;
import com.rising.high.tech.bigultimatenavdraw.model.Business_location;
import com.rising.high.tech.bigultimatenavdraw.model.Sell_lines;
import com.rising.high.tech.bigultimatenavdraw.model.Transaction;

import java.util.ArrayList;

import static android.icu.lang.UCharacter.LineBreak.QUOTATION;
import static com.rising.high.tech.bigultimatenavdraw.db.BusinessLocationDbController.STATION_ID;
import static com.rising.high.tech.bigultimatenavdraw.db.BusinessLocationDbController.STATION_NAME;
import static com.rising.high.tech.bigultimatenavdraw.db.BusinessLocationDbController.STATION_TABLE_NAME;
import static com.rising.high.tech.bigultimatenavdraw.db.ProductDbController.PRODUCT_ID;
import static com.rising.high.tech.bigultimatenavdraw.db.ProductDbController.PRODUCT_TABLE_NAME;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.DRAFT;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.EXPENSE;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.EXPENSE_REFUND;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.NO;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.OPENING_STOCK;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.PURCHASE;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.PURCHASE_RETURN;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.SELL;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.SELL_RETURN;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.YES;

public class TransactionDbController extends DBController {
    final String TAG = getClass().getSimpleName();

    private TransactionSellLineDbController transactionSellLineDbController;
    private TransactionPayementDbController transactionPayementDbController;
    private BusinessLocationDbController businessLocationDbController;

    public static final String TRANSACTION_TABLE_NAME = "transactions";
    public static final String TRANSACTION_ID = "id";
    public static final String TRANSACTION_LOCATION_ID = "location_id";
    public static final String TRANSACTION_CUSTOMER = "customer";
    public static final String TRANSACTION_TAX_CALCULATION_AMOUNT = "tax_calculation_amount";
    public static final String TRANSACTION_DISCOUNT_TYPE = "discount_type";
    public static final String TRANSACTION_DISCOUNT_AMOUNT = "discount_amount";
    public static final String TRANSACTION_SHIPPING_DETAILS = "shipping_details";
    public static final String TRANSACTION_AMOUNT_RETURN = "amount_return";
    public static final String TRANSACTION_IS_SUSPENDED = "is_suspend";
    public static final String TRANSACTION_STATUS = "status";
    public static final String TRANSACTION_FINAL_TOTAL = "final_total";
    public static final String TRANSACTION_SHIPPING = "shipping";
    public static final String TRANSACTION_SELL_PRICE_TAX = "sell_price_tax";
    public static final String TRANSACTION_IS_SYNC = "is_sync";
    public static final String TRANSACTION_DATE = "transaction_date";
    public static final String TRANSACTION_NOTE = "transaction_note";
    public static final String TRANSACTION_CUSTOMER_ID = "customer_id";
    public static final String TRANSACTION_INVOICE_NO = "invoice_no";
    public static final String TRANSACTION_PAYEMENT_STATUS = "payment_status";
    public static final String TRANSACTION_CONTACT_ID = "contact_id";
    public static final String TRANSACTION_TYPE = "type";
    public static final String TRANSACTION_REF_NO = "ref_no";
    public static final String TRANSACTION_TOTAL_BEFORE_TAX = "total_before_tax";
    public static final String TRANSACTION_TAX_AMOUNT = "tax_amount";
    public static final String TRANSACTION_CREATED_BY = "created_by";
    public static final String TRANSACTION_PAY_TERM_NUMBER = "pay_term_number";
    public static final String TRANSACTION_PAY_TERM_TYPE = "pay_term_type";
    public static final String TRANSACTION_SHIPPING_CHARGES = "shipping_charges";
    public static final String TRANSACTION_ADDITIONAL_NOTES = "additional_notes";
    public static final String TRANSACTION_EXPENSE_CATEGORY_ID = "expense_category_id";
    public static final String TRANSACTION_RECUR_INTERVAL_TYPE = "recur_interval_type";
    public static final String TRANSACTION_IS_RECURRING = "is_recurring";
    public static final String TRANSACTION_RECUR_REPETITIONS = "recur_repetitions";
    public static final String TRANSACTION_RECUR_INTERVAL = "recur_interval";
    public static final String TRANSACTION_PAYMENT_FOR = "payment_for";
    public static final String TRANSACTION_TRANSFERT_PARENT_ID = "transfer_parent_id";
    public static final String TRANSACTION_RETURN_PARENT_ID = "return_parent_id";
    public static final String TRANSACTION_ADJUSTMENT_TYPE = "adjustment_type";
    public static final String TRANSACTION_TOTAL_AMOUNT_RECOVERED = "total_amount_recovered";
    public static final String TRANSACTION_IS_QUOTATION = "is_quotation";
    public static final String TRANSACTION_SHIPPING_ADDRESS = "shipping_address";
    public static final String TRANSACTION_SHIPPING_STATUS = "shipping_status";
    public static final String TRANSACTION_DELIVEED_TO = "delivered_to";
    public static final String TRANSACTION_TAX_ID = "tax_id";
    public static final String TRANSACTION_OPENING_STOCK_PRODUCT_ID = "opening_stock_product_id";
    public static final String TRANSACTION_BUSINESS_ID = "business_id";
    public static final String TRANSACTION_IS_DELETED = "is_deleted";

    public static final String TRANSACTION_TABLE_CREATE =
            "CREATE TABLE " + TRANSACTION_TABLE_NAME + " (" +
                    TRANSACTION_ID + " INTEGER PRIMARY KEY AUTOINCREMENT, " +
                    TRANSACTION_LOCATION_ID + " TEXT, " +
                    TRANSACTION_TAX_CALCULATION_AMOUNT + " TEXT, " +
                    TRANSACTION_DISCOUNT_TYPE + " TEXT, " +
                    TRANSACTION_DISCOUNT_AMOUNT + " TEXT, " +
                    TRANSACTION_SHIPPING_DETAILS + " TEXT, " +
                    TRANSACTION_IS_SUSPENDED + " TEXT, " +
                    TRANSACTION_AMOUNT_RETURN + " TEXT, " +
                    TRANSACTION_STATUS + " TEXT, " +
                    TRANSACTION_IS_SYNC + " TEXT, " +
                    TRANSACTION_SELL_PRICE_TAX + " TEXT, " +
                    TRANSACTION_FINAL_TOTAL + " TEXT, " +
                    TRANSACTION_CUSTOMER + " TEXT, " +
                    TRANSACTION_SHIPPING + " TEXT, " +
                    TRANSACTION_DATE + " TEXT, " +
                    TRANSACTION_NOTE + " TEXT, " +
                    TRANSACTION_CUSTOMER_ID + " TEXT, " +
                    TRANSACTION_INVOICE_NO + " TEXT, " +
                    TRANSACTION_PAYEMENT_STATUS + " TEXT, " +
                    TRANSACTION_CONTACT_ID + "  INTEGER, " +
                    TRANSACTION_TYPE + "  TEXT, " +
                    TRANSACTION_REF_NO + "  TEXT, " +
                    TRANSACTION_TOTAL_BEFORE_TAX + "  TEXT, " +
                    TRANSACTION_TAX_AMOUNT + "  TEXT, " +
                    TRANSACTION_CREATED_BY + "  TEXT, " +
                    TRANSACTION_PAY_TERM_NUMBER + "  TEXT, " +
                    TRANSACTION_PAY_TERM_TYPE + "  TEXT, " +
                    TRANSACTION_SHIPPING_CHARGES + "  TEXT, " +
                    TRANSACTION_ADDITIONAL_NOTES + "  TEXT, " +
                    TRANSACTION_EXPENSE_CATEGORY_ID + "  TEXT, " +
                    TRANSACTION_RECUR_INTERVAL_TYPE + "  TEXT, " +
                    TRANSACTION_IS_RECURRING + "  INTEGER, " +
                    TRANSACTION_RECUR_REPETITIONS + "  INTEGER, " +
                    TRANSACTION_RECUR_INTERVAL + "  INTEGER, " +
                    TRANSACTION_PAYMENT_FOR + "  INTEGER, " +
                    TRANSACTION_TRANSFERT_PARENT_ID + "  INTEGER, " +
                    TRANSACTION_ADJUSTMENT_TYPE + "  TEXT, " +
                    TRANSACTION_TOTAL_AMOUNT_RECOVERED + "  TEXT, " +
                    TRANSACTION_RETURN_PARENT_ID + "  INTEGER, " +
                    TRANSACTION_IS_QUOTATION + " INTEGER, " +
                    TRANSACTION_SHIPPING_ADDRESS + " TEXT, " +
                    TRANSACTION_SHIPPING_STATUS + " TEXT, " +
                    TRANSACTION_DELIVEED_TO + " TEXT, " +
                    TRANSACTION_TAX_ID + " INTEGER, " +
                    TRANSACTION_BUSINESS_ID + " INTEGER, " +
                    TRANSACTION_OPENING_STOCK_PRODUCT_ID + " INTEGER, " +
                     TRANSACTION_IS_DELETED + " INTEGER) ;";

    public static final String TRANSACTION_TABLE_DROP = "DROP TABLE IF EXISTS " + TRANSACTION_TABLE_NAME + ";";

    public TransactionDbController(Context context) {
        super(context);
        transactionSellLineDbController = new TransactionSellLineDbController(context);
        transactionSellLineDbController.open();

        transactionPayementDbController = new TransactionPayementDbController(context);
        transactionPayementDbController.open();

        businessLocationDbController= new BusinessLocationDbController(context);
        businessLocationDbController.open();
    }

    public int insertLocal(Transaction transaction) {
        // Create a new map of values, where column names are the keys
        ContentValues pValues = new ContentValues();

        pValues.put(TRANSACTION_LOCATION_ID, transaction.getLocation_id());
        pValues.put(TRANSACTION_TAX_CALCULATION_AMOUNT, transaction.getTax_calculation_amount());
        pValues.put(TRANSACTION_DISCOUNT_TYPE, transaction.getDiscount_type());
        pValues.put(TRANSACTION_DISCOUNT_AMOUNT, transaction.getDiscount_amount());
        pValues.put(TRANSACTION_AMOUNT_RETURN, transaction.getAmount_return());
        pValues.put(TRANSACTION_IS_SUSPENDED, transaction.getIs_suspend());
        pValues.put(TRANSACTION_STATUS, transaction.getStatus());
        pValues.put(TRANSACTION_FINAL_TOTAL, transaction.getFinal_total());
        pValues.put(TRANSACTION_SELL_PRICE_TAX, transaction.getSell_price_tax());
        pValues.put(TRANSACTION_IS_SYNC, transaction.getIs_sync() != null ? transaction.getIs_sync() : "yes");
        pValues.put(TRANSACTION_CUSTOMER, transaction.getCustomer());
        pValues.put(TRANSACTION_SHIPPING, transaction.getShipping());
        pValues.put(TRANSACTION_DATE, transaction.getTransaction_date());
        pValues.put(TRANSACTION_NOTE, transaction.getNote());
        pValues.put(TRANSACTION_CUSTOMER_ID, transaction.getCustomer_id());
        pValues.put(TRANSACTION_CONTACT_ID, transaction.getContact_id());
        pValues.put(TRANSACTION_INVOICE_NO, transaction.getInvoice_no());
        pValues.put(TRANSACTION_PAYEMENT_STATUS, transaction.getPayment_status());
        pValues.put(TRANSACTION_TYPE, transaction.getType());
        pValues.put(TRANSACTION_ADDITIONAL_NOTES, transaction.getAdditional_notes());

        pValues.put(TRANSACTION_STATUS, transaction.getStatus());
        pValues.put(TRANSACTION_REF_NO, transaction.getRef_no());
        pValues.put(TRANSACTION_TOTAL_BEFORE_TAX, transaction.getTotal_before_tax());
        pValues.put(TRANSACTION_TAX_AMOUNT, transaction.getTax_amount());
        pValues.put(TRANSACTION_CREATED_BY, transaction.getCreated_by());
        pValues.put(TRANSACTION_PAY_TERM_NUMBER, transaction.getPay_term_number());
        pValues.put(TRANSACTION_PAY_TERM_TYPE, transaction.getPay_term_type());
        pValues.put(TRANSACTION_SHIPPING_CHARGES, transaction.getShipping_charges());
        pValues.put(TRANSACTION_ADDITIONAL_NOTES, transaction.getAdditional_notes());
        pValues.put(TRANSACTION_EXPENSE_CATEGORY_ID, transaction.getExpense_category_id());
        pValues.put(TRANSACTION_RECUR_INTERVAL_TYPE, transaction.getRecur_interval_type());
        pValues.put(TRANSACTION_IS_RECURRING, transaction.getIs_recurring());
        pValues.put(TRANSACTION_RECUR_REPETITIONS, transaction.getRecur_repetitions());
        pValues.put(TRANSACTION_RECUR_INTERVAL, transaction.getRecur_interval());
        pValues.put(TRANSACTION_PAYMENT_FOR, transaction.getPayment_for());
        pValues.put(TRANSACTION_TRANSFERT_PARENT_ID, transaction.getTransfer_parent_id());
        pValues.put(TRANSACTION_ADJUSTMENT_TYPE, transaction.getAdjustment_type());
        pValues.put(TRANSACTION_TOTAL_AMOUNT_RECOVERED, transaction.getTotal_amount_recovered());
        pValues.put(TRANSACTION_RETURN_PARENT_ID, transaction.getReturn_parent_id());
        pValues.put(TRANSACTION_TOTAL_AMOUNT_RECOVERED, transaction.getTotal_amount_recovered());
        pValues.put(TRANSACTION_IS_QUOTATION, transaction.getIs_quotation() != null ? transaction.getIs_quotation() : 0);
        pValues.put(TRANSACTION_SHIPPING_ADDRESS, transaction.getShipping_address());
        pValues.put(TRANSACTION_SHIPPING_STATUS, transaction.getShipping_status());
        pValues.put(TRANSACTION_DELIVEED_TO, transaction.getDelivered_to());
        pValues.put(TRANSACTION_TAX_ID, transaction.getTax_id());
        pValues.put(TRANSACTION_SHIPPING_DETAILS, transaction.getShipping_details());
        pValues.put(TRANSACTION_OPENING_STOCK_PRODUCT_ID, transaction.getOpening_stock_product_id());
        pValues.put(TRANSACTION_BUSINESS_ID, transaction.getBusiness_id());
        pValues.put(TRANSACTION_IS_DELETED, transaction.getIs_deleted());

        int newRowId = (int) mDb.insert(TRANSACTION_TABLE_NAME, null, pValues);
        return newRowId;

    }

    public int insert(Transaction transaction) {
        // Create a new map of values, where column names are the keys
        ContentValues pValues = new ContentValues();

        pValues.put(TRANSACTION_ID, transaction.getId());
        pValues.put(TRANSACTION_LOCATION_ID, transaction.getLocation_id());
        pValues.put(TRANSACTION_TAX_CALCULATION_AMOUNT, transaction.getTax_calculation_amount());
        pValues.put(TRANSACTION_DISCOUNT_AMOUNT, transaction.getDiscount_amount());
        pValues.put(TRANSACTION_AMOUNT_RETURN, transaction.getAmount_return());
        pValues.put(TRANSACTION_IS_SUSPENDED, transaction.getIs_suspend() != null ? transaction.getIs_suspend() : null);
        pValues.put(TRANSACTION_STATUS, transaction.getStatus());
        pValues.put(TRANSACTION_FINAL_TOTAL, transaction.getFinal_total());
        pValues.put(TRANSACTION_SELL_PRICE_TAX, transaction.getSell_price_tax());
        pValues.put(TRANSACTION_IS_SYNC, transaction.getIs_sync() != null ? transaction.getIs_sync() : "yes");
        pValues.put(TRANSACTION_CUSTOMER, transaction.getCustomer());
        pValues.put(TRANSACTION_SHIPPING, transaction.getShipping());
        pValues.put(TRANSACTION_DATE, transaction.getTransaction_date());
        pValues.put(TRANSACTION_NOTE, transaction.getNote());
        pValues.put(TRANSACTION_CUSTOMER_ID, transaction.getCustomer_id());
        pValues.put(TRANSACTION_CONTACT_ID, transaction.getContact_id());
        pValues.put(TRANSACTION_INVOICE_NO, transaction.getInvoice_no());
        pValues.put(TRANSACTION_PAYEMENT_STATUS, transaction.getPayment_status());

        pValues.put(TRANSACTION_TYPE, transaction.getType());
        pValues.put(TRANSACTION_ADDITIONAL_NOTES, transaction.getAdditional_notes());

        pValues.put(TRANSACTION_STATUS, transaction.getStatus());
        pValues.put(TRANSACTION_REF_NO, transaction.getRef_no());
        pValues.put(TRANSACTION_TOTAL_BEFORE_TAX, transaction.getTotal_before_tax());
        pValues.put(TRANSACTION_TAX_AMOUNT, transaction.getTax_amount());
        pValues.put(TRANSACTION_CREATED_BY, transaction.getCreated_by());
        pValues.put(TRANSACTION_PAY_TERM_NUMBER, transaction.getPay_term_number());
        pValues.put(TRANSACTION_PAY_TERM_TYPE, transaction.getPay_term_type());
        pValues.put(TRANSACTION_SHIPPING_CHARGES, transaction.getShipping_charges());
        pValues.put(TRANSACTION_ADDITIONAL_NOTES, transaction.getAdditional_notes());
        pValues.put(TRANSACTION_EXPENSE_CATEGORY_ID, transaction.getExpense_category_id());
        pValues.put(TRANSACTION_RECUR_INTERVAL_TYPE, transaction.getRecur_interval_type());
        pValues.put(TRANSACTION_IS_RECURRING, transaction.getIs_recurring());
        pValues.put(TRANSACTION_RECUR_REPETITIONS, transaction.getRecur_repetitions());
        pValues.put(TRANSACTION_RECUR_INTERVAL, transaction.getRecur_interval());
        pValues.put(TRANSACTION_PAYMENT_FOR, transaction.getPayment_for());
        pValues.put(TRANSACTION_TRANSFERT_PARENT_ID, transaction.getTransfer_parent_id());
        pValues.put(TRANSACTION_TOTAL_AMOUNT_RECOVERED, transaction.getTotal_amount_recovered());
        pValues.put(TRANSACTION_RETURN_PARENT_ID, transaction.getTransfer_parent_id());
        pValues.put(TRANSACTION_IS_QUOTATION, transaction.getIs_quotation());
        pValues.put(TRANSACTION_SHIPPING_ADDRESS, transaction.getShipping_address());
        pValues.put(TRANSACTION_SHIPPING_STATUS, transaction.getShipping_status());
        pValues.put(TRANSACTION_DELIVEED_TO, transaction.getDelivered_to());
        pValues.put(TRANSACTION_TAX_ID, transaction.getTax_id());
        pValues.put(TRANSACTION_SHIPPING_DETAILS, transaction.getShipping_details());
        pValues.put(TRANSACTION_OPENING_STOCK_PRODUCT_ID, transaction.getOpening_stock_product_id());
        pValues.put(TRANSACTION_BUSINESS_ID, transaction.getBusiness_id());
        pValues.put(TRANSACTION_IS_DELETED, transaction.getIs_deleted());

        int newRowId = (int) mDb.insert(TRANSACTION_TABLE_NAME, null, pValues); //renvoie l'id de l'enregistrement créé
        return newRowId;
    }

    public int updateTransaction(Transaction transaction) {
        // Create a new map of values, where column names are the keys
        ContentValues pValues = new ContentValues();

        //   pValues.put(TRANSACTION_LOCATION_ID, transaction.getLocation_id());
        pValues.put(TRANSACTION_TAX_CALCULATION_AMOUNT, transaction.getTax_calculation_amount());
        pValues.put(TRANSACTION_DISCOUNT_TYPE, transaction.getDiscount_type());
        pValues.put(TRANSACTION_DISCOUNT_AMOUNT, transaction.getDiscount_amount());
        pValues.put(TRANSACTION_AMOUNT_RETURN, transaction.getAmount_return());
        pValues.put(TRANSACTION_IS_SUSPENDED, transaction.getIs_suspend());
        pValues.put(TRANSACTION_STATUS, transaction.getStatus());

        pValues.put(TRANSACTION_FINAL_TOTAL, transaction.getFinal_total());
        pValues.put(TRANSACTION_SELL_PRICE_TAX, transaction.getSell_price_tax());
        pValues.put(TRANSACTION_IS_SYNC, transaction.getIs_sync() != null ? transaction.getIs_sync() : "yes");
        pValues.put(TRANSACTION_CUSTOMER, transaction.getCustomer());
        pValues.put(TRANSACTION_SHIPPING, transaction.getShipping());
        pValues.put(TRANSACTION_DATE, transaction.getTransaction_date());
        pValues.put(TRANSACTION_NOTE, transaction.getNote());
        pValues.put(TRANSACTION_CUSTOMER_ID, transaction.getCustomer_id());
        pValues.put(TRANSACTION_CONTACT_ID, transaction.getContact_id());
        pValues.put(TRANSACTION_INVOICE_NO, transaction.getInvoice_no());
        pValues.put(TRANSACTION_PAYEMENT_STATUS, transaction.getPayment_status());
        pValues.put(TRANSACTION_TYPE, transaction.getType());
        pValues.put(TRANSACTION_REF_NO, transaction.getRef_no());
        pValues.put(TRANSACTION_TOTAL_BEFORE_TAX, transaction.getTotal_before_tax());
        pValues.put(TRANSACTION_TAX_AMOUNT, transaction.getTax_amount());
        pValues.put(TRANSACTION_CREATED_BY, transaction.getCreated_by());
        pValues.put(TRANSACTION_PAY_TERM_NUMBER, transaction.getPay_term_number());
        pValues.put(TRANSACTION_PAY_TERM_TYPE, transaction.getPay_term_type());
        pValues.put(TRANSACTION_SHIPPING_CHARGES, transaction.getShipping_charges());
        pValues.put(TRANSACTION_ADJUSTMENT_TYPE, transaction.getAdjustment_type());
        pValues.put(TRANSACTION_TOTAL_AMOUNT_RECOVERED, transaction.getTotal_amount_recovered());
        pValues.put(TRANSACTION_RETURN_PARENT_ID, transaction.getTransfer_parent_id());
        pValues.put(TRANSACTION_IS_QUOTATION, transaction.getIs_quotation() != null ? transaction.getIs_quotation() : 0);
        pValues.put(TRANSACTION_SHIPPING_ADDRESS, transaction.getShipping_address());
        pValues.put(TRANSACTION_SHIPPING_STATUS, transaction.getShipping_status());
        pValues.put(TRANSACTION_DELIVEED_TO, transaction.getDelivered_to());
        pValues.put(TRANSACTION_TAX_ID, transaction.getTax_id());
        pValues.put(TRANSACTION_SHIPPING_DETAILS, transaction.getShipping_details());
        pValues.put(TRANSACTION_OPENING_STOCK_PRODUCT_ID, transaction.getOpening_stock_product_id());
        pValues.put(TRANSACTION_BUSINESS_ID, transaction.getBusiness_id());
        pValues.put(TRANSACTION_ADDITIONAL_NOTES, transaction.getAdditional_notes());
        pValues.put(TRANSACTION_IS_DELETED, transaction.getIs_deleted());

        int newRowId = mDb.update(TRANSACTION_TABLE_NAME, pValues, TRANSACTION_ID + " = '" + transaction.getId() + "'", null); //renvoie l'id de l'enregistrement créé

        return newRowId;
    }

    public int setSyncTransaction(Transaction transaction) {
        // Create a new map of values, where column names are the keys
        ContentValues pValues = new ContentValues();

        //   pValues.put(TRANSACTION_LOCATION_ID, transaction.getLocation_id());

        pValues.put(TRANSACTION_IS_SYNC, YES);

        int newRowId = mDb.update(TRANSACTION_TABLE_NAME, pValues, TRANSACTION_ID + " = '" + transaction.getId() + "'", null); //renvoie l'id de l'enregistrement créé

        return newRowId;
    }


    public Transaction getTransactionById(Integer id) {
        Transaction transaction = new Transaction();

        String selectQuery = "SELECT  * FROM " + TRANSACTION_TABLE_NAME + " WHERE " + TRANSACTION_ID + " = '" + id + "'";
        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {

            transaction.setFinal_total(cursor.getString(11));
            transaction.setLocation_id(cursor.getInt(1));
            transaction.setId(cursor.getInt(0));
            transaction.setCustomer(cursor.getString(12));
            transaction.setShipping(cursor.getString(13));
            transaction.setTransaction_date(cursor.getString(14));
            transaction.setIs_sync(cursor.getString(9));
            transaction.setInvoice_no(cursor.getString(17));
            transaction.setPayment_status(cursor.getString(18));
            transaction.setContact_id(cursor.getInt(19));
            transaction.setPay_term_number(cursor.getString(25));
            transaction.setPay_term_type(cursor.getString(26));
            transaction.setShipping_charges(cursor.getString(27));
            transaction.setAdditional_notes(cursor.getString(28));
            transaction.setDiscount_amount(cursor.getString(4));
            transaction.setDiscount_type(cursor.getString(3));
            transaction.setStatus(cursor.getString(8));
            transaction.setType(cursor.getString(20));
            transaction.setExpense_category_id(cursor.getInt(29));
            transaction.setRecur_interval_type(cursor.getString(30));
            transaction.setIs_recurring(cursor.getInt(31));
            transaction.setRecur_repetitions(cursor.getInt(32));
            transaction.setRecur_interval(cursor.getInt(33));
            transaction.setPayment_for(cursor.getInt(34));
            transaction.setTransfer_parent_id(cursor.getInt(35));
            transaction.setAdjustment_type(cursor.getString(36));
            transaction.setTotal_amount_recovered(cursor.getString(37));
            transaction.setReturn_parent_id(cursor.getInt(38));
            transaction.setIs_quotation(cursor.getInt(39));
            transaction.setShipping_address(cursor.getString(40));
            transaction.setShipping_status(cursor.getString(41));
            transaction.setDelivered_to(cursor.getString(42));
            transaction.setTax_id(cursor.getInt(43));
            transaction.setShipping_details(cursor.getString(5));
            transaction.setBusiness_id(cursor.getInt(44));
            transaction.setOpening_stock_product_id(cursor.getInt(45));
            transaction.setRef_no(cursor.getString(21));
            transaction.setIs_deleted(cursor.getInt(46));

        }

        // mDb.close();
        return transaction;

    }


    // Insert all product
    public void fill(ArrayList<Transaction> transactions) {
        if (!transactions.isEmpty()) {
            for (Transaction transaction : transactions) {
                this.insert(transaction);
            }
        }
    }

    public void deleteTransaction(int id) {
        mDb.execSQL("delete from " + TRANSACTION_TABLE_NAME + " WHERE " + TRANSACTION_ID + " = " + id);
    }
    public int markDeleteTransaction(Transaction transaction) {
        ContentValues pValues = new ContentValues();
        pValues.put(TRANSACTION_IS_DELETED, 1);
        int newRowId = mDb.update(TRANSACTION_TABLE_NAME, pValues, TRANSACTION_ID + " = '" + transaction.getId() + "'", null); //renvoie l'id de l'enregistrement créé

        return newRowId;
    }

    public void setUpdateYes(int id) {
        mDb.execSQL("UPDATE " + TRANSACTION_TABLE_NAME + " SET " + TRANSACTION_IS_SYNC + "= 'yes' " + " WHERE " + TRANSACTION_ID + " = " + id);
    }

    // update non sync to server
    public ArrayList<Transaction> getTransaction(String status) {
        ArrayList<Transaction> transactions = new ArrayList<>();

        String selectQuery = "SELECT  * FROM " + TRANSACTION_TABLE_NAME + " WHERE " + TRANSACTION_STATUS + " = '" + status + "'";
        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {

            do {
                Transaction transaction = new Transaction();

                transaction.setFinal_total(cursor.getString(11));
                transaction.setId(cursor.getInt(0));
                transaction.setCustomer(cursor.getString(12));
                transaction.setShipping(cursor.getString(13));
                transaction.setTransaction_date(cursor.getString(14));
                transaction.setIs_sync(cursor.getString(9));
                transaction.setCustomer_id(cursor.getInt(16));
                transaction.setInvoice_no(cursor.getString(17));
                transaction.setContact_id(cursor.getInt(19));
                transactions.add(transaction);

            } while (cursor.moveToNext());
        }

        // mDb.close();
        return transactions;

    }

    // update non sync to server
    public ArrayList<Transaction> getAllTransactionByStatus() {
        ArrayList<Transaction> transactions = new ArrayList<>();

        String selectQuery = "SELECT  * FROM " + TRANSACTION_TABLE_NAME;
        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {

            do {
                Transaction transaction = new Transaction();
                transaction.setFinal_total(cursor.getString(11));
                transaction.setLocation_id(cursor.getInt(1));
                transaction.setId(Integer.parseInt(cursor.getString(0)));
                transaction.setCustomer(cursor.getString(12));
                transaction.setShipping(cursor.getString(13));
                transaction.setTransaction_date(cursor.getString(14));
                transaction.setIs_sync(cursor.getString(9));
                transaction.setCustomer_id(cursor.getInt(16));
                transaction.setInvoice_no(cursor.getString(17));
                transaction.setPayment_status(cursor.getString(18));
                // transaction.setNote(cursor.getString(15));
                // product.setImage_product(cursor.getString(28));
                transactions.add(transaction);

            } while (cursor.moveToNext());
        }

        // mDb.close();
        return transactions;

    }

    // update non sync to server
    public Float getTotalPaidByCustomerId(int contact_id) {
        ArrayList<Transaction> transactions = new ArrayList<>();

        String selectQuery = "SELECT  * FROM " + TRANSACTION_TABLE_NAME + " WHERE " + TRANSACTION_CONTACT_ID + " = " + contact_id + " AND " + TRANSACTION_TYPE + " = '" + SELL + "'"+ " AND " + TRANSACTION_IS_DELETED + " = '" + 0 + "'";
        Cursor cursor = mDb.rawQuery(selectQuery, null);

        float total= 0.f;
        if (cursor.moveToFirst()) {
            do {
                total+= Float.parseFloat(cursor.getString(11));
            } while (cursor.moveToNext());
        }

        // mDb.close();
        return total;

    }

    // update non sync to server
    public ArrayList<Transaction> getTransactionsByOpeningStockProductId(Integer product_id) {
        ArrayList<Transaction> transactions = new ArrayList<>();

        String selectQuery = "SELECT  * FROM " + TRANSACTION_TABLE_NAME + " WHERE " + TRANSACTION_OPENING_STOCK_PRODUCT_ID + " = " + product_id +" AND " + TRANSACTION_TYPE + " = '" + OPENING_STOCK + "'";
        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {

            do {
                Transaction transaction = new Transaction();
                transaction.setFinal_total(cursor.getString(11));
                Business_location business_location= businessLocationDbController.getStationById(cursor.getInt(1));
                transaction.setLocation_id(business_location.getLocation_server_id());

                transaction.setId(cursor.getInt(0));
                transaction.setCustomer(cursor.getString(12));
                transaction.setShipping(cursor.getString(13));
                transaction.setTransaction_date(cursor.getString(14));
                transaction.setIs_sync(cursor.getString(9));
                transaction.setCustomer_id(cursor.getInt(16));
                transaction.setInvoice_no(cursor.getString(17));
                transaction.setPayment_status(cursor.getString(18));
                transaction.setTransaction_date(cursor.getString(14));

                transaction.setTotal_before_tax(cursor.getString(22));
                transaction.setOpening_stock_product_id(cursor.getInt(45));
                transactions.add(transaction);

            } while (cursor.moveToNext());
        }

        // mDb.close();
        return transactions;

    }
    // update non sync to server
    public ArrayList<Transaction> getNonSyncTransactionsByOpeningStock() {
        ArrayList<Transaction> transactions = new ArrayList<>();

        String selectQuery = "SELECT  * FROM " + TRANSACTION_TABLE_NAME + " WHERE " + TRANSACTION_IS_SYNC + " = '" + NO +"' AND " + TRANSACTION_TYPE + " = '"
                + OPENING_STOCK + "'" + " GROUP BY " + TRANSACTION_OPENING_STOCK_PRODUCT_ID;
        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {

            do {
                Transaction transaction = new Transaction();
                transaction.setFinal_total(cursor.getString(11));
                transaction.setLocation_id(cursor.getInt(1));
                transaction.setId(cursor.getInt(0));
                transaction.setCustomer(cursor.getString(12));
                transaction.setShipping(cursor.getString(13));
                transaction.setTransaction_date(cursor.getString(14));
                transaction.setIs_sync(cursor.getString(9));
                transaction.setCustomer_id(cursor.getInt(16));
                transaction.setInvoice_no(cursor.getString(17));
                transaction.setPayment_status(cursor.getString(18));
                transaction.setTransaction_date(cursor.getString(14));

                transaction.setTotal_before_tax(cursor.getString(22));
                transaction.setOpening_stock_product_id(cursor.getInt(45));
                transactions.add(transaction);

            } while (cursor.moveToNext());
        }

        // mDb.close();
        return transactions;

    }

    // update non sync to server
    public ArrayList<Transaction> filterPurchaseTransaction(Integer locationId, String status, String payementStatus, int customer_id, String dateDebut, String dateFin) {
        ArrayList<Transaction> transactions = new ArrayList<>();

        String selectQuery = "SELECT  * FROM " + TRANSACTION_TABLE_NAME + " WHERE " + TRANSACTION_TYPE + " = '" + PURCHASE + "'";
        String locationQuery = " AND " + ((locationId.equals(0)) ? (TRANSACTION_LOCATION_ID + " IS NOT NULL ") : (TRANSACTION_LOCATION_ID + " = '" + locationId + "'"));
        String statusQuery = " AND " + ((status != null) ? (TRANSACTION_STATUS + " like '%" + status + "%'") : (TRANSACTION_STATUS + " IS NOT NULL"));
        String payStatusQuery = " AND " + ((payementStatus != null) ? (TRANSACTION_PAYEMENT_STATUS + " like '%" + payementStatus + "%'") : (TRANSACTION_PAYEMENT_STATUS + " IS NOT NULL"));
        String customerQuery = " AND " + ((customer_id == 0) ? (TRANSACTION_CONTACT_ID + " IS NOT NULL") : (TRANSACTION_CONTACT_ID + " ='" + customer_id + "'"));
        String dateQuery = " AND " + ((dateDebut.equals("") || dateFin.equals("")) ? (TRANSACTION_DATE + " IS NOT NULL") : (TRANSACTION_DATE + " BETWEEN '" + dateDebut + "' AND '" + dateFin + "'"));

        selectQuery += dateQuery + customerQuery + payStatusQuery + statusQuery + locationQuery;

        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {

            do {
                Transaction transaction = new Transaction();
                transaction.setFinal_total(cursor.getString(11));
                transaction.setLocation_id(cursor.getInt(1));
                transaction.setId(Integer.parseInt(cursor.getString(0)));
                transaction.setCustomer(cursor.getString(12));
                transaction.setShipping(cursor.getString(13));
                transaction.setTransaction_date(cursor.getString(14));
                transaction.setIs_sync(cursor.getString(9));
                transaction.setStatus(cursor.getString(8));
                transaction.setCustomer_id(cursor.getInt(16));
                transaction.setInvoice_no(cursor.getString(17));
                transaction.setPayment_status(cursor.getString(18));
                transaction.setContact_id(cursor.getInt(19));
                transaction.setType(cursor.getString(20));
                transaction.setIs_quotation(cursor.getInt(39));
                transaction.setShipping_address(cursor.getString(40));
                transaction.setShipping_status(cursor.getString(41));
                transaction.setDelivered_to(cursor.getString(42));

                transactions.add(transaction);

            } while (cursor.moveToNext());
        }

        // mDb.close();
        return transactions;

    }


    // update non sync to server
    public ArrayList<Transaction> filterPurchaseReturnTransaction(String dateDebut, String dateFin) {
        ArrayList<Transaction> transactions = new ArrayList<>();

        String selectQuery = "SELECT  * FROM " + TRANSACTION_TABLE_NAME + " WHERE " + TRANSACTION_TYPE + " = '" + PURCHASE_RETURN + "'";
        String dateQuery = " AND " + ((dateDebut.equals("") || dateFin.equals("")) ? (TRANSACTION_DATE + " IS NOT NULL") : (TRANSACTION_DATE + " BETWEEN '" + dateDebut + "' AND '" + dateFin + "'"));

        selectQuery += dateQuery;

        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {

            do {
                Transaction transaction = new Transaction();
                transaction.setFinal_total(cursor.getString(11));
                transaction.setLocation_id(cursor.getInt(1));
                transaction.setId(Integer.parseInt(cursor.getString(0)));
                transaction.setCustomer(cursor.getString(12));
                transaction.setShipping(cursor.getString(13));
                transaction.setTransaction_date(cursor.getString(14));
                transaction.setIs_sync(cursor.getString(9));
                transaction.setStatus(cursor.getString(8));
                transaction.setCustomer_id(cursor.getInt(16));
                transaction.setInvoice_no(cursor.getString(17));
                transaction.setPayment_status(cursor.getString(18));
                transaction.setContact_id(cursor.getInt(19));
                transaction.setType(cursor.getString(20));
                transaction.setIs_quotation(cursor.getInt(39));
                transaction.setShipping_address(cursor.getString(40));
                transaction.setShipping_status(cursor.getString(41));
                transaction.setDelivered_to(cursor.getString(42));
                transaction.setRef_no(cursor.getString(21));

                transactions.add(transaction);

            } while (cursor.moveToNext());
        }

        // mDb.close();
        return transactions;

    }

    // update non sync to server
    public ArrayList<Transaction> getAllTransactionByStatus(String type) {
        ArrayList<Transaction> transactions = new ArrayList<>();

        String selectQuery = "SELECT  * FROM " + TRANSACTION_TABLE_NAME + " WHERE " + TRANSACTION_STATUS + " = '" + type + "'";
        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {

            do {
                Transaction transaction = new Transaction();
                transaction.setFinal_total(cursor.getString(11));
                transaction.setLocation_id(cursor.getInt(1));
                transaction.setId(Integer.parseInt(cursor.getString(0)));
                transaction.setCustomer(cursor.getString(12));
                transaction.setShipping(cursor.getString(13));
                transaction.setTransaction_date(cursor.getString(14));
                transaction.setIs_sync(cursor.getString(9));
                transaction.setCustomer_id(cursor.getInt(16));
                transaction.setInvoice_no(cursor.getString(17));
                transaction.setPayment_status(cursor.getString(18));
                transaction.setContact_id(cursor.getInt(19));

                transactions.add(transaction);

            } while (cursor.moveToNext());
        }
        // mDb.close();
        return transactions;

    }

    // update non sync to server
    public ArrayList<Transaction> getSellTransaction(String type) {
        ArrayList<Transaction> transactions = new ArrayList<>();

        String selectQuery = "SELECT  * FROM " + TRANSACTION_TABLE_NAME + " WHERE " + TRANSACTION_TYPE + " = '" + type + "' AND " + TRANSACTION_IS_QUOTATION + " = " + 0 + " AND " + TRANSACTION_STATUS + " = 'final'";
        Cursor cursor = mDb.rawQuery(selectQuery, null);
        if (cursor.moveToFirst()) {
            do {
                Transaction transaction = new Transaction();
                transaction.setId(cursor.getInt(0));
                transaction.setFinal_total(cursor.getString(11));
                transaction.setLocation_id(cursor.getInt(1));
                transaction.setCustomer(cursor.getString(12));
                transaction.setShipping(cursor.getString(13));
                transaction.setTransaction_date(cursor.getString(14));
                transaction.setIs_sync(cursor.getString(9));
                transaction.setStatus(cursor.getString(8));
                transaction.setCustomer_id(cursor.getInt(16));
                transaction.setInvoice_no(cursor.getString(17));
                transaction.setPayment_status(cursor.getString(18));
                transaction.setContact_id(cursor.getInt(19));
                transaction.setPay_term_number(cursor.getString(25));
                transaction.setShipping_charges(cursor.getString(27));
                transaction.setDiscount_amount(cursor.getString(4));
                transaction.setDiscount_type(cursor.getString(3));
                transaction.setStatus(cursor.getString(8));
                transaction.setType(cursor.getString(20));
                transaction.setTransfer_parent_id(cursor.getInt(35));
                transaction.setAdjustment_type(cursor.getString(36));
                transaction.setTotal_amount_recovered(cursor.getString(37));
                transaction.setAdditional_notes(cursor.getString(28));
                transaction.setReturn_parent_id(cursor.getInt(38));
                transaction.setIs_quotation(cursor.getInt(39));
                transaction.setShipping_address(cursor.getString(40));
                transaction.setShipping_status(cursor.getString(41));
                transaction.setDelivered_to(cursor.getString(42));
                transaction.setRef_no(cursor.getString(21));
                transaction.setIs_deleted(cursor.getInt(46));
                transactions.add(transaction);

            } while (cursor.moveToNext());
        }

        // mDb.close();
        return transactions;

    }


    // update non sync to server
    public ArrayList<Transaction> getTransactionType(String type) {
        ArrayList<Transaction> transactions = new ArrayList<>();

        String selectQuery = "SELECT  * FROM " + TRANSACTION_TABLE_NAME + " WHERE " + TRANSACTION_TYPE + " = '" + type + "' AND " + TRANSACTION_IS_QUOTATION + " = " + 0;
        Cursor cursor = mDb.rawQuery(selectQuery, null);
        if (cursor.moveToFirst()) {
            do {
                Transaction transaction = new Transaction();
                transaction.setId(cursor.getInt(0));
                transaction.setFinal_total(cursor.getString(11));
                transaction.setLocation_id(cursor.getInt(1));
                transaction.setCustomer(cursor.getString(12));
                transaction.setShipping(cursor.getString(13));
                transaction.setTransaction_date(cursor.getString(14));
                transaction.setIs_sync(cursor.getString(9));
                transaction.setStatus(cursor.getString(8));
                transaction.setCustomer_id(cursor.getInt(16));
                transaction.setInvoice_no(cursor.getString(17));
                transaction.setPayment_status(cursor.getString(18));
                transaction.setContact_id(cursor.getInt(19));
                transaction.setPay_term_number(cursor.getString(25));
                transaction.setShipping_charges(cursor.getString(27));
                transaction.setDiscount_amount(cursor.getString(4));
                transaction.setDiscount_type(cursor.getString(3));
                transaction.setStatus(cursor.getString(8));
                transaction.setType(cursor.getString(20));
                transaction.setTransfer_parent_id(cursor.getInt(35));
                transaction.setAdjustment_type(cursor.getString(36));
                transaction.setTotal_amount_recovered(cursor.getString(37));
                transaction.setAdditional_notes(cursor.getString(28));
                transaction.setReturn_parent_id(cursor.getInt(38));
                transaction.setIs_quotation(cursor.getInt(39));
                transaction.setShipping_address(cursor.getString(40));
                transaction.setShipping_status(cursor.getString(41));
                transaction.setDelivered_to(cursor.getString(42));
                transaction.setRef_no(cursor.getString(21));
                transaction.setCreated_by(cursor.getInt(24));
                transactions.add(transaction);

            } while (cursor.moveToNext());
        }

        // mDb.close();
        return transactions;

    }


    // update non sync to server
    public ArrayList<Transaction> getTransactionLikeType(String type, String query_string) {
        ArrayList<Transaction> transactions = new ArrayList<>();

     //   String selectQuery = "SELECT  * FROM " + TRANSACTION_TABLE_NAME + " WHERE " + TRANSACTION_TYPE + " = '" + type + "' AND " + TRANSACTION_IS_QUOTATION + " = " + 0 ;

        String selectQuery = "SELECT  * FROM " + TRANSACTION_TABLE_NAME + " INNER JOIN " + STATION_TABLE_NAME
                +" ON " + TRANSACTION_TABLE_NAME+"."+TRANSACTION_LOCATION_ID + " = " + STATION_TABLE_NAME+"."+ STATION_ID
                + " WHERE " + TRANSACTION_TABLE_NAME+"."+TRANSACTION_TYPE + " = '" + type + "' AND " + TRANSACTION_TABLE_NAME+"."+TRANSACTION_IS_QUOTATION + " = " + 0  +
                " AND (" + STATION_TABLE_NAME+"."+ STATION_NAME + " like '%" + query_string + "%'"  + " OR " + TRANSACTION_TABLE_NAME+"."+ TRANSACTION_STATUS + " like '%" + query_string + "%'" + " OR " + TRANSACTION_TABLE_NAME+"."+ TRANSACTION_DATE + " like '%" + query_string + "%' )"  ;

        Cursor cursor = mDb.rawQuery(selectQuery, null);
        if (cursor.moveToFirst()) {
            do {
                Transaction transaction = new Transaction();
                transaction.setId(cursor.getInt(0));
                transaction.setFinal_total(cursor.getString(11));
                transaction.setLocation_id(cursor.getInt(1));
                transaction.setCustomer(cursor.getString(12));
                transaction.setShipping(cursor.getString(13));
                transaction.setTransaction_date(cursor.getString(14));
                transaction.setIs_sync(cursor.getString(9));
                transaction.setStatus(cursor.getString(8));
                transaction.setCustomer_id(cursor.getInt(16));
                transaction.setInvoice_no(cursor.getString(17));
                transaction.setPayment_status(cursor.getString(18));
                transaction.setContact_id(cursor.getInt(19));
                transaction.setPay_term_number(cursor.getString(25));
                transaction.setShipping_charges(cursor.getString(27));
                transaction.setDiscount_amount(cursor.getString(4));
                transaction.setDiscount_type(cursor.getString(3));
                transaction.setStatus(cursor.getString(8));
                transaction.setType(cursor.getString(20));
                transaction.setTransfer_parent_id(cursor.getInt(35));
                transaction.setAdjustment_type(cursor.getString(36));
                transaction.setTotal_amount_recovered(cursor.getString(37));
                transaction.setAdditional_notes(cursor.getString(28));
                transaction.setReturn_parent_id(cursor.getInt(38));
                transaction.setIs_quotation(cursor.getInt(39));
                transaction.setShipping_address(cursor.getString(40));
                transaction.setShipping_status(cursor.getString(41));
                transaction.setDelivered_to(cursor.getString(42));
                transaction.setRef_no(cursor.getString(21));
                transaction.setCreated_by(cursor.getInt(24));
                transactions.add(transaction);

            } while (cursor.moveToNext());
        }

        // mDb.close();
        return transactions;

    }


    // update non sync to server
    public ArrayList<Transaction> getQutationList() {
        ArrayList<Transaction> transactions = new ArrayList<>();

        String selectQuery = "SELECT  * FROM " + TRANSACTION_TABLE_NAME + " WHERE " + TRANSACTION_IS_QUOTATION + " = '" + 1 + "'";
        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {

            do {
                Transaction transaction = new Transaction();
                transaction.setId(cursor.getInt(0));
                transaction.setFinal_total(cursor.getString(11));
                transaction.setLocation_id(cursor.getInt(1));
                transaction.setCustomer(cursor.getString(12));
                transaction.setShipping(cursor.getString(13));
                transaction.setTransaction_date(cursor.getString(14));
                transaction.setIs_sync(cursor.getString(9));
                transaction.setStatus(cursor.getString(8));
                transaction.setCustomer_id(cursor.getInt(16));
                transaction.setInvoice_no(cursor.getString(17));
                transaction.setPayment_status(cursor.getString(18));
                transaction.setContact_id(cursor.getInt(19));
                transaction.setPay_term_number(cursor.getString(25));

                transaction.setShipping_charges(cursor.getString(27));
                transaction.setDiscount_amount(cursor.getString(4));
                transaction.setDiscount_type(cursor.getString(3));
                transaction.setStatus(cursor.getString(8));
                transaction.setType(cursor.getString(20));
                transaction.setTransfer_parent_id(cursor.getInt(35));
                transaction.setAdjustment_type(cursor.getString(36));
                transaction.setTotal_amount_recovered(cursor.getString(37));
                transaction.setAdditional_notes(cursor.getString(28));


                transaction.setIs_quotation(cursor.getInt(39));
                transaction.setShipping_address(cursor.getString(40));
                transaction.setShipping_status(cursor.getString(41));
                transaction.setDelivered_to(cursor.getString(42));
                transactions.add(transaction);

            } while (cursor.moveToNext());
        }

        // mDb.close();
        return transactions;

    }


    // update non sync to server
    public ArrayList<Transaction> getExpense() {
        ArrayList<Transaction> transactions = new ArrayList<>();


        String selectQuery = "SELECT  * FROM " + TRANSACTION_TABLE_NAME + " WHERE " + TRANSACTION_TYPE + " = '" + EXPENSE + "'" + " OR " + TRANSACTION_TYPE + " = '" + EXPENSE_REFUND + "'";
        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {

            do {
                Transaction transaction = new Transaction();
                transaction.setFinal_total(cursor.getString(11));
                transaction.setLocation_id(cursor.getInt(1));
                transaction.setId(cursor.getInt(0));
                transaction.setCustomer(cursor.getString(12));
                transaction.setShipping(cursor.getString(13));
                transaction.setTransaction_date(cursor.getString(14));
                transaction.setIs_sync(cursor.getString(9));
                transaction.setStatus(cursor.getString(8));
                transaction.setCustomer_id(cursor.getInt(16));
                transaction.setInvoice_no(cursor.getString(17));
                transaction.setPayment_status(cursor.getString(18));
                transaction.setContact_id(cursor.getInt(19));
                transaction.setPay_term_number(cursor.getString(25));

                transaction.setShipping_charges(cursor.getString(27));
                transaction.setDiscount_amount(cursor.getString(4));
                transaction.setDiscount_type(cursor.getString(3));
                transaction.setStatus(cursor.getString(8));
                transaction.setType(cursor.getString(20));
                transaction.setAdditional_notes(cursor.getString(28));
                transaction.setExpense_category_id(cursor.getInt(29));
                transaction.setRecur_interval_type(cursor.getString(30));
                transaction.setIs_recurring(cursor.getInt(31));
                transaction.setRecur_repetitions(cursor.getInt(32));
                transaction.setRecur_interval(cursor.getInt(33));
                transaction.setPayment_for(cursor.getInt(34));
                transaction.setIs_quotation(cursor.getInt(39));
                transaction.setShipping_address(cursor.getString(40));
                transaction.setShipping_status(cursor.getString(41));
                transaction.setDelivered_to(cursor.getString(42));
                transaction.setRef_no(cursor.getString(21));

                transactions.add(transaction);

            } while (cursor.moveToNext());
        }

        // mDb.close();
        return transactions;

    }

    public ArrayList<Transaction> getTransactionByContactId(Integer id_contact, String dateDebut, String dateFin, String payment_status) {
        ArrayList<Transaction> transactions = new ArrayList<>();
        String selectQuery = "SELECT  * FROM " + TRANSACTION_TABLE_NAME + " WHERE " + TRANSACTION_CONTACT_ID + " = '" + id_contact + "'";
        String dateQuery = " AND " + ((dateDebut != null && dateFin != null) ? (TRANSACTION_DATE + " BETWEEN '" + dateDebut + "' AND '" + dateFin + "'") : (TRANSACTION_DATE + " IS NOT NULL "));
        String statusQuery = " AND " + ((payment_status.equals("all")) ? (TRANSACTION_PAYEMENT_STATUS + " IS NOT NULL ") : (TRANSACTION_PAYEMENT_STATUS + " = '" + payment_status + "'"));
        selectQuery+=dateQuery + statusQuery;

        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {
            do {
                Transaction transaction = new Transaction();
                transaction.setFinal_total(cursor.getString(11));
                transaction.setLocation_id(cursor.getInt(1));
                transaction.setId(Integer.parseInt(cursor.getString(0)));
                transaction.setCustomer(cursor.getString(12));
                transaction.setShipping(cursor.getString(13));
                transaction.setTransaction_date(cursor.getString(14));
                transaction.setIs_sync(cursor.getString(9));
                transaction.setCustomer_id(cursor.getInt(16));
                transaction.setInvoice_no(cursor.getString(17));
                transaction.setPayment_status(cursor.getString(18));
                transaction.setContact_id(cursor.getInt(19));

                transaction.setIs_quotation(cursor.getInt(39));
                transaction.setShipping_address(cursor.getString(40));
                transaction.setShipping_status(cursor.getString(41));
                transaction.setDelivered_to(cursor.getString(42));
                transactions.add(transaction);

            } while (cursor.moveToNext());
        }

        // mDb.close();
        return transactions;

    }

    public ArrayList<Transaction> getTransactionLedgerByContactId(Integer id_contact , String dateDebut, String dateFin) {
        ArrayList<Transaction> transactions = new ArrayList<>();
        String selectQuery = "SELECT  * FROM " + TRANSACTION_TABLE_NAME + " WHERE " + TRANSACTION_CONTACT_ID + " = '" + id_contact + "'";
        String dateQuery = " AND " + ((dateDebut != null && dateFin != null) ? (TRANSACTION_DATE + " BETWEEN '" + dateDebut + "' AND '" + dateFin + "'") : (TRANSACTION_DATE + " IS NOT NULL "));

        selectQuery+=dateQuery ;
        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {
            do {
                Transaction transaction = new Transaction();
                Transaction transaction1 = new Transaction();
                transaction.setFinal_total(cursor.getString(11));
                transaction1.setFinal_total(cursor.getString(11));
                transaction.setLocation_id(cursor.getInt(1));
                transaction1.setLocation_id(cursor.getInt(1));
                transaction.setId(cursor.getInt(0));
                transaction1.setId(cursor.getInt(0));
                transaction.setCustomer(cursor.getString(12));
                transaction1.setCustomer(cursor.getString(12));
                transaction.setShipping(cursor.getString(13));
                transaction1.setShipping(cursor.getString(13));
                transaction.setTransaction_date(cursor.getString(14));
                transaction1.setTransaction_date(cursor.getString(14));
                transaction.setIs_sync(cursor.getString(9));
                transaction1.setIs_sync(cursor.getString(9));
                transaction.setCustomer_id(cursor.getInt(16));
                transaction1.setCustomer_id(cursor.getInt(16));
                transaction.setInvoice_no(cursor.getString(17));
                transaction1.setInvoice_no(cursor.getString(17));
                transaction.setPayment_status(cursor.getString(18));
                transaction.setContact_id(cursor.getInt(19));
                transaction1.setContact_id(cursor.getInt(19));
                transaction.setIs_quotation(cursor.getInt(39));
                transaction1.setIs_quotation(cursor.getInt(39));
                transaction.setShipping_address(cursor.getString(40));
                transaction1.setShipping_address(cursor.getString(40));
                transaction.setShipping_status(cursor.getString(41));
                transaction1.setShipping_status(cursor.getString(41));
                transaction.setDelivered_to(cursor.getString(42));
                transaction1.setDelivered_to(cursor.getString(42));
                transaction.setType(cursor.getString(20));
                transaction1.setType("Payment");

                String method= transactionPayementDbController.getTransactionById(cursor.getInt(0)).getMethod();
                transaction1.setMethod(method);
                transactions.add(transaction1);
                transactions.add(transaction);

            } while (cursor.moveToNext());
        }

        // mDb.close();
        return transactions;

    }

    public Transaction getTransactionOpeningStockById(Integer product_id, Integer location_id) {
        Transaction transaction = new Transaction();

        String selectQuery = "SELECT  * FROM " + TRANSACTION_TABLE_NAME + " WHERE " + TRANSACTION_TYPE + " = '" + OPENING_STOCK + "'"
                + " AND " + TRANSACTION_OPENING_STOCK_PRODUCT_ID + " = " + product_id
                + " AND " + TRANSACTION_LOCATION_ID + " = " + location_id;

        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {
            transaction.setFinal_total(cursor.getString(11));
            transaction.setLocation_id(cursor.getInt(1));
            transaction.setId((cursor.getInt(0)));
            transaction.setCustomer(cursor.getString(12));
            transaction.setShipping(cursor.getString(13));
            transaction.setTransaction_date(cursor.getString(14));
            transaction.setIs_sync(cursor.getString(9));
            transaction.setInvoice_no(cursor.getString(17));
            transaction.setPayment_status(cursor.getString(18));
            transaction.setContact_id(cursor.getInt(19));
            transaction.setPay_term_number(cursor.getString(25));
            transaction.setPay_term_type(cursor.getString(26));
            transaction.setShipping_charges(cursor.getString(27));
            transaction.setAdditional_notes(cursor.getString(28));
            transaction.setDiscount_amount(cursor.getString(4));
            transaction.setDiscount_type(cursor.getString(3));
            transaction.setStatus(cursor.getString(8));
            transaction.setType(cursor.getString(20));
            transaction.setExpense_category_id(cursor.getInt(29));
            transaction.setRecur_interval_type(cursor.getString(30));
            transaction.setIs_recurring(cursor.getInt(31));
            transaction.setRecur_repetitions(cursor.getInt(32));
            transaction.setRecur_interval(cursor.getInt(33));
            transaction.setPayment_for(cursor.getInt(34));
            transaction.setTransfer_parent_id(cursor.getInt(35));
            transaction.setAdjustment_type(cursor.getString(36));
            transaction.setTotal_amount_recovered(cursor.getString(37));
            transaction.setReturn_parent_id(cursor.getInt(38));
            transaction.setIs_quotation(cursor.getInt(39));
            transaction.setShipping_address(cursor.getString(40));
            transaction.setShipping_status(cursor.getString(41));
            transaction.setDelivered_to(cursor.getString(42));
            transaction.setTax_id(cursor.getInt(43));
            transaction.setShipping_details(cursor.getString(5));
            transaction.setOpening_stock_product_id(cursor.getInt(45));
            transaction.setTransaction_id(cursor.getInt(19));

        }

        // mDb.close();
        return transaction;

    }


    // update non sync to server
    public ArrayList<Transaction> filterTransaction(Integer locationId, String payment_status, String dateDebut, String dateFin, String type) {
        ArrayList<Transaction> transactions = new ArrayList<>();

        String selectQuery = "SELECT  * FROM " + TRANSACTION_TABLE_NAME + " WHERE " + TRANSACTION_STATUS + " = 'final'";
        String locationQuery = " AND " + ((locationId.equals(0)) ? (TRANSACTION_LOCATION_ID + " IS NOT NULL ") : (TRANSACTION_LOCATION_ID + " = '" + locationId + "'"));
        String statusQuery = " AND " + ((payment_status.equals("all")) ? (TRANSACTION_PAYEMENT_STATUS + " IS NOT NULL ") : (TRANSACTION_PAYEMENT_STATUS + " = '" + payment_status + "'"));
        String dateQuery = " AND " + ((dateDebut != null && dateFin != null) ? (TRANSACTION_DATE + " BETWEEN '" + dateDebut + "' AND '" + dateFin + "'") : (TRANSACTION_DATE + " IS NOT NULL "));
        String typeQuery = " AND " + TRANSACTION_TYPE + " = '" + type + "'";

        selectQuery += locationQuery + statusQuery + dateQuery + typeQuery;

        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {

            do {
                Transaction transaction = new Transaction();
                transaction.setFinal_total(cursor.getString(11));
                transaction.setLocation_id(cursor.getInt(1));
                transaction.setId(cursor.getInt(0));
                transaction.setCustomer(cursor.getString(12));
                transaction.setShipping(cursor.getString(13));
                transaction.setTransaction_date(cursor.getString(14));
                transaction.setIs_sync(cursor.getString(9));
                transaction.setStatus(cursor.getString(8));
                transaction.setCustomer_id(cursor.getInt(16));
                transaction.setInvoice_no(cursor.getString(17));
                transaction.setPayment_status(cursor.getString(18));
                transaction.setContact_id(cursor.getInt(19));
                transaction.setPay_term_number(cursor.getString(25));

                transaction.setShipping_charges(cursor.getString(27));
                transaction.setDiscount_amount(cursor.getString(4));
                transaction.setDiscount_type(cursor.getString(3));
                transaction.setStatus(cursor.getString(8));
                transaction.setType(cursor.getString(20));
                transaction.setAdditional_notes(cursor.getString(28));
                transaction.setExpense_category_id(cursor.getInt(29));
                transaction.setRecur_interval_type(cursor.getString(30));
                transaction.setIs_recurring(cursor.getInt(31));
                transaction.setRecur_repetitions(cursor.getInt(32));
                transaction.setRecur_interval(cursor.getInt(33));
                transaction.setPayment_for(cursor.getInt(34));
                transaction.setIs_quotation(cursor.getInt(39));
                transaction.setShipping_address(cursor.getString(40));
                transaction.setShipping_status(cursor.getString(41));
                transaction.setDelivered_to(cursor.getString(42));
                transaction.setRef_no(cursor.getString(21));
                transaction.setIs_deleted(cursor.getInt(46));

                transactions.add(transaction);

            } while (cursor.moveToNext());
        }

        // mDb.close();
        return transactions;

    }

    // update non sync to server
    public ArrayList<Transaction> filterTransaction(String status, Integer locationId, Integer contactId, String dateDebut, String dateFin, int isQuotation) {
        ArrayList<Transaction> transactions = new ArrayList<>();

        String selectQuery = "SELECT  * FROM " + TRANSACTION_TABLE_NAME + " WHERE " + TRANSACTION_STATUS + " = '" + status + "' AND " + TRANSACTION_IS_QUOTATION + " = " + isQuotation;
        String locationQuery = " AND " + ((locationId.equals(0)) ? (TRANSACTION_LOCATION_ID + " IS NOT NULL ") : (TRANSACTION_LOCATION_ID + " = '" + locationId + "'"));
        String customerQuery = " AND " + ((contactId.equals(0)) ? (TRANSACTION_LOCATION_ID + " IS NOT NULL ") : (TRANSACTION_CONTACT_ID + " = '" + contactId + "'"));
        String dateQuery = " AND " + ((dateDebut != null && dateFin != null) ? (TRANSACTION_DATE + " BETWEEN '" + dateDebut + "' AND '" + dateFin + "'") : (TRANSACTION_DATE + " IS NOT NULL "));

        selectQuery += locationQuery + dateQuery + customerQuery;

        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {

            do {
                Transaction transaction = new Transaction();
                transaction.setId(cursor.getInt(0));
                transaction.setFinal_total(cursor.getString(11));
                transaction.setLocation_id(cursor.getInt(1));
                transaction.setCustomer(cursor.getString(12));
                transaction.setShipping(cursor.getString(13));
                transaction.setTransaction_date(cursor.getString(14));
                transaction.setIs_sync(cursor.getString(9));
                transaction.setStatus(cursor.getString(8));
                transaction.setCustomer_id(cursor.getInt(16));
                transaction.setInvoice_no(cursor.getString(17));
                transaction.setPayment_status(cursor.getString(18));
                transaction.setContact_id(cursor.getInt(19));
                transaction.setPay_term_number(cursor.getString(25));

                transaction.setShipping_charges(cursor.getString(27));
                transaction.setDiscount_amount(cursor.getString(4));
                transaction.setDiscount_type(cursor.getString(3));
                transaction.setStatus(cursor.getString(8));
                transaction.setType(cursor.getString(20));
                transaction.setTransfer_parent_id(cursor.getInt(35));
                transaction.setAdjustment_type(cursor.getString(36));
                transaction.setTotal_amount_recovered(cursor.getString(37));
                transaction.setAdditional_notes(cursor.getString(28));

                transaction.setIs_quotation(cursor.getInt(39));
                transaction.setShipping_address(cursor.getString(40));
                transaction.setShipping_status(cursor.getString(41));
                transaction.setDelivered_to(cursor.getString(42));
                transactions.add(transaction);

            } while (cursor.moveToNext());
        }

        // mDb.close();
        return transactions;

    }

    // update non sync to server
    public Boolean isTransactionSync(String status) {

        String selectQuery = "SELECT * FROM " + TRANSACTION_TABLE_NAME + " WHERE " + TRANSACTION_IS_SYNC + " = '" + status + "'";
        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.getCount() == 0) {
            return false;
        } else {
            return true;
        }

        // mDb.close();
    }

    // update non sync to server
    public ArrayList<Transaction> getSyncTransaction(String sync) {
        ArrayList<Transaction> transactions = new ArrayList<>();

        String selectQuery = "SELECT  * FROM " + TRANSACTION_TABLE_NAME + " WHERE " + TRANSACTION_IS_SYNC + " = '" + sync + "'";
        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {

            do {
                Transaction transaction = new Transaction();

                transaction.setFinal_total(cursor.getString(11));
                transaction.setLocation_id(cursor.getInt(1));
                transaction.setId(cursor.getInt(0));
                transaction.setCustomer(cursor.getString(12));
                transaction.setShipping(cursor.getString(13));
                transaction.setTransaction_date(cursor.getString(14));
                transaction.setIs_sync(cursor.getString(9));
                transaction.setCustomer_id(cursor.getInt(16));
                transaction.setContact_id(cursor.getInt(16));
                transaction.setInvoice_no(cursor.getString(17));
                transaction.setTransaction_id(cursor.getInt(19));
                transaction.setPayment_status(cursor.getString(18));
                // product.setImage_product(cursor.getString(28));

                transactions.add(transaction);

            } while (cursor.moveToNext());

        }

        // mDb.close();

        return transactions;

    }

    // update non sync to server
    public ArrayList<Transaction> getSyncSell(String sync) {
        ArrayList<Transaction> transactions = new ArrayList<>();

        String selectQuery = "SELECT  * FROM " + TRANSACTION_TABLE_NAME + " WHERE " + TRANSACTION_IS_SYNC + " = '" + sync + "'" + " AND "
                + TRANSACTION_TYPE + " = 'sell' " + " AND " + TRANSACTION_STATUS + " IN ('final', 'draft')" + " AND " + TRANSACTION_IS_DELETED + " = " + 0;

        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {

            do {
                Transaction transaction = new Transaction();
                transaction.setFinal_total(cursor.getString(11));

                Business_location business_location= businessLocationDbController.getStationById(cursor.getInt(1));
                transaction.setLocation_id(business_location.getLocation_server_id());
                transaction.setId((cursor.getInt(0)));
                transaction.setCustomer(cursor.getString(12));
                transaction.setShipping(cursor.getString(13));
                transaction.setTransaction_date(cursor.getString(14));
                transaction.setIs_sync(cursor.getString(9));
                transaction.setInvoice_no(cursor.getString(17));
                transaction.setPayment_status(cursor.getString(18));
                transaction.setContact_id(cursor.getInt(19));
                transaction.setPay_term_number(cursor.getString(25));
                transaction.setPay_term_type(cursor.getString(26));
                transaction.setShipping_charges(cursor.getString(27));
                transaction.setAdditional_notes(cursor.getString(28));
                transaction.setDiscount_amount(cursor.getString(4));
                transaction.setDiscount_type(cursor.getString(3));
                transaction.setStatus(cursor.getString(8));
                transaction.setType(cursor.getString(20));
                transaction.setExpense_category_id(cursor.getInt(29));
                transaction.setRecur_interval_type(cursor.getString(30));
                transaction.setIs_recurring(cursor.getInt(31));
                transaction.setRecur_repetitions(cursor.getInt(32));
                transaction.setRecur_interval(cursor.getInt(33));
                transaction.setPayment_for(cursor.getInt(34));
                transaction.setTransfer_parent_id(cursor.getInt(35));
                transaction.setAdjustment_type(cursor.getString(36));
                transaction.setTotal_amount_recovered(cursor.getString(37));
                transaction.setReturn_parent_id(cursor.getInt(38));
                transaction.setIs_quotation(cursor.getInt(39));
                transaction.setShipping_address(cursor.getString(40));
                transaction.setShipping_status(cursor.getString(41));
                transaction.setDelivered_to(cursor.getString(42));
                transaction.setTax_id(cursor.getInt(43));
                transaction.setShipping_details(cursor.getString(5));
                transaction.setOpening_stock_product_id(cursor.getInt(45));
                transaction.setBusiness_id(cursor.getInt(44));
                transaction.setTransaction_id(cursor.getInt(0));
                transaction.setCreated_by(cursor.getInt(24));

                ArrayList<Sell_lines> sell_linesArrayList = transactionSellLineDbController.getSyncSellLineByTransaction(cursor.getInt(0));
                ArrayList<Transaction> payment_linesArrayList = transactionPayementDbController.getAllTransactionById(cursor.getInt(0));

                transaction.setProducts(sell_linesArrayList);
                transaction.setPayments(payment_linesArrayList);


                transactions.add(transaction);

            } while (cursor.moveToNext());

        }

        // mDb.close();

        return transactions;

    }



    // update non sync to server
    public ArrayList<Transaction> getExpenseSell(String sync) {
        ArrayList<Transaction> transactions = new ArrayList<>();

        String selectQuery = "SELECT  * FROM " + TRANSACTION_TABLE_NAME + " WHERE " + TRANSACTION_IS_SYNC + " = '" + sync + "'" + " AND "
                + TRANSACTION_TYPE + " = 'expense' " + " AND " + TRANSACTION_STATUS + " IN ('final', 'draft')";
        Cursor cursor = mDb.rawQuery(selectQuery, null);
        if (cursor.moveToFirst())
        {
            do {
                Transaction transaction = new Transaction();
                transaction.setFinal_total(cursor.getString(11));
                transaction.setLocation_id(cursor.getInt(1));
                transaction.setId((cursor.getInt(0)));
                transaction.setCustomer(cursor.getString(12));
                transaction.setShipping(cursor.getString(13));
                transaction.setTransaction_date(cursor.getString(14));
                transaction.setIs_sync(cursor.getString(9));
                transaction.setInvoice_no(cursor.getString(17));
                transaction.setPayment_status(cursor.getString(18));
                transaction.setContact_id(cursor.getInt(19));
                transaction.setPay_term_number(cursor.getString(25));
                transaction.setPay_term_type(cursor.getString(26));
                transaction.setShipping_charges(cursor.getString(27));
                transaction.setAdditional_notes(cursor.getString(28));
                transaction.setDiscount_amount(cursor.getString(4));
                transaction.setDiscount_type(cursor.getString(3));
                transaction.setStatus(cursor.getString(8));
                transaction.setType(cursor.getString(20));
                transaction.setExpense_category_id(cursor.getInt(29));
                transaction.setRecur_interval_type(cursor.getString(30));
                transaction.setIs_recurring(cursor.getInt(31));
                transaction.setRecur_repetitions(cursor.getInt(32));
                transaction.setRecur_interval(cursor.getInt(33));
                transaction.setPayment_for(cursor.getInt(34));
                transaction.setTransfer_parent_id(cursor.getInt(35));
                transaction.setAdjustment_type(cursor.getString(36));
                transaction.setTotal_amount_recovered(cursor.getString(37));
                transaction.setReturn_parent_id(cursor.getInt(38));
                transaction.setIs_quotation(cursor.getInt(39));
                transaction.setShipping_address(cursor.getString(40));
                transaction.setShipping_status(cursor.getString(41));
                transaction.setDelivered_to(cursor.getString(42));
                transaction.setTax_id(cursor.getInt(43));
                transaction.setShipping_details(cursor.getString(5));
                transaction.setOpening_stock_product_id(cursor.getInt(45));
                transaction.setBusiness_id(cursor.getInt(44));
                transaction.setTransaction_id(cursor.getInt(0));
                transaction.setCreated_by(cursor.getInt(24));

                ArrayList<Sell_lines> sell_linesArrayList = transactionSellLineDbController.getSyncSellLineByTransaction(cursor.getInt(0));
                ArrayList<Transaction> payment_linesArrayList = transactionPayementDbController.getAllTransactionById(cursor.getInt(0));

                transaction.setProducts(sell_linesArrayList);
                transaction.setPayments(payment_linesArrayList);


                transactions.add(transaction);

            } while (cursor.moveToNext());

        }

        // mDb.close();

        return transactions;

    }

    public ArrayList<Transaction> getTransactionBetween(String dateDebut, String dateFin, String status) {
        ArrayList<Transaction> transactions = new ArrayList<>();

        String selectQuery = "SELECT * FROM  " + TRANSACTION_TABLE_NAME + " WHERE " + TRANSACTION_DATE + " BETWEEN '" + dateDebut + "' AND '" + dateFin + "'" + " AND " + TRANSACTION_STATUS + " = '" + status + "'";
        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {

            do {
                Transaction transaction = new Transaction();

                transaction.setFinal_total(cursor.getString(11));
                transaction.setId(Integer.parseInt(cursor.getString(0)));
                transaction.setCustomer(cursor.getString(12));
                transaction.setShipping(cursor.getString(13));
                transaction.setIs_sync(cursor.getString(9));
                transaction.setTransaction_date(cursor.getString(14));
                transaction.setCustomer_id(Integer.parseInt(cursor.getString(16)));
                transaction.setIs_quotation(cursor.getInt(39));
                transaction.setShipping_address(cursor.getString(40));
                transaction.setShipping_status(cursor.getString(41));
                transaction.setDelivered_to(cursor.getString(42));

                // product.setImage_product(cursor.getString(28));

                transactions.add(transaction);

            } while (cursor.moveToNext());

        }

        // mDb.close();

        return transactions;

    }

    public void clear() {
        mDb.execSQL("DELETE FROM " + TRANSACTION_TABLE_NAME);
    }


    public int getLastTransactionID() {
        int _id = 0;
        String selectQuery = "SELECT * FROM  " + TRANSACTION_TABLE_NAME + " ORDER BY " + TRANSACTION_ID + " DESC LIMIT 1";
        Cursor cursor = mDb.rawQuery(selectQuery, null);
        if (cursor.moveToLast()) {
            _id = cursor.getInt(0);
        }
        cursor.close();
        return _id;
    }

    public Transaction getOpeningBalanceContact(Integer contact_id) {
        Transaction transaction=new Transaction();
        String selectQuery = "SELECT * FROM  " + TRANSACTION_TABLE_NAME + " WHERE " + TRANSACTION_CONTACT_ID + " = '" + contact_id + "'" + " AND " + TRANSACTION_TYPE + " = 'opening_balance'";
        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {
            transaction.setFinal_total(cursor.getString(11));
            transaction.setLocation_id(cursor.getInt(1));
            transaction.setId((cursor.getInt(0)));
            transaction.setCustomer(cursor.getString(12));
            transaction.setShipping(cursor.getString(13));
            transaction.setTransaction_date(cursor.getString(14));
            transaction.setIs_sync(cursor.getString(9));
            transaction.setInvoice_no(cursor.getString(17));
            transaction.setPayment_status(cursor.getString(18));
            transaction.setContact_id(cursor.getInt(19));
            transaction.setPay_term_number(cursor.getString(25));
            transaction.setPay_term_type(cursor.getString(26));
            transaction.setShipping_charges(cursor.getString(27));
            transaction.setAdditional_notes(cursor.getString(28));
            transaction.setDiscount_amount(cursor.getString(4));
            transaction.setDiscount_type(cursor.getString(3));
            transaction.setStatus(cursor.getString(8));
            transaction.setType(cursor.getString(20));
            transaction.setExpense_category_id(cursor.getInt(29));
            transaction.setRecur_interval_type(cursor.getString(30));
            transaction.setIs_recurring(cursor.getInt(31));
            transaction.setRecur_repetitions(cursor.getInt(32));
            transaction.setRecur_interval(cursor.getInt(33));
            transaction.setPayment_for(cursor.getInt(34));
            transaction.setTransfer_parent_id(cursor.getInt(35));
            transaction.setAdjustment_type(cursor.getString(36));
            transaction.setTotal_amount_recovered(cursor.getString(37));
            transaction.setReturn_parent_id(cursor.getInt(38));
            transaction.setIs_quotation(cursor.getInt(39));
            transaction.setShipping_address(cursor.getString(40));
            transaction.setShipping_status(cursor.getString(41));
            transaction.setDelivered_to(cursor.getString(42));
            transaction.setTax_id(cursor.getInt(43));
            transaction.setShipping_details(cursor.getString(5));
            transaction.setOpening_stock_product_id(cursor.getInt(45));
            transaction.setRef_no(cursor.getString(21));
          //  transaction.setTransaction_id(cursor.getInt(19));
        }else {
            transaction =null;
        }
        // mDb.close();
        return transaction;

    }
    public String getParentSaleInvoiceNumber(String return_parent_id) {
        String _invoice_no = "";
        String selectQuery = "SELECT " + TRANSACTION_INVOICE_NO + " FROM  " + TRANSACTION_TABLE_NAME + " WHERE " + TRANSACTION_ID + " = '" + return_parent_id + "'";
        Cursor cursor = mDb.rawQuery(selectQuery, null);
        if (cursor.moveToFirst()) {
            _invoice_no = cursor.getString(0);
        }
        cursor.close();
        return _invoice_no;
    }
    public String getLastReturnSalesInvoiceNo() {
        String invoice_no = "";
        String selectQuery = "SELECT " + TRANSACTION_INVOICE_NO + " FROM  " + TRANSACTION_TABLE_NAME + " WHERE " + TRANSACTION_TYPE + " = 'sell_return'" + " ORDER BY " + TRANSACTION_ID + " DESC LIMIT 1";
        Cursor cursor = mDb.rawQuery(selectQuery, null);
        if (cursor.moveToLast()) {
            invoice_no = cursor.getString(0);
        }
        cursor.close();
        return invoice_no;
    }
    public String getLastSalesInvoiceNo() {
        String invoice_no = "";
        String selectQuery = "SELECT " + TRANSACTION_REF_NO + " FROM  " + TRANSACTION_TABLE_NAME + " WHERE " + TRANSACTION_TYPE + " = 'sell'" + " ORDER BY " + TRANSACTION_ID + " DESC LIMIT 1";
        Cursor cursor = mDb.rawQuery(selectQuery, null);
        if (cursor.moveToLast()) {
           if (cursor.getString(0)!=null)invoice_no = cursor.getString(0);
        }
        cursor.close();
        return invoice_no;
    }
    public String getLastPurchaseTransferInvoiceNo() {
        String invoice_no = "";
        String selectQuery = "SELECT " + TRANSACTION_REF_NO + " FROM  " + TRANSACTION_TABLE_NAME + " WHERE " + TRANSACTION_TYPE + " = 'purchase_transfer'" + " ORDER BY " + TRANSACTION_ID + " DESC LIMIT 1";
        Cursor cursor = mDb.rawQuery(selectQuery, null);
        if (cursor.moveToLast()) {
           if (cursor.getString(0)!=null)invoice_no = cursor.getString(0);
        }
        cursor.close();
        return invoice_no;
    }
    public String getLastSellTransferInvoiceNo() {
        String invoice_no = "";
        String selectQuery = "SELECT " + TRANSACTION_REF_NO + " FROM  " + TRANSACTION_TABLE_NAME + " WHERE " + TRANSACTION_TYPE + " = 'sell_transfer'" + " ORDER BY " + TRANSACTION_ID + " DESC LIMIT 1";
        Cursor cursor = mDb.rawQuery(selectQuery, null);
        if (cursor.moveToLast()) {
           if (cursor.getString(0)!=null)invoice_no = cursor.getString(0);
        }
        cursor.close();
        return invoice_no;
    }
    public String getLastStockAdjInvoiceNo() {
        String invoice_no = "";
        String selectQuery = "SELECT " + TRANSACTION_REF_NO + " FROM  " + TRANSACTION_TABLE_NAME + " WHERE " + TRANSACTION_TYPE + " = 'stock_adjustment'" + " ORDER BY " + TRANSACTION_ID + " DESC LIMIT 1";
        Cursor cursor = mDb.rawQuery(selectQuery, null);
        if (cursor.moveToLast()) {
           if (cursor.getString(0)!=null)invoice_no = cursor.getString(0);
        }
        cursor.close();
        return invoice_no;
    }
    public String getLastPurchaseInvoiceNo() {
        String invoice_no = "";
        String selectQuery = "SELECT " + TRANSACTION_REF_NO + " FROM  " + TRANSACTION_TABLE_NAME + " WHERE " + TRANSACTION_TYPE + " = 'purchase'" + " ORDER BY " + TRANSACTION_ID + " DESC LIMIT 1";
        Cursor cursor = mDb.rawQuery(selectQuery, null);
        if (cursor.moveToLast()) {
           if (cursor.getString(0)!=null)invoice_no = cursor.getString(0);
        }
        cursor.close();
        return invoice_no;
    }
    public String getLastExpenseInvoiceNo() {
        String invoice_no = "";
        String selectQuery = "SELECT " + TRANSACTION_REF_NO + " FROM  " + TRANSACTION_TABLE_NAME + " WHERE " + TRANSACTION_TYPE + " = 'expense'" + " OR " + TRANSACTION_TYPE + " = 'expense_refund'" + " ORDER BY " + TRANSACTION_ID + " DESC LIMIT 1";
        Cursor cursor = mDb.rawQuery(selectQuery, null);
        if (cursor.moveToLast()) {
           if (cursor.getString(0)!=null)invoice_no = cursor.getString(0);
        }
        cursor.close();
        return invoice_no;
    }
    public int updateSellReturnData(Transaction transaction) {
        // Create a new map of values, where column names are the keys
        ContentValues pValues = new ContentValues();
        pValues.put(TRANSACTION_DISCOUNT_TYPE, transaction.getDiscount_type());
        pValues.put(TRANSACTION_DISCOUNT_AMOUNT, transaction.getDiscount_amount());
        pValues.put(TRANSACTION_FINAL_TOTAL, transaction.getFinal_total());
        pValues.put(TRANSACTION_DATE, transaction.getTransaction_date());
        int newRowId = mDb.update(TRANSACTION_TABLE_NAME, pValues, TRANSACTION_ID + " = '" + transaction.getId() + "'", null);
        return newRowId;
    }
    public ArrayList<Transaction> filterSellReturnList(Integer locationId, Integer customer_id, String dateDebut, String dateFin) {
        ArrayList<Transaction> transactions = new ArrayList<>();

        String selectQuery = "SELECT  * FROM " + TRANSACTION_TABLE_NAME + " WHERE " + TRANSACTION_TYPE + " = '" + SELL_RETURN + "'";
        String locationQuery = " AND " + ((locationId.equals(0)) ? (TRANSACTION_LOCATION_ID + " IS NOT NULL ") : (TRANSACTION_LOCATION_ID + " = '" + locationId + "'"));

        String customerQuery = " AND " + ((customer_id == 0) ? (TRANSACTION_CONTACT_ID + " IS NOT NULL") : (TRANSACTION_CONTACT_ID + " ='" + customer_id + "'"));
        String dateQuery = " AND " + ((dateDebut.equals("") || dateFin.equals("")) ? (TRANSACTION_DATE + " IS NOT NULL") : (TRANSACTION_DATE + " BETWEEN '" + dateDebut + "' AND '" + dateFin + "'"));

        selectQuery += dateQuery + customerQuery + locationQuery;

        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {
            do {
                Transaction transaction = new Transaction();
                transaction.setId(cursor.getInt(0));
                transaction.setFinal_total(cursor.getString(11));
                transaction.setLocation_id(cursor.getInt(1));
                transaction.setCustomer(cursor.getString(12));
                transaction.setShipping(cursor.getString(13));
                transaction.setTransaction_date(cursor.getString(14));
                transaction.setIs_sync(cursor.getString(9));
                transaction.setStatus(cursor.getString(8));
                transaction.setCustomer_id(cursor.getInt(16));
                transaction.setInvoice_no(cursor.getString(17));
                transaction.setPayment_status(cursor.getString(18));
                transaction.setContact_id(cursor.getInt(19));
                transaction.setPay_term_number(cursor.getString(25));
                transaction.setShipping_charges(cursor.getString(27));
                transaction.setDiscount_amount(cursor.getString(4));
                transaction.setDiscount_type(cursor.getString(3));
                transaction.setStatus(cursor.getString(8));
                transaction.setType(cursor.getString(20));
                transaction.setTransfer_parent_id(cursor.getInt(35));
                transaction.setAdjustment_type(cursor.getString(36));
                transaction.setTotal_amount_recovered(cursor.getString(37));
                transaction.setAdditional_notes(cursor.getString(28));
                transactions.add(transaction);
            } while (cursor.moveToNext());
        }

        // mDb.close();
        return transactions;

    }
    public int getParentSaleCount(int return_parent_id) {
        int count = 0;
        String selectQuery = "SELECT * FROM  " + TRANSACTION_TABLE_NAME + " WHERE " + TRANSACTION_RETURN_PARENT_ID + " = '" + return_parent_id + "'";
        Cursor cursor = mDb.rawQuery(selectQuery, null);
        count = cursor.getCount();
        cursor.close();
        return count;
    }
    public int updatePaymentData(Transaction transaction) {
        // Create a new map of values, where column names are the keys
        ContentValues pValues = new ContentValues();
        pValues.put(TRANSACTION_PAYEMENT_STATUS, transaction.getPayment_status());
        int newRowId = mDb.update(TRANSACTION_TABLE_NAME, pValues, TRANSACTION_ID + " = '" + transaction.getId() + "'", null);
        return newRowId;
    }
}