package com.rising.high.tech.bigultimatenavdraw.db;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.util.Log;

import com.rising.high.tech.bigultimatenavdraw.model.Transaction;
import com.rising.high.tech.bigultimatenavdraw.util.StringFormat;

import java.util.ArrayList;

public class TransactionPayementDbController extends DBController {
    final String TAG = getClass().getSimpleName();
    private Context _context;

    public static final String TRANSACTION_PAY_TABLE_NAME = "transaction_payements";

    public static final String TRANSACTION_PAY_ID = "id";
    public static final String TRANSACTION_PAY_TRANSACTION_ID = "transaction_id";
    public static final String TRANSACTION_PAY_BUSINESS_ID = "business_id";
    public static final String TRANSACTION_PAY_AMOUNT = "amount";
    public static final String TRANSACTION_PAY_PARENT_ID = "parent_id";
    public static final String TRANSACTION_PAY_METHOD = "method";
    public static final String TRANSACTION_PAY_TRANSACTION_NO = "transaction_no";
    public static final String TRANSACTION_PAY_PAID_ON = "paid_on";
    public static final String TRANSACTION_PAY_IS_RETURN = "is_return";
    public static final String TRANSACTION_PAY_CREATED_BY = "created_by";
    public static final String TRANSACTION_PAY_PAYMENET_FOR = "payment_for";
    public static final String TRANSACTION_PAY_NOTE = "note";
    public static final String TRANSACTION_PAY_CARD_TYPE = "card_type";
    public static final String TRANSACTION_PAY_PAYMENET_REF_NO = "payment_ref_no";
    public static final String TRANSACTION_PAY_CARD_TRANSACTION_NUMBER = "card_transaction_number";
    public static final String TRANSACTION_PAY_CARD_NUMBER = "card_number";
    public static final String TRANSACTION_PAY_CARD_HOLDER_NAME = "card_holder_name";
    public static final String TRANSACTION_PAY_CARD_MONTH = "card_month";
    public static final String TRANSACTION_PAY_CARD_YEAR = "card_year";
    public static final String TRANSACTION_PAY_CARD_SECURITY = "card_security";
    public static final String TRANSACTION_PAY_CHEQUE_NUMBER = "cheque_number";
    public static final String TRANSACTION_PAY_BANK_ACCOUNT_NUMBER = "bank_account_number";
    public static final String TRANSACTION_PAY_DOCUMENT = "document";
    public static final String TRANSACTION_PAY_IS_ADVANCE = "is_advance";

    public static final String TRANSACTION_PAY_TABLE_CREATE =
            "CREATE TABLE " + TRANSACTION_PAY_TABLE_NAME + " (" +
                    TRANSACTION_PAY_ID + " INTEGER PRIMARY KEY AUTOINCREMENT, " +
                    TRANSACTION_PAY_TRANSACTION_ID + " INTEGER, " +
                    TRANSACTION_PAY_BUSINESS_ID + " INTEGER, " +
                    TRANSACTION_PAY_AMOUNT + " TEXT, " +
                    TRANSACTION_PAY_PARENT_ID + " INTEGER, " +
                    TRANSACTION_PAY_METHOD + " TEXT, " +
                    TRANSACTION_PAY_TRANSACTION_NO + " TEXT, " +
                    TRANSACTION_PAY_PAID_ON + " TEXT, " +
                    TRANSACTION_PAY_IS_RETURN + " TEXT, " +
                    TRANSACTION_PAY_CREATED_BY + " TEXT, " +
                    TRANSACTION_PAY_PAYMENET_FOR + " INTEGER, " +
                    TRANSACTION_PAY_NOTE + " TEXT, " +
                    TRANSACTION_PAY_CARD_TYPE + " TEXT, " +
                    TRANSACTION_PAY_PAYMENET_REF_NO + " TEXT, " +
                    TRANSACTION_PAY_CARD_TRANSACTION_NUMBER + " TEXT, " +
                    TRANSACTION_PAY_CARD_NUMBER + " TEXT, " +
                    TRANSACTION_PAY_CARD_HOLDER_NAME + " TEXT, " +
                    TRANSACTION_PAY_CARD_MONTH + " TEXT, " +
                    TRANSACTION_PAY_CARD_YEAR + " TEXT, " +
                    TRANSACTION_PAY_CARD_SECURITY + " TEXT, " +
                    TRANSACTION_PAY_CHEQUE_NUMBER + " TEXT, " +
                    TRANSACTION_PAY_BANK_ACCOUNT_NUMBER + " TEXT, " +
                    TRANSACTION_PAY_DOCUMENT + " TEXT, " +
                    TRANSACTION_PAY_IS_ADVANCE + " TEXT) ;";


    public static final String TRANSACTION_PAY_TABLE_DROP = "DROP TABLE IF EXISTS " + TRANSACTION_PAY_TABLE_NAME + ";";

    public TransactionPayementDbController(Context context) {
        super(context);
        _context=context;
    }

    public int insert(Transaction transaction) {
        // Create a new map of values, where column names are the keys
        ContentValues pValues = new ContentValues();

        pValues.put(TRANSACTION_PAY_ID, transaction.getId());
        pValues.put(TRANSACTION_PAY_TRANSACTION_ID, transaction.getTransaction_id());
        pValues.put(TRANSACTION_PAY_BUSINESS_ID, transaction.getBusiness_id());
        pValues.put(TRANSACTION_PAY_AMOUNT, transaction.getAmount());
        pValues.put(TRANSACTION_PAY_PARENT_ID, transaction.getParent_id());
        pValues.put(TRANSACTION_PAY_METHOD, transaction.getMethod());
        pValues.put(TRANSACTION_PAY_TRANSACTION_NO, transaction.getTransaction_no());
        pValues.put(TRANSACTION_PAY_PAID_ON, transaction.getPaid_on());
        pValues.put(TRANSACTION_PAY_IS_RETURN, transaction.getIs_return());
        pValues.put(TRANSACTION_PAY_CREATED_BY, transaction.getCreated_by());
        pValues.put(TRANSACTION_PAY_PAYMENET_FOR, transaction.getPayment_for());
        pValues.put(TRANSACTION_PAY_NOTE, transaction.getNote());
        pValues.put(TRANSACTION_PAY_CARD_TYPE, transaction.getCard_type());
        pValues.put(TRANSACTION_PAY_PAYMENET_REF_NO, transaction.getPayment_ref_no());
        pValues.put(TRANSACTION_PAY_CARD_TRANSACTION_NUMBER, transaction.getCard_transaction_number());
        pValues.put(TRANSACTION_PAY_CARD_NUMBER, transaction.getCard_number());
        pValues.put(TRANSACTION_PAY_CARD_HOLDER_NAME, transaction.getCard_holder_name());
        pValues.put(TRANSACTION_PAY_CARD_MONTH, transaction.getCard_month());
        pValues.put(TRANSACTION_PAY_CARD_YEAR, transaction.getCard_year());
        pValues.put(TRANSACTION_PAY_CARD_SECURITY, transaction.getCard_security());
        pValues.put(TRANSACTION_PAY_CHEQUE_NUMBER, transaction.getCheque_number());
        pValues.put(TRANSACTION_PAY_BANK_ACCOUNT_NUMBER, transaction.getBank_account_number());
        pValues.put(TRANSACTION_PAY_DOCUMENT, transaction.getDocument());
        pValues.put(TRANSACTION_PAY_IS_ADVANCE, transaction.getIs_advance());


        int newRowId = (int) mDb.insert(TRANSACTION_PAY_TABLE_NAME, null, pValues); //renvoie l'id de l'enregistrement créé
        return newRowId;
    }


    public int insertLocal(Transaction transaction) {
        // Create a new map of values, where column names are the keys
        ContentValues pValues = new ContentValues();

        pValues.put(TRANSACTION_PAY_TRANSACTION_ID, transaction.getTransaction_id());
        pValues.put(TRANSACTION_PAY_BUSINESS_ID, transaction.getBusiness_id());
        pValues.put(TRANSACTION_PAY_AMOUNT, transaction.getAmount());
        pValues.put(TRANSACTION_PAY_PARENT_ID, transaction.getParent_id());
        pValues.put(TRANSACTION_PAY_METHOD, transaction.getMethod());
        pValues.put(TRANSACTION_PAY_TRANSACTION_NO, transaction.getTransaction_no());
        pValues.put(TRANSACTION_PAY_PAID_ON, transaction.getPaid_on());
        pValues.put(TRANSACTION_PAY_IS_RETURN, transaction.getIs_return());
        pValues.put(TRANSACTION_PAY_CREATED_BY, transaction.getCreated_by());
        pValues.put(TRANSACTION_PAY_PAYMENET_FOR, transaction.getPayment_for());
        pValues.put(TRANSACTION_PAY_NOTE, transaction.getNote());
        pValues.put(TRANSACTION_PAY_CARD_TYPE, transaction.getCard_type());
        pValues.put(TRANSACTION_PAY_CARD_TRANSACTION_NUMBER, transaction.getCard_transaction_number());
        pValues.put(TRANSACTION_PAY_CARD_NUMBER, transaction.getCard_number());
        pValues.put(TRANSACTION_PAY_CARD_HOLDER_NAME, transaction.getCard_holder_name());
        pValues.put(TRANSACTION_PAY_CARD_MONTH, transaction.getCard_month());
        pValues.put(TRANSACTION_PAY_CARD_YEAR, transaction.getCard_year());
        pValues.put(TRANSACTION_PAY_CARD_SECURITY, transaction.getCard_security());
        pValues.put(TRANSACTION_PAY_CHEQUE_NUMBER, transaction.getCheque_number());
        pValues.put(TRANSACTION_PAY_BANK_ACCOUNT_NUMBER, transaction.getBank_account_number());
        pValues.put(TRANSACTION_PAY_DOCUMENT, transaction.getDocument());
        pValues.put(TRANSACTION_PAY_IS_ADVANCE, transaction.getIs_advance());
        pValues.put(TRANSACTION_PAY_PAYMENET_REF_NO, StringFormat.generateRefPaymentNo(_context));

        int newRowId = (int) mDb.insert(TRANSACTION_PAY_TABLE_NAME, null, pValues); //renvoie l'id de l'enregistrement créé
        return newRowId;
    }


    public int updatePayement(Transaction transaction) {
        // Create a new map of values, where column names are the keys
        ContentValues pValues = new ContentValues();

        pValues.put(TRANSACTION_PAY_TRANSACTION_ID, transaction.getTransaction_id());
        pValues.put(TRANSACTION_PAY_BUSINESS_ID, transaction.getBusiness_id());
        pValues.put(TRANSACTION_PAY_AMOUNT, transaction.getAmount());
        pValues.put(TRANSACTION_PAY_PARENT_ID, transaction.getParent_id());
        pValues.put(TRANSACTION_PAY_METHOD, transaction.getMethod());
        pValues.put(TRANSACTION_PAY_TRANSACTION_NO, transaction.getTransaction_no());
        pValues.put(TRANSACTION_PAY_PAID_ON, transaction.getPaid_on());
        pValues.put(TRANSACTION_PAY_IS_RETURN, transaction.getIs_return());
        pValues.put(TRANSACTION_PAY_CREATED_BY, transaction.getCreated_by());
        pValues.put(TRANSACTION_PAY_PAYMENET_FOR, transaction.getPayment_for());
        pValues.put(TRANSACTION_PAY_NOTE, transaction.getNote());
        pValues.put(TRANSACTION_PAY_CARD_TYPE, transaction.getCard_type());
        pValues.put(TRANSACTION_PAY_PAYMENET_REF_NO, transaction.getPayment_ref_no());
        pValues.put(TRANSACTION_PAY_CARD_TRANSACTION_NUMBER, transaction.getCard_transaction_number());
        pValues.put(TRANSACTION_PAY_CARD_NUMBER, transaction.getCard_number());
        pValues.put(TRANSACTION_PAY_CARD_HOLDER_NAME, transaction.getCard_holder_name());
        pValues.put(TRANSACTION_PAY_CARD_MONTH, transaction.getCard_month());
        pValues.put(TRANSACTION_PAY_CARD_YEAR, transaction.getCard_year());
        pValues.put(TRANSACTION_PAY_CARD_SECURITY, transaction.getCard_security());
        pValues.put(TRANSACTION_PAY_CHEQUE_NUMBER, transaction.getCheque_number());
        pValues.put(TRANSACTION_PAY_BANK_ACCOUNT_NUMBER, transaction.getBank_account_number());
        pValues.put(TRANSACTION_PAY_DOCUMENT, transaction.getDocument());
        pValues.put(TRANSACTION_PAY_IS_ADVANCE, transaction.getIs_advance());

        int newRowId = mDb.update(TRANSACTION_PAY_TABLE_NAME, pValues, TRANSACTION_PAY_ID + " = '" + transaction.getId() + "'", null); //renvoie l'id de l'enregistrement créé
        return newRowId;
    }

    // Insert all product
    public void fill(ArrayList<Transaction> transactions) {
        if (!transactions.isEmpty()) {
            for (Transaction transaction : transactions) {
                this.insert(transaction);
            }
        }
    }


    public void deletePayment(int id) {
        mDb.execSQL("delete from " + TRANSACTION_PAY_TABLE_NAME + " WHERE " + TRANSACTION_PAY_ID + " = " + id);
    }


    // update non sync to server
    public ArrayList<Transaction> getAllTransaction() {
        ArrayList<Transaction> transactions = new ArrayList<>();

        String selectQuery = "SELECT  * FROM " + TRANSACTION_PAY_TABLE_NAME;
        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {

            do {
                Transaction transaction = new Transaction();

                transaction.setId(cursor.getInt(0));
                transaction.setTransaction_id(cursor.getInt(1));
                transaction.setBusiness_id(cursor.getInt(2));
                transaction.setAmount(cursor.getInt(3));
                transaction.setParent_id(cursor.getInt(4));
                transaction.setMethod(cursor.getString(5));
                transaction.setTransaction_no(cursor.getString(6));
                transaction.setPaid_on(cursor.getString(7));
                transaction.setIs_return(cursor.getString(8));
                transaction.setCreated_by(cursor.getInt(9));
                transaction.setPayment_for(cursor.getInt(10));
                transaction.setNote(cursor.getString(11));
                transaction.setCard_type(cursor.getString(12));
                transaction.setPayment_ref_no(cursor.getString(13));
                transaction.setCard_transaction_number(cursor.getString(14));
                transaction.setCard_number(cursor.getString(15));
                transaction.setCard_holder_name(cursor.getString(16));
                transaction.setCard_month(cursor.getString(17));
                transaction.setCard_year(cursor.getString(18));
                transaction.setCard_security(cursor.getString(19));
                transaction.setCheque_number(cursor.getString(20));
                transaction.setBank_account_number(cursor.getString(21));
                transaction.setDocument(cursor.getString(22));
                transaction.setIs_advance(cursor.getString(23));


                transactions.add(transaction);

            } while (cursor.moveToNext());
        }

        // mDb.close();
        return transactions;

    }

    // update non sync to server
    public Transaction getTransactionById(Integer transaction_id) {
        Transaction transaction = new Transaction();

        String selectQuery = "SELECT  * FROM " + TRANSACTION_PAY_TABLE_NAME + " WHERE " + TRANSACTION_PAY_TRANSACTION_ID + " = " + transaction_id;
        Cursor cursor = mDb.rawQuery(selectQuery, null);
        if (cursor.moveToFirst()) {
            transaction.setId(cursor.getInt(0));
            transaction.setTransaction_id(cursor.getInt(1));
            transaction.setBusiness_id(cursor.getInt(2));
            transaction.setAmount(cursor.getFloat(3));
            transaction.setParent_id(cursor.getInt(4));
            transaction.setMethod(cursor.getString(5));
            transaction.setTransaction_no(cursor.getString(6));
            transaction.setPaid_on(cursor.getString(7));
            transaction.setIs_return(cursor.getString(8));
            transaction.setPayment_for(cursor.getInt(10));
            transaction.setNote(cursor.getString(11));
            transaction.setCard_type(cursor.getString(12));
            transaction.setPayment_ref_no(cursor.getString(13));
            transaction.setCard_transaction_number(cursor.getString(14));
            transaction.setCard_number(cursor.getString(15));
            transaction.setCard_holder_name(cursor.getString(16));
            transaction.setCard_month(cursor.getString(16));
            transaction.setCard_year(cursor.getString(17));
            transaction.setCard_security(cursor.getString(18));
            transaction.setCheque_number(cursor.getString(19));
            transaction.setBank_account_number(cursor.getString(20));
            transaction.setDocument(cursor.getString(21));
            transaction.setIs_advance(cursor.getString(22));

        }

        // mDb.close();
        return transaction;

    }

    public String getLastSellTransferPayInvoiceNo() {
        String invoice_no = "";
        String selectQuery = "SELECT " + TRANSACTION_PAY_PAYMENET_REF_NO + " FROM  " + TRANSACTION_PAY_TABLE_NAME + " ORDER BY " + TRANSACTION_PAY_ID + " DESC LIMIT 1";
        Cursor cursor = mDb.rawQuery(selectQuery, null);
        if (cursor.moveToLast()) {
            if (cursor.getString(0)!=null)invoice_no = cursor.getString(0);
        }
        cursor.close();
        return invoice_no;
    }

    // update non sync to server
    public ArrayList<Transaction> getAllTransactionById(Integer transaction_id) {
        ArrayList<Transaction> transactions = new ArrayList<>();

        String selectQuery = "SELECT  * FROM " + TRANSACTION_PAY_TABLE_NAME + " WHERE " + TRANSACTION_PAY_TRANSACTION_ID + " = " + transaction_id;
        Cursor cursor = mDb.rawQuery(selectQuery, null);
        if (cursor.moveToFirst()) {
            do {
            Transaction transaction = new Transaction();

            transaction.setId(cursor.getInt(0));
            transaction.setTransaction_id(cursor.getInt(1));
            transaction.setBusiness_id(cursor.getInt(2));
            transaction.setAmount(cursor.getFloat(3));
            transaction.setParent_id(cursor.getInt(4));
            transaction.setMethod(cursor.getString(5));
            transaction.setTransaction_no(cursor.getString(6));
            transaction.setIs_return(cursor.getString(7));
            transaction.setCreated_by(cursor.getInt(9));
            transaction.setPayment_for(cursor.getInt(10));
            transaction.setNote(cursor.getString(11));
            transaction.setCard_type(cursor.getString(12));
            transaction.setPayment_ref_no(cursor.getString(13));
            transaction.setCard_transaction_number(cursor.getString(14));
            transaction.setCard_number(cursor.getString(15));
            transaction.setCard_holder_name(cursor.getString(16));
            transaction.setCard_month(cursor.getString(17));
            transaction.setCard_year(cursor.getString(18));
            transaction.setCard_security(cursor.getString(19));
            transaction.setCheque_number(cursor.getString(20));
            transaction.setBank_account_number(cursor.getString(21));
            transaction.setDocument(cursor.getString(22));
            transaction.setIs_advance(cursor.getString(23));
            transaction.setNote(cursor.getString(11));
            transaction.setPaid_on(cursor.getString(7));
            transactions.add(transaction);
            } while (cursor.moveToNext());
        }

        // mDb.close();
        return transactions;

    }

    // update non sync to server
    // update non sync to server
    public ArrayList<Transaction> getPaymentByContactId(Integer id_contact) {
        ArrayList<Transaction> transactions = new ArrayList<>();

        String selectQuery = "SELECT  * FROM " + TRANSACTION_PAY_TABLE_NAME + " WHERE " + TRANSACTION_PAY_PAYMENET_FOR + " = '" + id_contact + "'";
        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {

            do {
                Transaction transaction = new Transaction();

                transaction.setId(cursor.getInt(0));
                transaction.setTransaction_id(cursor.getInt(1));
                transaction.setBusiness_id(cursor.getInt(2));
                transaction.setAmount(Float.parseFloat(cursor.getString(3)));
                transaction.setParent_id(cursor.getInt(4));
                transaction.setMethod(cursor.getString(5));
                transaction.setTransaction_no(cursor.getString(6));
                transaction.setPaid_on(cursor.getString(7));
                transaction.setIs_return(cursor.getString(8));
                transaction.setCreated_by(cursor.getInt(9));
                transaction.setPayment_for(cursor.getInt(10));
                transaction.setNote(cursor.getString(11));
                transaction.setCard_type(cursor.getString(12));
                transaction.setPayment_ref_no(cursor.getString(13));
                transaction.setCard_transaction_number(cursor.getString(14));
                transaction.setCard_number(cursor.getString(15));
                transaction.setCard_holder_name(cursor.getString(16));
                transaction.setCard_month(cursor.getString(17));
                transaction.setCard_year(cursor.getString(18));
                transaction.setCard_security(cursor.getString(19));
                transaction.setCheque_number(cursor.getString(20));
                transaction.setBank_account_number(cursor.getString(21));
                transaction.setDocument(cursor.getString(22));
                transaction.setIs_advance(cursor.getString(23));
              

                transactions.add(transaction);

            } while (cursor.moveToNext());
        }

        // mDb.close();
        return transactions;

    }


    public void clear() {
        mDb.execSQL("DELETE FROM " + TRANSACTION_PAY_TABLE_NAME);
    }

}