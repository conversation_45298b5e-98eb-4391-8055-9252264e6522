package com.rising.high.tech.bigultimatenavdraw.db;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.util.Log;

import com.google.gson.Gson;
import com.rising.high.tech.bigultimatenavdraw.model.Product;
import com.rising.high.tech.bigultimatenavdraw.model.Sell_lines;
import com.rising.high.tech.bigultimatenavdraw.model.Unit;
import com.rising.high.tech.bigultimatenavdraw.model.Variation;

import java.util.ArrayList;

import static com.rising.high.tech.bigultimatenavdraw.db.TransactionDbController.TRANSACTION_ID;
import static com.rising.high.tech.bigultimatenavdraw.db.TransactionDbController.TRANSACTION_LOCATION_ID;
import static com.rising.high.tech.bigultimatenavdraw.db.TransactionDbController.TRANSACTION_STATUS;
import static com.rising.high.tech.bigultimatenavdraw.db.TransactionDbController.TRANSACTION_TABLE_NAME;
import static com.rising.high.tech.bigultimatenavdraw.db.TransactionDbController.TRANSACTION_TYPE;

public class TransactionSellLineDbController extends DBController {

    private static final String TAG = "TransactionSellLineDbController";
    // **********   Table "CATERORY" fields ********************************************************************

    private ProductDbController productDbController;
    private VariationsDbController variationsDbController;
    public static final String SELL_LINE_TABLE_NAME = "transaction_sell_lines";

    public static final String SELL_LINE_ID = "id"; //int
    public static final String SELL_TRANSACTION_ID = "transaction_id";
    public static final String SELL_LINE_PRODUCT_ID = "product_id";
    public static final String SELL_LINE_QUANTITY = "quantity";
    public static final String SELL_LINE_UNIT_PRICE = "unit_price";
    public static final String SELL_LINE_UNIT_PRICE_BEFORE_DISCOUNT = "unit_price_before_discount";
    public static final String SELL_LINE_VARIATION_ID = "variation_id";
    public static final String SELL_LINE_UNIT_PRICE_INC_TAX = "unit_price_inc_tax";
    public static final String SELL_LINE_QTY_RETURN = "quantity_returned";

    public static final String SELL_LINE_TABLE_CREATE =
            "CREATE TABLE " + SELL_LINE_TABLE_NAME + " (" +
                    SELL_LINE_ID + " INTEGER PRIMARY KEY AUTOINCREMENT , " +
                    SELL_TRANSACTION_ID + " INTEGER, " +
                    SELL_LINE_PRODUCT_ID + " INTEGER, " +
                    SELL_LINE_QUANTITY + " INTEGER, " +
                    SELL_LINE_UNIT_PRICE + " TEXT, " +
                    SELL_LINE_UNIT_PRICE_BEFORE_DISCOUNT + " TEXT, " +
                    SELL_LINE_VARIATION_ID + " INTEGER, " +
                    SELL_LINE_UNIT_PRICE_INC_TAX + " TEXT, " +
                    SELL_LINE_QTY_RETURN + " TEXT) ;";

    public static final String SELL_LINE_TABLE_DROP = "DROP TABLE IF EXISTS " + SELL_LINE_TABLE_NAME + ";";

    public TransactionSellLineDbController(Context context) {
        super(context);
        productDbController = new ProductDbController(context);
        productDbController.open();

        variationsDbController = new VariationsDbController(context);
        variationsDbController.open();
    }

    public int insert(Sell_lines sell_lines) {
        // Create a new map of values, where column names are the keys
        ContentValues pValues = new ContentValues();

        pValues.put(SELL_LINE_ID, sell_lines.getId());
        pValues.put(SELL_TRANSACTION_ID, sell_lines.getTransaction_id());
        pValues.put(SELL_LINE_PRODUCT_ID, sell_lines.getProduct_id());
        pValues.put(SELL_LINE_QUANTITY, sell_lines.getQuantity());
        pValues.put(SELL_LINE_UNIT_PRICE, sell_lines.getUnit_price());
        pValues.put(SELL_LINE_UNIT_PRICE_BEFORE_DISCOUNT, sell_lines.getUnit_price_before_discount());
        pValues.put(SELL_LINE_VARIATION_ID, sell_lines.getVariation_id());
        pValues.put(SELL_LINE_UNIT_PRICE_INC_TAX, sell_lines.getUnit_price_inc_tax());
        pValues.put(SELL_LINE_QTY_RETURN, sell_lines.getQty_returned());

        int newRowId = (int) mDb.insert(SELL_LINE_TABLE_NAME, null, pValues); //renvoie l'id de l'enregistrement créé
        return newRowId;
    }


    public int editSellLine(Sell_lines sell_lines) {
        // Create a new map of values, where column names are the keys
        ContentValues pValues = new ContentValues();

        pValues.put(SELL_TRANSACTION_ID, sell_lines.getTransaction_id());
        pValues.put(SELL_LINE_PRODUCT_ID, sell_lines.getProduct_id());
        pValues.put(SELL_LINE_QUANTITY, sell_lines.getQuantity());
        pValues.put(SELL_LINE_UNIT_PRICE, sell_lines.getUnit_price());
        pValues.put(SELL_LINE_UNIT_PRICE_BEFORE_DISCOUNT, sell_lines.getUnit_price_before_discount());
        pValues.put(SELL_LINE_VARIATION_ID, sell_lines.getVariation_id());
        pValues.put(SELL_LINE_UNIT_PRICE_INC_TAX, sell_lines.getUnit_price_inc_tax());
        pValues.put(SELL_LINE_QTY_RETURN, sell_lines.getQty_returned());

        // Insert the new row, returning the primary key value of the new row
        int newRowId = mDb.update(SELL_LINE_TABLE_NAME, pValues, SELL_LINE_ID + " = '" + sell_lines.getId() + "'", null); //renvoie l'id de l'enregistrement créé

        // mDb.close();
        return newRowId;
    }


    public int insertLocal(Sell_lines sell_lines) {
        // Create a new map of values, where column names are the keys
        ContentValues pValues = new ContentValues();

        pValues.put(SELL_TRANSACTION_ID, sell_lines.getTransaction_id());
        pValues.put(SELL_LINE_PRODUCT_ID, sell_lines.getProduct_id());
        pValues.put(SELL_LINE_QUANTITY, sell_lines.getQuantity());
        pValues.put(SELL_LINE_UNIT_PRICE, sell_lines.getUnit_price());
        pValues.put(SELL_LINE_UNIT_PRICE_BEFORE_DISCOUNT, sell_lines.getUnit_price_before_discount());
        pValues.put(SELL_LINE_VARIATION_ID, sell_lines.getVariation_id());
        pValues.put(SELL_LINE_UNIT_PRICE_INC_TAX, sell_lines.getUnit_price_inc_tax());
        pValues.put(SELL_LINE_QTY_RETURN, sell_lines.getQty_returned());
        int newRowId = (int) mDb.insert(SELL_LINE_TABLE_NAME, null, pValues); //renvoie l'id de l'enregistrement créé
        return newRowId;
    }

    public void delete(int id) {
        mDb.execSQL("delete from " + SELL_LINE_TABLE_NAME + " WHERE " + SELL_LINE_ID + " = " + id);
    }

    public void deleteByTransaction(int transactionId) {
        mDb.execSQL("delete from " + SELL_LINE_TABLE_NAME + " WHERE " + SELL_TRANSACTION_ID + " = " + transactionId);
    }

    public void fill(ArrayList<Sell_lines> sell_lines) {
        if (!sell_lines.isEmpty()) {
            for (Sell_lines sell_lines1 : sell_lines) {
                this.insert(sell_lines1);
            }
        }
    }

    public void fillLocal(ArrayList<Sell_lines> sell_lines) {
        if (!sell_lines.isEmpty()) {
            for (Sell_lines sell_lines1 : sell_lines) {
                this.insertLocal(sell_lines1);
            }
        }
    }

    public void deleteAll() {
        mDb.execSQL("delete from " + SELL_LINE_TABLE_NAME);
    }

    public ArrayList<Sell_lines> getAllSellLine() {
        ArrayList<Sell_lines> sell_lines = new ArrayList<>();
        String selectQuery = "SELECT  * FROM " + SELL_LINE_TABLE_NAME;
        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {
            do {
                Sell_lines sell_lines1 = new Sell_lines();
                sell_lines1.setId(Integer.parseInt(cursor.getString(0)));
                sell_lines1.setTransaction_id(cursor.getInt(1));
                sell_lines1.setProduct_id(cursor.getInt(2));
                sell_lines1.setQuantity(cursor.getInt(3));
                sell_lines1.setUnit_price(cursor.getString(4));
                sell_lines1.setVariation_id(cursor.getInt(6));
                sell_lines1.setUnit_price_inc_tax(cursor.getString(7));

                sell_lines1.setProduct_name(productDbController.getProductById(cursor.getInt(2)).getName());

                sell_lines.add(sell_lines1);

            } while (cursor.moveToNext());
        }

        return sell_lines;
    }

    public Sell_lines getSellLineObjectByTransactionId(int id) {
        String selectQuery = "SELECT  * FROM " + SELL_LINE_TABLE_NAME + " WHERE " + SELL_TRANSACTION_ID + " = '" + id + "'";
        Cursor cursor = mDb.rawQuery(selectQuery, null);
        Sell_lines sell_lines = new Sell_lines();
        if (cursor.moveToFirst()) {
            do {

                sell_lines.setId(cursor.getInt(0));
                sell_lines.setTransaction_id(cursor.getInt(1));
                sell_lines.setProduct_id(cursor.getInt(2));
                sell_lines.setQuantity(cursor.getInt(3));
                sell_lines.setUnit_price(cursor.getString(4));
                sell_lines.setVariation_id(cursor.getInt(6));
                sell_lines.setUnit_price_inc_tax(cursor.getString(7));
                sell_lines.setProduct_name(productDbController.getProductById(cursor.getInt(2)).getName());


            } while (cursor.moveToNext());
        }

        return sell_lines;
    }

    public Boolean checkIfSellsByProduct(Integer product_id) {
        Integer count = 0;
        String selectQuery = "SELECT  * FROM " + SELL_LINE_TABLE_NAME + " WHERE " + SELL_LINE_PRODUCT_ID + " = '" + product_id + "'";

        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {
            do {

                count++;

            } while (cursor.moveToNext());
        }

        return count>0;
    }

    public ArrayList<Sell_lines> getSellLineByTransaction(Integer id_transaction) {
        ArrayList<Sell_lines> sell_lines = new ArrayList<>();
        String selectQuery = "SELECT  * FROM " + SELL_LINE_TABLE_NAME + " WHERE " + SELL_TRANSACTION_ID + " = '" + id_transaction + "'";

        Cursor cursor = mDb.rawQuery(selectQuery, null);
        if (cursor.moveToFirst()) {
            do {
                Sell_lines sell_lines1 = new Sell_lines();
                sell_lines1.setId(cursor.getInt(0));
                sell_lines1.setTransaction_id(cursor.getInt(1));
                sell_lines1.setProduct_id(cursor.getInt(2));
                sell_lines1.setQuantity(cursor.getInt(3));
                sell_lines1.setUnit_price(cursor.getString(4));
                sell_lines1.setProduct_name(productDbController.getProductById(cursor.getInt(2)).getName());
                sell_lines1.setUnit_price_before_discount(cursor.getString(5));
                sell_lines1.setVariation_id(cursor.getInt(6));
                sell_lines1.setUnit_price_inc_tax(cursor.getString(7));
                sell_lines1.setQty_returned(cursor.getInt(8));
                sell_lines.add(sell_lines1);

            } while (cursor.moveToNext());
        }
        return sell_lines;
    }

    public Boolean getCheckProductInsellLineList(Integer id_transaction, Integer id_product) {
        boolean isExist = false ;
        String selectQuery = "SELECT  * FROM " + SELL_LINE_TABLE_NAME + " WHERE " + SELL_TRANSACTION_ID + " = '" + id_transaction + "'" +
                " AND "  + SELL_LINE_PRODUCT_ID + " = '" + id_product + "'"  ;

        Cursor cursor = mDb.rawQuery(selectQuery, null);
        if (cursor.moveToFirst()) {
            do {

                isExist=true;

            } while (cursor.moveToNext());
        }
        return isExist;
    }


    public ArrayList<Product> getSellLineProductsByTransaction(Integer id_transaction) {
        ArrayList<Product> productArrayList = new ArrayList<>();
        String selectQuery = "SELECT  * FROM " + SELL_LINE_TABLE_NAME + " WHERE " + SELL_TRANSACTION_ID + " = '" + id_transaction + "'";

        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst())
        {
            do {
                Product product = productDbController.getProductById(cursor.getInt(2));
                product.setSell_qte(cursor.getInt(3));
                product.setDefault_sell_price(cursor.getString(4));
                productArrayList.add(product);

            } while (cursor.moveToNext());
        }

        return productArrayList;
    }


    public ArrayList<Sell_lines> getSyncSellLineByTransaction(Integer id_transaction) {
        ArrayList<Sell_lines> sell_lines = new ArrayList<>();
        String selectQuery = "SELECT  * FROM " + SELL_LINE_TABLE_NAME + " WHERE " + SELL_TRANSACTION_ID + " = '" + id_transaction + "'";

        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {
            do {
                Sell_lines sell_lines1 = new Sell_lines();
                sell_lines1.setId(cursor.getInt(0));
                sell_lines1.setTransaction_id(cursor.getInt(1));
                Product product = productDbController.getProductById(cursor.getInt(2));
                sell_lines1.setProduct_id(product.getProduct_server_id());
                sell_lines1.setQuantity(cursor.getInt(3));
                sell_lines1.setUnit_price(cursor.getString(4));
                sell_lines1.setProduct_name(productDbController.getProductById(cursor.getInt(2)).getName());
                sell_lines1.setUnit_price_before_discount(cursor.getString(5));
                //  sell_lines1.setVariation_id(65);
                Variation variation = variationsDbController.getVariationByProductId(cursor.getInt(2));
                sell_lines1.setVariation_id(variation.getVariation_server_id());

                sell_lines1.setUnit_price_inc_tax(cursor.getString(7));
                sell_lines1.setQty_returned(cursor.getInt(8));
                sell_lines.add(sell_lines1);

            } while (cursor.moveToNext());
        }

        return sell_lines;
    }


    public String getTotalSold(Integer id_product, Integer id_location) {
        String sell_lines = "0";
        String selectQuery = "SELECT SUM(" + SELL_LINE_QUANTITY + ") FROM " + SELL_LINE_TABLE_NAME + " INNER JOIN "
                + TRANSACTION_TABLE_NAME + " ON " + SELL_LINE_TABLE_NAME + "." + SELL_TRANSACTION_ID + " = " + TRANSACTION_TABLE_NAME + "." + TRANSACTION_ID
                + " WHERE " + TRANSACTION_TABLE_NAME + "." + TRANSACTION_TYPE + " = " + "'sell'"
                + " AND " + TRANSACTION_TABLE_NAME + "." + TRANSACTION_STATUS + " = " + "'final'"
                + " AND " + SELL_LINE_TABLE_NAME + "." + SELL_LINE_PRODUCT_ID + " = " + id_product
                + " AND " + TRANSACTION_TABLE_NAME + "." + TRANSACTION_LOCATION_ID + " = " + id_location;

        Cursor cursor = mDb.rawQuery(selectQuery, null);
        if (cursor.moveToFirst()) {
            sell_lines = cursor.getInt(0) + "";
        }
        return sell_lines;
    }

    public String getTotalTranfered(Integer id_product, Integer id_location) {
        String sell_lines = "0";
        String selectQuery = "SELECT SUM(" + SELL_LINE_QUANTITY + ") FROM " + SELL_LINE_TABLE_NAME + " INNER JOIN "
                + TRANSACTION_TABLE_NAME + " ON " + SELL_LINE_TABLE_NAME + "." + SELL_TRANSACTION_ID + " = " + TRANSACTION_TABLE_NAME + "." + TRANSACTION_ID
                + " WHERE " + TRANSACTION_TABLE_NAME + "." + TRANSACTION_TYPE + " = " + "'sell_transfer'"
                + " AND " + TRANSACTION_TABLE_NAME + "." + TRANSACTION_STATUS + " = " + "'final'"
                + " AND " + SELL_LINE_TABLE_NAME + "." + SELL_LINE_PRODUCT_ID + " = " + id_product
                + " AND " + TRANSACTION_TABLE_NAME + "." + TRANSACTION_LOCATION_ID + " = " + id_location;

        Cursor cursor = mDb.rawQuery(selectQuery, null);
        if (cursor.moveToFirst()) {
            sell_lines = cursor.getInt(0) + "";
        }
        return sell_lines;
    }

    public void updateReturnQty(int qty, int id) {
        mDb.execSQL("UPDATE " + SELL_LINE_TABLE_NAME + " SET " + SELL_LINE_QTY_RETURN + " = " + qty + " WHERE " + SELL_LINE_ID + " = " + id);
    }

}
