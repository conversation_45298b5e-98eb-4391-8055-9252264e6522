package com.rising.high.tech.bigultimatenavdraw.db;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;

import com.rising.high.tech.bigultimatenavdraw.model.Sell_lines;

import java.util.ArrayList;

public class TransactionSellLinePurchaseLineDbController extends DBController {

    // **********   Table "CATERORY" fields ********************************************************************

    private ProductDbController productDbController;
    public static final String TRANSACTION_SL_PL_TABLE_NAME = "transaction_sell_lines_purchase_lines";

    public static final String TRANSACTION_SL_PL_ID = "id"; //int
    public static final String TRANSACTION_SL_PL_SELL_LINE_ID= "sell_line_id";
    public static final String TRANSACTION_SL_PL_STOCK_ADJUSTEMENT_LINE_ID = "stock_adjustment_line_id";
    public static final String TRANSACTION_SL_PL_PURCHASE_LINE_ID = "purchase_line_id";
    public static final String TRANSACTION_SL_PL_QUANTITY = "quantity";
    public static final String TRANSACTION_SL_PL_QTY_QUANTITY = "qty_returned";

    public static final String TRANSACTION_SL_PL_TABLE_CREATE =
            "CREATE TABLE " + TRANSACTION_SL_PL_TABLE_NAME + " (" +
                    TRANSACTION_SL_PL_ID + " INTEGER PRIMARY KEY AUTOINCREMENT , " +
                    TRANSACTION_SL_PL_SELL_LINE_ID + " INTEGER, " +
                    TRANSACTION_SL_PL_STOCK_ADJUSTEMENT_LINE_ID + " INTEGER, " +
                    TRANSACTION_SL_PL_PURCHASE_LINE_ID + " INTEGER, " +
                    TRANSACTION_SL_PL_QUANTITY + " INTEGER, " +
                    TRANSACTION_SL_PL_QTY_QUANTITY + " INTEGER) ;";

    public static final String TRANSACTION_SL_PL_TABLE_DROP = "DROP TABLE IF EXISTS " + TRANSACTION_SL_PL_TABLE_NAME + ";";

    public TransactionSellLinePurchaseLineDbController(Context context) {
        super(context);
        productDbController= new ProductDbController(context);
        productDbController.open();
    }

    public int insert(Sell_lines sell_lines) {
        // Create a new map of values, where column names are the keys
        ContentValues pValues = new ContentValues();

        pValues.put(TRANSACTION_SL_PL_SELL_LINE_ID, sell_lines.getSell_line_id());
        pValues.put(TRANSACTION_SL_PL_STOCK_ADJUSTEMENT_LINE_ID, sell_lines.getStock_adjustment_line_id());
        pValues.put(TRANSACTION_SL_PL_PURCHASE_LINE_ID, sell_lines.getPurchase_line_id());
        pValues.put(TRANSACTION_SL_PL_QUANTITY, sell_lines.getQuantity());
        pValues.put(TRANSACTION_SL_PL_QTY_QUANTITY, sell_lines.getQty_returned());

        int newRowId = (int) mDb.insert(TRANSACTION_SL_PL_TABLE_NAME, null, pValues); //renvoie l'id de l'enregistrement créé
        return newRowId;
    }



    public int insertLocal(Sell_lines sell_lines) {
        // Create a new map of values, where column names are the keys
        ContentValues pValues = new ContentValues();

        pValues.put(TRANSACTION_SL_PL_ID, sell_lines.getId());
        pValues.put(TRANSACTION_SL_PL_SELL_LINE_ID, sell_lines.getSell_line_id());
        pValues.put(TRANSACTION_SL_PL_STOCK_ADJUSTEMENT_LINE_ID, sell_lines.getStock_adjustment_line_id());
        pValues.put(TRANSACTION_SL_PL_PURCHASE_LINE_ID, sell_lines.getPurchase_line_id());
        pValues.put(TRANSACTION_SL_PL_QUANTITY, sell_lines.getQuantity());
        pValues.put(TRANSACTION_SL_PL_QTY_QUANTITY, sell_lines.getQty_returned());
        int newRowId = (int) mDb.insert(TRANSACTION_SL_PL_TABLE_NAME, null, pValues); //renvoie l'id de l'enregistrement créé
        return newRowId;
    }

    public void fill(ArrayList<Sell_lines> sell_lines) {
        if (!sell_lines.isEmpty()) {
            for (Sell_lines sell_lines1 : sell_lines) {
                this.insert(sell_lines1);
            }
        }
    }

    public void fillLocal(ArrayList<Sell_lines> sell_lines) {
        if (!sell_lines.isEmpty()) {
            for (Sell_lines sell_lines1 : sell_lines) {
                this.insertLocal(sell_lines1);
            }
        }
    }

    public void deleteAll() {
        mDb.execSQL("delete from " + TRANSACTION_SL_PL_TABLE_NAME);
    }

    public ArrayList<Sell_lines> getAllSellLine() {
        ArrayList<Sell_lines> sell_lines = new ArrayList<>();
        String selectQuery = "SELECT  * FROM " + TRANSACTION_SL_PL_TABLE_NAME;
        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {
            do {
                Sell_lines sell_lines1 = new Sell_lines();

                sell_lines1.setId(cursor.getInt(0));
                sell_lines1.setSell_line_id(cursor.getInt(1));
                sell_lines1.setStock_adjustment_line_id(cursor.getInt(2));
                sell_lines1.setPurchase_line_id(cursor.getInt(3));
                sell_lines1.setQuantity(cursor.getInt(4));
                sell_lines1.setQty_returned(cursor.getInt(5));

                sell_lines.add(sell_lines1);

            } while (cursor.moveToNext());
        }

        return sell_lines;
    }


    public ArrayList<Sell_lines> getSellLineByTransaction(Integer id_transaction) {
        ArrayList<Sell_lines> sell_lines = new ArrayList<>();
        String selectQuery = "SELECT  * FROM " + TRANSACTION_SL_PL_TABLE_NAME + " WHERE " + TRANSACTION_SL_PL_ID + " = '" + id_transaction + "'";

        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {
            do {
                Sell_lines sell_lines1 = new Sell_lines();

                sell_lines1.setId(cursor.getInt(0));
                sell_lines1.setSell_line_id(cursor.getInt(1));
                sell_lines1.setStock_adjustment_line_id(cursor.getInt(2));
                sell_lines1.setPurchase_line_id(cursor.getInt(3));
                sell_lines1.setQuantity(cursor.getInt(4));
                sell_lines1.setQty_returned(cursor.getInt(5));

            } while (cursor.moveToNext());
        }

        return sell_lines;
    }


}
