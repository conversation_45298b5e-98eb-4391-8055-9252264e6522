package com.rising.high.tech.bigultimatenavdraw.db;

import android.content.ContentValues;
import android.content.Context;
import android.content.res.Resources;
import android.database.Cursor;

import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.model.Category;
import com.rising.high.tech.bigultimatenavdraw.model.Unit;

import java.util.ArrayList;

public class UnitDbController extends DBController {

    private Context _context;
    private Resources resources;
    public static final String UNIT_TABLE_NAME = "units";

    public static final String UNIT_ID = "id"; //int
    public static final String UNIT_actual_name = "actual_name";
    public static final String UNIT_short_name = "short_name";
    public static final String UNIT_allow_decimal = "allow_decimal";
    public static final String UNIT_BUSINESS_ID = "business_id";
    public static final String UNIT_SERVER_ID = "unit_server_id";
    public static final String UNIT_SYNC = "sync";


    public static final String UNIT_TABLE_CREATE =
            "CREATE TABLE " + UNIT_TABLE_NAME + " (" +
                    UNIT_ID + " INTEGER PRIMARY KEY AUTOINCREMENT, " +
                    UNIT_actual_name + " TEXT, " +
                    UNIT_short_name + " TEXT, " +
                    UNIT_allow_decimal + " INTEGER," +
                    UNIT_BUSINESS_ID + " INTEGER," +
                    UNIT_SYNC + "  TEXT," +
                    UNIT_SERVER_ID + " INTEGER) ;";

    public static final String UNIT_TABLE_DROP = "DROP TABLE IF EXISTS " + UNIT_TABLE_NAME + ";";

    public UnitDbController(Context context) {
        super(context);
        _context=context;
        resources=_context.getResources();
    }

    public int insert(Unit unit) {
        // Create a new map of values, where column names are the keys
        ContentValues pValues = new ContentValues();

        pValues.put(UNIT_ID, unit.getId());
        pValues.put(UNIT_actual_name, unit.getActual_name());
        pValues.put(UNIT_short_name, unit.getShort_name());
        pValues.put(UNIT_allow_decimal, unit.getAllow_decimal());
        pValues.put(UNIT_BUSINESS_ID, unit.getBusiness_id());

        // Insert the new row, returning the primary key value of the new row
        int newRowId = (int) mDb.insert(UNIT_TABLE_NAME, null, pValues); //renvoie l'id de l'enregistrement créé

        // mDb.close();
        return newRowId;
    }

    public int insertLocal(Unit unit) {
        // Create a new map of values, where column names are the keys
        ContentValues pValues = new ContentValues();

        pValues.put(UNIT_actual_name, unit.getActual_name());
        pValues.put(UNIT_short_name, unit.getShort_name());
        pValues.put(UNIT_allow_decimal, unit.getAllow_decimal());
        pValues.put(UNIT_BUSINESS_ID, unit.getBusiness_id());
        pValues.put(UNIT_SYNC, unit.getSync());
        pValues.put(UNIT_SERVER_ID, unit.getUnit_server_id());

        // Insert the new row, returning the primary key value of the new row
        int newRowId = (int) mDb.insert(UNIT_TABLE_NAME, null, pValues); //renvoie l'id de l'enregistrement créé

        // mDb.close();
        return newRowId;
    }

    public int editUnit(Unit unit) {
        // Create a new map of values, where column names are the keys
        ContentValues pValues = new ContentValues();

        pValues.put(UNIT_actual_name, unit.getActual_name());
        pValues.put(UNIT_short_name, unit.getShort_name());
        pValues.put(UNIT_allow_decimal, unit.getAllow_decimal());

        // Insert the new row, returning the primary key value of the new row
        int newRowId = mDb.update(UNIT_TABLE_NAME, pValues, UNIT_ID + " = '" + unit.getId() + "'", null); //renvoie l'id de l'enregistrement créé

        // mDb.close();
        return newRowId;
    }


    public void deleteItem(String name) {
        mDb.execSQL("delete from " + UNIT_TABLE_NAME + " WHERE " + UNIT_actual_name + " = '" + name + "'");
    }
    public void deleteItemById(Integer unit_id) {
        mDb.execSQL("delete from " + UNIT_TABLE_NAME + " WHERE " + UNIT_ID + " = " + unit_id + "");
    }

    public ArrayList<Unit> getAllUnits() {
        ArrayList<Unit> tempCompany = new ArrayList<>();
        String selectQuery = "SELECT  * FROM " + UNIT_TABLE_NAME;
        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {
            do {
                Unit unit = new Unit();
                unit.setId(cursor.getInt(0));
                unit.setActual_name(cursor.getString(1));
                unit.setShort_name(cursor.getString(2));
                unit.setAllow_decimal(cursor.getInt(3));
                unit.setBusiness_id(cursor.getInt(4));

                tempCompany.add(unit);

            } while (cursor.moveToNext());

        }

        // mDb.close();

        return tempCompany;
    }
    public ArrayList<Unit> getSyncUnits(String sync) {
        ArrayList<Unit> tempCompany = new ArrayList<>();
        String selectQuery = "SELECT  * FROM " + UNIT_TABLE_NAME + " WHERE " + UNIT_SYNC + " ='" + sync + "'";
        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {
            do {
                Unit unit = new Unit();
                unit.setId(cursor.getInt(0));
                unit.setActual_name(cursor.getString(1));
                unit.setShort_name(cursor.getString(2));
                unit.setAllow_decimal(cursor.getInt(3));
                unit.setBusiness_id(cursor.getInt(4));
                unit.setSync(cursor.getString(5));
                unit.setUnit_server_id(cursor.getInt(6));
                unit.setCreated_by(1);
              //  unit.setCreated_by(1);

                tempCompany.add(unit);

            } while (cursor.moveToNext());

        }

        // mDb.close();

        return tempCompany;
    }


    public int editServerCategory(Unit unit) {
        // Create a new map of values, where column names are the keys
        ContentValues pValues = new ContentValues();

        pValues.put(UNIT_SYNC, unit.getSync());
        pValues.put(UNIT_SERVER_ID, unit.getUnit_server_id());

        int newRowId = mDb.update(UNIT_TABLE_NAME, pValues, UNIT_ID + " = '" + unit.getId() + "'", null); //renvoie l'id de l'enregistrement créé

        return newRowId;
    }


    public ArrayList<Unit> getUnitsLike(String name) {
        ArrayList<Unit> tempCompany = new ArrayList<>();
        String selectQuery = "SELECT  * FROM " + UNIT_TABLE_NAME + " WHERE " + UNIT_actual_name + " LIKE '%" + name + "%' ";

        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {
            do {
                Unit unit = new Unit();
                unit.setId(cursor.getInt(0));
                unit.setActual_name(cursor.getString(1));
                unit.setShort_name(cursor.getString(2));
                unit.setAllow_decimal(cursor.getInt(3));
                unit.setBusiness_id(cursor.getInt(4));

                tempCompany.add(unit);

            } while (cursor.moveToNext());

        }

        // mDb.close();

        return tempCompany;
    }

    public ArrayList<Unit> getAllUnitSpinner() {
        ArrayList<Unit> tempCompany = new ArrayList<>();
        String selectQuery = "SELECT  * FROM " + UNIT_TABLE_NAME;
        Cursor cursor = mDb.rawQuery(selectQuery, null);

        tempCompany.add(new Unit(0, resources.getString(R.string.label_choose_all), resources.getString(R.string.label_choose_all)));
        if (cursor.moveToFirst()) {
            do {
                Unit unit = new Unit();
                unit.setId(cursor.getInt(0));
                unit.setActual_name(cursor.getString(1));
                unit.setShort_name(cursor.getString(2));
                unit.setAllow_decimal(cursor.getInt(3));
                unit.setBusiness_id(cursor.getInt(4));

                tempCompany.add(unit);

            } while (cursor.moveToNext());

        }

        // mDb.close();

        return tempCompany;
    }

    public ArrayList<Unit> getUnits() {
        ArrayList<Unit> tempCompany = new ArrayList<>();
        String selectQuery = "SELECT  * FROM " + UNIT_TABLE_NAME;
        Cursor cursor = mDb.rawQuery(selectQuery, null);
        tempCompany.add(new Unit(0, _context.getResources().getString(R.string.please_select_unit_spin), ""));
        if (cursor.moveToFirst()) {
            do {
                Unit unit = new Unit();
                unit.setId(cursor.getInt(0));
                unit.setActual_name(cursor.getString(1));
                unit.setShort_name(cursor.getString(2));
                unit.setAllow_decimal(cursor.getInt(3));
                unit.setBusiness_id(cursor.getInt(4));

                tempCompany.add(unit);

            } while (cursor.moveToNext());

        }

        // mDb.close();

        return tempCompany;
    }
    public Unit getUnitsById(Integer id) {
        Unit unit = new Unit();
        String selectQuery = "SELECT  * FROM " + UNIT_TABLE_NAME + " WHERE " + UNIT_ID + " = " + id;

        Cursor cursor = mDb.rawQuery(selectQuery, null);
        if (cursor.moveToFirst()) {

                unit.setId(cursor.getInt(0));
                unit.setActual_name(cursor.getString(1));
                unit.setShort_name(cursor.getString(2));
                unit.setAllow_decimal(cursor.getInt(3));
                unit.setBusiness_id(cursor.getInt(4));
                unit.setUnit_server_id(cursor.getInt(6));

        }

        // mDb.close();

        return unit;
    }

    // Insert all product
    public void fill(ArrayList<Unit> products) {
        if (!products.isEmpty()) {
            for (Unit product : products) {
                this.insert(product);
            }
        }
        //   mDb.close();
    }

    public void clear() {
        mDb.execSQL("DELETE FROM " + UNIT_TABLE_NAME);
    }

}
