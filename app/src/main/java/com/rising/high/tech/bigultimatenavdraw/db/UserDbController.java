package com.rising.high.tech.bigultimatenavdraw.db;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;

import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.model.Brand;
import com.rising.high.tech.bigultimatenavdraw.model.Business_location;
import com.rising.high.tech.bigultimatenavdraw.model.Contact;
import com.rising.high.tech.bigultimatenavdraw.model.User;
import com.rising.high.tech.bigultimatenavdraw.model.User;

import java.util.ArrayList;

public class UserDbController extends DBController {

    public static final String USER_TABLE_NAME = "users";
    private Context _context;

    public static final String USER_ID = "id"; //int
    public static final String USER_USER_TYPE = "user_type";
    public static final String USER_SURNAME = "surname";
    public static final String USER_FIRST_NAME = "first_name";
    public static final String USER_LAST_NAME = "last_name";
    public static final String USER_USERNAME = "username";
    public static final String USER_EMAIL = "email";
    public static final String USER_PASSWORD = "password";
    public static final String USER_LANGUAGE = "language";
    public static final String USER_CONTACT_NO = "contact_no";
    public static final String USER_ADDRESS = "address";
    public static final String USER_REMEMBER_TOKEN = "remember_token";
    public static final String USER_BUSINESS_ID = "business_id";
    public static final String USER_ALLOW_LOGIN = "allow_login";
    public static final String USER_STATUS = "status";
    public static final String USER_SELECTED_CONTACTS = "selected_contacts";
    public static final String USER_DOB = "dob";
    public static final String USER_GENDER = "gender";
    public static final String USER_MARITAL_STATUS = "marital_status";
    public static final String USER_BLOOD_GROUP = "blood_group";
    public static final String USER_CONTACT_NUMBER = "contact_number";
    public static final String USER_BANK_DETAILS= "bank_details";
    public static final String USER_ID_PROOF_NAME= "id_proof_name";
    public static final String USER_ID_PROOF_NUMBER= "id_proof_number";
    public static final String USER_SYNC= "sync";
    public static final String USER_SERVER_ID= "user_server_id";

    public static final String USER_TABLE_CREATE =
            "CREATE TABLE " + USER_TABLE_NAME + " (" +
                    USER_ID + " INTEGER PRIMARY KEY AUTOINCREMENT, " +
                    USER_USER_TYPE + " TEXT, " +
                    USER_SURNAME + " TEXT, " +
                    USER_FIRST_NAME + " TEXT," +
                    USER_LAST_NAME + " TEXT," +
                    USER_USERNAME + "  TEXT," +
                    USER_EMAIL + "  TEXT," +
                    USER_PASSWORD + "  TEXT," +
                    USER_LANGUAGE + "  TEXT," +
                    USER_CONTACT_NO + "  TEXT," +
                    USER_ADDRESS + "  TEXT," +
                    USER_REMEMBER_TOKEN + "  TEXT," +
                    USER_BUSINESS_ID + "  INTEGER," +
                    USER_ALLOW_LOGIN + "  INTEGER," +
                    USER_STATUS + "  TEXT," +
                    USER_SELECTED_CONTACTS + "  TEXT," +
                    USER_DOB + "  TEXT," +
                    USER_GENDER + "  TEXT," +
                    USER_MARITAL_STATUS + "  TEXT," +
                    USER_BLOOD_GROUP + "  TEXT," +
                    USER_CONTACT_NUMBER + "  TEXT," +
                    USER_BANK_DETAILS + "  TEXT," +
                    USER_ID_PROOF_NAME + "  TEXT," +
                    USER_ID_PROOF_NUMBER + " INTEGER," +
                    USER_SYNC + " TEXT," +
                       USER_SERVER_ID+ " INTEGER) ;";

    public static final String USER_TABLE_DROP = "DROP TABLE IF EXISTS " + USER_TABLE_NAME + ";";

    public UserDbController(Context context) {
        super(context);
        _context=context;
    }

    public int insert(User user) {
        // Create a new map of values, where column names are the keys
        ContentValues pValues = new ContentValues();

        pValues.put(USER_ID, user.getId());
        pValues.put(USER_USER_TYPE, user.getUser_type());
        pValues.put(USER_SURNAME, user.getSurname());
        pValues.put(USER_FIRST_NAME, user.getFirst_name());
        pValues.put(USER_LAST_NAME, user.getLast_name());
        pValues.put(USER_USERNAME, user.getUsername());
        pValues.put(USER_EMAIL, user.getEmail());
        pValues.put(USER_PASSWORD, user.getPassword());
        pValues.put(USER_LANGUAGE, user.getLanguage());
        pValues.put(USER_CONTACT_NO, user.getContact_no());
        pValues.put(USER_ADDRESS, user.getAddress());
        pValues.put(USER_REMEMBER_TOKEN, user.getRemember_token());
        pValues.put(USER_BUSINESS_ID, user.getBusiness_id());
        pValues.put(USER_ALLOW_LOGIN, user.getAllow_login());
        pValues.put(USER_STATUS, user.getStatus());
        pValues.put(USER_SELECTED_CONTACTS, user.getSelected_contacts());
        pValues.put(USER_DOB, user.getDob());
        pValues.put(USER_GENDER, user.getGender());
        pValues.put(USER_MARITAL_STATUS, user.getMarital_status());
        pValues.put(USER_BLOOD_GROUP, user.getBlood_group());
        pValues.put(USER_CONTACT_NUMBER, user.getContact_number());
        pValues.put(USER_BANK_DETAILS, user.getBank_details());
        pValues.put(USER_ID_PROOF_NAME, user.getId_proof_name());
        pValues.put(USER_ID_PROOF_NUMBER, user.getId_proof_number());
        pValues.put(USER_SYNC, user.getSync());

        // Insert the new row, returning the primary key value of the new row
        int newRowId = (int) mDb.insert(USER_TABLE_NAME, null, pValues); //renvoie l'id de l'enregistrement créé

        // mDb.close();
        return newRowId;
    }

    public int insertLocal(User user) {
        // Create a new map of values, where column names are the keys
        ContentValues pValues = new ContentValues();

        pValues.put(USER_USER_TYPE, user.getUser_type());
        pValues.put(USER_SURNAME, user.getSurname());
        pValues.put(USER_FIRST_NAME, user.getFirst_name());
        pValues.put(USER_LAST_NAME, user.getLast_name());
        pValues.put(USER_USERNAME, user.getUsername());
        pValues.put(USER_EMAIL, user.getEmail());
        pValues.put(USER_PASSWORD, user.getPassword());
        pValues.put(USER_LANGUAGE, user.getLanguage());
        pValues.put(USER_CONTACT_NO, user.getContact_no());
        pValues.put(USER_ADDRESS, user.getAddress());
        pValues.put(USER_REMEMBER_TOKEN, user.getRemember_token());
        pValues.put(USER_BUSINESS_ID, user.getBusiness_id());
        pValues.put(USER_ALLOW_LOGIN, user.getAllow_login());
        pValues.put(USER_STATUS, user.getStatus());
        pValues.put(USER_SELECTED_CONTACTS, user.getSelected_contacts());
        pValues.put(USER_DOB, user.getDob());
        pValues.put(USER_GENDER, user.getGender());
        pValues.put(USER_MARITAL_STATUS, user.getMarital_status());
        pValues.put(USER_BLOOD_GROUP, user.getBlood_group());
        pValues.put(USER_CONTACT_NUMBER, user.getContact_number());
        pValues.put(USER_BANK_DETAILS, user.getBank_details());
        pValues.put(USER_ID_PROOF_NAME, user.getId_proof_name());
        pValues.put(USER_ID_PROOF_NUMBER, user.getId_proof_number());
        pValues.put(USER_SYNC, user.getSync());
        // Insert the new row, returning the primary key value of the new row
        int newRowId = (int) mDb.insert(USER_TABLE_NAME, null, pValues); //renvoie l'id de l'enregistrement créé

        // mDb.close();
        return newRowId;
    }

    public int editUser(User user) {
        // Create a new map of values, where column names are the keys
        ContentValues pValues = new ContentValues();


        pValues.put(USER_USER_TYPE, user.getUser_type());
        pValues.put(USER_SURNAME, user.getSurname());
        pValues.put(USER_FIRST_NAME, user.getFirst_name());
        pValues.put(USER_LAST_NAME, user.getLast_name());
        pValues.put(USER_USERNAME, user.getUsername());
        pValues.put(USER_EMAIL, user.getEmail());
        pValues.put(USER_PASSWORD, user.getPassword());
        pValues.put(USER_LANGUAGE, user.getLanguage());
        pValues.put(USER_CONTACT_NO, user.getContact_no());
        pValues.put(USER_ADDRESS, user.getAddress());
        pValues.put(USER_REMEMBER_TOKEN, user.getRemember_token());
        pValues.put(USER_BUSINESS_ID, user.getBusiness_id());
        pValues.put(USER_ALLOW_LOGIN, user.getAllow_login());
        pValues.put(USER_STATUS, user.getStatus());
        pValues.put(USER_SELECTED_CONTACTS, user.getSelected_contacts());
        pValues.put(USER_DOB, user.getDob());
        pValues.put(USER_GENDER, user.getGender());
        pValues.put(USER_MARITAL_STATUS, user.getMarital_status());
        pValues.put(USER_BLOOD_GROUP, user.getBlood_group());
        pValues.put(USER_CONTACT_NUMBER, user.getContact_number());
        pValues.put(USER_BANK_DETAILS, user.getBank_details());
        pValues.put(USER_ID_PROOF_NAME, user.getId_proof_name());
        pValues.put(USER_ID_PROOF_NUMBER, user.getId_proof_number());
        pValues.put(USER_SYNC, user.getSync());

        // Insert the new row, returning the primary key value of the new row
        int newRowId = mDb.update(USER_TABLE_NAME, pValues, USER_ID + " = '" + user.getId() + "'", null); //renvoie l'id de l'enregistrement créé

        // mDb.close();
        return newRowId;
    }


    public void deleteItem(Integer id) {
        mDb.execSQL("delete from " + USER_TABLE_NAME + " WHERE " + USER_ID + " = '" + id + "'");
    }

    public ArrayList<User> getSpinUsers() {
        ArrayList<User> tmpContact = new ArrayList<>();
        tmpContact.add(new User(0, _context.getResources().getString(R.string.select_please_sleetc)));
        String selectQuery = "SELECT * FROM " + USER_TABLE_NAME;
        Cursor cursor = mDb.rawQuery(selectQuery, null);
        if (cursor.moveToFirst()) {
            do {
                User user = new User();

                user.setId(cursor.getInt(0));
                user.setUser_type(cursor.getString(1));
                user.setSurname(cursor.getString(2));
                user.setFirst_name(cursor.getString(3));
                user.setLast_name(cursor.getString(4));
                user.setUsername(cursor.getString(5));
                user.setEmail(cursor.getString(6));
                user.setPassword(cursor.getString(7));
                user.setLanguage(cursor.getInt(8));
                user.setContact_no(cursor.getInt(9));
                user.setAddress(cursor.getString(10));
                user.setRemember_token(cursor.getString(11));
                user.setBusiness_id(cursor.getInt(12));
                user.setAllow_login( cursor.getInt(13));
                user.setStatus(cursor.getString(14));
                user.setSelected_contacts( cursor.getInt(15));
                user.setDob(cursor.getString(16));
                user.setGender( cursor.getString(17));
                user.setMarital_status( cursor.getString(18));
                user.setBlood_group( cursor.getString(19));
                user.setContact_number( cursor.getString(20));
                user.setBank_details(cursor.getString(21));
                user.setId_proof_name( cursor.getString(22));
                user.setId_proof_number(cursor.getString(23));

                tmpContact.add(user);
            } while (cursor.moveToNext());
        }

        // mDb.close();

        return tmpContact;
    }

    public ArrayList<User> getAllUsers() {
        ArrayList<User> tempCompany = new ArrayList<>();
        String selectQuery = "SELECT  * FROM " + USER_TABLE_NAME;
        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {
            do {
                User user = new User();

                user.setId(cursor.getInt(0));
                user.setUser_type(cursor.getString(1));
                user.setSurname(cursor.getString(2));
                user.setFirst_name(cursor.getString(3));
                user.setLast_name(cursor.getString(4));
                user.setUsername(cursor.getString(5));
                user.setEmail(cursor.getString(6));
                user.setPassword(cursor.getString(7));
                user.setLanguage(cursor.getInt(8));
                user.setContact_no(cursor.getInt(9));
                user.setAddress(cursor.getString(10));
                user.setRemember_token(cursor.getString(11));
                user.setBusiness_id(cursor.getInt(12));
                user.setAllow_login( cursor.getInt(13));
                user.setStatus(cursor.getString(14));
                user.setSelected_contacts( cursor.getInt(15));
                user.setDob(cursor.getString(16));
                user.setGender( cursor.getString(17));
                user.setMarital_status( cursor.getString(18));
                user.setBlood_group( cursor.getString(19));
                user.setContact_number( cursor.getString(20));
                user.setBank_details(cursor.getString(21));
                user.setId_proof_name( cursor.getString(22));
                user.setId_proof_number(cursor.getString(23));

                tempCompany.add(user);

            } while (cursor.moveToNext());

        }

        // mDb.close();

        return tempCompany;
    }

    public ArrayList<User> getAllUsersLike(String name) {
        ArrayList<User> tempCompany = new ArrayList<>();
        String selectQuery = "SELECT  * FROM " + USER_TABLE_NAME + " WHERE " + USER_TABLE_NAME + " LIKE '%" + name +  "%' ";

        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {
            do {

                User user=new User();
                user.setId(cursor.getInt(0));
                user.setUser_type(cursor.getString(1));
                user.setSurname(cursor.getString(2));
                user.setFirst_name(cursor.getString(3));
                user.setLast_name(cursor.getString(4));
                user.setUsername(cursor.getString(5));
                user.setEmail(cursor.getString(6));
                user.setPassword(cursor.getString(7));
                user.setLanguage(cursor.getInt(8));
                user.setContact_no(cursor.getInt(9));
                user.setAddress(cursor.getString(10));
                user.setRemember_token(cursor.getString(11));
                user.setBusiness_id(cursor.getInt(12));
                user.setAllow_login( cursor.getInt(13));
                user.setStatus(cursor.getString(14));
                user.setSelected_contacts( cursor.getInt(15));
                user.setDob(cursor.getString(16));
                user.setGender( cursor.getString(17));
                user.setMarital_status( cursor.getString(18));
                user.setBlood_group( cursor.getString(19));
                user.setContact_number( cursor.getString(20));
                user.setBank_details(cursor.getString(21));
                user.setId_proof_name( cursor.getString(22));
                user.setId_proof_number(cursor.getString(23));

            } while (cursor.moveToNext());

        }
        // mDb.close();

        return tempCompany;
    }


    public int getUserId(String name, String password) {

        int isLogin = 0;
        String selectQuery = "SELECT  * FROM " + USER_TABLE_NAME + " WHERE " + USER_USERNAME + " = '" + name +  "'" + " AND " + USER_PASSWORD + " = '" + password +"'" ;
        Cursor cursor = mDb.rawQuery(selectQuery, null);
        if (cursor.moveToFirst()) {
            isLogin=cursor.getInt(0);
        }
        // mDb.close();

        return isLogin;
    }

    public int setSyncUsers(User user) {
        // Create a new map of values, where column names are the keys
        ContentValues pValues = new ContentValues();

        pValues.put(USER_SERVER_ID, user.getUser_server_id());
        pValues.put(USER_SYNC, user.getSync());

        // Insert the new row, returning the primary key value of the new row
        int newRowId = mDb.update(USER_TABLE_NAME, pValues, USER_ID + " = '" + user.getId() + "'", null); //renvoie l'id de l'enregistrement créé

        // mDb.close();
        return newRowId;
    }

    public ArrayList<User> getSyncUsers(String sync) {
        ArrayList<User> tempCompany = new ArrayList<>();
        String selectQuery = "SELECT  * FROM " + USER_TABLE_NAME + " WHERE " + USER_SYNC + " ='" + sync + "'";
        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {
            do {
                User user = new User();

                user.setId(cursor.getInt(0));
                user.setUser_type(cursor.getString(1));
                user.setSurname(cursor.getString(2));
                user.setFirst_name(cursor.getString(3));
                user.setLast_name(cursor.getString(4));
                user.setUsername(cursor.getString(5));
                user.setEmail(cursor.getString(6));
                user.setPassword(cursor.getString(7));
                user.setLanguage(cursor.getInt(8));
                user.setContact_no(cursor.getInt(9));
                user.setAddress(cursor.getString(10));
                user.setRemember_token(cursor.getString(11));
                user.setBusiness_id(cursor.getInt(12));
                user.setAllow_login( cursor.getInt(13));
                user.setStatus(cursor.getString(14));
                user.setSelected_contacts( cursor.getInt(15));
                user.setDob(cursor.getString(16));
                user.setGender( cursor.getString(17));
                user.setMarital_status( cursor.getString(18));
                user.setBlood_group( cursor.getString(19));
                user.setContact_number( cursor.getString(20));
                user.setBank_details(cursor.getString(21));
                user.setId_proof_name( cursor.getString(22));
                user.setId_proof_number(cursor.getString(23));

                tempCompany.add(user);

            } while (cursor.moveToNext());

        }

        // mDb.close();

        return tempCompany;
    }


    public int editServerCategory(User user) {
        // Create a new map of values, where column names are the keys
        ContentValues pValues = new ContentValues();

        int newRowId = mDb.update(USER_TABLE_NAME, pValues, USER_ID + " = '" + user.getId() + "'", null); //renvoie l'id de l'enregistrement créé

        return newRowId;
    }


    public User getUsersById(Integer id) {
        User user = new User();
        String selectQuery = "SELECT  * FROM " + USER_TABLE_NAME + " WHERE " + USER_ID + " = " + id;

        Cursor cursor = mDb.rawQuery(selectQuery, null);
        if (cursor.moveToFirst()) {


            user.setId(cursor.getInt(0));
            user.setUser_type(cursor.getString(1));
            user.setSurname(cursor.getString(2));
            user.setFirst_name(cursor.getString(3));
            user.setLast_name(cursor.getString(4));
            user.setUsername(cursor.getString(5));
            user.setEmail(cursor.getString(6));
            user.setPassword(cursor.getString(7));
            user.setLanguage(cursor.getInt(8));
            user.setContact_no(cursor.getInt(9));
            user.setAddress(cursor.getString(10));
            user.setRemember_token(cursor.getString(11));
            user.setBusiness_id(cursor.getInt(12));
            user.setAllow_login( cursor.getInt(13));
            user.setStatus(cursor.getString(14));
            user.setSelected_contacts( cursor.getInt(15));
            user.setDob(cursor.getString(16));
            user.setGender( cursor.getString(17));
            user.setMarital_status( cursor.getString(18));
            user.setBlood_group( cursor.getString(19));
            user.setContact_number( cursor.getString(20));
            user.setBank_details(cursor.getString(21));
            user.setId_proof_name( cursor.getString(22));
            user.setId_proof_number(cursor.getString(23));
        }

        // mDb.close();

        return user;
    }

    // Insert all product
    public void fill(ArrayList<User> products) {
        if (!products.isEmpty()) {
            for (User product : products) {
                this.insert(product);
            }
        }
        //   mDb.close();
    }

    public void clear() {
        mDb.execSQL("DELETE FROM " + USER_TABLE_NAME);
    }

}
