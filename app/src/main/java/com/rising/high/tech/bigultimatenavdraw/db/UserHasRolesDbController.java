package com.rising.high.tech.bigultimatenavdraw.db;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;

import com.rising.high.tech.bigultimatenavdraw.model.Role;
import com.rising.high.tech.bigultimatenavdraw.model.Role;

import java.util.ArrayList;

public class UserHasRolesDbController extends DBController {

    public static final String USERHASROLE_TABLE_NAME = "user_has_role";

    public static final String USERHASROLE_ROLE_ID = "role_id"; //int
    public static final String USERHASROLE_USER_ID = "user_id";

    public static final String USERHASROLE_TABLE_CREATE =
            "CREATE TABLE " + USERHASROLE_TABLE_NAME + " (" +
                    USERHASROLE_ROLE_ID + " INTEGER , " +
                    USERHASROLE_USER_ID + " INTEGER) ;";

    public static final String USERHASROLE_TABLE_DROP = "DROP TABLE IF EXISTS " + USERHASROLE_TABLE_NAME + ";";

    public UserHasRolesDbController(Context context) {
        super(context);
    }

    public int insert(Role role) {
        // Create a new map of values, where column names are the keys
        ContentValues pValues = new ContentValues();
        pValues.put(USERHASROLE_USER_ID, role.getUser_id());
        pValues.put(USERHASROLE_ROLE_ID, role.getRole_id());
        // Insert the new row, returning the primary key value of the new row
        int newRowId = (int) mDb.insert(USERHASROLE_TABLE_NAME, null, pValues); //renvoie l'id de l'enregistrement créé

        // mDb.close();
        return newRowId;
    }

    public int insertLocal(Role role) {
        // Create a new map of values, where column names are the keys
        ContentValues pValues = new ContentValues();

        pValues.put(USERHASROLE_USER_ID, role.getUser_id());
        pValues.put(USERHASROLE_ROLE_ID, role.getRole_id());

        // Insert the new row, returning the primary key value of the new row
        int newRowId = (int) mDb.insert(USERHASROLE_TABLE_NAME, null, pValues); //renvoie l'id de l'enregistrement créé

        // mDb.close();
        return newRowId;
    }

    public int editUserHasRole(Role role) {
        // Create a new map of values, where column names are the keys
        ContentValues pValues = new ContentValues();
        pValues.put(USERHASROLE_ROLE_ID, role.getRole_id());
        pValues.put(USERHASROLE_USER_ID, role.getUser_id());

        // Insert the new row, returning the primary key value of the new row
        int newRowId = mDb.update(USERHASROLE_TABLE_NAME, pValues, USERHASROLE_USER_ID + " = '" + role.getUser_id() + "'", null); //renvoie l'id de l'enregistrement créé

        // mDb.close();
        return newRowId;
    }

    public void deleteItem(Integer id) {
        mDb.execSQL("delete from " + USERHASROLE_TABLE_NAME + " WHERE " + USERHASROLE_ROLE_ID + " = '" + id + "'");
    }

    public ArrayList<Role> getAllRoles() {
        ArrayList<Role> tempCompany = new ArrayList<>();
        String selectQuery = "SELECT  * FROM " + USERHASROLE_TABLE_NAME;
        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {
            do {
                Role role = new Role();
                role.setRole_id(cursor.getInt(0));
                role.setUser_id(cursor.getInt(1));
                tempCompany.add(role);

            } while (cursor.moveToNext());

        }

        // mDb.close();

        return tempCompany;
    }


    public Role getRolesByUserId(int user_id) {
        Role role = new Role();

        String selectQuery = "SELECT  * FROM " + USERHASROLE_TABLE_NAME + " WHERE " + USERHASROLE_USER_ID + " = " + user_id;
        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {

                role.setRole_id(cursor.getInt(0));
                role.setUser_id(cursor.getInt(1));
        }

        // mDb.close();

        return role;
    }

    // Insert all product
    public void fill(ArrayList<Role> products) {
        if (!products.isEmpty()) {
            for (Role product : products) {
                this.insertLocal(product);
            }
        }
        //   mDb.close();
    }

    public void clear() {
        mDb.execSQL("DELETE FROM " + USERHASROLE_TABLE_NAME);
    }

}
