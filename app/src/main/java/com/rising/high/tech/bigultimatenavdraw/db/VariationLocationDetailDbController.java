package com.rising.high.tech.bigultimatenavdraw.db;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import com.rising.high.tech.bigultimatenavdraw.model.Variation_location_details;
import java.util.ArrayList;

import static com.rising.high.tech.bigultimatenavdraw.db.ProductDbController.PRODUCT_CATEGORY_ID;
import static com.rising.high.tech.bigultimatenavdraw.db.ProductDbController.PRODUCT_ID;
import static com.rising.high.tech.bigultimatenavdraw.db.ProductDbController.PRODUCT_TABLE_NAME;
import static com.rising.high.tech.bigultimatenavdraw.db.ProductLocationDbController.PRODUCT_LOCATION_LOCATION_ID;
import static com.rising.high.tech.bigultimatenavdraw.db.ProductLocationDbController.PRODUCT_LOCATION_PRODUCT_ID;
import static com.rising.high.tech.bigultimatenavdraw.db.ProductLocationDbController.PRODUCT_LOCATION_TABLE_NAME;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.PURCHASE;

public class VariationLocationDetailDbController extends DBController {

    private static final String TAG = "VariationLocationDetail";

    public static final String VARIATION_LOCATION_DETAIL_TABLE_NAME = "variation_location_detail";

    public static final String VARIATION_LOCATION_DETAIL_ID = "id"; //int
    public static final String VARIATION_LOCATION_DETAIL_PRODUCT_ID = "product_id";
    public static final String VARIATION_LOCATION_DETAIL_product_variation_id = "product_variation_id";
    public static final String VARIATION_LOCATION_DETAIL_variation_id = "variation_id";
    public static final String VARIATION_LOCATION_DETAIL_location_id = "location_id";
    public static final String VARIATION_LOCATION_DETAIL_qty_available = "qty_available";
    public static final String VARIATION_LOCATION_DETAIL_OLD_qty_available = "old_qty_available";

    public static final String VARIATION_LOCATION_DETAIL_TABLE_CREATE =
            "CREATE TABLE " + VARIATION_LOCATION_DETAIL_TABLE_NAME + " (" +
                    VARIATION_LOCATION_DETAIL_ID + " INTEGER PRIMARY KEY AUTOINCREMENT, " +
                    VARIATION_LOCATION_DETAIL_PRODUCT_ID + " INTEGER, " +
                    VARIATION_LOCATION_DETAIL_product_variation_id + " INTEGER, " +
                    VARIATION_LOCATION_DETAIL_variation_id + " INTEGER, " +
                    VARIATION_LOCATION_DETAIL_location_id + " INTEGER, " +
                    VARIATION_LOCATION_DETAIL_qty_available + " INTEGER, " +
                    VARIATION_LOCATION_DETAIL_OLD_qty_available + " INTEGER) ;";

    public static final String VARIATION_LOCATION_DETAIL_TABLE_DROP = "DROP TABLE IF EXISTS " + VARIATION_LOCATION_DETAIL_TABLE_NAME + ";";

    public VariationLocationDetailDbController(Context context) {
        super(context);
    }

    public int insert(Variation_location_details variation_location_details) {
        // Create a new map of values, where column names are the keys
        ContentValues pValues = new ContentValues();

        pValues.put(VARIATION_LOCATION_DETAIL_ID, variation_location_details.getId());
        pValues.put(VARIATION_LOCATION_DETAIL_PRODUCT_ID, variation_location_details.getProduct_id());
        pValues.put(VARIATION_LOCATION_DETAIL_product_variation_id, variation_location_details.getProduct_variation_id());
        pValues.put(VARIATION_LOCATION_DETAIL_variation_id, variation_location_details.getVariation_id());
        pValues.put(VARIATION_LOCATION_DETAIL_location_id, variation_location_details.getLocation_id());
        pValues.put(VARIATION_LOCATION_DETAIL_qty_available, variation_location_details.getQty_available());
        pValues.put(VARIATION_LOCATION_DETAIL_OLD_qty_available, variation_location_details.getQty_available());

        // Insert the new row, returning the primary key value of the new row
        int newRowId = (int) mDb.insert(VARIATION_LOCATION_DETAIL_TABLE_NAME, null, pValues); //renvoie l'id de l'enregistrement créé

        // mDb.close();
        return newRowId;
    }

    public int insertLocal(Variation_location_details variation_location_details) {
        // Create a new map of values, where column names are the keys
        ContentValues pValues = new ContentValues();

        pValues.put(VARIATION_LOCATION_DETAIL_PRODUCT_ID, variation_location_details.getProduct_id());
        pValues.put(VARIATION_LOCATION_DETAIL_product_variation_id, variation_location_details.getProduct_variation_id());
        pValues.put(VARIATION_LOCATION_DETAIL_variation_id, variation_location_details.getVariation_id());
        pValues.put(VARIATION_LOCATION_DETAIL_location_id, variation_location_details.getLocation_id());
        pValues.put(VARIATION_LOCATION_DETAIL_qty_available, variation_location_details.getQty_available());
        pValues.put(VARIATION_LOCATION_DETAIL_OLD_qty_available, variation_location_details.getOld_qty_available());


        // Insert the new row, returning the primary key value of the new row
        int newRowId = (int) mDb.insert(VARIATION_LOCATION_DETAIL_TABLE_NAME, null, pValues); //renvoie l'id de l'enregistrement créé

        // mDb.close();
        return newRowId;
    }

    public int update(Variation_location_details variation_location_details) {
        // Create a new map of values, where column names are the keys
        ContentValues pValues = new ContentValues();

        pValues.put(VARIATION_LOCATION_DETAIL_product_variation_id, variation_location_details.getProduct_variation_id());
        pValues.put(VARIATION_LOCATION_DETAIL_variation_id, variation_location_details.getVariation_id());
        pValues.put(VARIATION_LOCATION_DETAIL_location_id, variation_location_details.getLocation_id());
        pValues.put(VARIATION_LOCATION_DETAIL_qty_available, variation_location_details.getQty_available());
        pValues.put(VARIATION_LOCATION_DETAIL_OLD_qty_available, variation_location_details.getOld_qty_available());

        // Insert the new row, returning the primary key value of the new row
        int newRowId = mDb.update(VARIATION_LOCATION_DETAIL_TABLE_NAME, pValues, VARIATION_LOCATION_DETAIL_ID + " = '" + variation_location_details.getId() + "'", null); //renvoie l'id de l'enregistrement créé

        // mDb.close();
        return newRowId;
    }


    public int updateSellQty(Integer product_id, Integer location_id, Integer new_qty) {
        Variation_location_details variation_location_details = new Variation_location_details();

        String selectQuery = "SELECT * FROM " + VARIATION_LOCATION_DETAIL_TABLE_NAME + " WHERE " + VARIATION_LOCATION_DETAIL_PRODUCT_ID + " = " + product_id + " AND " + VARIATION_LOCATION_DETAIL_location_id + " = " + location_id;

        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {

            variation_location_details.setId(cursor.getInt(0));
            variation_location_details.setProduct_id(cursor.getInt(1));
            variation_location_details.setProduct_variation_id(cursor.getInt(2));
            variation_location_details.setVariation_id(cursor.getInt(3));
            variation_location_details.setLocation_id(cursor.getInt(4));
            variation_location_details. setQty_available(cursor.getInt(5));
            variation_location_details.setOld_qty_available(cursor.getInt(6));
        }
        // Create a new map of values, where column names are the keys
        ContentValues pValues = new ContentValues();

        pValues.put(VARIATION_LOCATION_DETAIL_product_variation_id, variation_location_details.getProduct_variation_id());
        pValues.put(VARIATION_LOCATION_DETAIL_variation_id, variation_location_details.getVariation_id());
        pValues.put(VARIATION_LOCATION_DETAIL_location_id, variation_location_details.getLocation_id());
        pValues.put(VARIATION_LOCATION_DETAIL_qty_available, (variation_location_details.getQty_available() - new_qty));
        pValues.put(VARIATION_LOCATION_DETAIL_OLD_qty_available, variation_location_details.getQty_available());

        // Insert the new row, returning the primary key value of the new row
        int newRowId = mDb.update(VARIATION_LOCATION_DETAIL_TABLE_NAME, pValues, VARIATION_LOCATION_DETAIL_ID + " = '" + variation_location_details.getId() + "'", null); //renvoie l'id de l'enregistrement créé

        // mDb.close();
        return newRowId;
    }

    public int updatePurchaseQty(Integer product_id, Integer location_id, Integer new_qty) {
        Variation_location_details variation_location_details = new Variation_location_details();

        String selectQuery = "SELECT  * FROM " + VARIATION_LOCATION_DETAIL_TABLE_NAME + " WHERE " + VARIATION_LOCATION_DETAIL_PRODUCT_ID + " = " + product_id + " AND " + VARIATION_LOCATION_DETAIL_location_id + " = " + location_id;

        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {

            variation_location_details.setId(cursor.getInt(0));
            variation_location_details.setProduct_id(cursor.getInt(1));
            variation_location_details.setProduct_variation_id(cursor.getInt(2));
            variation_location_details.setVariation_id(cursor.getInt(3));
            variation_location_details.setLocation_id(cursor.getInt(4));
            variation_location_details.setQty_available(cursor.getInt(5));
            variation_location_details.setOld_qty_available(cursor.getInt(6));
        }

        // Create a new map of values, where column names are the keys
        ContentValues pValues = new ContentValues();

        pValues.put(VARIATION_LOCATION_DETAIL_product_variation_id, variation_location_details.getProduct_variation_id());
        pValues.put(VARIATION_LOCATION_DETAIL_variation_id, variation_location_details.getVariation_id());
        pValues.put(VARIATION_LOCATION_DETAIL_location_id, variation_location_details.getLocation_id());
        pValues.put(VARIATION_LOCATION_DETAIL_qty_available, (variation_location_details.getQty_available() + new_qty));
        pValues.put(VARIATION_LOCATION_DETAIL_OLD_qty_available, variation_location_details.getQty_available());

        // Insert the new row, returning the primary key value of the new row
        int newRowId = mDb.update(VARIATION_LOCATION_DETAIL_TABLE_NAME, pValues, VARIATION_LOCATION_DETAIL_ID + " = '" + variation_location_details.getId() + "'", null); //renvoie l'id de l'enregistrement créé

        // mDb.close();
        return newRowId;
    }

    public void deleteItem(Integer id) {
        mDb.execSQL("delete from " + VARIATION_LOCATION_DETAIL_TABLE_NAME + " WHERE " + VARIATION_LOCATION_DETAIL_ID + " = '" + id + "'");
    }
    public void deleteAll() {
        mDb.execSQL("delete from " + VARIATION_LOCATION_DETAIL_TABLE_NAME);
    }

    public void deleteItemIdProductIdLocation(Integer product_id, Integer location_id) {
        mDb.execSQL("delete from " + VARIATION_LOCATION_DETAIL_TABLE_NAME + " WHERE " + PRODUCT_LOCATION_PRODUCT_ID + " = '" + product_id + "'" +" AND " + VARIATION_LOCATION_DETAIL_location_id + " = '" + location_id + "'" );
    }


    public ArrayList<Variation_location_details> getAllVariationLocationDetails(Integer location_id, Integer category_id) {
        ArrayList<Variation_location_details> variation_location_details = new ArrayList<>();
        String selectQuery = "SELECT  * FROM " + VARIATION_LOCATION_DETAIL_TABLE_NAME + " INNER JOIN " + PRODUCT_TABLE_NAME
                +" ON " + VARIATION_LOCATION_DETAIL_TABLE_NAME+"."+VARIATION_LOCATION_DETAIL_PRODUCT_ID + " = " + PRODUCT_TABLE_NAME+"."+ PRODUCT_ID
                + " WHERE " ;
        String selectLocation = (location_id.equals(0)) ? (VARIATION_LOCATION_DETAIL_TABLE_NAME+"."+PRODUCT_LOCATION_LOCATION_ID + " IS NOT NULL ") : VARIATION_LOCATION_DETAIL_TABLE_NAME+"."+PRODUCT_LOCATION_LOCATION_ID + " = '" + location_id + "'";
        String selectCategory = " AND " + ((category_id.equals(0)) ? (PRODUCT_TABLE_NAME+"."+PRODUCT_CATEGORY_ID + " IS NOT NULL ") : PRODUCT_TABLE_NAME+"."+PRODUCT_CATEGORY_ID + " = '" + category_id + "'");
  //      String selectLast = " AND " + VARIATION_LOCATION_DETAIL_TABLE_NAME+"."+VARIATION_LOCATION_DETAIL_PRODUCT_ID + " = " + PRODUCT_TABLE_NAME+"."+ PRODUCT_ID;
     //   String locationQuery = " AND " + ((locationId.equals(0)) ? (TRANSACTION_LOCATION_ID + " IS NOT NULL ") : (TRANSACTION_LOCATION_ID + " = '" + locationId + "'"));

        selectQuery += selectLocation + selectCategory ;
        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {
            do {
                Variation_location_details variation_location_details1 = new Variation_location_details();
                variation_location_details1.setId(cursor.getInt(0));
                variation_location_details1.setProduct_id(cursor.getInt(1));
                variation_location_details1.setProduct_variation_id(cursor.getInt(2));
                variation_location_details1.setVariation_id(cursor.getInt(3));
                variation_location_details1.setLocation_id(cursor.getInt(4));
                variation_location_details1.setQty_available(cursor.getInt(5));
                variation_location_details1.setOld_qty_available(cursor.getInt(6));

                variation_location_details.add(variation_location_details1);

            } while (cursor.moveToNext());

        }

        // mDb.close();

        return variation_location_details;
    }

    public Variation_location_details getVariationLocationDetailsById(Integer id) {
        Variation_location_details variation_location_details = new Variation_location_details();

        String selectQuery = "SELECT  * FROM " + VARIATION_LOCATION_DETAIL_TABLE_NAME + " WHERE " + VARIATION_LOCATION_DETAIL_ID + " = " + id;

        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {

            variation_location_details.setId(cursor.getInt(0));
            variation_location_details.setProduct_id(cursor.getInt(1));
            variation_location_details.setProduct_variation_id(cursor.getInt(2));
            variation_location_details.setVariation_id(cursor.getInt(3));
            variation_location_details.setLocation_id(cursor.getInt(4));
            variation_location_details.setQty_available(cursor.getInt(5));
            variation_location_details.setOld_qty_available(cursor.getInt(6));
        }

        // mDb.close();

        return variation_location_details;
    }

    public ArrayList<Variation_location_details> getVariationLocationDetailsByProductId(Integer product_id) {
        ArrayList<Variation_location_details> variation_location_details = new ArrayList<>();
        String selectQuery = "SELECT  * FROM " + VARIATION_LOCATION_DETAIL_TABLE_NAME + " INNER JOIN " + PRODUCT_LOCATION_TABLE_NAME + " ON "
                + VARIATION_LOCATION_DETAIL_TABLE_NAME + "." + VARIATION_LOCATION_DETAIL_PRODUCT_ID + " = " + PRODUCT_LOCATION_TABLE_NAME + "." + PRODUCT_LOCATION_PRODUCT_ID
                + " WHERE " + VARIATION_LOCATION_DETAIL_TABLE_NAME + "." + VARIATION_LOCATION_DETAIL_location_id + " = " + PRODUCT_LOCATION_TABLE_NAME + "." + PRODUCT_LOCATION_LOCATION_ID
                + " AND " + VARIATION_LOCATION_DETAIL_TABLE_NAME + "." + VARIATION_LOCATION_DETAIL_PRODUCT_ID + " = " + product_id;

        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {
            do {
                Variation_location_details variation_location_details1 = new Variation_location_details();

                if (cursor.getInt(4) > 0) {
                    variation_location_details1.setId(cursor.getInt(0));
                    variation_location_details1.setProduct_id(cursor.getInt(1));
                    variation_location_details1.setProduct_variation_id(cursor.getInt(2));
                    variation_location_details1.setVariation_id(cursor.getInt(3));
                    variation_location_details1.setLocation_id(cursor.getInt(4));
                    variation_location_details1.setQty_available(cursor.getInt(5));
                    variation_location_details1.setOld_qty_available(cursor.getInt(6));

                    variation_location_details.add(variation_location_details1);
                }

            } while (cursor.moveToNext());

        }

        // mDb.close();

        return variation_location_details;
    }

    public Variation_location_details getVariationLocationDetailsByStationIdProductId(Integer id_station, Integer id_product) {
        Variation_location_details variation_location_details = new Variation_location_details();

        String selectQuery = "SELECT  * FROM " + VARIATION_LOCATION_DETAIL_TABLE_NAME + " WHERE " + VARIATION_LOCATION_DETAIL_PRODUCT_ID + " = " + id_product + " AND " + VARIATION_LOCATION_DETAIL_location_id + " = " + id_station;

        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {

            variation_location_details.setId(cursor.getInt(0));
            variation_location_details.setProduct_id(cursor.getInt(1));
            variation_location_details.setProduct_variation_id(cursor.getInt(2));
            variation_location_details.setVariation_id(cursor.getInt(3));
            variation_location_details.setLocation_id(cursor.getInt(4));
            variation_location_details.setQty_available(cursor.getInt(5));
            variation_location_details.setOld_qty_available(cursor.getInt(6));
        }

        // mDb.close();

        return variation_location_details;
    }
    public boolean isProductHasVariationInStation(Integer id_station, Integer id_product) {
        boolean exist =false;

        String selectQuery = "SELECT  * FROM " + VARIATION_LOCATION_DETAIL_TABLE_NAME + " WHERE " + VARIATION_LOCATION_DETAIL_PRODUCT_ID + " = " + id_product + " AND " + VARIATION_LOCATION_DETAIL_location_id + " = " + id_station;

        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {
            do {
                exist=true;
            } while (cursor.moveToNext());
        }

        return exist;
    }

    // Insert all product
    public void fill(ArrayList<Variation_location_details> variation_location_details) {
        if (!variation_location_details.isEmpty()) {
            for (Variation_location_details variation_location_details1 : variation_location_details) {
                this.insert(variation_location_details1);
            }
        }
        //   mDb.close();
    }

    public void clear() {
        mDb.execSQL("DELETE FROM " + VARIATION_LOCATION_DETAIL_TABLE_NAME);
    }

    public void updateAvailableQty(int qty,int id) {
        mDb.execSQL("UPDATE " + VARIATION_LOCATION_DETAIL_TABLE_NAME + " SET " + VARIATION_LOCATION_DETAIL_qty_available + " = " + qty + " WHERE " + VARIATION_LOCATION_DETAIL_ID + " = " + id);
    }
}
