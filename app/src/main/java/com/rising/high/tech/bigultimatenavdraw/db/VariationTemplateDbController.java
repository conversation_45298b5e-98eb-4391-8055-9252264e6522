package com.rising.high.tech.bigultimatenavdraw.db;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;


import com.rising.high.tech.bigultimatenavdraw.model.Variation_template;

import java.util.ArrayList;

public class VariationTemplateDbController extends DBController {


    public static final String VARIATION_TEMPLATE_TABLE_NAME = "variation_templates";
    public static final String VARIATION_TEMPLATE_ID = "id"; //int
    public static final String VARIATION_TEMPLATE_NAME = "name";
    public static final String VARIATION_TEMPLATE_BUSINESS_ID = "business_id";

    public static final String VARIATION_TEMPLATE_TABLE_CREATE =
            "CREATE TABLE " + VARIATION_TEMPLATE_TABLE_NAME + " (" +
                    VARIATION_TEMPLATE_ID + " INTEGER PRIMARY KEY AUTOINCREMENT, " +
                    VARIATION_TEMPLATE_NAME + " TEXT, " +
                    VARIATION_TEMPLATE_BUSINESS_ID + " INTEGER) ;";

    public static final String VARIATION_TEMPLATE_TABLE_DROP = "DROP TABLE IF EXISTS " + VARIATION_TEMPLATE_TABLE_NAME + ";";

    public VariationTemplateDbController(Context context) {
        super(context);
    }

    public int insertLocal(Variation_template variation) {
        // Create a new map of values, where column names are the keys
        ContentValues pValues = new ContentValues();
        pValues.put(VARIATION_TEMPLATE_NAME, variation.getName());
        pValues.put(VARIATION_TEMPLATE_BUSINESS_ID, variation.getBusiness_id());
        return (int) mDb.insert(VARIATION_TEMPLATE_TABLE_NAME, null, pValues); //returns the id of the created record
    }

    public int updateLocal(Variation_template variation) {
        // Create a new map of values, where column names are the keys
        ContentValues pValues = new ContentValues();
        pValues.put(VARIATION_TEMPLATE_NAME, variation.getName());
        pValues.put(VARIATION_TEMPLATE_BUSINESS_ID, variation.getBusiness_id());
        return mDb.update(VARIATION_TEMPLATE_TABLE_NAME, pValues, VARIATION_TEMPLATE_ID + " = '" + variation.getId() + "'", null);

    }

    public void fill(ArrayList<Variation_template> variations) {
        if (!variations.isEmpty()) {
            for (Variation_template product : variations) {
                this.insertLocal(product);
            }
        }
    }

    public void deleteAll() {
        mDb.execSQL("delete from " + VARIATION_TEMPLATE_TABLE_NAME);
    }

    public void deleteItem(int id) {
        mDb.execSQL("delete from " + VARIATION_TEMPLATE_TABLE_NAME + " WHERE " + VARIATION_TEMPLATE_ID + " = '" + id + "'");
    }

    public ArrayList<Variation_template> getAllXVariation() {
        ArrayList<Variation_template> tmpVariationTemplate = new ArrayList<>();
        String selectQuery = "SELECT  * FROM " + VARIATION_TEMPLATE_TABLE_NAME;
        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {
            do {
                Variation_template variation = new Variation_template();
                variation.setId(Integer.parseInt(cursor.getString(0)));
                variation.setName(cursor.getString(1));
                variation.setBusiness_id(cursor.getInt(2));
                tmpVariationTemplate.add(variation);

            } while (cursor.moveToNext());
        }
        return tmpVariationTemplate;
    }

    public ArrayList<Variation_template> getVariationLike(String name) {
        ArrayList<Variation_template> tmpVariationTemplate = new ArrayList<>();
        String selectQuery = "SELECT  * FROM " + VARIATION_TEMPLATE_TABLE_NAME + " WHERE " + VARIATION_TEMPLATE_NAME + " LIKE '%" + name +  "%' ";

        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {
            do {
                Variation_template variation = new Variation_template();
                variation.setId(Integer.parseInt(cursor.getString(0)));
                variation.setName(cursor.getString(1));
                variation.setBusiness_id(cursor.getInt(2));
                tmpVariationTemplate.add(variation);

            } while (cursor.moveToNext());
        }


        return tmpVariationTemplate;

    }

}
