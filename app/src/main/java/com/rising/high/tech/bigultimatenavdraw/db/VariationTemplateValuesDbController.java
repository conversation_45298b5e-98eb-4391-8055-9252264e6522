package com.rising.high.tech.bigultimatenavdraw.db;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;

import com.rising.high.tech.bigultimatenavdraw.db.DBController;
import com.rising.high.tech.bigultimatenavdraw.model.Sell_lines;
import com.rising.high.tech.bigultimatenavdraw.model.VariationTemplateValues;
import com.rising.high.tech.bigultimatenavdraw.model.Variation_template;

import java.util.ArrayList;

public class VariationTemplateValuesDbController extends DBController {


    public static final String VARIATION_TEMPLATE_TABLE_NAME = "variation_value_templates";
    public static final String VARIATION_ID = "id"; //int
    public static final String VARIATION_TEMPLATE_NAME = "name";
    public static final String VARIATION_TEMPLATE_ID = "variation_template_id";

    public static final String VARIATION_TEMPLATE_TABLE_CREATE =
            "CREATE TABLE " + VARIATION_TEMPLATE_TABLE_NAME + " (" +
                    VARIATION_ID + " INTEGER PRIMARY KEY AUTOINCREMENT, " +
                    VARIATION_TEMPLATE_NAME + " TEXT, " +
                    VARIATION_TEMPLATE_ID + " INTEGER) ;";

    public static final String VARIATION_TEMPLATE_TABLE_DROP = "DROP TABLE IF EXISTS " + VARIATION_TEMPLATE_TABLE_NAME + ";";

    public VariationTemplateValuesDbController(Context context) {
        super(context);
    }

    public static int insertLocal(VariationTemplateValues variation) {
        // Create a new map of values, where column names are the keys
        ContentValues pValues = new ContentValues();
        pValues.put(VARIATION_TEMPLATE_NAME, variation.getName());
        pValues.put(VARIATION_TEMPLATE_ID, variation.getVariation_template_id());
        return (int) mDb.insert(VARIATION_TEMPLATE_TABLE_NAME, null, pValues); //returns the id of the created record
    }



    public void deleteAll() {
        mDb.execSQL("delete from " + VARIATION_TEMPLATE_TABLE_NAME);
    }

    public static void deleteItem(int variation_template_id) {
        mDb.execSQL("delete from " + VARIATION_TEMPLATE_TABLE_NAME + " WHERE " + VARIATION_TEMPLATE_ID + " = '" + variation_template_id + "'");
    }

    public ArrayList<VariationTemplateValues> getAllVariationValues(int variation_id) {
        ArrayList<VariationTemplateValues> tmpVariationTemplate = new ArrayList<>();
        String selectQuery = "SELECT  * FROM " + VARIATION_TEMPLATE_TABLE_NAME + " WHERE " + VARIATION_TEMPLATE_ID + " = " + variation_id;
        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {
            do {
                VariationTemplateValues variation = new VariationTemplateValues();
                variation.setId(Integer.parseInt(cursor.getString(0)));
                variation.setName(cursor.getString(1));
                variation.setVariation_template_id(cursor.getInt(2));
                tmpVariationTemplate.add(variation);

            } while (cursor.moveToNext());
        }
        return tmpVariationTemplate;
    }

}
