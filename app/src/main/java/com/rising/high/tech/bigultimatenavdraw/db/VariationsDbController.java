package com.rising.high.tech.bigultimatenavdraw.db;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.util.Log;

import com.google.gson.Gson;
import com.rising.high.tech.bigultimatenavdraw.model.Product;
import com.rising.high.tech.bigultimatenavdraw.model.Variation;
import com.rising.high.tech.bigultimatenavdraw.model.Variation;

import java.util.ArrayList;

public class VariationsDbController extends DBController {


    public static final String VARIATION_TABLE_NAME = "variations";

    public static final String VARIATION_ID = "id"; //int
    public static final String VARIATION_NAME = "name";
    public static final String VARIATION_PRODUCT_ID = "product_id";
    public static final String VARIATION_SUB_SKU = "sub_sku";
    public static final String VARIATION_PRODUCT_VARIATION_ID = "product_variation_id";
    public static final String VARIATION_DEFAULT_PURCHASE_PRICE = "default_purchase_price";
    public static final String VARIATION_DPP_INC_TAX = "dpp_inc_tax";
    public static final String VARIATION_PROFIT_PERCENT = "profit_percent";
    public static final String VARIATION_DEFAULT_SELL_PRICE = "default_sell_price";
    public static final String VARIATION_SELL_PRICE_INC_TAX = "sell_price_inc_tax";
    public static final String VARIATION_SERVER_ID = "variation_server_id";


    public static final String VARIATION_TABLE_CREATE =
            "CREATE TABLE " + VARIATION_TABLE_NAME + " (" +
                    VARIATION_ID + " INTEGER PRIMARY KEY AUTOINCREMENT, " +
                    VARIATION_NAME + " TEXT, " +
                    VARIATION_PRODUCT_ID + " INTEGER, " +
                    VARIATION_SUB_SKU + " TEXT, " +
                    VARIATION_PRODUCT_VARIATION_ID + " INTEGER, " +
                    VARIATION_DEFAULT_PURCHASE_PRICE + " TEXT, " +
                    VARIATION_DPP_INC_TAX + " TEXT, " +
                    VARIATION_PROFIT_PERCENT + " TEXT, " +
                    VARIATION_DEFAULT_SELL_PRICE + " TEXT, " +
                    VARIATION_SELL_PRICE_INC_TAX + " TEXT, " +
                     VARIATION_SERVER_ID + " INTEGER) ;";


    public static final String VARIATION_TABLE_DROP = "DROP TABLE IF EXISTS " + VARIATION_TABLE_NAME + ";";

    public VariationsDbController(Context context) {
        super(context);
    }

    public int insert(Variation variation) {
        // Create a new map of values, where column names are the keys
        ContentValues pValues = new ContentValues();

        pValues.put(VARIATION_ID, variation.getId());
        pValues.put(VARIATION_NAME, variation.getName());
        pValues.put(VARIATION_PRODUCT_ID, variation.getProduct_id());
        pValues.put(VARIATION_SUB_SKU, variation.getSub_sku());
        pValues.put(VARIATION_PRODUCT_VARIATION_ID, variation.getProduct_variation_id());
        pValues.put(VARIATION_DEFAULT_PURCHASE_PRICE, variation.getDefault_purchase_price());
        pValues.put(VARIATION_DPP_INC_TAX, variation.getDpp_inc_tax());
        pValues.put(VARIATION_PROFIT_PERCENT, variation.getProfit_percent());
        pValues.put(VARIATION_DEFAULT_SELL_PRICE, variation.getDefault_sell_price());
        pValues.put(VARIATION_SELL_PRICE_INC_TAX, variation.getSell_price_inc_tax());

        int newRowId = (int) mDb.insert(VARIATION_TABLE_NAME, null, pValues); //renvoie l'id de l'enregistrement créé
        return newRowId;
    }

    public int insertLocal(Variation variation) {
        // Create a new map of values, where column names are the keys
        ContentValues pValues = new ContentValues();
        pValues.put(VARIATION_NAME, variation.getName());
        pValues.put(VARIATION_PRODUCT_ID, variation.getProduct_id());
        pValues.put(VARIATION_SUB_SKU, variation.getSub_sku());
        pValues.put(VARIATION_PRODUCT_VARIATION_ID, variation.getProduct_variation_id());
        pValues.put(VARIATION_DEFAULT_PURCHASE_PRICE, variation.getDefault_purchase_price());
        pValues.put(VARIATION_DPP_INC_TAX, variation.getDpp_inc_tax());
        pValues.put(VARIATION_PROFIT_PERCENT, variation.getProfit_percent());
        pValues.put(VARIATION_DEFAULT_SELL_PRICE, variation.getDefault_sell_price());
        pValues.put(VARIATION_SELL_PRICE_INC_TAX, variation.getSell_price_inc_tax());

        int newRowId = (int) mDb.insert(VARIATION_TABLE_NAME, null, pValues); //renvoie l'id de l'enregistrement créé
        return newRowId;
    }

    public void fill(ArrayList<Variation> variations) {
        if (!variations.isEmpty()) {
            for (Variation product : variations) {
                this.insertLocal(product);
            }
        }
    }

    public void deleteAll() {
        mDb.execSQL("delete from " + VARIATION_TABLE_NAME);
    }

    public void deleteItem(String name) {
        mDb.execSQL("delete from " + VARIATION_TABLE_NAME + " WHERE " + VARIATION_NAME + " = '" + name + "'");
    }

    public ArrayList<Variation> getAllVariation() {
        ArrayList<Variation> tmpVariationTemplate = new ArrayList<>();
        String selectQuery = "SELECT  * FROM " + VARIATION_TABLE_NAME;
        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {
            do {
                Variation variation = new Variation();

                variation.setId(cursor.getInt(0));
                variation.setName(cursor.getString(1));
                variation.setProduct_id(cursor.getInt(2));
                variation.setSub_sku(cursor.getString(3));
                variation.setProduct_variation_id(cursor.getInt(4));
                variation.setDefault_purchase_price(cursor.getString(5));
                variation.setDpp_inc_tax(cursor.getString(6));
                variation.setProfit_percent(cursor.getString(7));
                variation.setDefault_sell_price(cursor.getString(8));
                variation.setSell_price_inc_tax(cursor.getString(9));


                tmpVariationTemplate.add(variation);

            } while (cursor.moveToNext());
        }


        return tmpVariationTemplate;

    }

    public Variation getVariationByProductId(Integer id) {
        Variation variation = new Variation();
        String selectQuery = "SELECT  * FROM " + VARIATION_TABLE_NAME + " WHERE " + VARIATION_PRODUCT_ID + " = " + id;
        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {

            variation.setId(cursor.getInt(0));
            variation.setName(cursor.getString(1));
            variation.setProduct_id(cursor.getInt(2));
            variation.setSub_sku(cursor.getString(3));
            variation.setProduct_variation_id(cursor.getInt(4));
            variation.setDefault_purchase_price(cursor.getString(5));
            variation.setDpp_inc_tax(cursor.getString(6));
            variation.setProfit_percent(cursor.getString(7));
            variation.setDefault_sell_price(cursor.getString(8));
            variation.setSell_price_inc_tax(cursor.getString(9));
            variation.setVariation_server_id(cursor.getInt(10));
        }


        return variation;

    }


    public ArrayList<Variation> getVariationLike(String name) {
        ArrayList<Variation> tmpVariationTemplate = new ArrayList<>();
        String selectQuery = "SELECT  * FROM " + VARIATION_TABLE_NAME + " WHERE " + VARIATION_NAME + " LIKE '%" + name + "%' ";

        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {
            do {
                Variation variation = new Variation();
                variation.setId(cursor.getInt(0));
                variation.setName(cursor.getString(1));
                variation.setProduct_id(cursor.getInt(2));
                variation.setSub_sku(cursor.getString(3));
                variation.setProduct_variation_id(cursor.getInt(4));
                variation.setDefault_purchase_price(cursor.getString(5));
                variation.setDpp_inc_tax(cursor.getString(6));
                variation.setProfit_percent(cursor.getString(7));
                variation.setDefault_purchase_price(cursor.getString(8));
                variation.setSell_price_inc_tax(cursor.getString(9));


                tmpVariationTemplate.add(variation);

            } while (cursor.moveToNext());
        }


        return tmpVariationTemplate;

    }


    public int updateVariation(Variation variation) {
        // Create a new map of values, where column names are the keys
        ContentValues pValues = new ContentValues();

        pValues.put(VARIATION_NAME, variation.getName());
        pValues.put(VARIATION_PRODUCT_ID, variation.getProduct_id());
        pValues.put(VARIATION_SUB_SKU, variation.getSub_sku());
        pValues.put(VARIATION_PRODUCT_VARIATION_ID, variation.getProduct_variation_id());
        pValues.put(VARIATION_DEFAULT_PURCHASE_PRICE, variation.getDefault_purchase_price());
        pValues.put(VARIATION_DPP_INC_TAX, variation.getDpp_inc_tax());
        pValues.put(VARIATION_PROFIT_PERCENT, variation.getProfit_percent());
        pValues.put(VARIATION_DEFAULT_SELL_PRICE, variation.getDefault_sell_price());
        pValues.put(VARIATION_SELL_PRICE_INC_TAX, variation.getSell_price_inc_tax());

        // Insert the new row, returning the primary key value of the new row
        int newRowId = mDb.update(VARIATION_TABLE_NAME, pValues, VARIATION_ID + " = '" + variation.getId() + "'", null); //renvoie l'id de l'enregistrement créé

        // mDb.close();
        return newRowId;
    }


    public int editServerId(Variation variation) {
        // Create a new map of values, where column names are the keys
        ContentValues pValues = new ContentValues();


        pValues.put(VARIATION_SERVER_ID, variation.getVariation_server_id());

        // Insert the new row, returning the primary key value of the new row
        int newRowId = mDb.update(VARIATION_TABLE_NAME, pValues, VARIATION_ID + " = '" + variation.getId() + "'", null); //renvoie l'id de l'enregistrement créé

        // mDb.close();
        return newRowId;
    }


}
