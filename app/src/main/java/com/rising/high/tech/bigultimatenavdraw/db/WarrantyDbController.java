package com.rising.high.tech.bigultimatenavdraw.db;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;

import com.rising.high.tech.bigultimatenavdraw.model.Warranty;

import java.util.ArrayList;

public class WarrantyDbController extends DBController {


    public static final String WARRANTY_TABLE_NAME = "warranties";

    public static final String WARRANTY_ID = "id"; //int
    public static final String WARRANTY_NAME = "name";
    public static final String WARRANTY_DESCRIPTION = "description";
    public static final String WARRANTY_DURATION = "duration";
    public static final String WARRANTY_DURATION_TYPE = "duration_type";
    public static final String WARRANTY_BUSINESS_ID = "business_id";

    public static final String WARRANTY_TABLE_CREATE =
            "CREATE TABLE " + WARRANTY_TABLE_NAME + " (" +
                    WARRANTY_ID + " INTEGER PRIMARY KEY AUTOINCREMENT, " +
                    WARRANTY_NAME + " TEXT, " +
                    WARRANTY_DESCRIPTION + " TEXT, " +
                    WARRANTY_DURATION + " TEXT, " +
                     WARRANTY_DURATION_TYPE + " TEXT, " +
                     WARRANTY_BUSINESS_ID + " INTEGER) ;";


    public static final String WARRANTY_TABLE_DROP = "DROP TABLE IF EXISTS " + WARRANTY_TABLE_NAME + ";";

    public WarrantyDbController(Context context) {
        super(context);
    }

    public int insert(Warranty warranty) {
        // Create a new map of values, where column names are the keys
        ContentValues pValues = new ContentValues();

        pValues.put(WARRANTY_ID, warranty.getId());
        pValues.put(WARRANTY_NAME, warranty.getName());
        pValues.put(WARRANTY_DESCRIPTION, warranty.getDescription());
        pValues.put(WARRANTY_DURATION, warranty.getDuration());
        pValues.put(WARRANTY_DURATION_TYPE, warranty.getDuration_type());
        pValues.put(WARRANTY_BUSINESS_ID, warranty.getBusiness_id());

        int newRowId = (int) mDb.insert(WARRANTY_TABLE_NAME, null, pValues); //renvoie l'id de l'enregistrement créé
        return newRowId;
    }

    public int insertLocal(Warranty warranty) {
        // Create a new map of values, where column names are the keys
        ContentValues pValues = new ContentValues();

        pValues.put(WARRANTY_NAME, warranty.getName());
        pValues.put(WARRANTY_DESCRIPTION, warranty.getDescription());
        pValues.put(WARRANTY_DURATION, warranty.getDuration());
        pValues.put(WARRANTY_DURATION_TYPE, warranty.getDuration_type());
        pValues.put(WARRANTY_BUSINESS_ID, warranty.getBusiness_id());

        int newRowId = (int) mDb.insert(WARRANTY_TABLE_NAME, null, pValues); //renvoie l'id de l'enregistrement créé
        return newRowId;
    }

    public int editWaranty(Warranty warranty) {
        // Create a new map of values, where column names are the keys
        ContentValues pValues = new ContentValues();

        pValues.put(WARRANTY_NAME, warranty.getName());
        pValues.put(WARRANTY_DESCRIPTION, warranty.getDescription());
        pValues.put(WARRANTY_DURATION, warranty.getDuration());
        pValues.put(WARRANTY_DURATION_TYPE, warranty.getDuration_type());

        int newRowId = mDb.update(WARRANTY_TABLE_NAME, pValues, WARRANTY_ID + " = '" + warranty.getId() + "'", null); //renvoie l'id de l'enregistrement créé

        
        return newRowId;
    }

    public void fill(ArrayList<Warranty> warrantys) {
        if (!warrantys.isEmpty()) {
            for (Warranty product : warrantys) {
                this.insert(product);
            }
        }
    }

    public void deleteAll() {
        mDb.execSQL("delete from " + WARRANTY_TABLE_NAME);
    }

    public void deleteItem(String name) {
        mDb.execSQL("delete from " + WARRANTY_TABLE_NAME + " WHERE " + WARRANTY_NAME + " = '" + name + "'");
    }

    public ArrayList<Warranty> getAllWarranty() {
        ArrayList<Warranty> tmpWarranty = new ArrayList<>();
        String selectQuery = "SELECT  * FROM " + WARRANTY_TABLE_NAME;
        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {
            do {
                Warranty warranty = new Warranty();
                warranty.setId(Integer.parseInt(cursor.getString(0)));
                warranty.setName(cursor.getString(1));
                warranty.setDescription(cursor.getString(2));
                warranty.setDuration(cursor.getString(3));
                warranty.setDuration_type(cursor.getString(4));
                warranty.setBusiness_id(cursor.getInt(5));

                tmpWarranty.add(warranty);

            } while (cursor.moveToNext());
        }


        return tmpWarranty;

    }

    public ArrayList<Warranty> getWarrantiesLike(String name) {
        ArrayList<Warranty> tmpWarranty = new ArrayList<>();
        String selectQuery = "SELECT  * FROM " + WARRANTY_TABLE_NAME + " WHERE " + WARRANTY_NAME + " LIKE '%" + name +  "%' ";

        Cursor cursor = mDb.rawQuery(selectQuery, null);

        if (cursor.moveToFirst()) {
            do {
                Warranty warranty = new Warranty();
                warranty.setId(Integer.parseInt(cursor.getString(0)));
                warranty.setName(cursor.getString(1));
                warranty.setDescription(cursor.getString(2));
                warranty.setDuration(cursor.getString(3));
                warranty.setDuration_type(cursor.getString(4));
                warranty.setBusiness_id(cursor.getInt(5));
                tmpWarranty.add(warranty);

            } while (cursor.moveToNext());
        }


        return tmpWarranty;

    }

}
