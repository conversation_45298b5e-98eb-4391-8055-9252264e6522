package com.rising.high.tech.bigultimatenavdraw.model;

public class Brand {
    private int id;
    private String name ;
    private String short_desc ;
    private int business_id ;

    public int getBusiness_id() {
        return business_id;
    }

    public void setBusiness_id(int business_id) {
        this.business_id = business_id;
    }

    public Brand(int id, String name, String short_desc) {
        this.id = id;
        this.name = name;
        this.short_desc = short_desc;
    }

    public Brand() {
    }

    public Brand(int id, String name) {
        this.id = id;
        this.name = name;
    }

    public void setId(int id) {
        this.id = id;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setShort_desc(String short_desc) {
        this.short_desc = short_desc;
    }

    public int getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public String getShort_desc() {
        return short_desc;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;

        if (o == null || getClass() != o.getClass()) return false;

        Brand state = (Brand) o;

        return name.equals(state.name) && id==state.id;
    }

    @Override
    public int hashCode() {
        return name.hashCode();
    }

}
