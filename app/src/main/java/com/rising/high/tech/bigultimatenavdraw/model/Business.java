package com.rising.high.tech.bigultimatenavdraw.model;

public class Business {

    private int id;
    private String name;
    private int currency_id;
    private Integer tax_number_1;
    private String default_sales_tax;
    private String default_profit_percent;
    private int owner_id;
    private String time_zone;
    private String fy_start_month;
    private String default_sales_discount;
    private String sell_price_tax;
    private String logo;
    private String sku_prefix;
    private int enable_product_expiry;
    private String transaction_edit_days;
    private Integer stock_expiry_alert_days;
    private String date_format;
    private String time_format;
    private String start_date;
    private String currency_symbol_placement;
    private String accounting_method;
    private String sync;
    private Integer business_server_id;

    public void setSync(String sync) {
        this.sync = sync;
    }

    public void setBusiness_server_id(Integer business_server_id) {
        this.business_server_id = business_server_id;
    }

    public String getSync() {
        return sync;
    }

    public Integer getBusiness_server_id() {
        return business_server_id;
    }

    public String getAccounting_method() {
        return accounting_method;
    }

    public void setAccounting_method(String accounting_method) {
        this.accounting_method = accounting_method;
    }

    public String getCurrency_symbol_placement() {
        return currency_symbol_placement;
    }

    public void setCurrency_symbol_placement(String currency_symbol_placement) {
        this.currency_symbol_placement = currency_symbol_placement;
    }

    public String getStart_date() {
        return start_date;
    }

    public void setStart_date(String start_date) {
        this.start_date = start_date;
    }

    public void setId(int id) {
        this.id = id;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setCurrency_id(int currency_id) {
        this.currency_id = currency_id;
    }

    public void setTax_number_1(Integer tax_number_1) {
        this.tax_number_1 = tax_number_1;
    }

    public void setDefault_sales_tax(String default_sales_tax) {
        this.default_sales_tax = default_sales_tax;
    }

    public void setDefault_profit_percent(String default_profit_percent) {
        this.default_profit_percent = default_profit_percent;
    }

    public void setOwner_id(int owner_id) {
        this.owner_id = owner_id;
    }

    public void setTime_zone(String time_zone) {
        this.time_zone = time_zone;
    }

    public void setFy_start_month(String fy_start_month) {
        this.fy_start_month = fy_start_month;
    }

    public void setDefault_sales_discount(String default_sales_discount) {
        this.default_sales_discount = default_sales_discount;
    }

    public void setSell_price_tax(String sell_price_tax) {
        this.sell_price_tax = sell_price_tax;
    }

    public void setLogo(String logo) {
        this.logo = logo;
    }

    public void setSku_prefix(String sku_prefix) {
        this.sku_prefix = sku_prefix;
    }

    public void setEnable_product_expiry(int enable_product_expiry) {
        this.enable_product_expiry = enable_product_expiry;
    }

    public void setTransaction_edit_days(String transaction_edit_days) {
        this.transaction_edit_days = transaction_edit_days;
    }

    public void setStock_expiry_alert_days(Integer stock_expiry_alert_days) {
        this.stock_expiry_alert_days = stock_expiry_alert_days;
    }

    public void setDate_format(String date_format) {
        this.date_format = date_format;
    }

    public void setTime_format(String time_format) {
        this.time_format = time_format;
    }

    public int getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public int getCurrency_id() {
        return currency_id;
    }

    public Integer getTax_number_1() {
        return tax_number_1;
    }

    public String getDefault_sales_tax() {
        return default_sales_tax;
    }

    public String getDefault_profit_percent() {
        return default_profit_percent;
    }

    public int getOwner_id() {
        return owner_id;
    }

    public String getTime_zone() {
        return time_zone;
    }

    public String getFy_start_month() {
        return fy_start_month;
    }

    public String getDefault_sales_discount() {
        return default_sales_discount;
    }

    public String getSell_price_tax() {
        return sell_price_tax;
    }

    public String getLogo() {
        return logo;
    }

    public String getSku_prefix() {
        return sku_prefix;
    }

    public int getEnable_product_expiry() {
        return enable_product_expiry;
    }

    public String getTransaction_edit_days() {
        return transaction_edit_days;
    }

    public Integer getStock_expiry_alert_days() {
        return stock_expiry_alert_days;
    }

    public String getDate_format() {
        return date_format;
    }

    public String getTime_format() {
        return time_format;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;

        if (o == null || getClass() != o.getClass()) return false;

        Business state = (Business) o;

        return name.equals(state.name) && id==state.id;
    }

    @Override
    public int hashCode() {
        return name.hashCode();
    }

}
