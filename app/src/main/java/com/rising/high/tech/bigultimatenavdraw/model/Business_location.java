package com.rising.high.tech.bigultimatenavdraw.model;

public class Business_location {

    private int id;
    private int business_id;
    private String localtion_id;
    private String name;
    private String landmark;
    private String country;
    private String state;
    private String city;
    private String zip_code;
    private String email;
    private String mobile;
    private Integer is_active;
    private Integer location_server_id;
    private String sync;

    public Business_location() {
    }

    public Business_location(int id, String name) {
        this.id = id;
        this.name = name;
    }

    public void setLocation_server_id(Integer location_server_id) {
        this.location_server_id = location_server_id;
    }

    public void setSync(String sync) {
        this.sync = sync;
    }

    public Integer getLocation_server_id() {
        return location_server_id;
    }

    public String getSync() {
        return sync;
    }

    public void setIs_active(Integer is_active) {
        this.is_active = is_active;
    }

    public Integer getIs_active() {
        return is_active;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public void setSelected(int selected) {
        this.selected = selected;
    }

    public int getSelected() {
        return selected;
    }

    private int selected;

    public void setLocaltion_id(String localtion_id) {
        this.localtion_id = localtion_id;
    }

    public String getLocaltion_id() {
        return localtion_id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public void setBusiness_id(int business_id) {
        this.business_id = business_id;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setLandmark(String landmark) {
        this.landmark = landmark;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public void setState(String state) {
        this.state = state;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public void setZip_code(String zip_code) {
        this.zip_code = zip_code;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public void setWebsite(String website) {
        this.website = website;
    }

    public int getId() {
        return id;
    }

    public int getBusiness_id() {
        return business_id;
    }

    public String getName() {
        return name;
    }

    public String getLandmark() {
        return landmark;
    }

    public String getCountry() {
        return country;
    }

    public String getState() {
        return state;
    }

    public String getCity() {
        return city;
    }

    public String getZip_code() {
        return zip_code;
    }

    public String getEmail() {
        return email;
    }

    public String getWebsite() {
        return website;
    }

    private String website;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;

        if (o == null || getClass() != o.getClass()) return false;

        Business_location state = (Business_location) o;

        return  id==state.id;
    }

    @Override
    public int hashCode() {
        return name.hashCode();
    }
}
