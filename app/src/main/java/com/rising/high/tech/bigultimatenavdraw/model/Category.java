package com.rising.high.tech.bigultimatenavdraw.model;

import java.util.ArrayList;

public class Category {
    private Integer id;
    private String name;
    private int business_id;
    private Integer parent_id;
    private int created_by;
    private String category_type;
    private String description;
    private String slug;
    private String short_code;
    private ArrayList<String> sub_categories;
    private String is_sync;
    private Integer category_server_id;
    private boolean selected;

    public Category() {
    }
    public Category(Integer id, String name) {
        this.id = id;
        this.name = name;
    }

    public void setSelected(boolean selected) {
        this.selected = selected;
    }

    public boolean isSelected() {
        return selected;
    }

    public void setCategory_server_id(Integer category_server_id) {
        this.category_server_id = category_server_id;
    }

    public Integer getCategory_server_id() {
        return category_server_id;
    }

    public void setIs_sync(String is_sync) {
        this.is_sync = is_sync;
    }

    public String getIs_sync() {
        return is_sync;
    }

    public void setSub_categories(ArrayList<String> sub_categories) {
        this.sub_categories = sub_categories;
    }

    public ArrayList<String> getSub_categories() {
        return sub_categories;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setBusiness_id(int business_id) {
        this.business_id = business_id;
    }

    public void setParent_id(Integer parent_id) {
        this.parent_id = parent_id;
    }

    public void setCreated_by(int created_by) {
        this.created_by = created_by;
    }

    public void setCategory_type(String category_type) {
        this.category_type = category_type;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public void setSlug(String slug) {
        this.slug = slug;
    }

    public void setShort_code(String short_code) {
        this.short_code = short_code;
    }

    public Integer getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public int getBusiness_id() {
        return business_id;
    }

    public Integer getParent_id() {
        return parent_id;
    }

    public int getCreated_by() {
        return created_by;
    }

    public String getCategory_type() {
        return category_type;
    }

    public String getDescription() {
        return description;
    }

    public String getSlug() {
        return slug;
    }

    public String getShort_code() {
        return short_code;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;

        if (o == null || getClass() != o.getClass()) return false;

        Category state = (Category) o;

       // return name.equals(state.name) && id == state.id;
        return id == state.id;
    }

    @Override
    public int hashCode() {
        return name.hashCode();
    }

}
