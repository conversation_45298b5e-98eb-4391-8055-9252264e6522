package com.rising.high.tech.bigultimatenavdraw.model;

public class Contact {

    private int id;
    private String name;
    private String prefix;
    private String email;
    private String mobile;
    private String city;
    private String country;
    private String state;
    private String land_mark;
    private String location;
    private String customer;
    private String type;
    private String id_location;
    private Integer business_id;
    private String created_by;
    private String customer_filed_1;
    private String customer_id;
    private String customer_filed_3;
    private String customer_filed_4;
    private String sync;
    private String contact_id;
    private String contact_status;
    private String first_name;
    private String last_name;
    private String address_line_1;
    private String created_at;
    private String shipping_address;
    private String zip_code;
    private String tax_number;
    private String balance;
    private Integer pay_term_number;
    private String credit_limit;
    private String pay_term_type;
    private Integer customer_group_id;
    private Integer contact_server_id;

    public void setContact_server_id(Integer contact_server_id) {
        this.contact_server_id = contact_server_id;
    }

    public Integer getContact_server_id() {
        return contact_server_id;
    }

    public void setCustomer_group_id(Integer customer_group_id) {
        this.customer_group_id = customer_group_id;
    }

    public Integer getCustomer_group_id() {
        return customer_group_id;
    }

    public void setPrefix(String prefix) {
        this.prefix = prefix;
    }

    public String getPrefix() {
        return prefix;
    }

    public Contact() {
    }

    public Contact(int id, String name) {
        this.id = id;
        this.name = name;
    }

    public void setCredit_limit(String credit_limit) {
        this.credit_limit = credit_limit;
    }

    public String getCredit_limit() {
        return credit_limit;
    }

    public void setPay_term_number(Integer pay_term_number) {
        this.pay_term_number = pay_term_number;
    }

    public void setPay_term_type(String pay_term_type) {
        this.pay_term_type = pay_term_type;
    }

    public String getPay_term_type() {
        return pay_term_type;
    }

    public Integer getPay_term_number() {
        return pay_term_number;
    }

    public void setBalance(String balance) {
        this.balance = balance;
    }

    public String getBalance() {
        return balance;
    }

    public void setTax_number(String tax_number) {
        this.tax_number = tax_number;
    }

    public String getTax_number() {
        return tax_number;
    }

    public void setShipping_address(String shipping_address) {
        this.shipping_address = shipping_address;
    }

    public void setZip_code(String zip_code) {
        this.zip_code = zip_code;
    }

    public String getShipping_address() {
        return shipping_address;
    }

    public String getZip_code() {
        return zip_code;
    }

    public void setCreated_at(String created_at) {
        this.created_at = created_at;
    }

    public String getCreated_at() {
        return created_at;
    }

    public void setAddress_line_1(String address_line_1) {
        this.address_line_1 = address_line_1;
    }

    public String getAddress_line_1() {
        return address_line_1;
    }

    public void setDob(String dob) {
        this.dob = dob;
    }

    public String getDob() {
        return dob;
    }

    private String dob;

    public void setSupplier_business_name(String supplier_business_name) {
        this.supplier_business_name = supplier_business_name;
    }

    public String getSupplier_business_name() {
        return supplier_business_name;
    }

    private String supplier_business_name;

    public void setFirst_name(String first_name) {
        this.first_name = first_name;
    }

    public void setLast_name(String last_name) {
        this.last_name = last_name;
    }

    public String getFirst_name() {
        return first_name;
    }

    public String getLast_name() {
        return last_name;
    }

    public void setContact_status(String contact_status) {
        this.contact_status = contact_status;
    }

    public String getContact_status() {
        return contact_status;
    }

    public void setContact_id(String contact_id) {
        this.contact_id = contact_id;
    }

    public String getContact_id() {
        return contact_id;
    }

    public void setId_location(String id_location) {
        this.id_location = id_location;
    }

    public String getId_location() {
        return id_location;
    }

    public void setSync(String sync) {
        this.sync = sync;
    }

    public String getSync() {
        return sync;
    }

    public void setType(String type) {
        this.type = type;
    }

    public void setBusiness_id(Integer business_id) {
        this.business_id = business_id;
    }

    public void setCreated_by(String created_by) {
        this.created_by = created_by;
    }

    public String getType() {
        return type;
    }

    public Integer getBusiness_id() {
            return business_id;
    }

    public String getCreated_by() {
        return created_by;
    }

    public void setCustomer_id(String customer_id) {
        this.customer_id = customer_id;
    }

    public String getCustomer_id() {
        return customer_id;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getLocation() {
        return location;
    }

    public void setCustomer(String customer) {
        this.customer = customer;
    }

    public String getCustomer() {
        return customer;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getState() {
        return state;
    }

    public void setId(int id) {
        this.id = id;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public void setLand_mark(String land_mark) {
        this.land_mark = land_mark;
    }

    public void setCustomer_filed_1(String customer_filed_1) {
        this.customer_filed_1 = customer_filed_1;
    }

    public void setCustomer_filed_3(String customer_filed_3) {
        this.customer_filed_3 = customer_filed_3;
    }

    public void setCustomer_filed_4(String customer_filed_4) {
        this.customer_filed_4 = customer_filed_4;
    }

    public int getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public String getEmail() {
        return email;
    }

    public String getMobile() {
        return mobile;
    }

    public String getCity() {
        return city;
    }

    public String getCountry() {
        return country;
    }

    public String getLand_mark() {
        return land_mark;
    }

    public String getCustomer_filed_1() {
        return customer_filed_1;
    }

    public String getCustomer_filed_3() {
        return customer_filed_3;
    }

    public String getCustomer_filed_4() {
        return customer_filed_4;
    }


    @Override
    public boolean equals(Object o) {
        if (this == o) return true;

        if (o == null || getClass() != o.getClass()) return false;

        Contact contact = (Contact) o;

        return name.equals(contact.name) && id==contact.id;
    }

    @Override
    public int hashCode() {
        return name.hashCode();
    }


}
