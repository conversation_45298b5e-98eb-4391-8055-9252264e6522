package com.rising.high.tech.bigultimatenavdraw.model;

public class Customer_groups {
    private int id;
    private int business_id;
    private String name ;
    private String amount;
    private String sync;
    private int created_by;
    private Integer cg_server_id;

    public Customer_groups(int id, String name) {
        this.id = id;
        this.name = name;
    }

    public Customer_groups() {
    }

    public void setSync(String sync) {
        this.sync = sync;
    }

    public void setCg_server_id(Integer cg_server_id) {
        this.cg_server_id = cg_server_id;
    }

    public String getSync() {
        return sync;
    }

    public Integer getCg_server_id() {
        return cg_server_id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public void setBusiness_id(int business_id) {
        this.business_id = business_id;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setAmount(String amount) {
        this.amount = amount;
    }

    public void setCreated_by(int created_by) {
        this.created_by = created_by;
    }

    public int getId() {
        return id;
    }

    public int getBusiness_id() {
        return business_id;
    }

    public String getName() {
        return name;
    }

    public String getAmount() {
        return amount;
    }

    public int getCreated_by() {
        return created_by;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;

        if (o == null || getClass() != o.getClass()) return false;

        Customer_groups customer_groups = (Customer_groups) o;

        return  id==customer_groups.id;
    }

    @Override
    public int hashCode() {
        return name.hashCode();
    }


}
