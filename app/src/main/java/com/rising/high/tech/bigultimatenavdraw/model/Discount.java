package com.rising.high.tech.bigultimatenavdraw.model;

public class Discount {
    private int id;
    private String name;
    private Integer business_id;
    private Integer brand_id;
    private Integer category_id;
    private Integer location_id;
    private int priority;
    private String discount_type;
    private String discount_amount;
    private String starts_at;
    private String ends_at;
    private int is_active;
    private int applicable_in_spg;
    private int applicable_in_cg;
    private String sync;
    private Integer discount_server_id;

    public void setSync(String sync) {
        this.sync = sync;
    }

    public void setDiscount_server_id(Integer discount_server_id) {
        this.discount_server_id = discount_server_id;
    }

    public String getSync() {
        return sync;
    }

    public Integer getDiscount_server_id() {
        return discount_server_id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setBusiness_id(Integer business_id) {
        this.business_id = business_id;
    }

    public void setBrand_id(Integer brand_id) {
        this.brand_id = brand_id;
    }

    public void setCategory_id(Integer category_id) {
        this.category_id = category_id;
    }

    public void setLocation_id(Integer location_id) {
        this.location_id = location_id;
    }

    public void setPriority(int priority) {
        this.priority = priority;
    }

    public void setDiscount_type(String discount_type) {
        this.discount_type = discount_type;
    }

    public void setDiscount_amount(String discount_amount) {
        this.discount_amount = discount_amount;
    }

    public void setStarts_at(String starts_at) {
        this.starts_at = starts_at;
    }

    public void setEnds_at(String ends_at) {
        this.ends_at = ends_at;
    }

    public void setIs_active(int is_active) {
        this.is_active = is_active;
    }

    public void setApplicable_in_spg(int applicable_in_spg) {
        this.applicable_in_spg = applicable_in_spg;
    }

    public void setApplicable_in_cg(int applicable_in_cg) {
        this.applicable_in_cg = applicable_in_cg;
    }

    public int getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public Integer getBusiness_id() {
        return business_id;
    }

    public Integer getBrand_id() {
        return brand_id;
    }

    public Integer getCategory_id() {
        return category_id;
    }

    public Integer getLocation_id() {
        return location_id;
    }

    public int getPriority() {
        return priority;
    }

    public String getDiscount_type() {
        return discount_type;
    }

    public String getDiscount_amount() {
        return discount_amount;
    }

    public String getStarts_at() {
        return starts_at;
    }

    public String getEnds_at() {
        return ends_at;
    }

    public int getIs_active() {
        return is_active;
    }

    public int getApplicable_in_spg() {
        return applicable_in_spg;
    }

    public int getApplicable_in_cg() {
        return applicable_in_cg;
    }
}
