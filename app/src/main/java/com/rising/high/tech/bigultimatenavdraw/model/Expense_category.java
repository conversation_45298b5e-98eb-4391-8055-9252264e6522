package com.rising.high.tech.bigultimatenavdraw.model;

public class Expense_category {
    private int id;
    private String name;
    private int business_id;
    private String code;

    public Expense_category() {
    }

    public Expense_category(int id, String name) {
        this.id = id;
        this.name = name;
    }

    public void setId(int id) {
        this.id = id;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setBusiness_id(int business_id) {
        this.business_id = business_id;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public int getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public String getCode() {
        return code;
    }

    public int getBusiness_id() {
        return business_id;
    }


    @Override
    public boolean equals(Object o) {
        if (this == o) return true;

        if (o == null || getClass() != o.getClass()) return false;

        Expense_category state = (Expense_category) o;

        return name.equals(state.name) && id==state.id;
    }

    @Override
    public int hashCode() {
        return name.hashCode();
    }


}
