package com.rising.high.tech.bigultimatenavdraw.model;
public class Payement {
    private int id;
    private String amount;
    private String method;
    private String card_number;
    private String card_holder_name;
    private String card_transaction_number;
    private String card_type;
    private String card_month;
    private String card_year;
    private String card_security;
    private String cheque_number;
    private String bank_account_number;
    private String transaction_no_1;
    private String transaction_no_2;
    private String transaction_no_3;
    private String note;

    public Payement(String method, String amount) {
        this.amount = amount;
        this.method = method;
    }

    public Payement() {
    }

    public void setId(int id) {
        this.id = id;
    }

    public void setAmount(String amount) {
        this.amount = amount;
    }

    public void setMethod(String method) {
        this.method = method;
    }

    public void setCard_number(String card_number) {
        this.card_number = card_number;
    }

    public void setCard_holder_name(String card_holder_name) {
        this.card_holder_name = card_holder_name;
    }

    public void setCard_transaction_number(String card_transaction_number) {
        this.card_transaction_number = card_transaction_number;
    }

    public void setCard_type(String card_type) {
        this.card_type = card_type;
    }

    public void setCard_month(String card_month) {
        this.card_month = card_month;
    }

    public void setCard_year(String card_year) {
        this.card_year = card_year;
    }

    public void setCard_security(String card_security) {
        this.card_security = card_security;
    }

    public void setCheque_number(String cheque_number) {
        this.cheque_number = cheque_number;
    }

    public void setBank_account_number(String bank_account_number) {
        this.bank_account_number = bank_account_number;
    }

    public void setTransaction_no_1(String transaction_no_1) {
        this.transaction_no_1 = transaction_no_1;
    }

    public void setTransaction_no_2(String transaction_no_2) {
        this.transaction_no_2 = transaction_no_2;
    }

    public void setTransaction_no_3(String transaction_no_3) {
        this.transaction_no_3 = transaction_no_3;
    }

    public void setNote(String note) {
        this.note = note;
    }

    public int getId() {
        return id;
    }

    public String getAmount() {
        return amount;
    }

    public String getMethod() {
        return method;
    }

    public String getCard_number() {
        return card_number;
    }

    public String getCard_holder_name() {
        return card_holder_name;
    }

    public String getCard_transaction_number() {
        return card_transaction_number;
    }

    public String getCard_type() {
        return card_type;
    }

    public String getCard_month() {
        return card_month;
    }

    public String getCard_year() {
        return card_year;
    }

    public String getCard_security() {
        return card_security;
    }

    public String getCheque_number() {
        return cheque_number;
    }

    public String getBank_account_number() {
        return bank_account_number;
    }

    public String getTransaction_no_1() {
        return transaction_no_1;
    }

    public String getTransaction_no_2() {
        return transaction_no_2;
    }

    public String getTransaction_no_3() {
        return transaction_no_3;
    }

    public String getNote() {
        return note;
    }
}
