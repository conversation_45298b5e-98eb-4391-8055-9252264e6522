package com.rising.high.tech.bigultimatenavdraw.model;

import android.graphics.Bitmap;

import java.util.ArrayList;

public class Product {
    private int id ;
    private String name;
    private int business_id;
    private String type;
    private int unit_id;
    private Integer lcoation_id;
    private Integer sub_unit_ids;
    private Integer brand_id;
    private Integer category_id;
    private Integer sub_category_id;
    private Integer tax;
    private String tax_type;  
    private int enable_stock;
    private Integer alert_quantity;
    private String sku;
    private String barcode_type;
    private String expiry_period;
    private int enable_sr_no;
    private String weight;
    private String image;
    private Bitmap image_product;
    private String product_description;
    private int warranty_id;
    private int created_by;
    private int is_inactive;
    private int not_for_selling;
    private String image_url;
    private Category category;
    private Unit unit;
    private Brand brand;
    private ArrayList<Product_variations> product_variations;
    private int sell_qte = 1;
    private String current_stock;
    private String qty_available;
    private String category_name;
    private String unit_shortname;
    private String unit_actualname;
    private String product_id;
    private String is_sync;
    private String default_sell_price;
    private String default_purchase_price;
    private String dpp_inc_tax;
    private String profit_percent;
    private String sell_price_inc_tax;
    private ArrayList<Product_location> product_locations;
    private Integer product_server_id;
    private Variation variations;
    private Integer variation_id;

    public void setBrand(Brand brand) {
        this.brand = brand;
    }

    public Brand getBrand() {
        return brand;
    }

    public void setVariation_id(Integer variation_id) {
        this.variation_id = variation_id;
    }

    public Integer getVariation_id() {
        return variation_id;
    }

    public void setProduct_server_id(Integer product_server_id) {
        this.product_server_id = product_server_id;
    }

    public Integer getProduct_server_id() {
        return product_server_id;
    }

    public void setVariations(Variation variations) {
        this.variations = variations;
    }

    public Variation getVariations() {
        return variations;
    }

    public void setDefault_sell_price(String default_sell_price) {
        this.default_sell_price = default_sell_price;
    }

    public void setDefault_purchase_price(String default_purchase_price) {
        this.default_purchase_price = default_purchase_price;
    }

    public void setDpp_inc_tax(String dpp_inc_tax) {
        this.dpp_inc_tax = dpp_inc_tax;
    }

    public void setProfit_percent(String profit_percent) {
        this.profit_percent = profit_percent;
    }

    public void setSell_price_inc_tax(String sell_price_inc_tax) {
        this.sell_price_inc_tax = sell_price_inc_tax;
    }

    public String getDefault_sell_price() {
        return default_sell_price;
    }

    public String getDefault_purchase_price() {
        return default_purchase_price;
    }

    public String getDpp_inc_tax() {
        return dpp_inc_tax;
    }

    public String getProfit_percent() {
        return profit_percent;
    }

    public String getSell_price_inc_tax() {
        return sell_price_inc_tax;
    }

    public void setImage_product(Bitmap image_product) {
        this.image_product = image_product;
    }

    public Bitmap getImage_product() {
        return image_product;
    }

    public void setLcoation_id(Integer lcoation_id) {
        this.lcoation_id = lcoation_id;
    }

    public Integer getLcoation_id() {
        return lcoation_id;
    }

    public void setIs_sync(String is_sync) {
        this.is_sync = is_sync;
    }

    public String getIs_sync() {
        return is_sync;
    }

    public void setProduct_id(String product_id) {
        this.product_id = product_id;
    }

    public String getProduct_id() {
        return product_id;
    }

    public void setProduct_locations(ArrayList<Product_location> product_locations) {
        this.product_locations = product_locations;
    }

    public ArrayList<Product_location> getProduct_locations() {
        return product_locations;
    }

    public Product(int id, String name) {
        this.id = id;
        this.name = name;
    }

    public Product() {
    }

    public void setUnit_shortname(String unit_shortname) {
        this.unit_shortname = unit_shortname;
    }

    public void setUnit_actualname(String unit_actualname) {
        this.unit_actualname = unit_actualname;
    }

    public String getUnit_shortname() {
        return unit_shortname;
    }

    public String getUnit_actualname() {
        return unit_actualname;
    }

    public void setUnit(Unit unit) {
        this.unit = unit;
    }

    public Unit getUnit() {
        return unit;
    }

    public void setCategory_name(String category_name) {
        this.category_name = category_name;
    }

    public String getCategory_name() {
        return category_name;
    }

    public void setQty_available(String qty_available) {
        this.qty_available = qty_available;
    }

    public String getQty_available() {
        return qty_available;
    }

    public void setSell_qte(int sell_qte) {
        this.sell_qte = sell_qte;
    }

    public void setCurrent_stock(String current_stock) {
        this.current_stock = current_stock;
    }

    public int getSell_qte() {
        return sell_qte;
    }

    public String getCurrent_stock() {
        return current_stock;
    }

    public void setProduct_variations(ArrayList<Product_variations> product_variations) {
        this.product_variations = product_variations;
    }

    public ArrayList<Product_variations> getProduct_variations() {
        return product_variations;
    }

    public void setImage_url(String image_url) {
        this.image_url = image_url;
    }

    public String getImage_url() {
        return image_url;
    }

    public void setCategory(Category category) {
        this.category = category;
    }

    public Category getCategory() {
        return category;
    }

    public void setId(int id) {
        this.id = id;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setBusiness_id(int business_id) {
        this.business_id = business_id;
    }

    public void setType(String type) {
        this.type = type;
    }

    public void setUnit_id(int unit_id) {
        this.unit_id = unit_id;
    }

    public void setSub_unit_ids(Integer sub_unit_ids) {
        this.sub_unit_ids = sub_unit_ids;
    }

    public void setBrand_id(Integer brand_id) {
        this.brand_id = brand_id;
    }

    public void setCategory_id(Integer category_id) {
        this.category_id = category_id;
    }

    public void setSub_category_id(Integer sub_category_id) {
        this.sub_category_id = sub_category_id;
    }

    public void setTax(Integer tax) {
        this.tax = tax;
    }

    public void setTax_type(String tax_type) {
        this.tax_type = tax_type;
    }

    public void setEnable_stock(int enable_stock) {
        this.enable_stock = enable_stock;
    }

    public void setAlert_quantity(Integer alert_quantity) {
        this.alert_quantity = alert_quantity;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public void setBarcode_type(String barcode_type) {
        this.barcode_type = barcode_type;
    }

    public void setExpiry_period(String expiry_period) {
        this.expiry_period = expiry_period;
    }

    public void setEnable_sr_no(int enable_sr_no) {
        this.enable_sr_no = enable_sr_no;
    }

    public void setWeight(String weight) {
        this.weight = weight;
    }

    public void setImage(String image) {
        this.image = image;
    }

    public void setProduct_description(String product_description) {
        this.product_description = product_description;
    }

    public void setWarranty_id(int warranty_id) {
        this.warranty_id = warranty_id;
    }

    public void setCreated_by(int created_by) {
        this.created_by = created_by;
    }

    public void setIs_inactive(int is_inactive) {
        this.is_inactive = is_inactive;
    }

    public void setNot_for_selling(int not_for_selling) {
        this.not_for_selling = not_for_selling;
    }

    public int getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public int getBusiness_id() {
        return business_id;
    }

    public String getType() {
        return type;
    }

    public int getUnit_id() {
        return unit_id;
    }

    public Integer getSub_unit_ids() {
        return sub_unit_ids;
    }

    public Integer getBrand_id() {
        return brand_id;
    }

    public Integer getCategory_id() {
        return category_id;
    }

    public Integer getSub_category_id() {
        return sub_category_id;
    }

    public Integer getTax() {
        return tax;
    }

    public String getTax_type() {
        return tax_type;
    }

    public int getEnable_stock() {
        return enable_stock;
    }

    public Integer getAlert_quantity() {
        return alert_quantity;
    }

    public String getSku() {
        return sku;
    }

    public String getBarcode_type() {
        return barcode_type;
    }

    public String getExpiry_period() {
        return expiry_period;
    }

    public int getEnable_sr_no() {
        return enable_sr_no;
    }

    public String getWeight() {
        return weight;
    }

    public String getImage() {
        return image;
    }

    public String getProduct_description() {
        return product_description;
    }

    public int getWarranty_id() {
        return warranty_id;
    }

    public int getCreated_by() {
        return created_by;
    }

    public int getIs_inactive() {
        return is_inactive;
    }

    public int getNot_for_selling() {
        return not_for_selling;
    }


    @Override
    public String toString() {
        return name;
    }


    @Override
    public boolean equals(Object o) {
        if (this == o) return true;

        if (o == null || getClass() != o.getClass()) return false;

        Product product = (Product) o;

        return name.equals(product.name) && id==product.id;
    }

    @Override
    public int hashCode() {
        return name.hashCode();
    }


}
