package com.rising.high.tech.bigultimatenavdraw.model;

import java.util.ArrayList;

public class ProductStock {
    private int id ;
    private String name;
    private int business_id;
    private int unit_id;
    private int sub_unit_ids;
    private int brand_id;
    private int category_id;
    private int sub_category_id;
    private int tax;
    private String tax_type;
    private int enable_stock;
    private int alert_quantity;
    private String sku;
    private String barcode_type;
    private String expiry_period;
    private int enable_sr_no;
    private String weight;
    private String image;
    private String product_description;
    private int warranty_id;
    private int created_by;
    private int is_inactive;
    private int not_for_selling;
    private String image_url;
    private String default_sell_price;
    private Category category;
    private ArrayList<Product_variations> product_variations;
    private int sell_qte = 1;
    private String current_stock;
    private String qty_availablee;
    private String category_name;
    private String unit_shortname;
    private String unit_actualname;
    private String product_id;
    private ArrayList<Business_location> product_locations;

    private String total_sold;
    private String stock_price;
    private String stock;
    private String product;
    private String unit_price;
    private String location_name;
    private String type;
    private String location_id;
    private String unit;

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getUnit() {
        return unit;
    }

    public void setTotal_sold(String total_sold) {
        this.total_sold = total_sold;
    }

    public void setStock_price(String stock_price) {
        this.stock_price = stock_price;
    }

    public void setStock(String stock) {
        this.stock = stock;
    }

    public void setProduct(String product) {
        this.product = product;
    }

    public void setUnit_price(String unit_price) {
        this.unit_price = unit_price;
    }

    public void setLocation_name(String location_name) {
        this.location_name = location_name;
    }

    public void setLocation_id(String location_id) {
        this.location_id = location_id;
    }

    public String getTotal_sold() {
        return total_sold;
    }

    public String getStock_price() {
        return stock_price;
    }

    public String getStock() {
        return stock;
    }

    public String getProduct() {
        return product;
    }

    public String getUnit_price() {
        return unit_price;
    }

    public String getLocation_name() {
        return location_name;
    }

    public String getLocation_id() {
        return location_id;
    }

    public void setProduct_id(String product_id) {
        this.product_id = product_id;
    }

    public String getProduct_id() {
        return product_id;
    }

    public void setProduct_locations(ArrayList<Business_location> product_locations) {
        this.product_locations = product_locations;
    }

    public ArrayList<Business_location> getProduct_locations() {
        return product_locations;
    }

    public ProductStock(int id, String name) {
        this.id = id;
        this.name = name;
    }

    public ProductStock() {
    }

    public void setUnit_shortname(String unit_shortname) {
        this.unit_shortname = unit_shortname;
    }

    public void setUnit_actualname(String unit_actualname) {
        this.unit_actualname = unit_actualname;
    }

    public String getUnit_shortname() {
        return unit_shortname;
    }

    public String getUnit_actualname() {
        return unit_actualname;
    }

    public void setCategory_name(String category_name) {
        this.category_name = category_name;
    }

    public String getCategory_name() {
        return category_name;
    }

    public void setQty_availablee(String qty_availablee) {
        this.qty_availablee = qty_availablee;
    }

    public String getQty_availablee() {
        return qty_availablee;
    }

    public void setSell_qte(int sell_qte) {
        this.sell_qte = sell_qte;
    }

    public void setCurrent_stock(String current_stock) {
        this.current_stock = current_stock;
    }

    public int getSell_qte() {
        return sell_qte;
    }

    public String getCurrent_stock() {
        return current_stock;
    }

    public void setDefault_sell_price(String default_sell_price) {
        this.default_sell_price = default_sell_price;
    }

    public String getDefault_sell_price() {
        return default_sell_price;
    }

    public void setProduct_variations(ArrayList<Product_variations> product_variations) {
        this.product_variations = product_variations;
    }

    public ArrayList<Product_variations> getProduct_variations() {
        return product_variations;
    }

    public void setImage_url(String image_url) {
        this.image_url = image_url;
    }

    public String getImage_url() {
        return image_url;
    }

    public void setCategory(Category category) {
        this.category = category;
    }

    public Category getCategory() {
        return category;
    }

    public void setId(int id) {
        this.id = id;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setBusiness_id(int business_id) {
        this.business_id = business_id;
    }

    public void setType(String type) {
        this.type = type;
    }

    public void setUnit_id(int unit_id) {
        this.unit_id = unit_id;
    }

    public void setSub_unit_ids(int sub_unit_ids) {
        this.sub_unit_ids = sub_unit_ids;
    }

    public void setBrand_id(int brand_id) {
        this.brand_id = brand_id;
    }

    public void setCategory_id(int category_id) {
        this.category_id = category_id;
    }

    public void setSub_category_id(int sub_category_id) {
        this.sub_category_id = sub_category_id;
    }

    public void setTax(int tax) {
        this.tax = tax;
    }

    public void setTax_type(String tax_type) {
        this.tax_type = tax_type;
    }

    public void setEnable_stock(int enable_stock) {
        this.enable_stock = enable_stock;
    }

    public void setAlert_quantity(int alert_quantity) {
        this.alert_quantity = alert_quantity;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public void setBarcode_type(String barcode_type) {
        this.barcode_type = barcode_type;
    }

    public void setExpiry_period(String expiry_period) {
        this.expiry_period = expiry_period;
    }

    public void setEnable_sr_no(int enable_sr_no) {
        this.enable_sr_no = enable_sr_no;
    }

    public void setWeight(String weight) {
        this.weight = weight;
    }

    public void setImage(String image) {
        this.image = image;
    }

    public void setProduct_description(String product_description) {
        this.product_description = product_description;
    }

    public void setWarranty_id(int warranty_id) {
        this.warranty_id = warranty_id;
    }

    public void setCreated_by(int created_by) {
        this.created_by = created_by;
    }

    public void setIs_inactive(int is_inactive) {
        this.is_inactive = is_inactive;
    }

    public void setNot_for_selling(int not_for_selling) {
        this.not_for_selling = not_for_selling;
    }

    public int getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public int getBusiness_id() {
        return business_id;
    }

    public String getType() {
        return type;
    }

    public int getUnit_id() {
        return unit_id;
    }

    public int getSub_unit_ids() {
        return sub_unit_ids;
    }

    public int getBrand_id() {
        return brand_id;
    }

    public int getCategory_id() {
        return category_id;
    }

    public int getSub_category_id() {
        return sub_category_id;
    }

    public int getTax() {
        return tax;
    }

    public String getTax_type() {
        return tax_type;
    }

    public int getEnable_stock() {
        return enable_stock;
    }

    public int getAlert_quantity() {
        return alert_quantity;
    }

    public String getSku() {
        return sku;
    }

    public String getBarcode_type() {
        return barcode_type;
    }

    public String getExpiry_period() {
        return expiry_period;
    }

    public int getEnable_sr_no() {
        return enable_sr_no;
    }

    public String getWeight() {
        return weight;
    }

    public String getImage() {
        return image;
    }

    public String getProduct_description() {
        return product_description;
    }

    public int getWarranty_id() {
        return warranty_id;
    }

    public int getCreated_by() {
        return created_by;
    }

    public int getIs_inactive() {
        return is_inactive;
    }

    public int getNot_for_selling() {
        return not_for_selling;
    }
}
