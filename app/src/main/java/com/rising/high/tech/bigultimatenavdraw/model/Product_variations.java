package com.rising.high.tech.bigultimatenavdraw.model;

import java.util.ArrayList;

public class Product_variations {

    private int id ;
    private String product_id;
    private String qty_available;
    private ArrayList<Variation> variations;

    public void setQty_available(String qty_available) {
        this.qty_available = qty_available;
    }

    public String getQty_available() {
        return qty_available;
    }

    public void setId(int id) {
        this.id = id;
    }

    public void setProduct_id(String product_id) {
        this.product_id = product_id;
    }

    public void setVariations(ArrayList<Variation> variations) {
        this.variations = variations;
    }

    public int getId() {
        return id;
    }

    public String getProduct_id() {
        return product_id;
    }

    public ArrayList<Variation> getVariations() {
        return variations;
    }


}
