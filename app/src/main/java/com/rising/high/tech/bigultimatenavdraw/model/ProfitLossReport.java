package com.rising.high.tech.bigultimatenavdraw.model;

public class ProfitLossReport {
    private String opening_stock;
    private String closing_stock;
    private String opening_stock_by_sp;
    private String closing_stock_by_sp;
    private String total_purchase_discount;
    private String total_sell;
    private String total_sell_discount;
    private String total_expense;
    private String net_profit;
    private String gross_profit;
    private String total_sell_return;

    public void setOpening_stock(String opening_stock) {
        this.opening_stock = opening_stock;
    }

    public void setClosing_stock(String closing_stock) {
        this.closing_stock = closing_stock;
    }

    public void setOpening_stock_by_sp(String opening_stock_by_sp) {
        this.opening_stock_by_sp = opening_stock_by_sp;
    }

    public void setClosing_stock_by_sp(String closing_stock_by_sp) {
        this.closing_stock_by_sp = closing_stock_by_sp;
    }

    public void setTotal_purchase_discount(String total_purchase_discount) {
        this.total_purchase_discount = total_purchase_discount;
    }

    public void setTotal_sell(String total_sell) {
        this.total_sell = total_sell;
    }

    public void setTotal_sell_discount(String total_sell_discount) {
        this.total_sell_discount = total_sell_discount;
    }

    public void setTotal_expense(String total_expense) {
        this.total_expense = total_expense;
    }

    public void setNet_profit(String net_profit) {
        this.net_profit = net_profit;
    }

    public void setGross_profit(String gross_profit) {
        this.gross_profit = gross_profit;
    }

    public void setTotal_sell_return(String total_sell_return) {
        this.total_sell_return = total_sell_return;
    }

    public String getOpening_stock() {
        return opening_stock;
    }

    public String getClosing_stock() {
        return closing_stock;
    }

    public String getOpening_stock_by_sp() {
        return opening_stock_by_sp;
    }

    public String getClosing_stock_by_sp() {
        return closing_stock_by_sp;
    }

    public String getTotal_purchase_discount() {
        return total_purchase_discount;
    }

    public String getTotal_sell() {
        return total_sell;
    }

    public String getTotal_sell_discount() {
        return total_sell_discount;
    }

    public String getTotal_expense() {
        return total_expense;
    }

    public String getNet_profit() {
        return net_profit;
    }

    public String getGross_profit() {
        return gross_profit;
    }

    public String getTotal_sell_return() {
        return total_sell_return;
    }
}
