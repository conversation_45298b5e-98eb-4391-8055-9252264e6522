package com.rising.high.tech.bigultimatenavdraw.model;

public class Purchase_line {
    private int id;
    private int transaction_id;
    private int product_id;
    private int quantity;
    private String pp_without_discount;
    private String purchase_price;
    private String purchase_price_inc_tax;
    private String item_tax;
    private String sellingPrice;
    private int variation_id;
    private int tax_id;
    private int quantity_sold;
    private int quantity_adjusted;
    private int quantity_returned;

    public void setSellingPrice(String sellingPrice) {
        this.sellingPrice = sellingPrice;
    }

    public String getSellingPrice() {
        return sellingPrice;
    }

    public String getPp_without_discount() {
        return pp_without_discount;
    }

    public String getPurchase_price() {
        return purchase_price;
    }

    public String getPurchase_price_inc_tax() {
        return purchase_price_inc_tax;
    }

    public String getItem_tax() {
        return item_tax;
    }

    public int getVariation_id() {
        return variation_id;
    }

    public int getTax_id() {
        return tax_id;
    }

    public int getQuantity_sold() {
        return quantity_sold;
    }

    public int getQuantity_adjusted() {
        return quantity_adjusted;
    }

    public int getQuantity_returned() {
        return quantity_returned;
    }

    public void setPp_without_discount(String pp_without_discount) {
        this.pp_without_discount = pp_without_discount;
    }

    public void setPurchase_price(String purchase_price) {
        this.purchase_price = purchase_price;
    }

    public void setPurchase_price_inc_tax(String purchase_price_inc_tax) {
        this.purchase_price_inc_tax = purchase_price_inc_tax;
    }

    public void setItem_tax(String item_tax) {
        this.item_tax = item_tax;
    }

    public void setVariation_id(int variation_id) {
        this.variation_id = variation_id;
    }

    public void setTax_id(int tax_id) {
        this.tax_id = tax_id;
    }

    public void setQuantity_sold(int quantity_sold) {
        this.quantity_sold = quantity_sold;
    }

    public void setQuantity_adjusted(int quantity_adjusted) {
        this.quantity_adjusted = quantity_adjusted;
    }

    public void setQuantity_returned(int quantity_returned) {
        this.quantity_returned = quantity_returned;
    }

    public void setProduct_name(String product_name) {
        this.product_name = product_name;
    }

    public String getProduct_name() {
        return product_name;
    }

    private String product_name;

    public void setId(int id) {
        this.id = id;
    }

    public void setTransaction_id(int transaction_id) {
        this.transaction_id = transaction_id;
    }

    public void setProduct_id(int product_id) {
        this.product_id = product_id;
    }

    public void setQuantity(int quantity) {
        this.quantity = quantity;
    }

    public int getId() {
        return id;
    }

    public int getTransaction_id() {
        return transaction_id;
    }

    public int getProduct_id() {
        return product_id;
    }

    public int getQuantity() {
        return quantity;
    }
}
