package com.rising.high.tech.bigultimatenavdraw.model;

public class ResponseUser<T> {
    private int api_status;
    private int count;
    private String api_message;
    private String message_Type;
    private String api_autorization;
    private T data;
    private int success;
    private int transaction_id;
    private String msg;

    public String getMessage() {
        return message;
    }

    private String message;

    public String getMessage_Type() {
        return message_Type;
    }

    public int getSuccess() {
        return success;
    }

    public int getTransaction_id() {
        return transaction_id;
    }

    public String getMsg() {
        return msg;
    }

    public void setCount(int count) {
        this.count = count;
    }

    public int getCount() {
        return count;
    }

    public int getApi_status() {
        return api_status;
    }

    public String getApi_message() {
        return api_message;
    }

    public String getApi_autorization() {
        return api_autorization;
    }

    public T getData() {
        return data;
    }
}
