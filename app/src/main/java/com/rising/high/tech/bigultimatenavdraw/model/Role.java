package com.rising.high.tech.bigultimatenavdraw.model;

import com.rising.high.tech.bigultimatenavdraw.R;

public class Role {
    private int id;
    private int permission_id;
    private int role_id;
    private int user_id;
    private String name;
    private String guard_name;
    private int business_id;
    private int is_default;
    private int is_service_staff;

    public Role(int permission_id, int role_id) {
        this.permission_id = permission_id;
        this.role_id = role_id;
    }

    public Role() {
    }

    public void setUser_id(int user_id) {
        this.user_id = user_id;
    }

    public int getUser_id() {
        return user_id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setGuard_name(String guard_name) {
        this.guard_name = guard_name;
    }

    public void setBusiness_id(int business_id) {
        this.business_id = business_id;
    }

    public void setIs_default(int is_default) {
        this.is_default = is_default;
    }

    public void setIs_service_staff(int is_service_staff) {
        this.is_service_staff = is_service_staff;
    }

    public int getId() {

        return id;
    }

    public String getName() {
        return name;
    }

    public String getGuard_name() {
        return guard_name;
    }

    public int getBusiness_id() {
        return business_id;
    }

    public int getIs_default() {
        return is_default;
    }

    public int getIs_service_staff() {
        return is_service_staff;
    }

    public void setPermission_id(int permission_id) {
        this.permission_id = permission_id;
    }

    public void setRole_id(int role_id) {
        this.role_id = role_id;
    }

    public int getPermission_id() {
        return permission_id;
    }

    public int getRole_id() {
        return role_id;
    }


    @Override
    public boolean equals(Object o) {
        if (this == o) return true;

        if (o == null || getClass() != o.getClass()) return false;

        Role role = (Role) o;

        return name.equals(role.name) && id==role.id;
    }

    @Override
    public int hashCode() {
        return name.hashCode();
    }



}
