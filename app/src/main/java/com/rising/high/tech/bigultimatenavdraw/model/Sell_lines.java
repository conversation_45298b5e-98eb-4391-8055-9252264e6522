package com.rising.high.tech.bigultimatenavdraw.model;

public class Sell_lines {
    private int id;
    private int transaction_id;
    private int product_id;
    private int quantity;
    private String pp_without_discount;
    private String purchase_price;
    private String unit_price_inc_tax;
    private String item_tax;
    private String unit_price;
    private String unit_price_before_discount;
    private int variation_id;
    private int tax_id;
    private int quantity_sold;
    private int quantity_adjusted;
    private int sell_line_id;
    private int stock_adjustment_line_id;
    private int purchase_line_id;
    private int qty_returned;
    private int lot_no_line_id;
    private int removed_purchase_line;

    public void setRemoved_purchase_line(int removed_purchase_line) {
        this.removed_purchase_line = removed_purchase_line;
    }

    public int getRemoved_purchase_line() {
        return removed_purchase_line;
    }

    public void setLot_no_line_id(int lot_no_line_id) {
        this.lot_no_line_id = lot_no_line_id;
    }

    public int getLot_no_line_id() {
        return lot_no_line_id;
    }

    public void setSell_line_id(int sell_line_id) {
        this.sell_line_id = sell_line_id;
    }

    public void setStock_adjustment_line_id(int stock_adjustment_line_id) {
        this.stock_adjustment_line_id = stock_adjustment_line_id;
    }

    public void setPurchase_line_id(int purchase_line_id) {
        this.purchase_line_id = purchase_line_id;
    }

    public void setQty_returned(int qty_returned) {
        this.qty_returned = qty_returned;
    }

    public int getSell_line_id() {
        return sell_line_id;
    }

    public int getStock_adjustment_line_id() {
        return stock_adjustment_line_id;
    }

    public int getPurchase_line_id() {
        return purchase_line_id;
    }

    public int getQty_returned() {
        return qty_returned;
    }

    public void setUnit_price_before_discount(String unit_price_before_discount) {
        this.unit_price_before_discount = unit_price_before_discount;
    }

    public String getUnit_price_before_discount() {
        return unit_price_before_discount;
    }

    public void setUnit_price(String unit_price) {
        this.unit_price = unit_price;
    }

    public String getUnit_price() {
        return unit_price;
    }

    public String getPp_without_discount() {
        return pp_without_discount;
    }

    public String getPurchase_price() {
        return purchase_price;
    }

    public String getUnit_price_inc_tax() {
        return unit_price_inc_tax;
    }

    public String getItem_tax() {
        return item_tax;
    }

    public int getVariation_id() {
        return variation_id;
    }

    public int getTax_id() {
        return tax_id;
    }

    public int getQuantity_sold() {
        return quantity_sold;
    }

    public int getQuantity_adjusted() {
        return quantity_adjusted;
    }


    public void setPp_without_discount(String pp_without_discount) {
        this.pp_without_discount = pp_without_discount;
    }

    public void setPurchase_price(String purchase_price) {
        this.purchase_price = purchase_price;
    }

    public void setUnit_price_inc_tax(String unit_price_inc_tax) {
        this.unit_price_inc_tax = unit_price_inc_tax;
    }

    public void setItem_tax(String item_tax) {
        this.item_tax = item_tax;
    }

    public void setVariation_id(int variation_id) {
        this.variation_id = variation_id;
    }

    public void setTax_id(int tax_id) {
        this.tax_id = tax_id;
    }

    public void setQuantity_sold(int quantity_sold) {
        this.quantity_sold = quantity_sold;
    }

    public void setQuantity_adjusted(int quantity_adjusted) {
        this.quantity_adjusted = quantity_adjusted;
    }

    public void setProduct_name(String product_name) {
        this.product_name = product_name;
    }

    public String getProduct_name() {
        return product_name;
    }

    private String product_name;

    public void setId(int id) {
        this.id = id;
    }

    public void setTransaction_id(int transaction_id) {
        this.transaction_id = transaction_id;
    }

    public void setProduct_id(int product_id) {
        this.product_id = product_id;
    }

    public void setQuantity(int quantity) {
        this.quantity = quantity;
    }

    public int getId() {
        return id;
    }

    public int getTransaction_id() {
        return transaction_id;
    }

    public int getProduct_id() {
        return product_id;
    }

    public int getQuantity() {
        return quantity;
    }
}
