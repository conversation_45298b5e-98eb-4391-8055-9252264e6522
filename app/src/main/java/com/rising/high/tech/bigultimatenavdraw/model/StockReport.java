package com.rising.high.tech.bigultimatenavdraw.model;

public class StockReport {
    private String sku;
    private String product;
    private String location;
    private String unit_price;
    private String current_stock;
    private String current_stock_value_bpp;
    private String current_stock_value_bsp;
    private String potential_profit;
    private String total_unit_sold;
    private String total_unit_transfered;
    private String total_unit_adjusted;

    public String getSku() {
        return sku;
    }

    public String getProduct() {
        return product;
    }

    public String getLocation() {
        return location;
    }

    public String getUnit_price() {
        return unit_price;
    }

    public String getCurrent_stock() {
        return current_stock;
    }

    public String getCurrent_stock_value_bpp() {
        return current_stock_value_bpp;
    }

    public String getCurrent_stock_value_bsp() {
        return current_stock_value_bsp;
    }

    public String getPotential_profit() {
        return potential_profit;
    }

    public String getTotal_unit_sold() {
        return total_unit_sold;
    }

    public String getTotal_unit_transfered() {
        return total_unit_transfered;
    }

    public String getTotal_unit_adjusted() {
        return total_unit_adjusted;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public void setProduct(String product) {
        this.product = product;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public void setUnit_price(String unit_price) {
        this.unit_price = unit_price;
    }

    public void setCurrent_stock(String current_stock) {
        this.current_stock = current_stock;
    }

    public void setCurrent_stock_value_bpp(String current_stock_value_bpp) {
        this.current_stock_value_bpp = current_stock_value_bpp;
    }

    public void setCurrent_stock_value_bsp(String current_stock_value_bsp) {
        this.current_stock_value_bsp = current_stock_value_bsp;
    }

    public void setPotential_profit(String potential_profit) {
        this.potential_profit = potential_profit;
    }

    public void setTotal_unit_sold(String total_unit_sold) {
        this.total_unit_sold = total_unit_sold;
    }

    public void setTotal_unit_transfered(String total_unit_transfered) {
        this.total_unit_transfered = total_unit_transfered;
    }

    public void setTotal_unit_adjusted(String total_unit_adjusted) {
        this.total_unit_adjusted = total_unit_adjusted;
    }
}
