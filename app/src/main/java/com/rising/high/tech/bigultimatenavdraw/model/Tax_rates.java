package com.rising.high.tech.bigultimatenavdraw.model;

public class Tax_rates {

    private int id;
    private int business_id;
    private String name;
    private String amount;
    private int is_tax_group;
    private int for_tax_group;
    private int created_by;
    private String sync;
    private Integer tr_server_id;

    public Tax_rates() {
    }

    public Tax_rates(int id, String name, String amount) {
        this.id = id;
        this.name = name;
        this.amount = amount;
    }

    public void setSync(String sync) {
        this.sync = sync;
    }

    public void setTr_server_id(Integer tr_server_id) {
        this.tr_server_id = tr_server_id;
    }

    public Integer getTr_server_id() {
        return tr_server_id;
    }

    public String getSync() {
        return sync;
    }

    public void setId(int id) {
        this.id = id;
    }

    public void setBusiness_id(int business_id) {
        this.business_id = business_id;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setAmount(String amount) {
        this.amount = amount;
    }

    public void setIs_tax_group(int is_tax_group) {
        this.is_tax_group = is_tax_group;
    }

    public void setFor_tax_group(int for_tax_group) {
        this.for_tax_group = for_tax_group;
    }

    public void setCreated_by(int created_by) {
        this.created_by = created_by;
    }

    public int getId() {
        return id;
    }

    public int getBusiness_id() {
        return business_id;
    }

    public String getName() {
        return name;
    }

    public String getAmount() {
        return amount;
    }

    public int getIs_tax_group() {
        return is_tax_group;
    }

    public int getFor_tax_group() {
        return for_tax_group;
    }

    public int getCreated_by() {
        return created_by;
    }



    @Override
    public boolean equals(Object o) {
        if (this == o) return true;

        if (o == null || getClass() != o.getClass()) return false;

        Tax_rates tax_rates = (Tax_rates) o;

        return id==tax_rates.id;
    }

    @Override
    public int hashCode() {
        return name.hashCode();
    }



}
