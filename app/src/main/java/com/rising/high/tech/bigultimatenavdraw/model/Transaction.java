package com.rising.high.tech.bigultimatenavdraw.model;

import java.util.ArrayList;

public class Transaction {
    private int id;
    private int location_id;
    private int business_id;
    private int transaction_id;
    private int created_by;
    private String transaction_date;
    private int is_direct_sale;
    private String invoice_no;
    private String name;
    private String customer;
    private String status;
    private String final_total;
    private String tax_amount;
    private String discount_amount;
    private String discount_type;
    private String total_before_tax;
    private int rp_redeemed;
    private String rp_redeemed_amount;
    private int rp_earned;
    private String business_location;
    private String return_transaction_id;
    private String total_paid;
    private String return_exists;
    private String return_paid;
    private String amount_return;
    private String tax_calculation_amount;
    private String shipping_details;
    private String shipping_charges;
    private String shipping;
    private Integer contact_id;
    private Integer transfer_parent_id;
    private Integer return_parent_id;
    private String is_suspend;
    private String sell_price_tax;
    private String is_sync;
    private String note;
    private String payment_status;
    private String ref_no;
    private String transaction_no;
    private String additional_notes;
    private float amount;
    private int customer_id;
    private ArrayList<Sell_lines> sell_lines;
    private ArrayList<Transaction> payment_lines;
    private ArrayList<Sell_lines> products;
    private ArrayList<Transaction> payments;
    private boolean isExpanded;
    private Integer parent_id;
    private String method;
    private String is_advance;
    private String paid_on;
    private String is_return;
    private Integer payment_for;
    private String card_type;
    private String payment_ref_no;
    private String card_transaction_number;
    private String card_number;
    private String card_holder_name;
    private String card_month;
    private String card_year;
    private String card_security;
    private String cheque_number;
    private String bank_account_number;
    private String document;
    private String type;
    private String pay_term_number;
    private String pay_term_type;
    private String recur_interval_type;
    private Integer expense_category_id;
    private Integer is_recurring;
    private Integer recur_repetitions;
    private Integer recur_interval;
    private String adjustment_type;
    private String total_amount_recovered;
    private String shipping_status;
    private String delivered_to;
    private Integer tax_id;
    private Integer opening_stock_product_id;
    private int is_deleted;

    public void setIs_deleted(int is_deleted) {
        this.is_deleted = is_deleted;
    }

    public int getIs_deleted() {
        return is_deleted;
    }

    public void setPayments(ArrayList<Transaction> payments) {
        this.payments = payments;
    }

    public void setProducts(ArrayList<Sell_lines> products) {
        this.products = products;
    }

    public ArrayList<Transaction> getPayments() {
        return payments;
    }

    public ArrayList<Sell_lines> getProducts() {
        return products;
    }

    public void setOpening_stock_product_id(Integer opening_stock_product_id) {
        this.opening_stock_product_id = opening_stock_product_id;
    }

    public Integer getOpening_stock_product_id() {
        return opening_stock_product_id;
    }

    public void setTax_id(Integer tax_id) {
        this.tax_id = tax_id;
    }

    public Integer getTax_id() {
        return tax_id;
    }

    public void setReturn_parent_id(Integer return_parent_id) {
        this.return_parent_id = return_parent_id;
    }

    public Integer getReturn_parent_id() {
        return return_parent_id;
    }

    public void setDelivered_to(String delivered_to) {
        this.delivered_to = delivered_to;
    }

    public String getDelivered_to() {
        return delivered_to;
    }

    public void setShipping_status(String shipping_status) {
        this.shipping_status = shipping_status;
    }

    public String getShipping_status() {
        return shipping_status;
    }

    public void setShipping_address(String shipping_address) {
        this.shipping_address = shipping_address;
    }

    public String getShipping_address() {
        return shipping_address;
    }

    private String shipping_address;

    public void setIs_quotation(Integer is_quotation) {
        this.is_quotation = is_quotation;
    }

    public Integer getIs_quotation() {
        return is_quotation;
    }

    private Integer is_quotation;

    public void setTotal_amount_recovered(String total_amount_recovered) {
        this.total_amount_recovered = total_amount_recovered;
    }

    public String getTotal_amount_recovered() {
        return total_amount_recovered;
    }

    public void setAdjustment_type(String adjustment_type) {
        this.adjustment_type = adjustment_type;
    }

    public String getAdjustment_type() {
        return adjustment_type;
    }

    public void setTransfer_parent_id(Integer transfer_parent_id) {
        this.transfer_parent_id = transfer_parent_id;
    }

    public Integer getTransfer_parent_id() {
        return transfer_parent_id;
    }

    public void setRecur_interval(Integer recur_interval) {
        this.recur_interval = recur_interval;
    }

    public Integer getRecur_interval() {
        return recur_interval;
    }

    public void setRecur_repetitions(Integer recur_repetitions) {
        this.recur_repetitions = recur_repetitions;
    }

    public Integer getRecur_repetitions() {
        return recur_repetitions;
    }

    public void setRecur_interval_type(String recur_interval_type) {
        this.recur_interval_type = recur_interval_type;
    }

    public String getRecur_interval_type() {
        return recur_interval_type;
    }

    public void setIs_recurring(Integer is_recurring) {
        this.is_recurring = is_recurring;
    }

    public Integer getIs_recurring() {
        return is_recurring;
    }

    public void setExpense_category_id(Integer expense_category_id) {
        this.expense_category_id = expense_category_id;
    }

    public Integer getExpense_category_id() {
        return expense_category_id;
    }

    public String getAdditional_notes() {
        return additional_notes;
    }

    public void setAdditional_notes(String additional_notes) {
        this.additional_notes = additional_notes;
    }

    public void setShipping_charges(String shipping_charges) {
        this.shipping_charges = shipping_charges;
    }

    public String getShipping_charges() {
        return shipping_charges;
    }

    public void setPay_term_type(String pay_term_type) {
        this.pay_term_type = pay_term_type;
    }

    public String getPay_term_type() {
        return pay_term_type;
    }

    public void setPay_term_number(String pay_term_number) {
        this.pay_term_number = pay_term_number;
    }

    public String getPay_term_number() {
        return pay_term_number;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getType() {
        return type;
    }

    public void setPayment_for(Integer payment_for) {
        this.payment_for = payment_for;
    }

    public Integer getPayment_for() {
        return payment_for;
    }

    public void setPayment_lines(ArrayList<Transaction> payment_lines) {
        this.payment_lines = payment_lines;
    }

    public ArrayList<Transaction> getPayment_lines() {
        return payment_lines;
    }

    public void setIs_advance(String is_advance) {
        this.is_advance = is_advance;
    }

    public void setPaid_on(String paid_on) {
        this.paid_on = paid_on;
    }

    public void setIs_return(String is_return) {
        this.is_return = is_return;
    }


    public void setCard_type(String card_type) {
        this.card_type = card_type;
    }

    public void setPayment_ref_no(String payment_ref_no) {
        this.payment_ref_no = payment_ref_no;
    }

    public void setCard_transaction_number(String card_transaction_number) {
        this.card_transaction_number = card_transaction_number;
    }

    public void setCard_number(String card_number) {
        this.card_number = card_number;
    }

    public void setCard_holder_name(String card_holder_name) {
        this.card_holder_name = card_holder_name;
    }

    public void setCard_month(String card_month) {
        this.card_month = card_month;
    }

    public void setCard_year(String card_year) {
        this.card_year = card_year;
    }

    public void setCard_security(String card_security) {
        this.card_security = card_security;
    }

    public void setCheque_number(String cheque_number) {
        this.cheque_number = cheque_number;
    }

    public void setBank_account_number(String bank_account_number) {
        this.bank_account_number = bank_account_number;
    }

    public void setDocument(String document) {
        this.document = document;
    }


    public String getIs_advance() {
        return is_advance;
    }

    public String getPaid_on() {
        return paid_on;
    }

    public String getIs_return() {
        return is_return;
    }


    public String getCard_type() {
        return card_type;
    }

    public String getPayment_ref_no() {
        return payment_ref_no;
    }

    public String getCard_transaction_number() {
        return card_transaction_number;
    }

    public String getCard_number() {
        return card_number;
    }

    public String getCard_holder_name() {
        return card_holder_name;
    }

    public String getCard_month() {
        return card_month;
    }

    public String getCard_year() {
        return card_year;
    }

    public String getCard_security() {
        return card_security;
    }

    public String getCheque_number() {
        return cheque_number;
    }

    public String getBank_account_number() {
        return bank_account_number;
    }

    public String getDocument() {
        return document;
    }

    public void setTransaction_no(String transaction_no) {
        this.transaction_no = transaction_no;
    }

    public String getTransaction_no() {
        return transaction_no;
    }

    public void setMethod(String method) {
        this.method = method;
    }

    public String getMethod() {
        return method;
    }

    public void setParent_id(Integer parent_id) {
        this.parent_id = parent_id;
    }

    public Integer getParent_id() {
        return parent_id;
    }

    public void setCreated_by(int created_by) {
        this.created_by = created_by;
    }

    public int getCreated_by() {
        return created_by;
    }

    public void setTransaction_id(int transaction_id) {
        this.transaction_id = transaction_id;
    }

    public int getTransaction_id() {
        return transaction_id;
    }

    public void setContact_id(Integer contact_id) {
        this.contact_id = contact_id;
    }

    public Integer getContact_id() {
        return contact_id;
    }

    public void setRef_no(String ref_no) {
        this.ref_no = ref_no;
    }

    public String getRef_no() {
        return ref_no;
    }

    public void setExpanded(boolean expanded) {
        isExpanded = expanded;
    }

    public boolean isExpanded() {
        return isExpanded;
    }

    public void setPayment_status(String payment_status) {
        this.payment_status = payment_status;
    }

    public String getPayment_status() {
        return payment_status;
    }

    public void setLocation_id(int location_id) {
        this.location_id = location_id;
    }

    public void setBusiness_id(int business_id) {
        this.business_id = business_id;
    }

    public void setSell_lines(ArrayList<Sell_lines> sell_lines) {
        this.sell_lines = sell_lines;
    }

    public int getLocation_id() {
        return location_id;
    }

    public int getBusiness_id() {
        return business_id;
    }

    public ArrayList<Sell_lines> getSell_lines() {
        return sell_lines;
    }

    public void setCustomer_id(int customer_id) {
        this.customer_id = customer_id;
    }

    public int getCustomer_id() {
        return customer_id;
    }

    public void setCustomer(String customer) {
        this.customer = customer;
    }

    public String getCustomer() {
        return customer;
    }

    public void setNote(String note) {
        this.note = note;
    }

    public String getNote() {
        return note;
    }

    public void setShipping(String shipping) {
        this.shipping = shipping;
    }

    public String getShipping() {
        return shipping;
    }

    public void setSell_price_tax(String sell_price_tax) {
        this.sell_price_tax = sell_price_tax;
    }

    public String getSell_price_tax() {
        return sell_price_tax;
    }

    public String getIs_sync() {
        return is_sync;
    }

    public void setIs_sync(String is_sync) {
        this.is_sync = is_sync;
    }

    public void setTax_calculation_amount(String tax_calculation_amount) {
        this.tax_calculation_amount = tax_calculation_amount;
    }

    public void setShipping_details(String shipping_details) {
        this.shipping_details = shipping_details;
    }

    public void setIs_suspend(String is_suspend) {
        this.is_suspend = is_suspend;
    }

    public String getTax_calculation_amount() {
        return tax_calculation_amount;
    }

    public String getShipping_details() {
        return shipping_details;
    }

    public String getIs_suspend() {
        return is_suspend;
    }

    public void setTransaction_date(String transaction_date) {
        this.transaction_date = transaction_date;
    }

    public void setIs_direct_sale(int is_direct_sale) {
        this.is_direct_sale = is_direct_sale;
    }

    public void setInvoice_no(String invoice_no) {
        this.invoice_no = invoice_no;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setStatus(String payment_status) {
        this.status = payment_status;
    }

    public void setFinal_total(String final_total) {
        this.final_total = final_total;
    }

    public void setTax_amount(String tax_amount) {
        this.tax_amount = tax_amount;
    }

    public void setDiscount_amount(String discount_amount) {
        this.discount_amount = discount_amount;
    }

    public void setDiscount_type(String discount_type) {
        this.discount_type = discount_type;
    }

    public void setTotal_before_tax(String total_before_tax) {
        this.total_before_tax = total_before_tax;
    }

    public void setRp_redeemed(int rp_redeemed) {
        this.rp_redeemed = rp_redeemed;
    }

    public void setRp_redeemed_amount(String rp_redeemed_amount) {
        this.rp_redeemed_amount = rp_redeemed_amount;
    }

    public void setRp_earned(int rp_earned) {
        this.rp_earned = rp_earned;
    }

    public void setBusiness_location(String business_location) {
        this.business_location = business_location;
    }

    public void setReturn_transaction_id(String return_transaction_id) {
        this.return_transaction_id = return_transaction_id;
    }

    public void setTotal_paid(String total_paid) {
        this.total_paid = total_paid;
    }

    public void setReturn_exists(String return_exists) {
        this.return_exists = return_exists;
    }

    public void setReturn_paid(String return_paid) {
        this.return_paid = return_paid;
    }

    public void setAmount_return(String amount_return) {
        this.amount_return = amount_return;
    }

    public String getTransaction_date() {
        return transaction_date;
    }

    public int getIs_direct_sale() {
        return is_direct_sale;
    }

    public String getInvoice_no() {
        return invoice_no;
    }

    public String getName() {
        return name;
    }

    public String getStatus() {
        return status;
    }

    public String getFinal_total() {
        return final_total;
    }

    public String getTax_amount() {
        return tax_amount;
    }

    public String getDiscount_amount() {
        return discount_amount;
    }

    public String getDiscount_type() {
        return discount_type;
    }

    public String getTotal_before_tax() {
        return total_before_tax;
    }

    public int getRp_redeemed() {
        return rp_redeemed;
    }

    public String getRp_redeemed_amount() {
        return rp_redeemed_amount;
    }

    public int getRp_earned() {
        return rp_earned;
    }

    public String getBusiness_location() {
        return business_location;
    }

    public String getReturn_transaction_id() {
        return return_transaction_id;
    }

    public String getTotal_paid() {
        return total_paid;
    }

    public String getReturn_exists() {
        return return_exists;
    }

    public String getReturn_paid() {
        return return_paid;
    }

    public String getAmount_return() {
        return amount_return;
    }

    public void setAmount(float amount) {
        this.amount = amount;
    }

    public float getAmount() {
        return amount;
    }

    public Transaction(int id, float amount) {
        this.id = id;
        this.amount = amount;
    }

    public Transaction() {
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getId() {
        return id;
    }
}
