package com.rising.high.tech.bigultimatenavdraw.model;

public class Unit {
    private int id;
    private String actual_name;
    private String short_name;
    private Integer allow_decimal;
    private Integer business_id;
    private Integer unit_server_id;
    private String sync;
    private Integer created_by;

    public Integer getBusiness_id() {
        return business_id;
    }

    public void setBusiness_id(Integer business_id) {
        this.business_id = business_id;
    }

    public Unit() {
    }

    public void setCreated_by(Integer created_by) {
        this.created_by = created_by;
    }

    public Integer getCreated_by() {
        return created_by;
    }

    public Unit(int id, String actual_name, String short_name) {
        this.id = id;
        this.actual_name = actual_name;
        this.short_name = short_name;
    }

    public void setUnit_server_id(Integer unit_server_id) {
        this.unit_server_id = unit_server_id;
    }

    public void setSync(String sync) {
        this.sync = sync;
    }

    public Integer getUnit_server_id() {
        return unit_server_id;
    }

    public String getSync() {
        return sync;
    }

    public void setAllow_decimal(Integer allow_decimal) {
        this.allow_decimal = allow_decimal;
    }

    public Integer getAllow_decimal() {
        return allow_decimal;
    }

    public void setId(int id) {
        this.id = id;
    }

    public void setActual_name(String actual_name) {
        this.actual_name = actual_name;
    }

    public void setShort_name(String short_name) {
        this.short_name = short_name;
    }

    public int getId() {
        return id;
    }

    public String getActual_name() {
        return actual_name;
    }

    public String getShort_name() {
        return short_name;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;

        if (o == null || getClass() != o.getClass()) return false;

        Unit unit = (Unit) o;

        return id==unit.id;
    }

    @Override
    public int hashCode() {
        return actual_name.hashCode();
    }

}
