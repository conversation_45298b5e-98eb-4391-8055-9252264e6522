package com.rising.high.tech.bigultimatenavdraw.model;

public class User {
    private int id;
    private String user_type;
    private String surname;
    private String first_name;
    private String last_name;
    private String username;
    private String email;
    private String password;
    private int language;
    private int contact_no;
    private String address;
    private String remember_token;
    private int business_id;
    private int allow_login;
    private String status;
    private String dob;
    private String gender;
    private String marital_status;
    private String blood_group;
    private String contact_number;
    private String bank_details;
    private String id_proof_name;
    private String id_proof_number;
    private Integer selected_contacts;
    private String sync;
    private int user_server_id;

    public User(int id, String username) {
        this.id = id;
        this.username = username;
    }

    public User() {
    }

    public void setUser_server_id(int user_server_id) {
        this.user_server_id = user_server_id;
    }

    public int getUser_server_id() {
        return user_server_id;
    }

    public void setSync(String sync) {
        this.sync = sync;
    }

    public String getSync() {
        return sync;
    }

    public void setSelected_contacts(Integer selected_contacts) {
        this.selected_contacts = selected_contacts;
    }

    public Integer getSelected_contacts() {
        return selected_contacts;
    }

    public void setId(int id) {
        this.id = id;
    }

    public void setUser_type(String user_type) {
        this.user_type = user_type;
    }

    public void setSurname(String surname) {
        this.surname = surname;
    }

    public void setFirst_name(String first_name) {
        this.first_name = first_name;
    }

    public void setLast_name(String last_name) {
        this.last_name = last_name;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public void setLanguage(int language) {
        this.language = language;
    }

    public void setContact_no(int contact_no) {
        this.contact_no = contact_no;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public void setRemember_token(String remember_token) {
        this.remember_token = remember_token;
    }

    public void setBusiness_id(int business_id) {
        this.business_id = business_id;
    }

    public void setAllow_login(int allow_login) {
        this.allow_login = allow_login;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public void setDob(String dob) {
        this.dob = dob;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public void setMarital_status(String marital_status) {
        this.marital_status = marital_status;
    }

    public void setBlood_group(String blood_group) {
        this.blood_group = blood_group;
    }

    public void setContact_number(String contact_number) {
        this.contact_number = contact_number;
    }

    public void setBank_details(String bank_details) {
        this.bank_details = bank_details;
    }

    public void setId_proof_name(String id_proof_name) {
        this.id_proof_name = id_proof_name;
    }

    public void setId_proof_number(String id_proof_number) {
        this.id_proof_number = id_proof_number;
    }

    public int getId() {
        return id;
    }

    public String getUser_type() {
        return user_type;
    }

    public String getSurname() {
        return surname;
    }

    public String getFirst_name() {
        return first_name;
    }

    public String getLast_name() {
        return last_name;
    }

    public String getUsername() {
        return username;
    }

    public String getEmail() {
        return email;
    }

    public String getPassword() {
        return password;
    }

    public int getLanguage() {
        return language;
    }

    public int getContact_no() {
        return contact_no;
    }

    public String getAddress() {
        return address;
    }

    public String getRemember_token() {
        return remember_token;
    }

    public int getBusiness_id() {
        return business_id;
    }

    public int getAllow_login() {
        return allow_login;
    }

    public String getStatus() {
        return status;
    }

    public String getDob() {
        return dob;
    }

    public String getGender() {
        return gender;
    }

    public String getMarital_status() {
        return marital_status;
    }

    public String getBlood_group() {
        return blood_group;
    }

    public String getContact_number() {
        return contact_number;
    }

    public String getBank_details() {
        return bank_details;
    }

    public String getId_proof_name() {
        return id_proof_name;
    }

    public String getId_proof_number() {
        return id_proof_number;
    }


    @Override
    public boolean equals(Object o) {
        if (this == o) return true;

        if (o == null || getClass() != o.getClass()) return false;

        User user = (User) o;

        return username.equals(user.username) && id==user.id;
    }

    @Override
    public int hashCode() {
        return username.hashCode();
    }


}
