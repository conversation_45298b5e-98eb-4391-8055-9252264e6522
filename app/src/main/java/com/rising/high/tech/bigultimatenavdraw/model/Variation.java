package com.rising.high.tech.bigultimatenavdraw.model;

import java.util.ArrayList;

public class Variation{
    private int id;
    private int product_id;
    private int product_variation_id;
    private String sub_sku;
    private String name;
    private String default_sell_price;
    private String default_purchase_price;
    private String dpp_inc_tax;
    private String profit_percent;
    private String sell_price_inc_tax;
    private ArrayList<Variation_location_details> variation_location_details;
    private Integer variation_server_id;

    public void setVariation_server_id(Integer variation_server_id) {
        this.variation_server_id = variation_server_id;
    }

    public Integer getVariation_server_id() {
        return variation_server_id;
    }

    public void setDefault_purchase_price(String default_purchase_price) {
        this.default_purchase_price = default_purchase_price;
    }

    public void setProduct_id(int product_id) {
        this.product_id = product_id;
    }

    public void setProduct_variation_id(int product_variation_id) {
        this.product_variation_id = product_variation_id;
    }

    public void setSub_sku(String sub_sku) {
        this.sub_sku = sub_sku;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getProduct_id() {
        return product_id;
    }

    public int getProduct_variation_id() {
        return product_variation_id;
    }

    public String getSub_sku() {
        return sub_sku;
    }

    public String getName() {
        return name;
    }

    public void setProfit_percent(String profit_percent) {
        this.profit_percent = profit_percent;
    }

    public String getDefault_purchase_price() {
        return default_purchase_price;
    }

    public String getProfit_percent() {
        return profit_percent;
    }

    public void setVariation_location_details(ArrayList<Variation_location_details> variation_location_details) {
        this.variation_location_details = variation_location_details;
    }

    public ArrayList<Variation_location_details> getVariation_location_details() {
        return variation_location_details;
    }

    public void setId(int id) {
        this.id = id;
    }

    public void setDpp_inc_tax(String dpp_inc_tax) {
        this.dpp_inc_tax = dpp_inc_tax;
    }

    public void setDefault_sell_price(String default_sell_price) {
        this.default_sell_price = default_sell_price;
    }

    public void setSell_price_inc_tax(String sell_price_inc_tax) {
        this.sell_price_inc_tax = sell_price_inc_tax;
    }

    public int getId() {
        return id;
    }

    public String getDpp_inc_tax() {
        return dpp_inc_tax;
    }

    public String getDefault_sell_price() {
        return default_sell_price;
    }

    public String getSell_price_inc_tax() {
        return sell_price_inc_tax;
    }
}
