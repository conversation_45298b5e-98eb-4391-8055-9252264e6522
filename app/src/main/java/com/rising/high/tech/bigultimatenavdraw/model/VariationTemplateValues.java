package com.rising.high.tech.bigultimatenavdraw.model;

public class VariationTemplateValues {

    private int id;
    private String name;
    private int variation_template_id ;

    public VariationTemplateValues(String name, int variation_template_id) {
        this.name = name;
        this.variation_template_id  = variation_template_id;
    }

    public int getVariation_template_id() {
        return variation_template_id;
    }

    public void setVariation_template_id(int variation_template_id) {
        this.variation_template_id = variation_template_id;
    }

    public VariationTemplateValues() {
    }

    public void setId(int id) {
        this.id = id;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getId() {
        return id;
    }

    public String getName() {
        return name;
    }


}
