package com.rising.high.tech.bigultimatenavdraw.model;

public class Variation_location_details {

    private int qty_available ;
    private int old_qty_available ;
    private int sell_qty ;
    private int id ;
    private int location_id;
    private int product_id;
    private int product_variation_id;
    private int variation_id;
    private String purchase_price;
    private String quantity;
    private String transaction_date;
    private String final_total;

    public void setSell_qty(int sell_qty) {
        this.sell_qty = sell_qty;
    }

    public int getSell_qty() {
        return sell_qty;
    }

    public void setFinal_total(String final_total) {
        this.final_total = final_total;
    }

    public String getFinal_total() {
        return final_total;
    }

    public void setTransaction_date(String transaction_date) {
        this.transaction_date = transaction_date;
    }

    public String getTransaction_date() {
        return transaction_date;
    }


    public void setPurchase_price(String purchase_price) {
        this.purchase_price = purchase_price;
    }

    public void setQuantity(String quantity) {
        this.quantity = quantity;
    }

    public String getPurchase_price() {
        return purchase_price;
    }

    public String getQuantity() {
        return quantity;
    }

    public void setProduct_variation_id(int product_variation_id) {
        this.product_variation_id = product_variation_id;
    }

    public void setOld_qty_available(int old_qty_available) {
        this.old_qty_available = old_qty_available;
    }

    public int getOld_qty_available() {
        return old_qty_available;
    }

    public int getProduct_variation_id() {
        return product_variation_id;
    }

    public void setVariation_id(int variation_id) {
        this.variation_id = variation_id;
    }

    public void setProduct_id(int product_id) {
        this.product_id = product_id;
    }

    public int getVariation_id() {
        return variation_id;
    }

    public int getProduct_id() {
        return product_id;
    }

    public void setId(int id) {
        this.id = id;
    }



    public void setLocation_id(int location_id) {
        this.location_id = location_id;
    }

    public int getId() {
        return id;
    }


    public int getLocation_id() {
        return location_id;
    }

    public void setQty_available(int name) {
        this.qty_available = name;
    }
    public int getQty_available() {
        return qty_available;
    }
}