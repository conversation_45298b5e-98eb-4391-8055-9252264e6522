package com.rising.high.tech.bigultimatenavdraw.ui;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.res.Resources;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.util.Log;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.MenuItem;
import android.view.MotionEvent;
import android.view.View;
import android.view.Menu;
import android.view.Window;
import android.view.WindowManager;
import android.view.inputmethod.InputMethodManager;
import android.widget.AdapterView;
import android.widget.Button;
import android.widget.ExpandableListView;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.Spinner;
import android.widget.TextView;

import com.google.android.material.navigation.NavigationView;
import com.google.gson.Gson;
import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.db.BusinessLocationDbController;
import com.rising.high.tech.bigultimatenavdraw.db.CompanyDbController;
import com.rising.high.tech.bigultimatenavdraw.db.ContactDbController;
import com.rising.high.tech.bigultimatenavdraw.model.Business;
import com.rising.high.tech.bigultimatenavdraw.model.Business_location;
import com.rising.high.tech.bigultimatenavdraw.model.Contact;
import com.rising.high.tech.bigultimatenavdraw.ui.adapter.SpinHomeStationAdapter;
import com.rising.high.tech.bigultimatenavdraw.ui.adapter.SpinStationAdapter;
import com.rising.high.tech.bigultimatenavdraw.ui.brand.ListBrandFragment;
import com.rising.high.tech.bigultimatenavdraw.ui.businessetting.BusinessSettingsFragment;
import com.rising.high.tech.bigultimatenavdraw.ui.businesslocation.BusinessLocationFragment;
import com.rising.high.tech.bigultimatenavdraw.ui.category.ListCategoryFragment;
import com.rising.high.tech.bigultimatenavdraw.ui.categoryexpense.CategoryExpenseFragment;
import com.rising.high.tech.bigultimatenavdraw.ui.contact.ListContactFragment;
import com.rising.high.tech.bigultimatenavdraw.ui.customergroups.ListCustomerGroupsFragment;
import com.rising.high.tech.bigultimatenavdraw.ui.expense.ListExpenseFragment;
import com.rising.high.tech.bigultimatenavdraw.ui.discount.ListDiscountFragment;
import com.rising.high.tech.bigultimatenavdraw.ui.draft.ListDraftFragment;
import com.rising.high.tech.bigultimatenavdraw.ui.home.HomeFragment;
import com.rising.high.tech.bigultimatenavdraw.ui.product.ListProductFragment;
import com.rising.high.tech.bigultimatenavdraw.ui.profitloss.ProfitLossFragment;
import com.rising.high.tech.bigultimatenavdraw.ui.purchasereturn.PurchasesReturnFragment;
import com.rising.high.tech.bigultimatenavdraw.ui.purchase.PurchaseFragment;
import com.rising.high.tech.bigultimatenavdraw.ui.quotation.ListQuotationFragment;
import com.rising.high.tech.bigultimatenavdraw.ui.reportstock.ReportStockFragment;
import com.rising.high.tech.bigultimatenavdraw.ui.rightmenu.RightMenuFragment;
import com.rising.high.tech.bigultimatenavdraw.ui.role.RolesFragment;
import com.rising.high.tech.bigultimatenavdraw.ui.sell.ListVenteFragment;
import com.rising.high.tech.bigultimatenavdraw.ui.sellreturn.ListSellReturnFragment;
import com.rising.high.tech.bigultimatenavdraw.ui.stockadjustement.StockAdjustementFragment;
import com.rising.high.tech.bigultimatenavdraw.ui.stocktransfers.StockTransferFragment;
import com.rising.high.tech.bigultimatenavdraw.ui.sync.SyncFragment;
import com.rising.high.tech.bigultimatenavdraw.ui.taxrates.TaxRatesFragment;
import com.rising.high.tech.bigultimatenavdraw.ui.unit.ListUnitFragment;
import com.rising.high.tech.bigultimatenavdraw.ui.usermanagement.UsersFragment;
import com.rising.high.tech.bigultimatenavdraw.ui.variation.ListVariationFragment;
import com.rising.high.tech.bigultimatenavdraw.ui.warranty.ListWarrantyFragment;
import com.rising.high.tech.bigultimatenavdraw.util.SessionManager;
import com.rising.high.tech.bigultimatenavdraw.util.StringFormat;

import androidx.core.view.GravityCompat;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;
import androidx.drawerlayout.widget.DrawerLayout;
import androidx.appcompat.app.AppCompatActivity;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.TimeZone;

import static com.rising.high.tech.bigultimatenavdraw.util.Constant.ACTIVE;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.ALL_SALLES;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.BRAND;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.BRANDS;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.BRAND_VIEW;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.BUSINESS_LOCATION;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.BUSINESS_LOCATION_VIEW;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.BUSINESS_SETTING;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.BUSINESS_SETTINGS;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.BUSINESS_SETTING_ACCESS;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.CATEGORIES;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.CATEGORY;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.CATEGORY_VIEW;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.CONTACT;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.CONTACTS;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.CONTACT_VIEW;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.CUSTOMER;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.CUSTOMER_GROUPS;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.DISCOUNTS;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.DISCOUNT_ACCESS;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.DRAFT;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.EXPENSE_ACCESS;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.EXPENSE_CATEGEORIES;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.HOME;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.LIST_DRAFT;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.LIST_EXPENSE;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.LIST_PRODUCTS;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.LIST_PURCHASES;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.LIST_QUOTATION;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.LIST_SELL_RETURN;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.LOCAL_USE;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.PRODUCT;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.PRODUCT_ADD;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.PRODUCT_VIEW;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.PROFIT_LOSS;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.PROFIT_LOSS_REPORT_VIEW;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.PURCHASE;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.PURCHASES_RETURN;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.PURCHASE_VIEW;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.QUOTATION;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.REPORT_ACCESS;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.ROLES;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.ROLES_VIEW;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.SELL;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.SELL_RETURN_ACCESS;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.SELL_VIEW;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.STOCK_ADJUSTEMENT;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.STOCK_ADJUSTEMENT_ACCESS;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.STOCK_REPORT;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.STOCK_REPORT_VIEW;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.STOCK_TRANSFERT;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.SYNCHRONISATION;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.TAX_RATE;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.TAX_RATES;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.TAX_RATE_VIEW;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.UNIT;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.UNITS;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.UNIT_VIEW;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.USER;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.USERS;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.USER_VIEW;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.VARIATIONS;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.VARIATION_VIEW;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.VIEW_CASH_REGISTER;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.WARRANTIES;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.WARRANTY_VIEW;


public class NaviMainActivity extends AppCompatActivity {
    private static final String TAG = "NaviMainActivity";

    Resources res;
    // Session Manager Class
    SessionManager session;
    HashMap<String, Object> user;
    ExpandableListAdapter mMenuAdapter;
    List<ExpandedMenuModel> listDataHeader;
    HashMap<ExpandedMenuModel, List<String>> listDataChild;
    private ContactDbController contactDbController;
    private CompanyDbController companyDbController;

    ImageView menu_button, right_menu_button;
    LinearLayout header_toolbar;
    DrawerLayout drawer_layout;
    NavigationView drawer_nav_view;
    TextView currentDateTime,currentLocation;
    ExpandableListView expandableList;
    LinearLayout profileContainer;
    TextView user_txt;
    private BusinessLocationDbController businessLocationDbController;
    private SpinHomeStationAdapter spinStationAdapter;
    Spinner spinner_station;
    private Integer currentIdLocation;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        getWindow().setFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN,
                WindowManager.LayoutParams.FLAG_FULLSCREEN);
        setContentView(R.layout.activity_navi_main);

        res = getResources();

        session = new SessionManager(getApplicationContext());
        user = session.getUserDetails();
        initDB();
        firstLogin();
        NavigationView navigationView = findViewById(R.id.nav_view);

        /**
         * Start navigation drawer code
         */
        drawer_layout = findViewById(R.id.drawer_layout);
        expandableList = findViewById(R.id.navigation_menu);
        header_toolbar = findViewById(R.id.header_toolbar);
        currentDateTime = header_toolbar.findViewById(R.id.currentDateTime);
        currentLocation = header_toolbar.findViewById(R.id.currentLocation);
        menu_button = header_toolbar.findViewById(R.id.menu_button);
        right_menu_button = header_toolbar.findViewById(R.id.right_menu_button);
        profileContainer=header_toolbar.findViewById(R.id.profile_container);
        user_txt=header_toolbar.findViewById(R.id.user_txt);
        drawer_layout = findViewById(R.id.drawer_layout);
        drawer_nav_view = findViewById(R.id.nav_view);
        spinner_station = header_toolbar.findViewById(R.id.spinner_station);

        drawer_nav_view.setItemIconTintList(null);


        getCurrentTimeZOne();
        if (navigationView != null) {
            setupDrawerContent(navigationView);
        }
        prepareListData();
        mMenuAdapter = new ExpandableListAdapter(this, listDataHeader, listDataChild, expandableList);
        // setting list adapter
        expandableList.setAdapter(mMenuAdapter);
        expandableList.setOnChildClickListener(new ExpandableListView.OnChildClickListener() {
            @Override
            public boolean onChildClick(ExpandableListView expandableListView, View view, int i, int i1, long l) {
                String tag = view.getTag().toString().toLowerCase();
                navigateChildtByTag(tag);
                return false;
            }
        });
        expandableList.setOnGroupClickListener(new ExpandableListView.OnGroupClickListener() {
            @Override
            public boolean onGroupClick(ExpandableListView expandableListView, View view, int i, long l) {

                String tagView=view.getTag().toString().trim().toLowerCase();
                navigateParentByTag(tagView);
                return false;
            }
        });
        /**
         * Finish code
         */

        menu_button.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                drawer_layout.openDrawer(Gravity.LEFT);
            }
        });

        right_menu_button.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

                naviguate(new RightMenuFragment());
            }
        });

        profileContainer.setOnClickListener(v->
        {
            signOut();
        });

        user_txt.setText(user.get(session.KEY_NAME)+"");


        right_menu_button.setVisibility(session.checkHasAllPermission()? View.VISIBLE:View.GONE);



        businessLocationDbController = new BusinessLocationDbController(this);
        businessLocationDbController.open();

        spinStationAdapter = new SpinHomeStationAdapter(this, R.layout.custom_spinner_item, businessLocationDbController.getAllStationSpinner());
        spinner_station.setAdapter(spinStationAdapter);

        if ((int) user.get(session.LOCATION_ID) > 0) {
            Business_location businesslocation = businessLocationDbController.getStationById((int) user.get(session.LOCATION_ID));
            spinner_station.setSelection(spinStationAdapter.getPosition(businesslocation));
            spinner_station.setEnabled(false);
        }

        spinner_station.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
               // session.getUserDetails();
                if (position > 0) {
                    Business_location businesslocation = (Business_location) spinner_station.getSelectedItem();
                    session.saveInt(session.LOCATION_ID, businesslocation.getId());
                    currentIdLocation = businesslocation.getId();
                }else {
                    session.saveInt(session.LOCATION_ID, 0);
                }
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {
            }
        });

        /**
         * Fragment to start with
         */

        if (session.checkFirstModule())
        {
            naviguate(new HomeFragment());
        }else
        {
            naviguate(new BusinessSettingsFragment());
        };
    }

    private void initDB() {
        contactDbController = new ContactDbController(this);
        contactDbController.open();

        companyDbController = new CompanyDbController(this);

        Business businessData = companyDbController.getCompanyData();
        session.saveBusinessModel(businessData);

        System.out.println("BusinessId "+ businessData.getId() + " " + businessData.getName());

    }

    private void firstLogin() {
        if (!(Boolean) user.get(session.IS_LOGIN)) {
            session.saveBoolean(session.IS_LOGIN, true);
            session.saveString(session.KEY_SYMBOL, " $");


             // Create default Walk-in customer customer

            Contact contact = new Contact();
            /**
             * TODO : make Business id general
             */
            contact.setBusiness_id(1);
            contact.setType(CUSTOMER);
            contact.setName("Walk-In Customer");
            contact.setFirst_name("Walk-In");
            contact.setLast_name("Customer");
            contact.setContact_status(ACTIVE);
            contact.setCreated_by("1");
            contact.setCreated_at(StringFormat.actualTime());

            contactDbController.insertLocal(contact);
        }
    }


    private void navigateParentByTag(String tag)
    {
        switch (tag){
            case HOME:{
                naviguate(new HomeFragment());
                break;
            }
            case STOCK_TRANSFERT:{
                naviguate(new StockTransferFragment());
                break;
            }
            case STOCK_ADJUSTEMENT:{
                naviguate(new StockAdjustementFragment());
                break;
            }
            case SYNCHRONISATION:{
                naviguate(new SyncFragment());
                break;
            }
        }
    }
    private void navigateChildtByTag(String tag)
    {
        Log.d(TAG, " child route "+ tag);
        switch (tag){
            case LIST_PRODUCTS:{
                naviguate(new ListProductFragment());
                break;
            }
            case VARIATIONS:{
                naviguate(new ListVariationFragment());
                break;
            }
            case UNITS:{
                naviguate(new ListUnitFragment());
                break;
            }
            case CATEGORIES:{
                naviguate(new ListCategoryFragment());
                break;
            }
            case WARRANTIES:{
                naviguate(new ListWarrantyFragment());
                break;
            }
            case BRANDS:{
                naviguate(new ListBrandFragment());
                break;
            }
            case CONTACTS:{
                naviguate(new ListContactFragment());
                break;
            }
            case CUSTOMER_GROUPS:{
                naviguate(new ListCustomerGroupsFragment());
                break;
            }
            case LIST_PURCHASES:{
                naviguate(new PurchaseFragment());
                break;
            }
            case PURCHASES_RETURN:{
                naviguate(new PurchasesReturnFragment());
                break;
            }
            case ALL_SALLES:{
                naviguate(new ListVenteFragment());
                break;
            }
            case DRAFT:{
                naviguate(new ListDraftFragment());
                break;
            }
            case QUOTATION:{
                naviguate(new ListQuotationFragment());
                break;
            }
            case LIST_SELL_RETURN:{
                naviguate(new ListSellReturnFragment());
                break;
            }
            case DISCOUNTS:{
                naviguate(new ListDiscountFragment());
                break;
            }
            case LIST_EXPENSE:{
                naviguate(new ListExpenseFragment());
                break;
            }
            case EXPENSE_CATEGEORIES:{
                naviguate(new CategoryExpenseFragment());
                break;
            }
            case BUSINESS_SETTINGS:{
                naviguate(new BusinessSettingsFragment());
                break;
            }
            case BUSINESS_LOCATION:{
                naviguate(new BusinessLocationFragment());
                break;
            }
            case TAX_RATES:{
                naviguate(new TaxRatesFragment());
                break;
            }
            case USERS:{
                naviguate(new UsersFragment());
                break;
            }
            case ROLES:{
                naviguate(new RolesFragment());
                break;
            }
            case PROFIT_LOSS:{
                naviguate(new ProfitLossFragment());
                break;
            }
            case STOCK_REPORT:{
                naviguate(new ReportStockFragment());
                break;
            }
        }
    }

    private void prepareListData() {
        listDataHeader = new ArrayList<ExpandedMenuModel>();
        listDataChild = new HashMap<ExpandedMenuModel, List<String>>();

        /**
         childs menus
         */
        List<String> heading1 = new ArrayList<String>();
        // listDataChild.put(listDataHeader.get(0), heading1);// Header, Child data

        //List<String> heading1 = new ArrayList<String>();
        if (session.checkPermissionModule(VIEW_CASH_REGISTER)) {
            ExpandedMenuModel item1 = new ExpandedMenuModel();
            item1.setIconName(res.getString(R.string.menu_home)+"@home");
            item1.setIconImg(R.drawable.products_smart_ic);
            // Adding data header
            listDataHeader.add(item1);
            listDataChild.put(item1, heading1);// Header, Child data
        }

        /***
         product menu
         */
        List<String> heading2 = new ArrayList<String>();
        if (session.checkPermissionSubModule(PRODUCT_VIEW)) {
            heading2.add(res.getString(R.string.label_product_list) +"@List Products");
        }
//        if (session.checkPermissionSubModule(VARIATION_VIEW))
//        {
//            heading2.add(res.getString(R.string.label_variation)+"@Variations");
//        }
        if (session.checkPermissionSubModule(UNIT_VIEW)) {
            heading2.add(res.getString(R.string.label_unit)+"@Units");
        }
        if (session.checkPermissionSubModule(CATEGORY_VIEW)) {
            heading2.add(res.getString(R.string.label_category)+"@Categories");
        }
        if (session.checkPermissionSubModule(WARRANTY_VIEW)) {
            heading2.add(res.getString(R.string.label_garenty)+"@Warranties");
        }
        if (session.checkPermissionSubModule(BRAND_VIEW)) {
            heading2.add(res.getString(R.string.label_brand)+"@Brands");
        }
        if (heading2.size() > 0) {
            ExpandedMenuModel item2 = new ExpandedMenuModel();
            item2.setIconName(res.getString(R.string.menu_produit)+"@products");
            item2.setIconImg(R.drawable.product_ic_icon);
            listDataHeader.add(item2);
            listDataChild.put(item2, heading2);// Header, Child data
        }

        /***
         * contact menu
         */
        if (session.checkPermissionSubModule(CONTACT_VIEW)) {
            List<String> heading3 = new ArrayList<String>();
            heading3.add(res.getString(R.string.label_contact)+"@Contacts");
            heading3.add(res.getString(R.string.string_customer_groups)+"@Customer Groups");

            ExpandedMenuModel item3 = new ExpandedMenuModel();
            item3.setIconName(res.getString(R.string.label_contact)+"@contacts");
            item3.setIconImg(R.drawable.smart_contact_ic);
            listDataHeader.add(item3);

            listDataChild.put(item3, heading3);// Header, Child data
        }

        /***
         * purchase menu
         */
        if (session.checkPermissionSubModule(PURCHASE_VIEW)) {
            List<String> heading4 = new ArrayList<String>();
            heading4.add(res.getString(R.string.label_list_purchases)+"@List Purchases");
            heading4.add(res.getString(R.string.label_list_return_purchase)+"@Purchase Return");
            ExpandedMenuModel item4 = new ExpandedMenuModel();
            item4.setIconName(res.getString(R.string.label_achats)+"@purchases");
            item4.setIconImg(R.drawable.purchase_ic);
            listDataHeader.add(item4);
            listDataChild.put(item4, heading4);// Header, Child data
        }

        /***
         * sell menu
         */
        List<String> heading5 = new ArrayList<String>();
        if (session.checkPermissionSubModule(SELL_VIEW)) {
            heading5.add(res.getString(R.string.label_all_salles)+"@All Sales");
        }
        if (session.checkPermissionSubModule(LIST_DRAFT)) {
            heading5.add(res.getString(R.string.label_draft_lbl)+"@Draft");
        }
        if (session.checkPermissionSubModule(LIST_QUOTATION)) {
            heading5.add(res.getString(R.string.label_quotation)+"@Quotation");
        }
        if (session.checkPermissionSubModule(SELL_RETURN_ACCESS)) {
            heading5.add(res.getString(R.string.label_lsit_return_sell)+"@List sell return");
        }
        if (session.checkPermissionSubModule(DISCOUNT_ACCESS)) {
            heading5.add(res.getString(R.string.label_discounts)+"@Discounts");
        }
        if (heading5.size() > 0) {
            ExpandedMenuModel item5 = new ExpandedMenuModel();
            item5.setIconName(res.getString(R.string.label_sell)+"@sell");
            item5.setIconImg(R.drawable.sales_smart_ic);
            listDataHeader.add(item5);
            listDataChild.put(item5, heading5);// Header, Child data
        }

        /***
         * stock transfer menu
         */
        if (session.checkPermissionSubModule(STOCK_ADJUSTEMENT_ACCESS)) {
            List<String> heading6 = new ArrayList<String>();
            ExpandedMenuModel item6 = new ExpandedMenuModel();
            item6.setIconName(res.getString(R.string.label_stock_transfers)+"@stock transfers");
            item6.setIconImg(R.drawable.stock_transfer_ic);
            listDataHeader.add(item6);
            listDataChild.put(item6, heading6);// Header, Child data
        }

        /***
         * stock adjustement menu
         */
        if (session.checkPermissionSubModule(PURCHASE_VIEW)) {
            List<String> heading7 = new ArrayList<String>();
            ExpandedMenuModel item7 = new ExpandedMenuModel();
            item7.setIconName(res.getString(R.string.label_stock_adjustement)+"@stock adjustement");
            item7.setIconImg(R.drawable.stock_ajustement_ic);
            listDataHeader.add(item7);
            listDataChild.put(item7, heading7);// Header, Child data
        }

        /***
         * expense menu
         */
        if (session.checkPermissionSubModule(EXPENSE_ACCESS)) {
            List<String> heading8 = new ArrayList<String>();
            heading8.add(res.getString(R.string.label_list_expense)+"@List Expense");
            heading8.add(res.getString(R.string.label_expense_categoriess)+"@Expense Categories");
            ExpandedMenuModel item8 = new ExpandedMenuModel();
            item8.setIconName(res.getString(R.string.label_expense)+"@expenses");
            item8.setIconImg(R.drawable.stock_expenses_ic);
            listDataHeader.add(item8);
            listDataChild.put(item8, heading8);// Header, Child data
        }

        /***
         * report menu
         */
        if (session.checkPermissionSubModule(REPORT_ACCESS)) {
            List<String> heading9 = new ArrayList<String>();
//            if (session.checkPermissionSubModule(PROFIT_LOSS_REPORT_VIEW)) {
//                heading9.add(res.getString(R.string.label_profit_loss));
//            }
            if (session.checkPermissionSubModule(STOCK_REPORT_VIEW)) {
                heading9.add(res.getString(R.string.label_profit_report)+"@Stock report");
            }
            if (heading9.size() > 0) {
                ExpandedMenuModel item9 = new ExpandedMenuModel();
                item9.setIconName(res.getString(R.string.label_reports)+"@reports");
                item9.setIconImg(R.drawable.stock_reports_ic);
                listDataHeader.add(item9);
                listDataChild.put(item9, heading9);// Header, Child data
            }
        }


        /***
         * setting menu
         */
        List<String> heading10 = new ArrayList<String>();
        if (session.checkPermissionSubModule(BUSINESS_SETTING_ACCESS))
        {
            heading10.add(res.getString(R.string.label_business_setting)+"@Business Settings");
        }
        if (session.checkPermissionSubModule(BUSINESS_LOCATION_VIEW))
        {
            heading10.add(res.getString(R.string.label_business_location)+"@Business location");
        }
        if (session.checkPermissionSubModule(TAX_RATE_VIEW))
        {
            heading10.add(res.getString(R.string.label_tax_rates)+"@Tax Rates");
        }
        if (heading10.size()>0)
        {
            ExpandedMenuModel item10 = new ExpandedMenuModel();
            item10.setIconName(res.getString(R.string.label_settings)+"@settings");
            item10.setIconImg(R.drawable.settings_ic);
            listDataHeader.add(item10);
            listDataChild.put(item10, heading10);// Header, Child data
        }

        /***
         * business managment menu
         */
        List<String> heading11 = new ArrayList<String>();
        if (session.checkPermissionSubModule(USER_VIEW)){
            heading11.add(res.getString(R.string.label_users)+"@Users");
        }
        if (session.checkPermissionSubModule(ROLES_VIEW)){
            heading11.add(res.getString(R.string.label_roles)+"@Roles");
        }
        if (heading11.size()>0)
        {
            ExpandedMenuModel item11 = new ExpandedMenuModel();
            item11.setIconName(res.getString(R.string.user_management)+"@user management");
            item11.setIconImg(R.drawable.ic_bs_manager);
            listDataHeader.add(item11);
            listDataChild.put(item11, heading11);// Header, Child data
        }

        /***
         * synchronisation menu
         */
        if (!session.getBoolean(LOCAL_USE))
        {
            List<String> heading12 = new ArrayList<String>();
            ExpandedMenuModel item12 = new ExpandedMenuModel();
            item12.setIconName(res.getString(R.string.label_sync)+"@"+SYNCHRONISATION);
            item12.setIconImg(R.drawable.synch_ic);
            listDataHeader.add(item12);
            listDataChild.put(item12, heading12);// Header, Child data
        }

    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        return true;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        switch (item.getItemId()) {
            case android.R.id.home:
                drawer_layout.openDrawer(GravityCompat.START);
                return true;
        }
        return super.onOptionsItemSelected(item);
    }

    private void setupDrawerContent(NavigationView navigationView) {
        //revision: this don't works, use setOnChildClickListener() and setOnGroupClickListener() above instead
        navigationView.setNavigationItemSelectedListener(
                new NavigationView.OnNavigationItemSelectedListener() {
                    @Override
                    public boolean onNavigationItemSelected(MenuItem menuItem) {
                        menuItem.setChecked(true);
                        drawer_layout.closeDrawers();
                        return true;
                    }
                });
    }


    /***
     * Naviguation bitween fragments
     */
//    public void replaceFragment(int parent, int child) {
//        // Check if no view has focus:
//        View view = this.getCurrentFocus();
//        if (view != null) {
//            InputMethodManager imm = (InputMethodManager) getSystemService(Context.INPUT_METHOD_SERVICE);
//            imm.hideSoftInputFromWindow(view.getWindowToken(), 0);
//        }
//
//        switch (parent) {
//            case 0: {
//                naviguate(new HomeFragment());
//                break;
//            }
//            case 1: {
//                switch (child) {
//                    case 0: {
//                        naviguate(new ListProductFragment());
//                        break;
//                    }
//                    case 1: {
//                        naviguate(new ListVariationFragment());
//                        break;
//                    }
//                    case 2: {
//                        naviguate(new ListUnitFragment());
//                        break;
//                    }
//                    case 3: {
//                        naviguate(new ListCategoryFragment());
//                        break;
//                    }
//                    case 4: {
//                        naviguate(new ListWarrantyFragment());
//                        break;
//                    }
//                    case 5: {
//                        naviguate(new ListBrandFragment());
//                        break;
//                    }
//
//                }
//                break;
//            }
//            case 2: {  switch (child) {
//                case 0: {
//                    naviguate(new ListContactFragment());
//                    break;
//                }
//                case 1: {
//                    naviguate(new ListCustomerGroupsFragment());
//                    break;
//                }
//            }
//                break;
//            }
//            case 3: {
//                switch (child) {
//                    case 0: {
//                        naviguate(new PurchaseFragment());
//                        break;
//                    }
//                    case 1: {
//                        naviguate(new PurchasesReturnFragment());
//                        break;
//                    }
//                }
//                break;
//            }
//            case 4: {
//                switch (child) {
//                    case 0: {
//                        naviguate(new ListVenteFragment());
//                        break;
//                    }
//                    case 1: {
//                        naviguate(new ListDraftFragment());
//                        break;
//                    }
//                    case 2: {
//                        naviguate(new ListQuotationFragment());
//                        break;
//                    }
//                    case 3: {
//                        naviguate(new ListSellReturnFragment());
//                        break;
//                    }
//                    case 4: {
//                        naviguate(new ListDiscountFragment());
//                        break;
//                    }
//                }
//                break;
//            }
//            case 5: {
//                naviguate(new StockTransferFragment());
//                break;
//            }
//            case 6: {
//                naviguate(new StockAdjustementFragment());
//                break;
//            }
//            case 7: {
//                switch (child) {
//                    case 0: {
//                        naviguate(new ListExpenseFragment());
//                        break;
//                    }
//                    case 1: {
//                        naviguate(new CategoryExpenseFragment());
//                        break;
//                    }
//                }
//                break;
//            }
//            case 8: {
//                switch (child) {
//                    case 0: {
//                        naviguate(new ProfitLossFragment());
//                        break;
//                    }
//                    case 1: {
//                        naviguate(new ReportStockFragment());
//                        break;
//                    }
//                }
//                break;
//            }
//            case 9: {
//                switch (child) {
//                    case 0: {
//                        naviguate(new BusinessSettingsFragment());
//                        break;
//                    }
//                    case 1: {
//                        naviguate(new BusinessLocationFragment());
//                        break;
//                    }
//                    case 2: {
//                        naviguate(new TaxRatesFragment());
//                        break;
//                    }
//                }
//                break;
//            }
//            case 10: {
//                switch (child) {
//                    case 0: {
//                        naviguate(new UsersFragment());
//                        break;
//                    }
//                    case 1: {
//                        naviguate(new RolesFragment());
//                        break;
//                    }
//
//                }
//                break;
//            }
//
//            case 11: {
//                naviguate(new SyncFragment());
//                break;
//            }
//
//
//        }
//    }


//    private void showRightMenu() {
//        //Preparing views
//        // get prompts.xml view
//
//        LayoutInflater li = LayoutInflater.from(this);
//        View promptsView = li.inflate(R.layout.view_right_menu_main, null);
//
//        androidx.appcompat.app.AlertDialog.Builder alertDialogBuilder = new androidx.appcompat.app.AlertDialog.Builder(this);
//        alertDialogBuilder.setView(promptsView);
//
//        final LinearLayout id_home = promptsView.findViewById(R.id.id_home);
//        final LinearLayout id_stock = promptsView.findViewById(R.id.id_stock);
//        final LinearLayout id_prudcts = promptsView.findViewById(R.id.id_prudcts);
//        final LinearLayout id_purchaes_products = promptsView.findViewById(R.id.id_purchaes_products);
//        final LinearLayout id_sales = promptsView.findViewById(R.id.id_sales);
//        final LinearLayout id_stock_transfer = promptsView.findViewById(R.id.id_stock_transfer);
//        final LinearLayout id_stock_adjustement = promptsView.findViewById(R.id.id_stock_adjustement);
//        final LinearLayout id_expenses = promptsView.findViewById(R.id.id_expenses);
//        final LinearLayout id_reports = promptsView.findViewById(R.id.id_reports);
//
//        // container main
//        final LinearLayout main_container = promptsView.findViewById(R.id.main_container);
//        final LinearLayout product_container = promptsView.findViewById(R.id.product_container);
//        final LinearLayout id_list_product = promptsView.findViewById(R.id.id_list_product);
//        final LinearLayout sell_container = promptsView.findViewById(R.id.sell_container);
//        final LinearLayout id_variation = promptsView.findViewById(R.id.id_variation);
//        final LinearLayout id_units = promptsView.findViewById(R.id.id_units);
//        final LinearLayout id_categories = promptsView.findViewById(R.id.id_categories);
//        final LinearLayout id_warranties = promptsView.findViewById(R.id.id_warranties);
//        final LinearLayout id_brands = promptsView.findViewById(R.id.id_brands);
//        final LinearLayout id_all_sell = promptsView.findViewById(R.id.id_all_sell);
//        final LinearLayout id_draft = promptsView.findViewById(R.id.id_draft);
//        final LinearLayout id_quotation = promptsView.findViewById(R.id.id_quotation);
//        final LinearLayout id_sell_return = promptsView.findViewById(R.id.id_sell_return);
//        final LinearLayout id_discount = promptsView.findViewById(R.id.id_discount);
//        final LinearLayout id_list_expenses = promptsView.findViewById(R.id.id_list_expenses);
//        final LinearLayout id_expense_categories = promptsView.findViewById(R.id.id_expense_categories);
//        final LinearLayout id_purchase_container = promptsView.findViewById(R.id.id_purchase_container);
//
//        final LinearLayout expense_container = promptsView.findViewById(R.id.expense_container);
//        final LinearLayout id_list_purchase = promptsView.findViewById(R.id.id_list_purchase);
//        final LinearLayout id_purchase_return = promptsView.findViewById(R.id.id_purchase_return);
//
//        final androidx.appcompat.app.AlertDialog mAlertDialog = alertDialogBuilder.create();
//        if (mAlertDialog.getWindow() != null)
//            mAlertDialog.getWindow().getAttributes().windowAnimations = R.style.SlidingDialogAnimation;
//
//        id_home.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                naviguate(new HomeFragment());
//                mAlertDialog.dismiss();
//            }
//        });
//
//        id_stock.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                naviguate(new ListContactFragment());
//                mAlertDialog.dismiss();
//            }
//        });
//
//        id_discount.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                naviguate(new ListDiscountFragment());
//                mAlertDialog.dismiss();
//            }
//        });
//
//        id_quotation.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                naviguate(new ListQuotationFragment());
//                mAlertDialog.dismiss();
//            }
//        });
//
//        id_sell_return.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                naviguate(new PurchasesReturnFragment());
//                mAlertDialog.dismiss();
//            }
//        });
//
//        id_prudcts.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                // naviguate(new ListProductFragment());
//                main_container.setVisibility(View.GONE);
//                product_container.setVisibility(View.VISIBLE);
//                //     mAlertDialog.dismiss();
//            }
//        });
//
//        id_list_product.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                naviguate(new ListProductFragment());
//                mAlertDialog.dismiss();
//            }
//        });
//
//
//        id_purchase_return.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                naviguate(new PurchasesReturnFragment());
//                mAlertDialog.dismiss();
//            }
//        });
//
//
//        id_draft.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                naviguate(new ListDraftFragment());
//                mAlertDialog.dismiss();
//            }
//        });
//
//        id_variation.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                naviguate(new ListVariationFragment());
//                mAlertDialog.dismiss();
//            }
//        });
//        id_brands.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                naviguate(new ListBrandFragment());
//                mAlertDialog.dismiss();
//            }
//        });
//        id_all_sell.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                naviguate(new ListVenteFragment());
//                mAlertDialog.dismiss();
//            }
//        });
//        id_expense_categories.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                naviguate(new CategoryExpenseFragment());
//                mAlertDialog.dismiss();
//            }
//        });
//
//        id_categories.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                naviguate(new ListCategoryFragment());
//                mAlertDialog.dismiss();
//            }
//        });
//        id_warranties.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                naviguate(new ListWarrantyFragment());
//                mAlertDialog.dismiss();
//            }
//        });
//
//        id_units.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                naviguate(new ListUnitFragment());
//                mAlertDialog.dismiss();
//            }
//        });
//
//        id_purchaes_products.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//
//                id_purchase_container.setVisibility(View.VISIBLE);
//                main_container.setVisibility(View.GONE);
//            }
//        });
//
//        id_list_purchase.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                naviguate(new PurchaseFragment());
//
//                mAlertDialog.dismiss();
//            }
//        });
//        id_list_expenses.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                naviguate(new ListExpenseFragment());
//                mAlertDialog.dismiss();
//            }
//        });
//
//
//        id_sales.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                //    naviguate(new ListVenteFragment());
//                sell_container.setVisibility(View.VISIBLE);
//                main_container.setVisibility(View.GONE);
//                //  mAlertDialog.dismiss();
//            }
//        });
//
//        id_stock_transfer.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                naviguate(new StockTransferFragment());
//                mAlertDialog.dismiss();
//            }
//        });
//
//        id_stock_adjustement.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                naviguate(new StockAdjustementFragment());
//                mAlertDialog.dismiss();
//            }
//        });
//
//        id_expenses.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                //  naviguate(new ListExpenseFragment());
//                expense_container.setVisibility(View.VISIBLE);
//                main_container.setVisibility(View.GONE);
//                //  mAlertDialog.dismiss();
//            }
//        });
//        id_reports.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                naviguate(new ReportStockFragment());
//                mAlertDialog.dismiss();
//            }
//        });
//
//        WindowManager.LayoutParams lp = new WindowManager.LayoutParams();
//        lp.copyFrom(mAlertDialog.getWindow().getAttributes());
//        lp.width = WindowManager.LayoutParams.MATCH_PARENT;
//        lp.height = WindowManager.LayoutParams.MATCH_PARENT;
//        mAlertDialog.show();
//        mAlertDialog.getWindow().setAttributes(lp);
//
//        //  mAlertDialog.show();
//    }

    public void naviguate(Fragment newFragment) {
        FragmentTransaction transaction = getSupportFragmentManager().beginTransaction();
        // Replace whatever is in the fragment_container view with this fragment,
        // and add the transaction to the back stack if needed
        transaction.replace(R.id.nav_host_fragment, newFragment);
        // Commit the transaction
        transaction.commit();
        if (drawer_layout.isDrawerOpen(GravityCompat.START)) {
            //drawer is open
            drawer_layout.closeDrawers();
            drawer_layout.close();
        }
    }

    void getCurrentTimeZOne()
    {
        Date date = new Date();
        @SuppressLint("SimpleDateFormat") DateFormat df = new SimpleDateFormat("hh:mm a | MMM dd yyyy");
        currentDateTime.setText(df.format(date));
        TimeZone tz = TimeZone.getDefault();
        currentLocation.setText(tz.getID());
    }

    @Override
    public void onBackPressed() {

    }

    private void signOut() {
        //Preparing views
        // get prompts.xml view
        LayoutInflater li = LayoutInflater.from(this);
        View promptsView = li.inflate(R.layout.profile_dialog_main, null);

        androidx.appcompat.app.AlertDialog.Builder alertDialogBuilder = new androidx.appcompat.app.AlertDialog.Builder(
                this);

        alertDialogBuilder.setView(promptsView);

        final Button btn_logout = promptsView.findViewById(R.id.btn_logout);
        final Button ButtonClose = promptsView.findViewById(R.id.btn_close);
        final TextView user_name_txt = promptsView.findViewById(R.id.user_name_txt);

        final androidx.appcompat.app.AlertDialog mAlertDialog = alertDialogBuilder.create();
        if (mAlertDialog.getWindow() != null)
            mAlertDialog.getWindow().getAttributes().windowAnimations = R.style.SlidingDialogAnimation;
        mAlertDialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));

        user_name_txt.setText(user.get(session.KEY_NAME)+"");
        ButtonClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mAlertDialog.dismiss();
            }
        });

        btn_logout.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
              //  logOut(NaviMainActivity.this);

                session.logoutUser();
            }
        });


        mAlertDialog.show();
    }



}