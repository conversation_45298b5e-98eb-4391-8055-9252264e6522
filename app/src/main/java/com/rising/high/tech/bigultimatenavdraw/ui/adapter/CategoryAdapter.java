package com.rising.high.tech.bigultimatenavdraw.ui.adapter;

import android.content.Context;
import android.graphics.Color;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.model.Category;

import java.util.ArrayList;
import java.util.Random;

public class CategoryAdapter extends BaseAdapter {
    Context context;
    ArrayList<Category> dataCategoryList = new ArrayList<>();
    LayoutInflater inflter;

    public CategoryAdapter(Context applicationContext) {
        this.context = applicationContext;
        inflter = (LayoutInflater.from(applicationContext));
    }

    public void setDataCategoryAdapter(ArrayList<Category> att) {
        this.dataCategoryList = att;
        notifyDataSetChanged();
    }

    @Override
    public int getCount() {
        return dataCategoryList.size();
    }

    @Override
    public Object getItem(int i) {
        return null;
    }

    @Override
    public long getItemId(int i) {
        return 0;
    }

    @Override
    public View getView(int i, View view, ViewGroup viewGroup) {
        final int pos = i;
        view = inflter.inflate(R.layout.dry_item_category, null); // inflate the layout

        TextView titleCategory = view.findViewById(R.id.titleCategory);
        titleCategory.setText(dataCategoryList.get(i).getName());
//        LinearLayout container_place = view.findViewById(R.id.container_place);
        TextView titleBorder = view.findViewById(R.id.titleBorder);
        LinearLayout container = view.findViewById(R.id.id_container);

        int[] color_arr = {R.color.color1,R.color.color3,R.color.color4,R.color.color5,R.color.color6,R.color.color7};
        int rnd = new Random().nextInt(color_arr.length);
        titleBorder.setBackgroundResource(color_arr[rnd]);

//        int color = Color.argb(255, i*300, i*100, i*300);
//        titleBorder.setBackgroundColor(color);

        /* TextView titleCategory = view.findViewById(R.id.titleCategory);
        ImageButton imageArticle = view.findViewById(R.id.imageArticle);

        String rName = "ic_" + dataCategoryList.get(i).getName().toLowerCase().replaceAll("\\s+", "");
        int checkExistence = context.getResources().getIdentifier(rName, "drawable", context.getPackageName());

        if ( checkExistence != 0 ) {  // the resource exists...
            int resID = context.getResources().getIdentifier(rName, "drawable", context.getPackageName());
            imageArticle.setImageResource(resID);
        }
        else {  // checkExistence == 0  // the resource does NOT exist!!
            imageArticle.setImageResource(R.drawable.ic_drinks);
        }

        titleCategory.setText(dataCategoryList.get(i).getName() + "");

        imageArticle.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Log.d("TAG", "clicked from adapter ");
                if (mOnDataChangeListener != null) {
                    mOnDataChangeListener.onDataChanged(dataCategoryList.get(pos));
                }
                notifyDataSetChanged();
            }
        });
       */
       if (dataCategoryList.get(pos).isSelected()) container.setBackgroundResource(R.color.blue_light);


        titleCategory.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                for (int i=0;i<dataCategoryList.size(); i++)
                {
                    dataCategoryList.get(i).setSelected(false);
                }
                dataCategoryList.get(pos).setSelected(true);

                notifyDataSetChanged();
                if (mOnDataChangeListener != null) {
                    mOnDataChangeListener.onDataChanged(dataCategoryList.get(pos));
                }
                notifyDataSetChanged();
            }
        });
        return view;
    }

    public interface OnDataChangeListener {
        void onDataChanged(Category article);
    }

    OnDataChangeListener mOnDataChangeListener;

    public void setOnDataChangeListener(OnDataChangeListener onDataChangeListener) {
        mOnDataChangeListener = onDataChangeListener;
    }
}