package com.rising.high.tech.bigultimatenavdraw.ui.adapter;

import android.content.Context;
import android.graphics.Bitmap;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.Gallery;
import android.widget.ImageView;
import android.widget.RelativeLayout;

import java.util.ArrayList;
import java.util.List;

public class ImageAdapter extends BaseAdapter {

    // Declare Variables
    Context mContext;
    LayoutInflater inflater;
    private List<Bitmap> Imagelist = null;
    private ArrayList<Bitmap> arraylist;

    public ImageAdapter(Context context, List<Bitmap> Imagelist) {
        mContext = context;
        this.Imagelist = Imagelist;
        inflater = LayoutInflater.from(mContext);
        this.arraylist = new ArrayList<Bitmap>();
        this.arraylist.addAll(Imagelist);
    }

    public class ViewHolder {
        ImageView image;
    }

    @Override
    public int getCount() {
        return Imagelist.size();
    }

    @Override
    public Bitmap getItem(int position) {
        return Imagelist.get(position);
    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    // returns an ImageView view
    public View getView(int position, View convertView, ViewGroup parent) {

        ImageView imageView = new ImageView(mContext);
        // imageView.setImageDrawable(mContext.getResources().getDrawable(R.drawable.router_connected));
        imageView.setLayoutParams(new Gallery.LayoutParams(400, 400));

        // Image url
        Bitmap image = Imagelist.get(position);
        imageView.setImageBitmap(image);

        // do stuff initializing your imgView as before
        RelativeLayout borderImg = new RelativeLayout(mContext);
        borderImg.setPadding(1, 1, 1, 1);
        borderImg.setBackgroundColor(0xff000000);
        borderImg.addView(imageView);
        return borderImg;

        // imageView.setBackgroundResource(itemBackground);
        // return imageView;
    }

}
