package com.rising.high.tech.bigultimatenavdraw.ui.adapter;

import static com.rising.high.tech.bigultimatenavdraw.util.Constant.EXCLUSIVE;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.INCLUSIVE;

import android.content.Context;
import android.content.res.Resources;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import com.google.android.material.snackbar.Snackbar;
import com.google.gson.Gson;
import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.db.VariationLocationDetailDbController;
import com.rising.high.tech.bigultimatenavdraw.db.VariationsDbController;
import com.rising.high.tech.bigultimatenavdraw.model.Product;
import com.rising.high.tech.bigultimatenavdraw.model.Variation;
import com.rising.high.tech.bigultimatenavdraw.model.Variation_location_details;
import com.rising.high.tech.bigultimatenavdraw.util.SessionManager;
import com.squareup.picasso.Callback;
import com.squareup.picasso.Picasso;

import java.util.ArrayList;
import java.util.HashMap;

public class ProductAdapter extends BaseAdapter {

    private static final String TAG = "ProductAdapter";
    Context context;
    ArrayList<Product> dataProductList = new ArrayList<>();
    LayoutInflater inflter;
    Resources resources;
    private SessionManager session;
    private HashMap<String, Object> user;
    private final VariationLocationDetailDbController variationLocationDetailDbController;
    private final VariationsDbController variationsDbController;

    public ProductAdapter(Context applicationContext) {
        this.context = applicationContext;
        resources = context.getResources();

        session = new SessionManager(context);
        user = session.getUserDetails();

        variationLocationDetailDbController = new VariationLocationDetailDbController(context);
        variationLocationDetailDbController.open();

        variationsDbController = new VariationsDbController(context);
        variationsDbController.open();
        inflter = (LayoutInflater.from(applicationContext));
    }

    public void setProductAdapter(ArrayList<Product> att) {
        this.dataProductList = att;
        notifyDataSetChanged();
    }

    @Override
    public int getCount() {
        return dataProductList.size();
    }

    @Override
    public Object getItem(int i) {
        return null;
    }

    @Override
    public long getItemId(int i) {
        return 0;
    }

    @Override
    public View getView(int i, View view, ViewGroup viewGroup) {
        view = inflter.inflate(R.layout.dry_article_item, null); // inflate the layout

        ImageView imageArticle = view.findViewById(R.id.imageArticle);
        TextView titleArticle = view.findViewById(R.id.titleArticle);
        TextView product_price = view.findViewById(R.id.product_price);
        LinearLayout linearLayout = view.findViewById(R.id.linearLayout);
        titleArticle.setText(dataProductList.get(i).getName());
        Variation variation = variationsDbController.getVariationByProductId(dataProductList.get(i).getId());

        String currentSellPrice="0";
        if (dataProductList.get(i).getTax_type()!=null){
            if (dataProductList.get(i).getTax_type().matches(EXCLUSIVE)){
                currentSellPrice = variation.getDefault_sell_price();
            }else if (dataProductList.get(i).getTax_type().matches(INCLUSIVE)){
                currentSellPrice = variation.getSell_price_inc_tax();
            }else {
                currentSellPrice = variation.getDefault_sell_price();
            }
        }else {
            currentSellPrice = variation.getDefault_sell_price();
        }

        product_price.setText(currentSellPrice+" "+ user.get(SessionManager.KEY_SYMBOL));

        imageArticle.setImageDrawable(resources.getDrawable(R.drawable.image_notavailable));
        if (dataProductList.get(i).getIs_sync().equals("no")) {
            if (dataProductList.get(i).getImage_product() != null)
                imageArticle.setImageBitmap(dataProductList.get(i).getImage_product());

        } else {
            if (dataProductList.get(i).getImage_url() != null) {
                Picasso.get().load(dataProductList.get(i).getImage_url()).resize(100, 100).into(imageArticle, new Callback() {
                    @Override
                    public void onSuccess() {
                    }

                    @Override
                    public void onError(Exception e) {
                        imageArticle.setImageDrawable(resources.getDrawable(R.drawable.image_notavailable));
                    }
                });
            }
        }

        final int pos = i;
        imageArticle.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                user = session.getUserDetails();
                int id_station = (int) user.get(session.LOCATION_ID);
                int id_product = dataProductList.get(i).getId();

                Variation_location_details variation_location_details = variationLocationDetailDbController.getVariationLocationDetailsByStationIdProductId(id_station, id_product);
                Integer qtyAvailable = variation_location_details.getQty_available();

                if ((int) user.get(session.LOCATION_ID) == 0) {
                    user = session.getUserDetails();
                    Snackbar snackbar = Snackbar.make(linearLayout, context.getResources().getString(R.string.lbl_please_select_station), Snackbar.LENGTH_LONG);
                    snackbar.show();
                }else  if (qtyAvailable > 0 && dataProductList.get(i).getNot_for_selling() == 0) {
                    if (mOnDataChangeListener != null) {
                        mOnDataChangeListener.onDataChanged(dataProductList.get(i));
                    }
                } else {
                    Snackbar snackbar = Snackbar.make(linearLayout, context.getResources().getString(R.string.label_empty_product), Snackbar.LENGTH_LONG);
                    snackbar.show();
                }
            }
        });

        return view;
    }


    public interface OnDataChangeListener {
        void onDataChanged(Product product);
    }

    OnDataChangeListener mOnDataChangeListener;

    public void setOnDataChangeListener(OnDataChangeListener onDataChangeListener) {
        mOnDataChangeListener = onDataChangeListener;
    }
}