package com.rising.high.tech.bigultimatenavdraw.ui.adapter;

import android.content.Context;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.db.VariationsDbController;
import com.rising.high.tech.bigultimatenavdraw.model.Product;
import com.rising.high.tech.bigultimatenavdraw.model.Variation;
import com.rising.high.tech.bigultimatenavdraw.util.SessionManager;

import java.util.ArrayList;


public class QuickSellQuotationAdapter extends RecyclerView.Adapter<QuickSellQuotationAdapter.DShortProductViewHolder> {

    private ArrayList<Product> dataList = new ArrayList<>();

    private Boolean isStock = false;
    private VariationsDbController variationsDbController;
    public QuickSellQuotationAdapter(Boolean isStock) {
        this.isStock=isStock;
    }
    private SessionManager session;

    public QuickSellQuotationAdapter() {
    }

    Context context;
    private boolean firstLog = true;
    @Override
    public DShortProductViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        context = parent.getContext();
        session = new SessionManager(context);

        variationsDbController= new VariationsDbController(context);
        variationsDbController.open();

        return new DShortProductViewHolder(LayoutInflater.from(parent.getContext())
                .inflate(R.layout.d_short_product_item, parent, false));
    }

    @Override
    public void onBindViewHolder(DShortProductViewHolder holder, int position) {

        Variation variation = variationsDbController.getVariationByProductId(dataList.get(position).getId());
        holder.id_article_name.setText(dataList.get(position).getName());
        holder.id_pu.setText(variation.getDefault_sell_price());
        holder.id_pu.setEnabled(!isStock);
        holder.id_unit.setText(dataList.get(position).getUnit_shortname());
        if (dataList.get(position).getSell_qte()<=1) {
            dataList.get(position).setSell_qte(1);
        }
        int quntitiy = dataList.get(position).getSell_qte();
        Float price = Float.parseFloat(variation.getDefault_sell_price());
        holder.id_sub_total.setText((quntitiy * price) + "");
        holder.id_qt.setText(dataList.get(position).getSell_qte() + "");

        holder.currency.setText(" "+session.getUserDetails().get(session.KEY_SYMBOL));
        holder.id_qt.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {

                if (charSequence.toString().length() != 0) {

                    firstLog = false;
                    int quntitiy = Integer.parseInt(holder.id_qt.getText().toString());
                    Float price = Float.parseFloat(holder.id_pu.getText().toString());

                    holder.id_sub_total.setText((quntitiy * price) + "");
                   // dataList.get(position).setPrice(quntitiy * price);
                    dataList.get(position).setSell_qte(quntitiy);

                    if (mOnDataChangeListener != null) {
                        mOnDataChangeListener.onDataChanged(dataList);
                    }
                    //  notifyDataSetChanged();
                    // notifyItemChanged(position);
                }
            }

            @Override
            public void afterTextChanged(Editable editable) {

            }
        });
//
        holder.id_pu.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {

                if (charSequence.toString().length() != 0) {

                    firstLog = false;

                    Float quntitiy = Float.parseFloat(holder.id_qt.getText().toString());
                    Float price = Float.parseFloat(holder.id_pu.getText().toString());

                    holder.id_sub_total.setText((quntitiy * price) + "");
                   // dataList.get(position).setSingle_dpp(quntitiy+"");
                   //dataList.get(position).setUnit_price(price);

                    if (mOnDataChangeListener != null) {
                        mOnDataChangeListener.onDataChanged(dataList);
                    }

                }
            }

            @Override
            public void afterTextChanged(Editable editable) {

            }
        });


        holder.id_delete.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                dataList.remove(position);
                notifyDataSetChanged();

                if (mOnDataChangeListener != null) {
                    mOnDataChangeListener.onDataChanged(dataList);
                }

            }
        });
        if (position % 2 == 1) {
            holder.linearLayout.setBackgroundColor(context.getResources().getColor(R.color.white));
        } else {
            holder.linearLayout.setBackgroundColor(context.getResources().getColor(R.color.lightGrey));

        }
    }

    public void setData(ArrayList<Product> arrayList) {
        this.dataList = arrayList;
        if (mOnDataChangeListener != null) {
            mOnDataChangeListener.onDataChanged(this.dataList);
        }
        notifyDataSetChanged();
    }

    public ArrayList<Product> getData() {
        return this.dataList;
    }

    public void updateData(Product product) {
        this.dataList .add(product);
        if (mOnDataChangeListener != null) {

            mOnDataChangeListener.onDataChanged(this.dataList);

        }
        notifyDataSetChanged();
    }


    @Override
    public int getItemCount() {
        return dataList.size();
    }

    public class DShortProductViewHolder extends RecyclerView.ViewHolder {

        TextView id_article_name, id_sub_total, id_unit, currency;
        EditText id_qt, id_pu;
        ImageView id_delete;
        LinearLayout linearLayout;

        public DShortProductViewHolder(View itemView) {
            super(itemView);

            linearLayout = itemView.findViewById(R.id.linearLayout);
            id_article_name = itemView.findViewById(R.id.id_article_name);
            id_qt = itemView.findViewById(R.id.id_qt);
            id_pu = itemView.findViewById(R.id.id_pu);
            id_sub_total = itemView.findViewById(R.id.id_sub_total);
            id_delete = itemView.findViewById(R.id.id_delete);
            id_unit = itemView.findViewById(R.id.id_unit);
            currency = itemView.findViewById(R.id.currency);

        }
    }


    public interface OnDataChangeListener {
        void onDataChanged(ArrayList<Product> products);
    }

    OnDataChangeListener mOnDataChangeListener;

    public void setOnDataChangeListener(OnDataChangeListener onDataChangeListener) {
        mOnDataChangeListener = onDataChangeListener;
    }

}
