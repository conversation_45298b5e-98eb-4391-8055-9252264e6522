package com.rising.high.tech.bigultimatenavdraw.ui.adapter;

import android.content.Context;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.db.BusinessLocationDbController;
import com.rising.high.tech.bigultimatenavdraw.db.ProductDbController;
import com.rising.high.tech.bigultimatenavdraw.db.VariationLocationDetailDbController;
import com.rising.high.tech.bigultimatenavdraw.db.VariationsDbController;
import com.rising.high.tech.bigultimatenavdraw.model.Business_location;
import com.rising.high.tech.bigultimatenavdraw.model.Product;
import com.rising.high.tech.bigultimatenavdraw.model.Variation;
import com.rising.high.tech.bigultimatenavdraw.model.Variation_location_details;

import java.util.ArrayList;


public class VariationProductLocationDetailsAdapter extends RecyclerView.Adapter<VariationProductLocationDetailsAdapter.DShortVariation_location_detailsViewHolder> {
    private static final String TAG = "VariationLocationDetail";
    private ArrayList<Variation_location_details> dataList = new ArrayList<>();
    private BusinessLocationDbController businessLocationDbController;
    private ProductDbController productDbController;
    private VariationsDbController variationsDbController;
    private VariationLocationDetailDbController variationLocationDetailDbController;

    public VariationProductLocationDetailsAdapter() {
    }

    Context context;

    private boolean firstLog = false;

    @Override
    public DShortVariation_location_detailsViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        context = parent.getContext();
        businessLocationDbController = new BusinessLocationDbController(context);
        businessLocationDbController.open();

        productDbController = new ProductDbController(context);
        productDbController.open();

        variationsDbController = new VariationsDbController(context);
        variationsDbController.open();

        variationLocationDetailDbController = new VariationLocationDetailDbController(context);
        variationLocationDetailDbController.open();

        return new DShortVariation_location_detailsViewHolder(LayoutInflater.from(parent.getContext())
                .inflate(R.layout.list_short_product_item, parent, false));
    }

    @Override
    public void onBindViewHolder(DShortVariation_location_detailsViewHolder holder, int position) {

        Business_location businesslocation = businessLocationDbController.getStationById(dataList.get(position).getLocation_id());
        Product product = productDbController.getProductById(dataList.get(position).getProduct_id());

        holder.id_article_name.setText(product.getName() + "(" + businesslocation.getName() + ")");
        Variation variation=variationsDbController.getVariationByProductId(product.getId());
        holder.id_pu.setText(variation.getDefault_purchase_price());
        holder.id_pu.setEnabled(false);
        holder.id_qt.setText(dataList.get(position).getQty_available() + "");
        holder.id_unit.setText(product.getUnit_shortname());


        Variation_location_details variation_location_details= variationLocationDetailDbController.getVariationLocationDetailsByStationIdProductId(dataList.get(position).getLocation_id(),dataList.get(position).getProduct_id());
        Integer quntitiy = variation_location_details.getQty_available();
        Float price = Float.parseFloat(variation.getDefault_purchase_price());
        holder.id_sub_total.setText((quntitiy * price) + "");


        holder.id_qt.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {
                if (charSequence.toString().length() != 0) {
                    holder.id_sub_total.setText((Float.parseFloat(holder.id_qt.getText().toString()) * price) + "");
                    dataList.get(position).setQty_available(Integer.parseInt(charSequence.toString()));
                    dataList.get(position).setOld_qty_available(Integer.parseInt(charSequence.toString()));
                }
            }

            @Override
            public void afterTextChanged(Editable editable) {

            }
        });

        holder.id_delete.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                dataList.remove(position);
                notifyDataSetChanged();

                if (mOnDataChangeListener != null) {
                    mOnDataChangeListener.onDataChanged(dataList);
                }

            }
        });

    }

    public void setData(ArrayList<Variation_location_details> arrayList) {
        this.dataList = arrayList;
        if (mOnDataChangeListener != null) {
            mOnDataChangeListener.onDataChanged(this.dataList);
        }
        notifyDataSetChanged();
    }

    public ArrayList<Variation_location_details> getData() {
        return this.dataList;
    }

    public void updateData(Variation_location_details variation_location_details) {
        this.dataList.add(variation_location_details);
        if (mOnDataChangeListener != null) {

            mOnDataChangeListener.onDataChanged(this.dataList);

        }
        notifyDataSetChanged();
    }


    @Override
    public int getItemCount() {
        return dataList.size();
    }

    public class DShortVariation_location_detailsViewHolder extends RecyclerView.ViewHolder {

        TextView id_article_name, id_sub_total, id_unit;
        EditText id_qt, id_pu;
        ImageView id_delete;

        public DShortVariation_location_detailsViewHolder(View itemView) {
            super(itemView);

            id_article_name = itemView.findViewById(R.id.id_article_name);
            id_qt = itemView.findViewById(R.id.id_qt);
            id_pu = itemView.findViewById(R.id.id_pu);
            id_sub_total = itemView.findViewById(R.id.id_sub_total);
            id_delete = itemView.findViewById(R.id.id_delete);
            id_unit = itemView.findViewById(R.id.id_unit);

        }
    }


    public interface OnDataChangeListener {
        void onDataChanged(ArrayList<Variation_location_details> variation_location_detailss);
    }

    OnDataChangeListener mOnDataChangeListener;

    public void setOnDataChangeListener(OnDataChangeListener onDataChangeListener) {
        mOnDataChangeListener = onDataChangeListener;
    }

}
