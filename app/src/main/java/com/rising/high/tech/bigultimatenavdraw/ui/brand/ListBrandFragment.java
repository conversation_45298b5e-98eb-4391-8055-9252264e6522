package com.rising.high.tech.bigultimatenavdraw.ui.brand;

import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.Spinner;
import android.widget.TextView;

import androidx.appcompat.widget.AppCompatButton;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.appcompat.widget.SearchView;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.gson.Gson;
import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.db.BrandDbController;
import com.rising.high.tech.bigultimatenavdraw.model.Brand;
import com.rising.high.tech.bigultimatenavdraw.model.Business_location;
import com.rising.high.tech.bigultimatenavdraw.model.Category;
import com.rising.high.tech.bigultimatenavdraw.ui.brand.adapter.ListItemBrandAdapter;
import com.rising.high.tech.bigultimatenavdraw.util.FileUtil;
import com.rising.high.tech.bigultimatenavdraw.util.SessionManager;
import com.rising.high.tech.bigultimatenavdraw.util.StringFormat;
import com.rising.high.tech.bigultimatenavdraw.util.serverInteraction;

import java.util.ArrayList;
import java.util.Objects;

//import butterknife.BindView;
//import butterknife.ButterKnife;

import static com.rising.high.tech.bigultimatenavdraw.util.Constant.BRAND_ADD;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.LOCAL_USE;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.PRODUCT_ADD;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.SERVER_MASTER;

public class ListBrandFragment extends Fragment {
    private static final String TAG = "ListBrandFragment";
    private Context _context;
    Button addBtn;
    RecyclerView recycle_brand;
    Spinner spinnerCategory, spinnerStation, spinnerUnit;
    AppCompatButton btnAdd;
    ListItemBrandAdapter listItemBrandAdapter;
    BrandDbController brandDbController;

    @BindView(R.id.id_filter)
    Button btnFilter;
    @BindView(R.id.id_search_edit)
    SearchView searchEdit;
    @BindView(R.id.noItemFound)
    TextView noItemFound;
    public ListBrandFragment() {
        // Required empty public constructor
    }
    SessionManager session;

    private ArrayList<Brand> dataList = new ArrayList<>();
    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        // Inflate the layout for this fragment
        View root = inflater.inflate(R.layout.fragment_list_brandt, container, false);
        _context = getContext();
        ButterKnife.bind(this, root);

        session = new SessionManager(_context);
        addBtn = root.findViewById(R.id.id_add);
        recycle_brand = root.findViewById(R.id.recycle_brand);
        spinnerCategory = root.findViewById(R.id.spinner_category);
        spinnerStation = root.findViewById(R.id.spinner_station);
        spinnerUnit = root.findViewById(R.id.spinner_unit);
        btnAdd = root.findViewById(R.id.btn_add);
        checkRoles();
        brandDbController = new BrandDbController(_context);
        brandDbController.open();
        setUpRecyclerView();

        initListner();
        return root;
    }

    private void checkRoles()
    {
        if (session.getBoolean(SERVER_MASTER) || !session.checkPermissionSubModule(BRAND_ADD))
        {
            btnAdd.setVisibility(View.INVISIBLE);
        }
    }

    private void initListner()
    {
        btnAdd.setOnClickListener(v -> {
            Brand brand = new Brand();
            addBrand(brand,false);
        });
        searchEdit.setQueryHint("Search Here");
        searchEdit.setOnQueryTextListener(new SearchView.OnQueryTextListener() {

            @Override
            public boolean onQueryTextSubmit(String query) {
                listItemBrandAdapter.setData(brandDbController.getAllBrandsLike(query));
                setItemView();
                return false;
            }

            @Override
            public boolean onQueryTextChange(String newText) {
                listItemBrandAdapter.setData(brandDbController.getAllBrandsLike(newText));
                setItemView();
                return false;
            }
        });
    }
    private void setUpRecyclerView()
    {
        dataList=brandDbController.getAllBrands();
        listItemBrandAdapter = new ListItemBrandAdapter();
        listItemBrandAdapter.setData(dataList);
        listItemBrandAdapter.setonClickAction(new ListItemBrandAdapter.onClickAction() {
            @Override
            public void onClickEdit(Brand brand) {
                addBrand(brand,true);
            }

            @Override
            public void onClickDelete(int position) {
                deleteItem(position);
            }
        });
        recycle_brand.setAdapter(listItemBrandAdapter);
        recycle_brand.setLayoutManager(new LinearLayoutManager(_context));
        listItemBrandAdapter.notifyDataSetChanged();
        setItemView();
    }
    public void setItemView() {
        if (listItemBrandAdapter.getItemCount() > 0) {
            recycle_brand.setVisibility(View.VISIBLE);
            noItemFound.setVisibility(View.GONE);
        } else {
            recycle_brand.setVisibility(View.GONE);
            noItemFound.setVisibility(View.VISIBLE);
        }
    }

    private void filter() {
        Category category = (Category) spinnerCategory.getSelectedItem();
        Business_location businesslocation = (Business_location) spinnerStation.getSelectedItem();
        Integer unit = spinnerUnit.getSelectedItemPosition();
        Log.d(TAG, "selected item est " + new Gson().toJson(category));
        Log.d(TAG, "selected unit est " + unit + " station : " + businesslocation.getId());
    }

    public void replaceFragment(Fragment someFragment) {
        FragmentTransaction transaction = getFragmentManager().beginTransaction();
        transaction.replace(R.id.nav_host_fragment, someFragment);
        transaction.addToBackStack(null);
        transaction.commit();
    }


    private void addBrand(Brand brand,boolean isEdit) {
        //Preparing views
        // get prompts.xml view
        LayoutInflater li = LayoutInflater.from(_context);
        View promptsView = li.inflate(R.layout.brand_add_main, null);
        androidx.appcompat.app.AlertDialog.Builder alertDialogBuilder = new androidx.appcompat.app.AlertDialog.Builder(
                _context);

        alertDialogBuilder.setView(promptsView);

        final EditText brand_name = promptsView.findViewById(R.id.brand_name);
        final EditText desc = promptsView.findViewById(R.id.id_descc);

        final Button ButtonSave = promptsView.findViewById(R.id.btn_save);
        final ImageView ButtonClose = promptsView.findViewById(R.id.btn_close);
        final AppCompatTextView titleContent = promptsView.findViewById(R.id.titleContent);

        final androidx.appcompat.app.AlertDialog mAlertDialog = alertDialogBuilder.create();
        if (mAlertDialog.getWindow() != null)
            mAlertDialog.getWindow().getAttributes().windowAnimations = R.style.SlidingDialogAnimation;
        Objects.requireNonNull(mAlertDialog.getWindow()).setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));

        ButtonClose.setOnClickListener(v -> mAlertDialog.dismiss());
        if(isEdit)
        {
            brand_name.setText(brand.getName());
            desc.setText(brand.getShort_desc());
            titleContent.setText(getResources().getString(R.string.edit_brand));
        }


        ButtonSave.setOnClickListener(v -> {
            if (!brand_name.getText().toString().isEmpty()) {

                brand.setName(brand_name.getText().toString());
                brand.setShort_desc(desc.getText().toString());
                brand.setBusiness_id(session.getBusinessModel().getId());
                int inserted,msg;
                if(isEdit)
                {
                    inserted = brandDbController.updateLocal(brand);
                    msg = R.string.brands_updated_success;
                }
                else
                {
                     inserted = brandDbController.insertLocal(brand);
                      msg=R.string.brands_added_success;
                }
                if (inserted > 0) {
                    FileUtil.showDialog(_context, getString(R.string.success), getResources().getString(msg));
                    dataList=brandDbController.getAllBrands();
                    listItemBrandAdapter.setData(dataList);
                    listItemBrandAdapter.notifyDataSetChanged();
                    mAlertDialog.dismiss();
                    setItemView();
                } else {
                    StringFormat.showSnackBar(promptsView, R.string.failed_to_update_data,true);
                }
            } else {
                brand_name.setError(getResources().getString(R.string.enter_brand_name));
            }

        });


        mAlertDialog.show();
    }

    private void deleteItem(int position) {
        //Preparing views
        // get prompts.xml view
        LayoutInflater li = LayoutInflater.from(_context);
        View promptsView = li.inflate(R.layout.delete_dialog_main, null);

        androidx.appcompat.app.AlertDialog.Builder alertDialogBuilder = new androidx.appcompat.app.AlertDialog.Builder(_context);
        alertDialogBuilder.setView(promptsView);

        final Button ButtonSave = promptsView.findViewById(R.id.btn_save);
        final Button ButtonClose = promptsView.findViewById(R.id.btn_close);

        final androidx.appcompat.app.AlertDialog mAlertDialog = alertDialogBuilder.create();
        if (mAlertDialog.getWindow() != null)
            mAlertDialog.getWindow().getAttributes().windowAnimations = R.style.SlidingDialogAnimation;
        mAlertDialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));

        ButtonClose.setOnClickListener(v -> mAlertDialog.dismiss());

        ButtonSave.setOnClickListener(v -> {

            brandDbController.deleteItem(dataList.get(position).getId());
            dataList.remove(position);
            mAlertDialog.dismiss();
            listItemBrandAdapter.notifyDataSetChanged();
            setItemView();

        });


        mAlertDialog.show();
    }

}