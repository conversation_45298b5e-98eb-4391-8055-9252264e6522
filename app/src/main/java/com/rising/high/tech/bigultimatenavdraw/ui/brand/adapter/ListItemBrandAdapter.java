package com.rising.high.tech.bigultimatenavdraw.ui.brand.adapter;

import android.content.Context;
import android.content.res.Resources;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.db.BrandDbController;
import com.rising.high.tech.bigultimatenavdraw.model.Brand;
import com.rising.high.tech.bigultimatenavdraw.model.Variation_template;
import com.rising.high.tech.bigultimatenavdraw.ui.variation.adapter.ListVariationAdapter;
import com.rising.high.tech.bigultimatenavdraw.util.FileUtil;
import com.rising.high.tech.bigultimatenavdraw.util.SessionManager;
import com.squareup.picasso.Callback;
import com.squareup.picasso.Picasso;

import java.util.ArrayList;

import static com.rising.high.tech.bigultimatenavdraw.util.Constant.BRAND_ADD;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.BRAND_DELETE;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.BRAND_EDIT;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.CATEGORY_DELETE;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.CATEGORY_EDIT;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.SERVER_MASTER;


public class ListItemBrandAdapter extends RecyclerView.Adapter<ListItemBrandAdapter.ListBrandViewHolder> {

    private ArrayList<Brand> dataList = new ArrayList<>();
    private Resources resources;
    private BrandDbController brandDbController;
    Context context;
    SessionManager session;

    @Override
    public ListBrandViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        context = parent.getContext();
        resources = context.getResources();
        session = new SessionManager(context);
        brandDbController = new BrandDbController(context);
        brandDbController.open();
        return new ListBrandViewHolder(LayoutInflater.from(parent.getContext())
                .inflate(R.layout.brands_item, parent, false));
    }

    @Override
    public void onBindViewHolder(ListBrandViewHolder holder, int position) {
        if (session.getBoolean(SERVER_MASTER) || !session.checkPermissionSubModule(BRAND_EDIT)) {
            holder.btnEdit.setVisibility(View.GONE);
        }
        if (session.getBoolean(SERVER_MASTER) || !session.checkPermissionSubModule(BRAND_DELETE)) {
            holder.btnDelete.setVisibility(View.GONE);
        }
        holder.name.setText(dataList.get(position).getName());
        holder.desc.setText(dataList.get(position).getShort_desc());
        if (position % 2 == 1) {
            holder.linearLayout.setBackgroundColor(context.getResources().getColor(R.color.white));
        } else {
            holder.linearLayout.setBackgroundColor(context.getResources().getColor(R.color.lightGrey));

        }
    }

    public void setData(ArrayList<Brand> arrayList) {
        this.dataList = arrayList;
        notifyDataSetChanged();
    }


    @Override
    public int getItemCount() {
        return dataList.size();
    }

    public class ListBrandViewHolder extends RecyclerView.ViewHolder {

        TextView name, desc;
        ImageView btnDelete,btnEdit;
        LinearLayout linearLayout;
        public ListBrandViewHolder(View itemView) {
            super(itemView);

            linearLayout = itemView.findViewById(R.id.linearLayout);
            name = itemView.findViewById(R.id.id_name);
            desc = itemView.findViewById(R.id.desc);
            btnDelete = itemView.findViewById(R.id.btn_delete);
            btnEdit = itemView.findViewById(R.id.btn_edit);
            btnDelete.setOnClickListener(v -> {
                if (onClickAction != null) {
                    onClickAction.onClickDelete(getAdapterPosition());
                }
                notifyItemChanged(getAdapterPosition());
            });

            btnEdit.setOnClickListener(v -> {
                if (onClickAction != null) {
                    onClickAction.onClickEdit(dataList.get(getAdapterPosition()));
                }
                notifyItemChanged(getAdapterPosition());

            });
        }
    }

    public interface onClickAction {
        void onClickEdit(Brand brand);
        void onClickDelete(int position);
    }

    onClickAction onClickAction;

    public void setonClickAction(onClickAction mOnClickAction) {
        onClickAction = mOnClickAction;
    }


}
