package com.rising.high.tech.bigultimatenavdraw.ui.businesslocation;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.Gallery;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.Spinner;
import android.widget.Toast;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;

import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.db.CategoryDbController;
import com.rising.high.tech.bigultimatenavdraw.db.ProductDbController;
import com.rising.high.tech.bigultimatenavdraw.db.ProductLocationDbController;
import com.rising.high.tech.bigultimatenavdraw.db.BusinessLocationDbController;
import com.rising.high.tech.bigultimatenavdraw.db.UnitDbController;
import com.rising.high.tech.bigultimatenavdraw.db.VariationLocationDetailDbController;
import com.rising.high.tech.bigultimatenavdraw.model.Business_location;
import com.rising.high.tech.bigultimatenavdraw.ui.NaviMainActivity;
import com.rising.high.tech.bigultimatenavdraw.ui.adapter.SpinCategoryAdapter;
import com.rising.high.tech.bigultimatenavdraw.ui.adapter.SpinStationAdapter;
import com.rising.high.tech.bigultimatenavdraw.ui.adapter.SpinUnitAdapter;
import com.rising.high.tech.bigultimatenavdraw.ui.stocktransfers.StockTransferFragment;
import com.rising.high.tech.bigultimatenavdraw.util.FileUtil;

import java.util.ArrayList;

import butterknife.BindView;
import butterknife.ButterKnife;

import static com.rising.high.tech.bigultimatenavdraw.util.Constant.ID;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.NO;
import static io.reactivex.rxjava3.annotations.SchedulerSupport.NONE;

public class AddBusinessLocationFragment extends Fragment {
    private static final String TAG = "AddProductFragment";

    private Context _context;
    private SpinStationAdapter spinStationAdapter;

    private BusinessLocationDbController businessLocationDbController;
    private ProductLocationDbController productLocationDbController;
    private VariationLocationDetailDbController variationLocationDetailDbController;

    Button btnBack;
    SpinCategoryAdapter spinCategoryAdapter;
    SpinUnitAdapter spinUnitAdapter;
    CategoryDbController categoryDbController;
    UnitDbController unitDbController;
    Spinner spinnerCategory, spinnerUnit;
    LinearLayout mStockContainer;
    ProductDbController productDbController;
    ImageButton imageAddAttach;
    Gallery gallery;

    private ArrayList<Business_location> currentBusinesslocation = null;
    private ArrayList<Business_location> selectedBusinesslocations = null;

    @BindView(R.id.business_name)
    EditText business_name;
    @BindView(R.id.city)
    EditText city;
    @BindView(R.id.state_req)
    EditText state_req;
    @BindView(R.id.zip_code)
    EditText zip_code;
    @BindView(R.id.country)
    EditText country;
    @BindView(R.id.mobile)
    EditText mobile;
    @BindView(R.id.add_btn)
    Button btnAdd;
    @BindView(R.id.land_mark)
    EditText land_mark;
    @BindView(R.id.email)
    EditText email;
    private Boolean isEdit = false;
    private Integer indexId = 0;
    private Integer locationId = 0;

    public AddBusinessLocationFragment(boolean isEdit) {
        // Required empty public constructor
        this.isEdit = isEdit;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        // Inflate the layout for this fragment
        View root = inflater.inflate(R.layout.activity_business_location, container, false);
        ButterKnife.bind(this, root);

        _context = getContext();

        if (isEdit) {
            Bundle args = getArguments();
            indexId = args.getInt(ID, 0);
        }

        btnBack = root.findViewById(R.id.id_back);

        initDB();
        initSpinners();
        initClickListners();
        setData();
        return root;
    }

    private void setData() {
        if (isEdit) {
            Business_location business_location = businessLocationDbController.getStationById(indexId);
            btnAdd.setText(getResources().getString(R.string.label_updatee));
            locationId = business_location.getId();
            business_name.setText(business_location.getName());
            land_mark.setText(business_location.getLandmark());
            city.setText(business_location.getCity());
            zip_code.setText(business_location.getZip_code());
            state_req.setText(business_location.getState());
            country.setText(business_location.getCountry());
            mobile.setText(business_location.getMobile());
            email.setText(business_location.getEmail());
        }
    }

    private void initDB() {

        businessLocationDbController = new BusinessLocationDbController(_context);
        businessLocationDbController.open();
    }

    private void initSpinners() {

    }

    private void initClickListners() {

        btnBack.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                replaceFragment(new BusinessLocationFragment());
            }
        });

        btnAdd.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!business_name.getText().toString().equals("") && !city.getText().toString().equals("")
                        && !zip_code.getText().toString().equals("")
                        && !state_req.getText().toString().equals("")
                        && !country.getText().toString().equals("")) {

                    Business_location businesslocation = new Business_location();
                    businesslocation.setName(business_name.getText().toString());
                    businesslocation.setCity(city.getText().toString());
                    businesslocation.setZip_code(zip_code.getText().toString());
                    businesslocation.setState(state_req.getText().toString());
                    businesslocation.setCountry(country.getText().toString());

                    businesslocation.setLandmark(land_mark.getText().toString());
                    businesslocation.setZip_code(zip_code.getText().toString());
                    businesslocation.setMobile(mobile.getText().toString());
                    businesslocation.setEmail(email.getText().toString());
                    businesslocation.setIs_active(1);
                    businesslocation.setSync(NO);

                    int idIndex;
                    if (isEdit) {
                        businesslocation.setId(locationId);
                        idIndex = businessLocationDbController.update(businesslocation);
                    } else {
                        idIndex = businessLocationDbController.insertLocal(businesslocation);
                    }

                    if (idIndex > 0) {
                        FileUtil.showDialog(_context, "Successful", getResources().getString(R.string.business_settings_success));
                        _context.startActivity(new Intent(_context, NaviMainActivity.class));
                   //     replaceFragment(new BusinessLocationFragment());
                    }


                } else {
                    Toast.makeText(_context, getResources().getString(R.string.label_all_field_required), Toast.LENGTH_LONG).show();
                }
            }
        });


    }


    public void replaceFragment(Fragment someFragment) {
        FragmentTransaction transaction = getFragmentManager().beginTransaction();
        transaction.replace(R.id.nav_host_fragment, someFragment);
        transaction.addToBackStack(null);
        transaction.commit();
    }


}