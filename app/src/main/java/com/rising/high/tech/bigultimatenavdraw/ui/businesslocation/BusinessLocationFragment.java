package com.rising.high.tech.bigultimatenavdraw.ui.businesslocation;

import android.content.Context;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.Spinner;
import android.widget.TextView;

import androidx.appcompat.widget.SearchView;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.db.BusinessLocationDbController;
import com.rising.high.tech.bigultimatenavdraw.ui.businesslocation.adapter.BusinessLocationAdapter;
import com.rising.high.tech.bigultimatenavdraw.util.SessionManager;


import static com.rising.high.tech.bigultimatenavdraw.util.Constant.BUSINESS_LOCATION_ADD;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.CATEGORY_ADD;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.SERVER_MASTER;

public class BusinessLocationFragment extends Fragment {

    private static final String TAG = "BusinessLocationFragment";

    private Context _context;
    Button addBtn;
    RecyclerView recycle_lcoation_station;
    Spinner spinnerCategory, spinnerStation, spinnerUnit;
    Button  btnAdd;
    BusinessLocationAdapter businessLocationAdapter;
    BusinessLocationDbController businessLocationDbController;
    Button btnFilter;
    SearchView search_edit;
    TextView noItemFound;

    SessionManager session;


    public BusinessLocationFragment() {
        // Required empty public constructor
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        // Inflate the layout for this fragment
        View root = inflater.inflate(R.layout.fragment_list_location_station, container, false);
        _context = getContext();
        session = new SessionManager(_context);


        addBtn = root.findViewById(R.id.id_add);
        recycle_lcoation_station = root.findViewById(R.id.recycle_lcoation_station);
        spinnerCategory = root.findViewById(R.id.spinner_category);
        spinnerStation = root.findViewById(R.id.spinner_station);
        spinnerUnit = root.findViewById(R.id.spinner_unit);
        btnAdd = root.findViewById(R.id.btn_add);

        businessLocationAdapter = new BusinessLocationAdapter();
        recycle_lcoation_station.setAdapter(businessLocationAdapter);
        recycle_lcoation_station.setLayoutManager(new LinearLayoutManager(_context));

        businessLocationDbController = new BusinessLocationDbController(_context);
        businessLocationDbController.open();

        businessLocationAdapter.setData(businessLocationDbController.getAllStation());

        intiListners();

        setItemView();

        checkRoles();
        return root;
    }

    private void checkRoles()
    {
        if (session.getBoolean(SERVER_MASTER)  || !session.checkPermissionSubModule(BUSINESS_LOCATION_ADD))
        {
            btnAdd.setVisibility(View.INVISIBLE);
        }
    }

    private void intiListners()
    {
        btnAdd.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                //addBrand();
                replaceFragment(new AddBusinessLocationFragment(false));
            }
        });

        search_edit.setQueryHint("Search Here");
        search_edit.setOnQueryTextListener(new androidx.appcompat.widget.SearchView.OnQueryTextListener() {

            @Override
            public boolean onQueryTextSubmit(String query) {
                businessLocationAdapter.setData(businessLocationDbController.getCategoriesLike(query));
                setItemView();
                return false;
            }

            @Override
            public boolean onQueryTextChange(String newText) {
                businessLocationAdapter.setData(businessLocationDbController.getCategoriesLike(newText));
                setItemView();
                return false;
            }
        });

        businessLocationAdapter.setOnDataChangeListener(new BusinessLocationAdapter.OnDataChangeListener() {
            @Override
            public void onDataDeleted() {
                setItemView();
            }
        });
    }

    public void replaceFragment(Fragment someFragment) {
        FragmentTransaction transaction = getFragmentManager().beginTransaction();
        transaction.replace(R.id.nav_host_fragment, someFragment);
        transaction.addToBackStack(null);
        transaction.commit();
    }


    public void setItemView() {
        if (businessLocationAdapter.getItemCount() > 0) {
                recycle_lcoation_station.setVisibility(View.VISIBLE);
            noItemFound.setVisibility(View.GONE);
        } else {
            recycle_lcoation_station.setVisibility(View.GONE);
            noItemFound.setVisibility(View.VISIBLE);
        }
    }

//    private void addBrand() {
//        //Preparing views
//        // get prompts.xml view
//        LayoutInflater li = LayoutInflater.from(_context);
//        View promptsView = li.inflate(R.layout.brand_add_main, null);
//
//
//        androidx.appcompat.app.AlertDialog.Builder alertDialogBuilder = new androidx.appcompat.app.AlertDialog.Builder(
//                _context);
//
//        alertDialogBuilder.setView(promptsView);
//
//        final EditText brand_name = promptsView.findViewById(R.id.brand_name);
//        final EditText desc = promptsView.findViewById(R.id.id_descc);
//
//        final Button ButtonSave = promptsView.findViewById(R.id.btn_save);
//        final Button ButtonClose = promptsView.findViewById(R.id.btn_close);
//
//
//        final androidx.appcompat.app.AlertDialog mAlertDialog = alertDialogBuilder.create();
//        if (mAlertDialog.getWindow() != null)
//            mAlertDialog.getWindow().getAttributes().windowAnimations = R.style.SlidingDialogAnimation;
//        mAlertDialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
//
//        ButtonClose.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                mAlertDialog.dismiss();
//            }
//        });
//
//
//        ButtonSave.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                if (!brand_name.getText().toString().equals("")) {
//
//                    Brand brand = new Brand();
//                    brand.setName(brand_name.getText().toString());
//                    brand.setShort_desc(desc.getText().toString());
//                    int inserted = brandDbController.insertLocal(brand);
//                    if (inserted > 0) {
//                        Toast.makeText(getContext(), getResources().getString(R.string.lbl_added_success), Toast.LENGTH_LONG).show();
//                        listItemBrandAdapter.setData(brandDbController.getAllBrands());
//                        listItemBrandAdapter.notifyDataSetChanged();
//                        mAlertDialog.dismiss();
//                    } else {
//                        Toast.makeText(getContext(), "Error while adding", Toast.LENGTH_LONG).show();
//                    }
//                } else {
//                    Toast.makeText(getContext(), getResources().getString(R.string.label_all_field_required), Toast.LENGTH_LONG).show();
//                }
//
//            }
//        });
//
//
//        mAlertDialog.show();
//    }


}