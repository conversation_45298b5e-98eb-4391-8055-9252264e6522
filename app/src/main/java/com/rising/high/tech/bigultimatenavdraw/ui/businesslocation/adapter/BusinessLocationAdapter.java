package com.rising.high.tech.bigultimatenavdraw.ui.businesslocation.adapter;

import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.TextView;

import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.AppCompatImageView;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.RecyclerView;
import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.db.BusinessLocationDbController;
import com.rising.high.tech.bigultimatenavdraw.model.Business_location;
import com.rising.high.tech.bigultimatenavdraw.model.Transaction;
import com.rising.high.tech.bigultimatenavdraw.ui.businesslocation.AddBusinessLocationFragment;
import com.rising.high.tech.bigultimatenavdraw.ui.businesslocation.BusinessLocationFragment;
import com.rising.high.tech.bigultimatenavdraw.ui.draft.adapter.DraftAdapter;
import com.rising.high.tech.bigultimatenavdraw.util.SessionManager;

import java.util.ArrayList;

import static com.rising.high.tech.bigultimatenavdraw.util.Constant.BRAND_DELETE;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.BRAND_EDIT;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.BUSINESS_LOCATION_DELETE;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.BUSINESS_LOCATION_EDIT;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.SERVER_MASTER;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.UNIT_DELETE;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.UNIT_EDIT;

public class BusinessLocationAdapter extends RecyclerView.Adapter<BusinessLocationAdapter.CategoryViewHolder> {

    private ArrayList<Business_location> dataList = new ArrayList<>();
    SessionManager session;

    Context context;

    BusinessLocationDbController businessLocationDbController;

    @Override
    public CategoryViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        context = parent.getContext();
        session = new SessionManager(context);

        businessLocationDbController = new BusinessLocationDbController(context);
        businessLocationDbController.open();
        return new CategoryViewHolder(LayoutInflater.from(parent.getContext())
                .inflate(R.layout.bussines_location_item, parent, false));
    }

    @Override
    public void onBindViewHolder(CategoryViewHolder holder, int position) {
        if (session.getBoolean(SERVER_MASTER) || !session.checkPermissionSubModule(BUSINESS_LOCATION_EDIT)) {
            holder.btnEdit.setVisibility(View.GONE);
        }
        if (session.getBoolean(SERVER_MASTER) || !session.checkPermissionSubModule(BUSINESS_LOCATION_DELETE)) {
            holder.btnDelete.setVisibility(View.GONE);
        }
        holder.name.setText(dataList.get(position).getName());
        holder.city_id.setText(dataList.get(position).getCity());
        holder.country_id.setText(dataList.get(position).getCountry());
        holder.zip_code_id.setText(dataList.get(position).getZip_code());
        holder.state_id.setText(dataList.get(position).getState());
        if (session.getBoolean(SERVER_MASTER) || !session.checkPermissionSubModule(BUSINESS_LOCATION_EDIT)){
            holder.btnEdit.setVisibility(View.GONE);
        }
        if (!session.checkPermissionSubModule(BUSINESS_LOCATION_DELETE)){
            holder.btnDelete.setVisibility(View.GONE);
        }
    }

    public void setData(ArrayList<Business_location> arrayList) {
        this.dataList = arrayList;
        notifyDataSetChanged();
    }


    @Override
    public int getItemCount() {
        return dataList.size();
    }

    public class CategoryViewHolder extends RecyclerView.ViewHolder {

        TextView  name, code, desc, city_id, country_id, zip_code_id, state_id;
        AppCompatImageView btnDelete, btnEdit;

        public CategoryViewHolder(View itemView) {
            super(itemView);


            name = itemView.findViewById(R.id.id_name);
            code = itemView.findViewById(R.id.cat_code);
            desc = itemView.findViewById(R.id.id_desc);
            city_id = itemView.findViewById(R.id.city_id);
            country_id = itemView.findViewById(R.id.country_id);
            state_id = itemView.findViewById(R.id.state_id);

            btnEdit = itemView.findViewById(R.id.btn_edit);
            zip_code_id = itemView.findViewById(R.id.zip_code_id);

            btnDelete = itemView.findViewById(R.id.btn_delete);

            btnDelete.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    deleteItem(getAdapterPosition());
                }
            });

            btnEdit.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    editBusinessLocation(getAdapterPosition());
                }
            });

        }
    }

    private void editBusinessLocation(int position){
        naviguateFragment(new AddBusinessLocationFragment(true), dataList.get(position).getId());
    }

//    private void editCategory(int position) {
//        //Preparing views
//        // get prompts.xml view
//        LayoutInflater li = LayoutInflater.from(context);
//        View promptsView = li.inflate(R.layout.category_add_main, null);
//
//
//        androidx.appcompat.app.AlertDialog.Builder alertDialogBuilder = new androidx.appcompat.app.AlertDialog.Builder(
//                context);
//
//        alertDialogBuilder.setView(promptsView);
//
//        final EditText catgeroyCode = promptsView.findViewById(R.id.id_category_code);
//        final EditText catgeroyName = promptsView.findViewById(R.id.catgeroy_name);
//        final EditText desc = promptsView.findViewById(R.id.id_desc);
//        final Button ButtonSave = promptsView.findViewById(R.id.btn_save);
//        final Button ButtonClose = promptsView.findViewById(R.id.btn_close);
//
//        ButtonSave.setText(context.getResources().getString(R.string.label_updatee));
//        catgeroyName.setText(dataList.get(position).getName());
//        catgeroyCode.setText(dataList.get(position).getShort_code() !=null ?dataList.get(position).getShort_code() :"");
//        desc.setText(dataList.get(position).getDescription() !=null ?dataList.get(position).getDescription() :"");
//
//        final androidx.appcompat.app.AlertDialog mAlertDialog = alertDialogBuilder.create();
//        if (mAlertDialog.getWindow() != null)
//            mAlertDialog.getWindow().getAttributes().windowAnimations = R.style.SlidingDialogAnimation;
//        mAlertDialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
//
//        ButtonClose.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                mAlertDialog.dismiss();
//            }
//        });
//
//
//        ButtonSave.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                if (!catgeroyName.getText().toString().equals("") ) {
//
//                    Category category = dataList.get(position);
//                    category.setBusiness_id(1);
//                    category.setCreated_by(1);
//                    category.setName(catgeroyName.getText().toString());
//                    category.setDescription(desc.getText().toString());
//                    category.setShort_code(catgeroyCode.getText().toString());
//
//
//                    int inserted = categoryDbController.editCategory(category);
//                    if (inserted > 0) {
//                        Toast.makeText(context, context.getResources().getString(R.string.lbl_added_success), Toast.LENGTH_LONG).show();
//
//                        notifyItemChanged(position);
//                        mAlertDialog.dismiss();
//                    } else {
//                        Toast.makeText(context, "Error while adding", Toast.LENGTH_LONG).show();
//                    }
//                } else {
//                    Toast.makeText(context, context.getResources().getString(R.string.label_all_field_required), Toast.LENGTH_LONG).show();
//                }
//
//            }
//        });
//
//
//        mAlertDialog.show();
//    }


    private void deleteItem(int postition) {
        //Preparing views
        // get prompts.xml view
        LayoutInflater li = LayoutInflater.from(context);
        View promptsView = li.inflate(R.layout.delete_dialog_main, null);

        androidx.appcompat.app.AlertDialog.Builder alertDialogBuilder = new androidx.appcompat.app.AlertDialog.Builder(
                context);

        alertDialogBuilder.setView(promptsView);

        final Button ButtonSave = promptsView.findViewById(R.id.btn_save);
        final Button ButtonClose = promptsView.findViewById(R.id.btn_close);

        final androidx.appcompat.app.AlertDialog mAlertDialog = alertDialogBuilder.create();
        if (mAlertDialog.getWindow() != null)
            mAlertDialog.getWindow().getAttributes().windowAnimations = R.style.SlidingDialogAnimation;
        mAlertDialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));

        ButtonClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mAlertDialog.dismiss();
            }
        });

        ButtonSave.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

                businessLocationDbController.deleteItem(dataList.get(postition).getId());
                dataList.remove(postition);
                mOnDataChangeListener.onDataDeleted();

                mAlertDialog.dismiss();
                notifyDataSetChanged();

            }
        });

        mAlertDialog.show();
    }

    private void naviguateFragment(Fragment myFragment, Integer id) {
        AppCompatActivity activity = (AppCompatActivity) context;

        if (id != null) {
            Bundle bundle = new Bundle();
            bundle.putInt("id", id);
            myFragment.setArguments(bundle);
        }

        activity.getSupportFragmentManager().beginTransaction().replace(R.id.nav_host_fragment, myFragment).addToBackStack(null).commit();
    }

    public interface OnDataChangeListener {
        void onDataDeleted();
    }

    BusinessLocationAdapter.OnDataChangeListener mOnDataChangeListener;

    public void setOnDataChangeListener(BusinessLocationAdapter.OnDataChangeListener onDataChangeListener) {
        mOnDataChangeListener = onDataChangeListener;
    }


}
