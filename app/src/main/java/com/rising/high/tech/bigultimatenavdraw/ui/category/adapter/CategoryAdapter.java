package com.rising.high.tech.bigultimatenavdraw.ui.category.adapter;

import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.widget.AppCompatImageView;
import androidx.recyclerview.widget.RecyclerView;

import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.db.CategoryDbController;
import com.rising.high.tech.bigultimatenavdraw.model.Category;
import com.rising.high.tech.bigultimatenavdraw.model.Variation_template;
import com.rising.high.tech.bigultimatenavdraw.ui.variation.adapter.ListVariationAdapter;
import com.rising.high.tech.bigultimatenavdraw.util.FileUtil;
import com.rising.high.tech.bigultimatenavdraw.util.SessionManager;

import java.util.ArrayList;

import static com.rising.high.tech.bigultimatenavdraw.util.Constant.CATEGORY_DELETE;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.CATEGORY_EDIT;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.SERVER_MASTER;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.UNIT_DELETE;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.UNIT_EDIT;


public class CategoryAdapter extends RecyclerView.Adapter<CategoryAdapter.CategoryViewHolder> {

    private ArrayList<Category> dataList = new ArrayList<>();
    SessionManager session;

    Context context;

    CategoryDbController categoryDbController;

    @Override
    public CategoryViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        context = parent.getContext();
        session = new SessionManager(context);

        categoryDbController = new CategoryDbController(context);
        categoryDbController.open();
        return new CategoryViewHolder(LayoutInflater.from(parent.getContext())
                .inflate(R.layout.category_item, parent, false));
    }

    @Override
    public void onBindViewHolder(CategoryViewHolder holder, int position) {
        if (session.getBoolean(SERVER_MASTER) || !session.checkPermissionSubModule(CATEGORY_EDIT)) {
            holder.btnEdit.setVisibility(View.GONE);
        }
        if (session.getBoolean(SERVER_MASTER) || !session.checkPermissionSubModule(CATEGORY_DELETE)) {
            holder.btnDelete.setVisibility(View.GONE);
        }
        holder.name.setText(dataList.get(position).getName());
        holder.code.setText(dataList.get(position).getShort_code());
        holder.desc.setText(dataList.get(position).getDescription());

        if (position % 2 == 1) {
            holder.linearLayout.setBackgroundColor(context.getResources().getColor(R.color.white));
        } else {
            holder.linearLayout.setBackgroundColor(context.getResources().getColor(R.color.lightGrey));
        }
    }

    public void setData(ArrayList<Category> arrayList) {
        this.dataList = arrayList;
        notifyDataSetChanged();
    }


    @Override
    public int getItemCount() {
        return dataList.size();
    }

    public class CategoryViewHolder extends RecyclerView.ViewHolder {

        TextView  name, code, desc;
        AppCompatImageView btnDelete, btnEdit;
        LinearLayout linearLayout;

        public CategoryViewHolder(View itemView) {
            super(itemView);


            linearLayout = itemView.findViewById(R.id.linearLayout);
            name = itemView.findViewById(R.id.id_name);
            code = itemView.findViewById(R.id.cat_code);
            desc = itemView.findViewById(R.id.id_desc);
            btnEdit = itemView.findViewById(R.id.btn_edit);

            btnDelete = itemView.findViewById(R.id.btn_delete);

            btnDelete.setOnClickListener(v -> {
                if (onClickAction != null) {
                    onClickAction.onClickDelete(getAdapterPosition(),dataList.get(getAdapterPosition()));
                }
                notifyItemChanged(getAdapterPosition());
            });

            btnEdit.setOnClickListener(v -> {
                if (onClickAction != null) {
                onClickAction.onClickEdit(dataList.get(getAdapterPosition()));
            }
                notifyItemChanged(getAdapterPosition());
            });

        }
    }



    public interface onClickAction {
        void onClickEdit(Category category);
        void onClickDelete(int position,Category category);
    }

    onClickAction onClickAction;

    public void setonClickAction(onClickAction mOnClickAction) {
        onClickAction = mOnClickAction;
    }

}
