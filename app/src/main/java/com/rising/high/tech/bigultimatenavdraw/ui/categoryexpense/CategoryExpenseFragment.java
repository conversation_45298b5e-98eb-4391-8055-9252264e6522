package com.rising.high.tech.bigultimatenavdraw.ui.categoryexpense;

import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.db.CategoryDbController;
import com.rising.high.tech.bigultimatenavdraw.db.ExpenseCategoriesDbController;
import com.rising.high.tech.bigultimatenavdraw.model.Category;
import com.rising.high.tech.bigultimatenavdraw.model.Expense_category;
import com.rising.high.tech.bigultimatenavdraw.ui.categoryexpense.adapter.CategoryExpenseAdapter;
import com.rising.high.tech.bigultimatenavdraw.util.SessionManager;
import com.rising.high.tech.bigultimatenavdraw.util.serverInteraction;


import static com.rising.high.tech.bigultimatenavdraw.util.Constant.SERVER_MASTER;

public class CategoryExpenseFragment extends Fragment {

    private static final String TAG = "CategoryExpenseFragment";
    private Context _context;

    ExpenseCategoriesDbController expenseCategoriesDbController;
    CategoryExpenseAdapter categoryExpenseAdapter;

    RecyclerView recyclerCategoryExpense;
    Button btnAdd;
    Button btnFilter;
    EditText search_edit;

    TextView noItemFound;
    SessionManager session;

    public CategoryExpenseFragment() {
        // Required empty public constructor
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        // Inflate the layout for this fragment
        View root = inflater.inflate(R.layout.fragment_list_category_expense, container, false);
        _context = getContext();
        session = new SessionManager(_context);


        initDB();

        categoryExpenseAdapter = new CategoryExpenseAdapter();
        recyclerCategoryExpense.setAdapter(categoryExpenseAdapter);
        recyclerCategoryExpense.setLayoutManager(new LinearLayoutManager(_context));
        categoryExpenseAdapter.setData(expenseCategoriesDbController.getAllExpense_categories());

        initListners();

        checkRoles();
        setItemView();
        return root;
    }

    private void checkRoles()
    {
        if (session.getBoolean(SERVER_MASTER))
        {
            btnAdd.setVisibility(View.INVISIBLE);
        }
    }

    private void initListners(){
        btnAdd.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                addCategoryExpense();
            }
        });


        btnFilter.setOnClickListener(v -> {
            if (search_edit.getText().toString().length()!=0) {
                categoryExpenseAdapter.setData(expenseCategoriesDbController.getAllExpense_categoriesLike(search_edit.getText().toString()));
                setItemView();
            }
        });

        categoryExpenseAdapter.setOnDataChangeListener(new CategoryExpenseAdapter.OnDataChangeListener() {
            @Override
            public void onDataDeleted() {
                setItemView();
            }
        });
    }
    private void initDB(){

        expenseCategoriesDbController = new ExpenseCategoriesDbController(_context);
        expenseCategoriesDbController.open();
    }

    public void replaceFragment(Fragment someFragment) {
        FragmentTransaction transaction = getFragmentManager().beginTransaction();
        transaction.replace(R.id.nav_host_fragment, someFragment);
        transaction.addToBackStack(null);
        transaction.commit();
    }


    private void addCategoryExpense() {
        //Preparing views
        // get prompts.xml view
        LayoutInflater li = LayoutInflater.from(_context);
        View promptsView = li.inflate(R.layout.category_expense_add_main, null);


        androidx.appcompat.app.AlertDialog.Builder alertDialogBuilder = new androidx.appcompat.app.AlertDialog.Builder(
                _context);

        alertDialogBuilder.setView(promptsView);

        final EditText catgeroyCode = promptsView.findViewById(R.id.id_category_code);
        final EditText catgeroyName = promptsView.findViewById(R.id.catgeroy_name);
        final Button ButtonSave = promptsView.findViewById(R.id.btn_save);
        final ImageView ButtonClose = promptsView.findViewById(R.id.btn_close);


        final androidx.appcompat.app.AlertDialog mAlertDialog = alertDialogBuilder.create();
        if (mAlertDialog.getWindow() != null)
            mAlertDialog.getWindow().getAttributes().windowAnimations = R.style.SlidingDialogAnimation;
        mAlertDialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));

        ButtonClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mAlertDialog.dismiss();
            }
        });


        ButtonSave.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!catgeroyName.getText().toString().equals("") ) {

                    Expense_category expense_category = new Expense_category();
                    expense_category.setBusiness_id(1);
                    expense_category.setName(catgeroyName.getText().toString());
                    expense_category.setCode(catgeroyCode.getText().toString());


                    int inserted = expenseCategoriesDbController.insertLocal(expense_category);
                    if (inserted > 0) {
                        Toast.makeText(getContext(), getResources().getString(R.string.lbl_added_success), Toast.LENGTH_LONG).show();
                        categoryExpenseAdapter.setData(expenseCategoriesDbController.getAllExpense_categories());
                        categoryExpenseAdapter.notifyDataSetChanged();
                        setItemView();
                        mAlertDialog.dismiss();
                    } else {
                        Toast.makeText(getContext(), "Error while adding", Toast.LENGTH_LONG).show();
                    }
                } else {
                    Toast.makeText(getContext(), getResources().getString(R.string.label_all_field_required), Toast.LENGTH_LONG).show();
                }

            }
        });


        mAlertDialog.show();
    }

    public void setItemView()
    {
        if(categoryExpenseAdapter.getItemCount() > 0)
        {
            recyclerCategoryExpense.setVisibility(View.VISIBLE);
            noItemFound.setVisibility(View.GONE);
        }
        else
        {
            recyclerCategoryExpense.setVisibility(View.GONE);
            noItemFound.setVisibility(View.VISIBLE);
        }
    }
}