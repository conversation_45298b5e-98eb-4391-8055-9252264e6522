package com.rising.high.tech.bigultimatenavdraw.ui.categoryexpense.adapter;

import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.TextView;
import android.widget.Toast;

import androidx.recyclerview.widget.RecyclerView;

import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.db.CategoryDbController;
import com.rising.high.tech.bigultimatenavdraw.db.ExpenseCategoriesDbController;
import com.rising.high.tech.bigultimatenavdraw.model.Category;
import com.rising.high.tech.bigultimatenavdraw.model.Expense_category;
import com.rising.high.tech.bigultimatenavdraw.ui.customergroups.adapter.CustomerGroupsAdapter;

import java.util.ArrayList;


public class CategoryExpenseAdapter extends RecyclerView.Adapter<CategoryExpenseAdapter.CategoryViewHolder> {

    private static final String TAG = "CategoryExpenseAdapter";
    private ArrayList<Expense_category> dataList = new ArrayList<>();

    Context context;

    ExpenseCategoriesDbController expenseCategoriesDbController;

    @Override
    public CategoryViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        context = parent.getContext();

        expenseCategoriesDbController = new ExpenseCategoriesDbController(context);
        expenseCategoriesDbController.open();
        return new CategoryViewHolder(LayoutInflater.from(parent.getContext())
                .inflate(R.layout.category_expense_item, parent, false));
    }

    @Override
    public void onBindViewHolder(CategoryViewHolder holder, int position) {
        holder.name.setText(dataList.get(position).getName());
        holder.code.setText(dataList.get(position).getCode());
    }

    public void setData(ArrayList<Expense_category> arrayList) {
        this.dataList = arrayList;
        notifyDataSetChanged();
    }


    @Override
    public int getItemCount() {
        return dataList.size();
    }

    public class CategoryViewHolder extends RecyclerView.ViewHolder {

        TextView  name, code, desc;
        Button btnDelete, btnEdit;

        public CategoryViewHolder(View itemView) {
            super(itemView);


            name = itemView.findViewById(R.id.category_name);
            code = itemView.findViewById(R.id.category_code);
            btnEdit = itemView.findViewById(R.id.btn_edit);
            btnDelete = itemView.findViewById(R.id.btn_delete);

            btnDelete.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    deleteItem(getAdapterPosition());
                }
            });

            btnEdit.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    editCategory(getAdapterPosition());
                }
            });

        }
    }

    private void editCategory(int position) {
        //Preparing views
        // get prompts.xml view
        LayoutInflater li = LayoutInflater.from(context);
        View promptsView = li.inflate(R.layout.category_expense_add_main, null);


        androidx.appcompat.app.AlertDialog.Builder alertDialogBuilder = new androidx.appcompat.app.AlertDialog.Builder(
                context);

        alertDialogBuilder.setView(promptsView);

        final EditText catgeroyCode = promptsView.findViewById(R.id.id_category_code);
        final EditText catgeroyName = promptsView.findViewById(R.id.catgeroy_name);
        final Button ButtonSave = promptsView.findViewById(R.id.btn_save);
        final Button ButtonClose = promptsView.findViewById(R.id.btn_close);

        ButtonSave.setText(context.getResources().getString(R.string.label_updatee));

        catgeroyName.setText(dataList.get(position).getName());
        catgeroyCode.setText(dataList.get(position).getCode());

        final androidx.appcompat.app.AlertDialog mAlertDialog = alertDialogBuilder.create();
        if (mAlertDialog.getWindow() != null)
            mAlertDialog.getWindow().getAttributes().windowAnimations = R.style.SlidingDialogAnimation;
        mAlertDialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));

        ButtonClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mAlertDialog.dismiss();
            }
        });


        ButtonSave.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!catgeroyName.getText().toString().equals("") ) {

                    Expense_category category = dataList.get(position);
                    category.setBusiness_id(1);
                    category.setName(catgeroyName.getText().toString());
                    category.setCode(catgeroyCode.getText().toString());

                    int inserted = expenseCategoriesDbController.editExpenseCategory(category);
                    if (inserted > 0) {
                        Toast.makeText(context, context.getResources().getString(R.string.lbl_added_success), Toast.LENGTH_LONG).show();

                        notifyItemChanged(position);
                        mAlertDialog.dismiss();
                    } else {
                        Toast.makeText(context, "Error while adding", Toast.LENGTH_LONG).show();
                    }
                } else {
                    Toast.makeText(context, context.getResources().getString(R.string.label_all_field_required), Toast.LENGTH_LONG).show();
                }

            }
        });


        mAlertDialog.show();
    }


    private void deleteItem(int postition) {
        //Preparing views
        // get prompts.xml view
        LayoutInflater li = LayoutInflater.from(context);
        View promptsView = li.inflate(R.layout.delete_dialog_main, null);

        androidx.appcompat.app.AlertDialog.Builder alertDialogBuilder = new androidx.appcompat.app.AlertDialog.Builder(
                context);

        alertDialogBuilder.setView(promptsView);

        final Button ButtonSave = promptsView.findViewById(R.id.btn_save);
        final Button ButtonClose = promptsView.findViewById(R.id.btn_close);

        final androidx.appcompat.app.AlertDialog mAlertDialog = alertDialogBuilder.create();
        if (mAlertDialog.getWindow() != null)
            mAlertDialog.getWindow().getAttributes().windowAnimations = R.style.SlidingDialogAnimation;
        mAlertDialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));

        ButtonClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mAlertDialog.dismiss();
            }
        });

        ButtonSave.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

                expenseCategoriesDbController.deleteItem(dataList.get(postition).getId());
                dataList.remove(postition);
                mOnDataChangeListener.onDataDeleted();

                mAlertDialog.dismiss();
                notifyDataSetChanged();

            }
        });


        mAlertDialog.show();
    }

    public interface OnDataChangeListener {
        void onDataDeleted();
    }

    CategoryExpenseAdapter.OnDataChangeListener mOnDataChangeListener;

    public void setOnDataChangeListener(CategoryExpenseAdapter.OnDataChangeListener onDataChangeListener) {
        mOnDataChangeListener = onDataChangeListener;
    }
}
