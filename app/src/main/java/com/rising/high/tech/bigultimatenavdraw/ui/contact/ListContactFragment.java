package com.rising.high.tech.bigultimatenavdraw.ui.contact;

import android.content.Context;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.inputmethod.InputMethodManager;
import android.widget.Button;
import android.widget.EditText;
import android.widget.Spinner;
import android.widget.TextView;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.gson.Gson;
import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.db.ContactDbController;
import com.rising.high.tech.bigultimatenavdraw.ui.contact.adapter.CustomerAdapter;
import com.rising.high.tech.bigultimatenavdraw.util.SessionManager;
import com.rising.high.tech.bigultimatenavdraw.util.serverInteraction;


import static com.rising.high.tech.bigultimatenavdraw.util.Constant.CONTACT_ADD;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.CONTACT_VIEW;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.SERVER_MASTER;

public class ListContactFragment extends Fragment {

    private static final String TAG = "ListContactFragment";

    private Context _context;
    RecyclerView recycleCustomer;
    EditText searchEdit;
    Button btnAdd, btnSearch;
    Spinner spinnerType;
    TextView noItemFound;

    ContactDbController contactDbController;
    CustomerAdapter customerAdapter;
    SessionManager session;

    public ListContactFragment() {
        // Required empty public constructor
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        // Inflate the layout for this fragment
        View root = inflater.inflate(R.layout.fragment_contact_list_main, container, false);

        _context = getContext();
        session = new SessionManager(_context);
        noItemFound = root.findViewById(R.id.noItemFound);
        recycleCustomer = root.findViewById(R.id.recycle_customers);
        searchEdit = root.findViewById(R.id.id_search_edit);
        spinnerType = root.findViewById(R.id.spinner_type);
        btnSearch = root.findViewById(R.id.id_search);
        recycleCustomer = root.findViewById(R.id.recycle_customers);
        btnAdd = root.findViewById(R.id.id_add);
        contactDbController = new ContactDbController(_context);
        contactDbController.open();
        customerAdapter = new CustomerAdapter();
        recycleCustomer.setAdapter(customerAdapter);
        recycleCustomer.setLayoutManager(new LinearLayoutManager(_context));
        customerAdapter.setData(contactDbController.getAllContact());

        setItemView();
        initListners();
        setItemView();
        checkRoles();

        return root;
    }

    private void checkRoles() {
        if (session.getBoolean(SERVER_MASTER) || !session.checkPermissionSubModule(CONTACT_ADD)) {
            btnAdd.setVisibility(View.INVISIBLE);
        }
    }

    private void initListners() {
           /* searchEdit.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                if (s.length() != 0) {
                    Log.d(TAG, "edit text " + new Gson().toJson(contactDbController.getCustomerLike(s.toString())));
                    customerAdapter.setData(contactDbController.getCustomerLike(s.toString()));
                    customerAdapter.notifyDataSetChanged();
                } else {
                    customerAdapter.setData(contactDbController.getAllContact());
                    customerAdapter.notifyDataSetChanged();

                }
            }

            @Override
            public void afterTextChanged(Editable s) {

            }
        });*/

        btnAdd.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                replaceFragment(new AddContactFragment());
            }
        });

        btnSearch.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

                customerAdapter.setData(contactDbController.getCustomerType(searchEdit.getText().toString(), spinnerType.getSelectedItemPosition()));
                customerAdapter.notifyDataSetChanged();
                setItemView();

                final InputMethodManager imm = (InputMethodManager) getActivity().getSystemService(Context.INPUT_METHOD_SERVICE);
                imm.hideSoftInputFromWindow(getView().getWindowToken(), 0);

            }
        });
    }

    public void replaceFragment(Fragment someFragment) {
        FragmentTransaction transaction = getFragmentManager().beginTransaction();
        transaction.replace(R.id.nav_host_fragment, someFragment);
        transaction.addToBackStack(null);
        transaction.commit();
    }

    public void setItemView() {
        if (customerAdapter.getItemCount() > 0) {
            recycleCustomer.setVisibility(View.VISIBLE);
            noItemFound.setVisibility(View.GONE);
        } else {
            recycleCustomer.setVisibility(View.GONE);
            noItemFound.setVisibility(View.VISIBLE);
        }
    }
}