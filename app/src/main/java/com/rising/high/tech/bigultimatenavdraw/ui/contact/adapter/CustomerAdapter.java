package com.rising.high.tech.bigultimatenavdraw.ui.contact.adapter;

import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.RecyclerView;


import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.db.ContactDbController;
import com.rising.high.tech.bigultimatenavdraw.db.TransactionDbController;
import com.rising.high.tech.bigultimatenavdraw.db.TransactionPayementDbController;
import com.rising.high.tech.bigultimatenavdraw.model.Contact;
import com.rising.high.tech.bigultimatenavdraw.model.Transaction;
import com.rising.high.tech.bigultimatenavdraw.ui.contact.EditContactFragment;
import com.rising.high.tech.bigultimatenavdraw.ui.contact.staps.CollectionFragment;
import com.rising.high.tech.bigultimatenavdraw.util.SessionManager;
import com.rising.high.tech.bigultimatenavdraw.util.StringFormat;

import java.util.ArrayList;
import java.util.Calendar;

import static android.net.wifi.SupplicantState.INACTIVE;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.ACTIVE;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.BOTH;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.CONTACT_DELETE;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.CONTACT_EDIT;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.CUSTOMER;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.FINAL;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.SERVER_MASTER;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.SUPPLIER;


public class CustomerAdapter extends RecyclerView.Adapter<CustomerAdapter.CustomerViewHolder> {

    private static final String TAG = "CustomerAdapter";
    private ArrayList<Contact> dataList = new ArrayList<>();
    private ContactDbController contactDbController;
    private TransactionPayementDbController transactionPayementDbController;
    Context context;
    private SessionManager session;
    final Calendar c = Calendar.getInstance();
    TransactionDbController transactionDbController;

    @Override
    public CustomerViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        context = parent.getContext();
        session = new SessionManager(context);
        transactionDbController = new TransactionDbController(context);

        contactDbController = new ContactDbController(context);
        transactionPayementDbController = new TransactionPayementDbController(context);
        return new CustomerViewHolder(LayoutInflater.from(parent.getContext())
                .inflate(R.layout.contact_item, parent, false));
    }

    @Override
    public void onBindViewHolder(CustomerViewHolder holder, int position) {

        //   holder.contact_id.setText(dataList.get(position).getContact_id());
        holder.customer_name.setText(dataList.get(position).getName());
        holder.customer_email.setText(dataList.get(position).getEmail());
        holder.cutomer_tel.setText(dataList.get(position).getMobile());

        if (dataList.get(position).getType().matches(BOTH)){
        holder.cutomer_type.setText(CUSTOMER+","+ SUPPLIER);}
        else {
            holder.cutomer_type.setText(dataList.get(position).getType());
        }

        holder.added_on.setText(dataList.get(position).getCreated_at());
        holder.customer_status.setText(dataList.get(position).getContact_status());
        holder.pay_term.setText(dataList.get(position).getPay_term_number() + " " + (dataList.get(position).getPay_term_type() != null ? dataList.get(position).getPay_term_type() : ""));

        ArrayList<String> spinnerArray = new ArrayList<>();
        String[] list = context.getResources().getStringArray(R.array.array_action_contact_supplier);
        spinnerArray.add(list[0]);
        spinnerArray.add(list[1]);
        spinnerArray.add(list[2]);
        spinnerArray.add(list[3]);
        spinnerArray.add(list[4]);
        if (dataList.get(position).getType().matches(CUSTOMER)) {
            if (dataList.get(position).getContact_status().matches(ACTIVE)){
                spinnerArray.add("Desactivate");
            }else {
                spinnerArray.add("Activate");
            }
        }

        ArrayAdapter<String> spinnerArrayAdapter = new ArrayAdapter<String>
                (context, android.R.layout.simple_spinner_item,
                        spinnerArray); //selected item will look like a spinner set from XML
        spinnerArrayAdapter.setDropDownViewResource(android.R.layout
                .simple_spinner_dropdown_item);
        holder.spinnerAction.setAdapter(spinnerArrayAdapter);


        Transaction transactionBalance = transactionDbController.getOpeningBalanceContact(dataList.get(position).getId());
        holder.opening_blance.setText(transactionBalance!=null? transactionBalance.getFinal_total(): "00");


        if (position % 2 == 1) {
            holder.linearLayout.setBackgroundColor(context.getResources().getColor(R.color.white));
        } else {
            holder.linearLayout.setBackgroundColor(context.getResources().getColor(R.color.lightGrey));
        }
    }

    public void setData(ArrayList<Contact> arrayList) {
        this.dataList = arrayList;
        notifyDataSetChanged();
    }

    @Override
    public int getItemCount() {
        return dataList.size();
    }

    public class CustomerViewHolder extends RecyclerView.ViewHolder {

        TextView customer_status, contact_id, customer_name, customer_email, cutomer_tel, total_purchase_due_return, cutomer_type, added_on, total_purchaee_due, pay_term, opening_blance, advance_balance;
        Spinner spinnerAction;
        LinearLayout linearLayout;

        public CustomerViewHolder(View itemView) {
            super(itemView);

            linearLayout = itemView.findViewById(R.id.linearLayout);
            //  contact_id = itemView.findViewById(R.id.contact_id);
            customer_name = itemView.findViewById(R.id.customer_name);
            customer_email = itemView.findViewById(R.id.customer_email);
            cutomer_tel = itemView.findViewById(R.id.cutomer_tel);
            cutomer_type = itemView.findViewById(R.id.cutomer_type);
            spinnerAction = itemView.findViewById(R.id.spinner_action);
            added_on = itemView.findViewById(R.id.added_on);
            total_purchaee_due = itemView.findViewById(R.id.total_purchaee_due);
            total_purchase_due_return = itemView.findViewById(R.id.total_purchase_due_return);
            pay_term = itemView.findViewById(R.id.pay_term);
            opening_blance = itemView.findViewById(R.id.opening_blance);
            advance_balance = itemView.findViewById(R.id.advance_balance);
            customer_status = itemView.findViewById(R.id.customer_status);

            spinnerAction.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
                @Override
                public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                    spinnerAction.setSelection(0, true);
                    switch (position) {
                        case 1: {
                            pay(getAdapterPosition());
                            break;
                        }
                        case 2: {
                            naviguateFragment(new CollectionFragment(), dataList.get(getAdapterPosition()).getId());
                            break;
                        }
                        case 3: {
                            if (session.getBoolean(SERVER_MASTER) || !session.checkPermissionSubModule(CONTACT_EDIT)) {
                                Toast.makeText(context, context.getResources().getString(R.string.label_not_autorized), Toast.LENGTH_LONG).show();
                            } else {
                                naviguateFragment(new EditContactFragment(), dataList.get(getAdapterPosition()).getId());
                            }
                            break;
                        }
                        case 4: {
                            if (dataList.get(getAdapterPosition()).getId() == 1) {
                                Toast.makeText(context, context.getResources().getString(R.string.label_not_autorized), Toast.LENGTH_LONG).show();
                            } else if (session.getBoolean(SERVER_MASTER) || !session.checkPermissionSubModule(CONTACT_DELETE)) {
                                Toast.makeText(context, context.getResources().getString(R.string.label_not_autorized), Toast.LENGTH_LONG).show();
                            } else {
                                deleteItem(getAdapterPosition());
                            }
                            break;
                        }
                        case 5: {
                            if (dataList.get(getAdapterPosition()).getId() == 1) {
                                Toast.makeText(context, context.getResources().getString(R.string.label_not_autorized), Toast.LENGTH_LONG).show();
                            } else {
                                activateDesactivate(getAdapterPosition());
                            }
                            break;

                        }
                    }
                }

                @Override
                public void onNothingSelected(AdapterView<?> parent) {

                }
            });
        }
    }

    private void pay(Integer position) {
        //Preparing views
        // get prompts.xml view
        LayoutInflater li = LayoutInflater.from(context);
        View promptsView = li.inflate(R.layout.pay_cutomer_dialog_main, null);

        androidx.appcompat.app.AlertDialog.Builder alertDialogBuilder = new androidx.appcompat.app.AlertDialog.Builder(
                context);

        alertDialogBuilder.setView(promptsView);

        final Button ButtonSave = promptsView.findViewById(R.id.btn_save);
        final Button ButtonClose = promptsView.findViewById(R.id.btn_close);
        final TextView customer_name = promptsView.findViewById(R.id.customer_name);
        final TextView amount = promptsView.findViewById(R.id.amount);
        final TextView total_paid = promptsView.findViewById(R.id.total_paid);
        final TextView payement_note = promptsView.findViewById(R.id.payement_note);
        final TextView paid_on = promptsView.findViewById(R.id.paid_on);
        final Spinner spinner_type = promptsView.findViewById(R.id.spinner_type);
        paid_on.setText(StringFormat.populateSetDate(c.get(Calendar.YEAR), c.get(Calendar.MONTH), c.get(Calendar.DAY_OF_MONTH)));
        final androidx.appcompat.app.AlertDialog mAlertDialog = alertDialogBuilder.create();
        if (mAlertDialog.getWindow() != null)
            mAlertDialog.getWindow().getAttributes().windowAnimations = R.style.SlidingDialogAnimation;
        mAlertDialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));

        customer_name.setText(dataList.get(position).getName());

        float total_paid_m = transactionDbController.getTotalPaidByCustomerId(dataList.get(position).getId());
        total_paid.setText(total_paid_m+"" + session.getUserDetails().get(session.KEY_SYMBOL));

        Transaction transactionBalance = transactionDbController.getOpeningBalanceContact(dataList.get(position).getId());
        amount.setText(transactionBalance!=null? transactionBalance.getFinal_total(): "00");

        ButtonClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mAlertDialog.dismiss();
            }
        });

        ButtonSave.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Transaction transactionPayement = new Transaction();
                if (!amount.getText().toString().matches("")){

                    transactionPayement.setAmount(Float.parseFloat(amount.getText().toString()));
                    transactionPayement.setMethod(spinner_type.getSelectedItem().toString());
                    transactionPayement.setPayment_for(dataList.get(position).getId());
                    transactionPayement.setNote(payement_note.getText().toString());
                    transactionPayement.setPaid_on(paid_on.getText().toString());

                    transactionPayementDbController.insertLocal(transactionPayement);
                    Toast.makeText(context, "Added with success" , Toast.LENGTH_SHORT).show();

                    mAlertDialog.dismiss();
                    notifyDataSetChanged();
                }else {
                    Toast.makeText(context, "Add amount" , Toast.LENGTH_SHORT).show();
                }

            }
        });
        mAlertDialog.show();
    }

    private void deleteItem(int position) {
        //Preparing views
        // get prompts.xml view
        LayoutInflater li = LayoutInflater.from(context);
        View promptsView = li.inflate(R.layout.delete_dialog_main, null);

        androidx.appcompat.app.AlertDialog.Builder alertDialogBuilder = new androidx.appcompat.app.AlertDialog.Builder(
                context);

        alertDialogBuilder.setView(promptsView);

        final Button ButtonSave = promptsView.findViewById(R.id.btn_save);
        final Button ButtonClose = promptsView.findViewById(R.id.btn_close);

        final androidx.appcompat.app.AlertDialog mAlertDialog = alertDialogBuilder.create();
        if (mAlertDialog.getWindow() != null)
            mAlertDialog.getWindow().getAttributes().windowAnimations = R.style.SlidingDialogAnimation;
        mAlertDialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));

        ButtonClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mAlertDialog.dismiss();
            }
        });

        ButtonSave.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

                //productDbController.deleteProduct(dataList.get(postition).getId());
                contactDbController.deleteCustomer(dataList.get(position).getId());
                dataList.remove(position);
                mAlertDialog.dismiss();
                notifyDataSetChanged();
            }
        });


        mAlertDialog.show();
    }

    private void activateDesactivate(int position) {
        //Preparing views
        // get prompts.xml view
        LayoutInflater li = LayoutInflater.from(context);
        View promptsView = li.inflate(R.layout.activate_contact_dialog_main, null);

        androidx.appcompat.app.AlertDialog.Builder alertDialogBuilder = new androidx.appcompat.app.AlertDialog.Builder(
                context);

        alertDialogBuilder.setView(promptsView);

        final Button ButtonSave = promptsView.findViewById(R.id.btn_save);
        final Button ButtonClose = promptsView.findViewById(R.id.btn_close);
        final TextView text_desactivate = promptsView.findViewById(R.id.text_desactivate);
        if (dataList.get(position).getContact_status().matches(ACTIVE)) {
            text_desactivate.setText(context.getResources().getString(R.string.label_desactivatee));
            //   text_activate.setVisibility(View.GONE);

        } else {
            text_desactivate.setText(context.getResources().getString(R.string.label_activatee));

        }
        final androidx.appcompat.app.AlertDialog mAlertDialog = alertDialogBuilder.create();
        if (mAlertDialog.getWindow() != null)
            mAlertDialog.getWindow().getAttributes().windowAnimations = R.style.SlidingDialogAnimation;
        mAlertDialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));


        ButtonClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mAlertDialog.dismiss();
            }
        });

        ButtonSave.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

                //productDbController.deleteProduct(dataList.get(postition).getId());
                dataList.get(position).setContact_status(dataList.get(position).getContact_status().matches("active") ? "inactive" : "active");
                contactDbController.editContact(dataList.get(position));
                notifyDataSetChanged();
                mAlertDialog.dismiss();
            }
        });


        mAlertDialog.show();
    }


    private void naviguateFragment(Fragment myFragment, Integer id) {
        AppCompatActivity activity = (AppCompatActivity) context;

        if (id != null) {
            Bundle bundle = new Bundle();
            bundle.putInt("id", id);
            myFragment.setArguments(bundle);
        }

        activity.getSupportFragmentManager().beginTransaction().replace(R.id.nav_host_fragment, myFragment).addToBackStack(null).commit();
    }

}
