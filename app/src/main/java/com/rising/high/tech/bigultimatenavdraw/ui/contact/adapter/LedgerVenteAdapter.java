package com.rising.high.tech.bigultimatenavdraw.ui.contact.adapter;

import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.widget.AppCompatImageView;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.gson.Gson;
import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.db.BusinessLocationDbController;
import com.rising.high.tech.bigultimatenavdraw.db.ContactDbController;
import com.rising.high.tech.bigultimatenavdraw.db.TransactionDbController;
import com.rising.high.tech.bigultimatenavdraw.db.TransactionPayementDbController;
import com.rising.high.tech.bigultimatenavdraw.model.Business_location;
import com.rising.high.tech.bigultimatenavdraw.model.Contact;
import com.rising.high.tech.bigultimatenavdraw.model.Transaction;
import com.rising.high.tech.bigultimatenavdraw.ui.purchase.adapter.SubPaymentAdapter;
import com.rising.high.tech.bigultimatenavdraw.util.SessionManager;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;

import static com.rising.high.tech.bigultimatenavdraw.util.Constant.PAID;

public class LedgerVenteAdapter extends RecyclerView.Adapter<LedgerVenteAdapter.VenteViewHolder> {

    private final String TAG = this.getClass().getSimpleName();
    private ArrayList<Transaction> dataList = new ArrayList<>();
    private BusinessLocationDbController businessLocationDbController;
    private ContactDbController contactDbController;
    private TransactionDbController transactionDbController;
    private TransactionPayementDbController transactionPayementDbController;
    private SessionManager session;
    private HashMap<String, Object> user;
    Context context;
    private SubPaymentAdapter subPaymentAdapter;

    @Override
    public VenteViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        context = parent.getContext();

        session = new SessionManager(context);
        user = session.getUserDetails();

        businessLocationDbController = new BusinessLocationDbController(context);
        businessLocationDbController.open();

        contactDbController = new ContactDbController(context);
        contactDbController.open();

        transactionPayementDbController = new TransactionPayementDbController(context);
        transactionPayementDbController.open();

        transactionDbController = new TransactionDbController(context);
        transactionDbController.open();
        subPaymentAdapter = new SubPaymentAdapter();

        return new VenteViewHolder(LayoutInflater.from(parent.getContext())
                .inflate(R.layout.leadger_vente_item_layout, parent, false));

    }

    @Override
    public void onBindViewHolder(VenteViewHolder holder, int position) {
        holder.dateTxt.setText(dataList.get(position).getTransaction_date());
        holder.type.setText(dataList.get(position).getType());
        String stationName = businessLocationDbController.getStationById(dataList.get(position).getLocation_id()).getName();
        holder.locationTxt.setText(stationName);

        Log.d(TAG, "////payment status " + dataList.get(position).getPayment_status());
        holder.payementStatus.setText(dataList.get(position).getPayment_status());
        if (dataList.get(position).getPayment_status() !=null && dataList.get(position).getPayment_status().matches(PAID)){
            holder.payementStatus.setBackground(context.getResources().getDrawable(R.drawable.rounded_green_bg));}

        if (dataList.get(position).getMethod()!=null)
        {
            holder.payementStatus.setVisibility(View.INVISIBLE);
        }

        holder.montantTotal.setText(dataList.get(position).getFinal_total() + user.get(session.KEY_SYMBOL));

     //   holder.customerName.setText(contactDbController.getCustomerById(dataList.get(position).getContact_id()).getName());
        holder.payementMethod.setText(dataList.get(position).getMethod());
        //holder.payementMethod.setText();

    }

    public void setData(ArrayList<Transaction> arrayList) {
        Collections.reverse(arrayList);
        this.dataList = arrayList;
        notifyDataSetChanged();
    }

    @Override
    public int getItemCount() {
        return dataList.size();
    }

    public class VenteViewHolder extends RecyclerView.ViewHolder {
        TextView dateTxt, type, locationTxt, payementStatus, montantTotal, customerName, payementMethod;
        RecyclerView recycle_sub_vente;
        LinearLayout sub_item, container;

        Button btnDetail;
        Spinner spinnerAction;

        public VenteViewHolder(View itemView) {
            super(itemView);

            dateTxt = itemView.findViewById(R.id.id_date);
            type = itemView.findViewById(R.id.type);
            locationTxt = itemView.findViewById(R.id.location_txt);
            payementStatus = itemView.findViewById(R.id.payement_status);
            montantTotal = itemView.findViewById(R.id.montant_total);
            container = itemView.findViewById(R.id.container);
            btnDetail = itemView.findViewById(R.id.btn_detail);
            customerName = itemView.findViewById(R.id.customer_name);
            payementMethod = itemView.findViewById(R.id.payement_method);

            spinnerAction = itemView.findViewById(R.id.spinner_action);

            spinnerAction.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
                @Override
                public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                    switch (position) {
                        case 1: {
                            // dataList.get(getAdapterPosition()).setExpanded(!dataList.get(getAdapterPosition()).isExpanded());
                            if (mOnDataChangeListener != null) {
                                mOnDataChangeListener.onDataChanged(dataList.get(getAdapterPosition()));
                            }
                            notifyItemChanged(getAdapterPosition());
                            spinnerAction.setSelection(0, true);
                            break;
                        }
                        case 2: {
                            if (!dataList.get(position).getPayment_status().matches(PAID))
                            {
                                deleteItem(getAdapterPosition());
                                spinnerAction.setSelection(0, true);
                            }
                            else {
                                Toast.makeText(context, context.getResources().getString(R.string.label_cannot_delete_transaction), Toast.LENGTH_LONG).show();
                            }
                            break;
                        }
                        case 3: {
                            viewPayement(getAdapterPosition());
                            spinnerAction.setSelection(0, true);
                            break;
                        }
                        case 4: {
                            mOnDataChangeListener.onSellReturn(dataList.get(getAdapterPosition()));
                            spinnerAction.setSelection(0, true);
                            break;
                        }
                    }
                }

                @Override
                public void onNothingSelected(AdapterView<?> parent) {

                }
            });
        }
    }

    private void viewPayement(int position) {
        //Preparing views
        // get prompts.xml view

        LayoutInflater li = LayoutInflater.from(context);
        View promptsView = li.inflate(R.layout.view_payement_purchase_dialog_main, null);

        androidx.appcompat.app.AlertDialog.Builder alertDialogBuilder = new androidx.appcompat.app.AlertDialog.Builder(
                context);

        alertDialogBuilder.setView(promptsView);

        final Button ButtonSave = promptsView.findViewById(R.id.btn_save);
        final AppCompatImageView ButtonClose = promptsView.findViewById(R.id.btn_close);

        final TextView supplier_name = promptsView.findViewById(R.id.supplier_name);
        final TextView supplier_phone = promptsView.findViewById(R.id.supplier_phone);
        final TextView business_location = promptsView.findViewById(R.id.business_location);
        final RecyclerView recyclerView = promptsView.findViewById(R.id.recycle_sub_vente);
        final TextView label_reference_num = promptsView.findViewById(R.id.label_reference_num);
        final TextView purchase_date = promptsView.findViewById(R.id.purchase_date);
        final TextView purchase_status = promptsView.findViewById(R.id.purchase_status);
        final TextView payement_status = promptsView.findViewById(R.id.payement_status);

        recyclerView.setAdapter(subPaymentAdapter);
        recyclerView.setLayoutManager(new LinearLayoutManager(context));

        ArrayList<Transaction> purchaseLines = transactionPayementDbController.getAllTransactionById(dataList.get(position).getId());
        subPaymentAdapter.setData(purchaseLines);
        subPaymentAdapter.setOnDataChangeListener(new SubPaymentAdapter.OnDataChangeListener() {
            @Override
            public void onDataChanged() {
                Log.d(TAG, " transaction this " + transactionDbController.getTransactionById(dataList.get(position).getId()).getPayment_status());
                dataList.get(position).setPayment_status(transactionDbController.getTransactionById(dataList.get(position).getId()).getPayment_status());
                notifyItemChanged(position);
            }
        });

        Contact contact = contactDbController.getCustomerById(dataList.get(position).getContact_id());

        supplier_name.setText(contact.getName());
        supplier_phone.setText(contact.getMobile());
        Business_location businesslocation = businessLocationDbController.getStationById(dataList.get(position).getLocation_id());

        business_location.setText(businesslocation.getName() + " " + businesslocation.getCity() + " " + businesslocation.getCountry());
        label_reference_num.setText(dataList.get(position).getRef_no());
        purchase_date.setText(dataList.get(position).getTransaction_date());
        purchase_status.setText(dataList.get(position).getStatus());
        payement_status.setText(dataList.get(position).getPayment_status());

        //  final ImageView image_product = promptsView.findViewById(R.id.image_product);

        final androidx.appcompat.app.AlertDialog mAlertDialog = alertDialogBuilder.create();
        if (mAlertDialog.getWindow() != null)
            mAlertDialog.getWindow().getAttributes().windowAnimations = R.style.SlidingDialogAnimation;
        mAlertDialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));


        ButtonClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mAlertDialog.dismiss();
            }
        });


        mAlertDialog.show();
    }

    private void deleteItem(int position) {
        //Preparing views
        // get prompts.xml view
        LayoutInflater li = LayoutInflater.from(context);
        View promptsView = li.inflate(R.layout.delete_dialog_main, null);

        androidx.appcompat.app.AlertDialog.Builder alertDialogBuilder = new androidx.appcompat.app.AlertDialog.Builder(
                context);

        alertDialogBuilder.setView(promptsView);

        final Button ButtonSave = promptsView.findViewById(R.id.btn_save);
        final Button ButtonClose = promptsView.findViewById(R.id.btn_close);

        final androidx.appcompat.app.AlertDialog mAlertDialog = alertDialogBuilder.create();
        if (mAlertDialog.getWindow() != null)
            mAlertDialog.getWindow().getAttributes().windowAnimations = R.style.SlidingDialogAnimation;
        mAlertDialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));


        ButtonClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mAlertDialog.dismiss();
            }
        });

        ButtonSave.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

                transactionDbController.deleteTransaction(dataList.get(position).getId());
                transactionPayementDbController.deletePayment(dataList.get(position).getId());
//                for (Purchase_line purchaseLine : purchaseLineDbController.getPurchaseLineByTransaction(dataList.get(position).getId())) {
//                    purchaseLineDbController.deletePurchase(purchaseLine.getId());
//                }
                dataList.remove(position);
                if (mOnDataChangeListener != null && dataList.size() > 0) {
                    mOnDataChangeListener.onDataChanged(dataList.get(position));
                } else {
                    mOnDataChangeListener.onDataChanged(null);
                }
                mAlertDialog.dismiss();
                notifyDataSetChanged();
            }
        });

        mAlertDialog.show();
    }


    public interface OnDataChangeListener {
        void onDataChanged(Transaction transaction);
        void onSellReturn(Transaction transaction);
    }

    OnDataChangeListener mOnDataChangeListener;

    public void setOnDataChangeListener(OnDataChangeListener onDataChangeListener) {
        mOnDataChangeListener = onDataChangeListener;
    }

}
