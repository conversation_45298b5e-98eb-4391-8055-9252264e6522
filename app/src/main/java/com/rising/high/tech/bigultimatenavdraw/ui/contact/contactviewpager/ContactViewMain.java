package com.rising.high.tech.bigultimatenavdraw.ui.contact.contactviewpager;

import androidx.appcompat.app.AppCompatActivity;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentPagerAdapter;
import androidx.fragment.app.FragmentTransaction;
import androidx.viewpager.widget.ViewPager;

import android.content.Context;
import android.content.res.Resources;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.TextView;

import com.google.android.material.tabs.TabLayout;
import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.db.ContactDbController;
import com.rising.high.tech.bigultimatenavdraw.model.Contact;
import com.rising.high.tech.bigultimatenavdraw.ui.contact.ListContactFragment;
import com.rising.high.tech.bigultimatenavdraw.ui.contact.viewcontact.DocumentsNoteMain;
import com.rising.high.tech.bigultimatenavdraw.ui.contact.viewcontact.LedgerMain;
import com.rising.high.tech.bigultimatenavdraw.ui.contact.viewcontact.PayementMain;
import com.rising.high.tech.bigultimatenavdraw.ui.contact.viewcontact.SaleMain;

import java.util.ArrayList;
import java.util.List;

public class ContactViewMain extends Fragment {
    private static final String TAG = "ContactViewMain";
    TabLayout MyTabs;
    ViewPager MyPage;
    int[] tabIcons = {R.drawable.ic_ledger, R.drawable.ic_low_price,
            R.drawable.ic_payment_history, R.drawable.ic_baseline_attach_file_24, R.drawable.ic_baseline_remove_red_eye_24,
            R.drawable.ic_baseline_remove_red_eye_24, R.drawable.ic_baseline_remove_red_eye_24};
    Resources resources;
    private Context _context;
    private int indexId = 0;
    TextView contactName, customerType, customerMobile, customerAdress;
    private Button backBtn;
    private ContactDbController contactDbController;

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        View PageTwo = inflater.inflate(R.layout.activity_main3, container, false);
        resources = getResources();
        _context = getContext();

        //declare views
        MyTabs = PageTwo.findViewById(R.id.MyTabs);
        MyPage = PageTwo.findViewById(R.id.MyPage);

        contactName = PageTwo.findViewById(R.id.contact_name);
        customerType = PageTwo.findViewById(R.id.customer_type);
        customerMobile = PageTwo.findViewById(R.id.customer_mobile);
        customerAdress = PageTwo.findViewById(R.id.customer_adress);
        backBtn = PageTwo.findViewById(R.id.id_back);

        contactDbController = new ContactDbController(_context);
        contactDbController.open();
        Bundle args = getArguments();
        indexId = args.getInt("id", 0);

        Contact contact = contactDbController.getCustomerById(indexId);
        // Permet d'afficher le bouton de navigation up sur l'application
        contactName.setText(contact.getName());
        customerType.setText(contact.getType());
        customerMobile.setText(contact.getMobile());
        customerAdress.setText(contact.getAddress_line_1());

        backBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                replaceFragment(new ListContactFragment());
            }
        });
        // Permet d'afficher le bouton de navigation up sur l'application


        return PageTwo;
    }

    @Override
    public void onStart() {
        super.onStart();
        initTabLayout();

    }


    public void SetUpViewPager(ViewPager viewpage) {

        MyViewPageAdapter Adapter = new MyViewPageAdapter(((AppCompatActivity) _context).getSupportFragmentManager());
//        Adapter.AddFragmentPage(new DSummary(), resources.getString(R.string.label_all_field_required));
//        Adapter.AddFragmentPage(new DPumpsStatus(), resources.getString(R.string.lbl_action_edit));


        Adapter.AddFragmentPage(new LedgerMain(indexId), "Ledger");
        Adapter.AddFragmentPage(new SaleMain(indexId), "Sales");
        Adapter.AddFragmentPage(new DocumentsNoteMain(indexId), "Documents & Note");
        Adapter.AddFragmentPage(new PayementMain(indexId), "Payement");

        /*
        You can add more Fragment Adapter
        But the minimum of the ViewPager is 3 index Page
         */
        //We Need Fragment class now
        viewpage.setAdapter(Adapter);
    }

    private void initTabLayout() {
        MyTabs.setupWithViewPager(MyPage);
        SetUpViewPager(MyPage);

        MyTabs.getTabAt(0).setIcon(tabIcons[0]).getIcon();
        MyTabs.getTabAt(1).setIcon(tabIcons[1]).getIcon();
        MyTabs.getTabAt(2).setIcon(tabIcons[3]).getIcon();
        MyTabs.getTabAt(3).setIcon(tabIcons[2]).getIcon();
    }

    //Custom Adapter Here
    public class MyViewPageAdapter extends FragmentPagerAdapter {
        private List<Fragment> MyFragment = new ArrayList<>();
        private List<String> MyPageTittle = new ArrayList<>();

        public MyViewPageAdapter(FragmentManager manager) {
            super(manager);
        }

        public void AddFragmentPage(Fragment Frag, String Title) {
            MyFragment.add(Frag);
            MyPageTittle.add(Title);
        }

        @Override
        public Fragment getItem(int position) {
            return MyFragment.get(position);
        }

        @Override
        public CharSequence getPageTitle(int position) {
            return MyPageTittle.get(position);
        }

        @Override
        public int getCount() {
            return MyFragment.size();
        }
    }


    public void replaceFragment(Fragment someFragment) {
        FragmentTransaction transaction = getFragmentManager().beginTransaction();
        transaction.replace(R.id.nav_host_fragment, someFragment);
        transaction.addToBackStack(null);
        transaction.remove(ContactViewMain.this);
        transaction.commit();
    }

}