package com.rising.high.tech.bigultimatenavdraw.ui.contact.staps;

import android.os.Bundle;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentStatePagerAdapter;
import androidx.fragment.app.FragmentTransaction;
import androidx.viewpager.widget.ViewPager;

import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.TextView;

import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.db.ContactDbController;
import com.rising.high.tech.bigultimatenavdraw.model.Contact;
import com.rising.high.tech.bigultimatenavdraw.ui.contact.ListContactFragment;
import com.rising.high.tech.bigultimatenavdraw.ui.contact.contactviewpager.ContactViewMain;
import com.rising.high.tech.bigultimatenavdraw.ui.contact.viewcontact.DocumentsNoteMain;
import com.rising.high.tech.bigultimatenavdraw.ui.contact.viewcontact.LedgerMain;
import com.rising.high.tech.bigultimatenavdraw.ui.contact.viewcontact.PayementMain;
import com.rising.high.tech.bigultimatenavdraw.ui.contact.viewcontact.SaleMain;

public class CollectionFragment extends Fragment {

    private static final String TAG = "CollectionDemoFragment";
    // When requested, this adapter returns a DemoObjectFragment,
    // representing an object in the collection.
    DemoCollectionPagerAdapter demoCollectionPagerAdapter;
    ViewPager viewPager;
    Integer indexId;
    private ContactDbController contactDbController;
    TextView contactName, customerType, customerMobile, customerAdress;

    Button backBtn;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater,
                             @Nullable ViewGroup container,
                             @Nullable Bundle savedInstanceState) {

        View root=inflater.inflate(R.layout.fragment_collection_demo, container, false);
        Bundle args = getArguments();
        indexId = args.getInt("id", 0);
        Log.d(TAG, "index id ### " + indexId);
        backBtn = root.findViewById(R.id.id_back);

        contactName = root.findViewById(R.id.contact_name);
        customerType = root.findViewById(R.id.customer_type);
        customerMobile = root.findViewById(R.id.customer_mobile);
        customerAdress = root.findViewById(R.id.customer_adress);

        contactDbController = new ContactDbController(getContext());
        contactDbController.open();

        Contact contact = contactDbController.getCustomerById(indexId);
        // Permet d'afficher le bouton de navigation up sur l'application
        contactName.setText(contact.getName());
        customerType.setText(contact.getType());
        customerMobile.setText(contact.getMobile());
        customerAdress.setText(contact.getAddress_line_1());

        backBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                replaceFragment(new ListContactFragment());
            }
        });

        return root;


    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        demoCollectionPagerAdapter = new DemoCollectionPagerAdapter(getChildFragmentManager());
        viewPager = view.findViewById(R.id.pager);
        viewPager.setAdapter(demoCollectionPagerAdapter);
    }


    // Since this is an object collection, use a FragmentStatePagerAdapter,
// and NOT a FragmentPagerAdapter.
    public class DemoCollectionPagerAdapter extends FragmentStatePagerAdapter {
        public DemoCollectionPagerAdapter(FragmentManager fm) {
            super(fm);
        }

        @Override
        public Fragment getItem(int i) {
            Fragment ledgerMainFragment = new LedgerMain(1);
            Bundle args = new Bundle();
            // Our object is just an integer :-P
            args.putInt(DemoObjectFragment.ARG_OBJECT, i + 1);
            ledgerMainFragment.setArguments(args);

            if (i==0){
                return  new LedgerMain(indexId);
            }else if (i==1){
                return  new SaleMain(indexId);
            }else if (i==2){
                return  new DocumentsNoteMain(indexId);
            }else
            return new PayementMain(indexId);
        }

        @Override
        public int getCount() {
            return 4;
        }

        @Override
        public CharSequence getPageTitle(int position) {
            String title="";
            if (position==0){
                title="Ledger";
            }else if (position==1){
                title="Sales";
            }else if (position==2){
                title="Documents & Note";
            }else
                title="Payement";

            return title;
        }
    }

    public void replaceFragment(Fragment someFragment) {
        FragmentTransaction transaction = getFragmentManager().beginTransaction();
        transaction.replace(R.id.nav_host_fragment, someFragment);
        transaction.addToBackStack(null);
        transaction.remove(CollectionFragment.this);
        transaction.commit();
    }


}
