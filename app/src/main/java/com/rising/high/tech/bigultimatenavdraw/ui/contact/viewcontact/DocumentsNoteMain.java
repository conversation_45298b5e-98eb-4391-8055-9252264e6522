package com.rising.high.tech.bigultimatenavdraw.ui.contact.viewcontact;

import android.content.Context;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.db.TransactionPayementDbController;
import com.rising.high.tech.bigultimatenavdraw.ui.contact.adapter.PayementContactAdapter;

public class DocumentsNoteMain extends Fragment {
    final String TAG = this.getClass().getSimpleName();
    PayementContactAdapter payementContactAdapter;
    TransactionPayementDbController transactionPayementDbController;
    private Context _context;
    RecyclerView recyclePayement;
    private Integer id_contact;

    public DocumentsNoteMain(Integer id) {
        this.id_contact = id;
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        View PageTwo = inflater.inflate(R.layout.fragment_documents_note_contact, container, false);
        _context = getContext();

        recyclePayement = PageTwo.findViewById(R.id.recycle_payement);

        return PageTwo;
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);


    }


    private void getSumamary() {

    }
}
