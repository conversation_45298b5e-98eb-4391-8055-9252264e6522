package com.rising.high.tech.bigultimatenavdraw.ui.contact.viewcontact;

import android.app.DatePickerDialog;
import android.app.TimePickerDialog;
import android.content.Context;
import android.content.res.Resources;
import android.graphics.Color;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.DatePicker;
import android.widget.EditText;
import android.widget.TextView;
import android.widget.TimePicker;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.gson.Gson;
import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.db.CompanyDbController;
import com.rising.high.tech.bigultimatenavdraw.db.ContactDbController;
import com.rising.high.tech.bigultimatenavdraw.db.TransactionDbController;
import com.rising.high.tech.bigultimatenavdraw.model.Business;
import com.rising.high.tech.bigultimatenavdraw.model.Contact;
import com.rising.high.tech.bigultimatenavdraw.ui.contact.adapter.LedgerVenteAdapter;
import com.rising.high.tech.bigultimatenavdraw.ui.sell.adapter.VenteAdapter;
import com.rising.high.tech.bigultimatenavdraw.util.SessionManager;
import com.rising.high.tech.bigultimatenavdraw.util.StringFormat;

import java.util.Calendar;
import java.util.HashMap;

//import butterknife.BindView;
//import butterknife.ButterKnife;

import static com.rising.high.tech.bigultimatenavdraw.util.Constant.ALL;
import static com.rising.high.tech.bigultimatenavdraw.util.StringFormat.populateSetDate;
import static com.rising.high.tech.bigultimatenavdraw.util.StringFormat.populateSetFullDate;

public class LedgerMain extends Fragment {

    private static final String TAG = "LedgerMain";
    private RecyclerView recycleLedger;
    private Integer id_contact;
    Resources resources;

    @BindView(R.id.new_start_date)
    EditText btnStartDate;
    @BindView(R.id.new_end_date)
    EditText btnEndDate;
    @BindView(R.id.id_filter)
    Button btnfilter;
    @BindView(R.id.noItemFound)
    TextView noItemFound;
    @BindView(R.id.total_paid_txt)
    TextView totalPaidTxt;
    @BindView(R.id.opening_balance_txt)
    TextView openingBalanceTxt;
    @BindView(R.id.advanced_balance_txt)
    TextView advancedBalanceTxt;
    private HashMap<String, String> currentMap = new HashMap<>();
    SessionManager session;

    TextView contactName, customerMobile, tv_company_name;

    LedgerVenteAdapter venteAdapter;
    private Context _context;
    TransactionDbController transactionDbController;
    private ContactDbController contactDbController;
    private CompanyDbController companyDbController;
    final Calendar c = Calendar.getInstance();

    public LedgerMain(Integer id) {
        this.id_contact = id;
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        View PageTwo = inflater.inflate(R.layout.fragment_ledjer_contact, container, false);
        ButterKnife.bind(this, PageTwo);

        resources = getResources();
        _context = getContext();
        session = new SessionManager(_context);

        recycleLedger = PageTwo.findViewById(R.id.recycle_ledger);
        contactName = PageTwo.findViewById(R.id.contact_name);
        customerMobile = PageTwo.findViewById(R.id.customer_mobile);
        tv_company_name = PageTwo.findViewById(R.id.tv_company_name);


        venteAdapter = new LedgerVenteAdapter();

        recycleLedger.setAdapter(venteAdapter);
        recycleLedger.setLayoutManager(new LinearLayoutManager(_context));


        return PageTwo;
    }


    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        transactionDbController = new TransactionDbController(_context);
        transactionDbController.open();
        contactDbController = new ContactDbController(_context);
        contactDbController.open();
        companyDbController=new CompanyDbController(_context);
        companyDbController.open();
        Business business=companyDbController.getCompanyData();
        Contact contact = contactDbController.getCustomerById(id_contact);
        venteAdapter.setData(transactionDbController.getTransactionLedgerByContactId(id_contact, null, null));
        noItemFound.setVisibility(transactionDbController.getTransactionLedgerByContactId(id_contact, null, null).size()==0? View.VISIBLE: View.GONE);
        recycleLedger.setVisibility(transactionDbController.getTransactionLedgerByContactId(id_contact, null, null).size()==0? View.GONE: View.VISIBLE);
        contactName.setText(contact.getName());
        customerMobile.setText(contact.getMobile());
        float total_paid = transactionDbController.getTotalPaidByCustomerId(id_contact);
        totalPaidTxt.setText(total_paid+"" + session.getUserDetails().get(session.KEY_SYMBOL));

        tv_company_name.setText(business.getName());


        if (transactionDbController.getOpeningBalanceContact(id_contact) != null){
            openingBalanceTxt.setText(transactionDbController.getOpeningBalanceContact(id_contact).getFinal_total()+"" + session.getUserDetails().get(session.KEY_SYMBOL));
            advancedBalanceTxt.setText(transactionDbController.getOpeningBalanceContact(id_contact).getFinal_total()+"" + session.getUserDetails().get(session.KEY_SYMBOL));
        }

        btnStartDate.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

                DatePickerDialog datePickerDialog = new DatePickerDialog(_context,
                        new DatePickerDialog.OnDateSetListener() {
                            @Override
                            public void onDateSet(DatePicker datePicker, int year, int month, int day) {
                                String myFormat = populateSetDate(year, month, day);
                                btnStartDate.setText(myFormat);
                                currentMap.put("date_debut", myFormat);
                            }
                        }, c.get(Calendar.YEAR), c.get(Calendar.MONTH), c.get(Calendar.DAY_OF_MONTH));
                datePickerDialog.show();


            }
        });
        btnEndDate.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

                DatePickerDialog datePickerDialog = new DatePickerDialog(_context,
                        new DatePickerDialog.OnDateSetListener() {
                            @Override
                            public void onDateSet(DatePicker datePicker, int year, int month, int day) {
                                String myFormat = populateSetDate(year, month, day);
                                btnEndDate.setText(myFormat);
                                currentMap.put("date_fin", myFormat);
                            }
                        }, c.get(Calendar.YEAR), c.get(Calendar.MONTH), c.get(Calendar.DAY_OF_MONTH));
                datePickerDialog.show();

            }
        });

        btnfilter.setOnClickListener(v->{
            venteAdapter.setData(transactionDbController.getTransactionLedgerByContactId(id_contact,  currentMap.get("date_debut"), currentMap.get("date_fin")));
            noItemFound.setVisibility(transactionDbController.getTransactionLedgerByContactId(id_contact, currentMap.get("date_debut"), currentMap.get("date_fin")).size()==0? View.VISIBLE: View.GONE);
            recycleLedger.setVisibility(transactionDbController.getTransactionLedgerByContactId(id_contact, currentMap.get("date_debut"), currentMap.get("date_fin")).size()==0? View.GONE: View.VISIBLE);

        });

    }


    private void getSumamary() {

    }
}
