package com.rising.high.tech.bigultimatenavdraw.ui.contact.viewcontact;

import android.content.Context;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.gson.Gson;
import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.db.ContactDbController;
import com.rising.high.tech.bigultimatenavdraw.db.TransactionDbController;
import com.rising.high.tech.bigultimatenavdraw.db.TransactionPayementDbController;
import com.rising.high.tech.bigultimatenavdraw.model.Contact;
import com.rising.high.tech.bigultimatenavdraw.ui.contact.adapter.PayementContactAdapter;


public class PayementMain extends Fragment {
    final String TAG = this.getClass().getSimpleName();
    PayementContactAdapter payementContactAdapter;
    TransactionPayementDbController transactionPayementDbController;
    private Context _context;
    RecyclerView recyclePayement;
    private Integer id_contact;

    TextView noItemFound;
    public PayementMain(Integer id) {
        this.id_contact = id;
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        View PageTwo = inflater.inflate(R.layout.fragment_transaction_payment_contact, container, false);
        _context = getContext();

        recyclePayement = PageTwo.findViewById(R.id.recycle_payement);

        payementContactAdapter = new PayementContactAdapter();

        recyclePayement.setAdapter(payementContactAdapter);
        recyclePayement.setLayoutManager(new LinearLayoutManager(_context));


        return PageTwo;
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        transactionPayementDbController = new TransactionPayementDbController(_context);
        transactionPayementDbController.open();

        payementContactAdapter.setData(transactionPayementDbController.getPaymentByContactId(id_contact));
        noItemFound.setVisibility(transactionPayementDbController.getPaymentByContactId(id_contact).size()==0? View.VISIBLE: View.GONE);
        recyclePayement.setVisibility(transactionPayementDbController.getPaymentByContactId(id_contact).size()==0? View.GONE: View.VISIBLE);

    }


    private void getSumamary() {

    }
}
