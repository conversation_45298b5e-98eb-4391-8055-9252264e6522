package com.rising.high.tech.bigultimatenavdraw.ui.contact.viewcontact;

import android.content.Context;
import android.content.res.Resources;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentPagerAdapter;
import androidx.fragment.app.FragmentTransaction;
import androidx.viewpager.widget.ViewPager;

import com.google.android.material.tabs.TabLayout;
import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.db.ContactDbController;
import com.rising.high.tech.bigultimatenavdraw.model.Contact;
import com.rising.high.tech.bigultimatenavdraw.ui.contact.ListContactFragment;

import java.util.ArrayList;
import java.util.List;

public class ViewContactMain extends Fragment {
    private static final String TAG = "ViewContactMain";
    TabLayout MyTabs;
    ViewPager MyPage;
    private Context _context;
    private ContactDbController contactDbController;

    int[] tabIcons = {R.drawable.ic_ledger, R.drawable.ic_low_price,
            R.drawable.ic_payment_history, R.drawable.ic_baseline_remove_red_eye_24, R.drawable.ic_baseline_remove_red_eye_24,
            R.drawable.ic_baseline_remove_red_eye_24, R.drawable.ic_baseline_remove_red_eye_24};
    Resources resources;
    private int indexId = 0;
    private Button backBtn;

    TextView contactName, customerType, customerMobile, customerAdress;

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        // Inflate the layout for this fragment
        View root = inflater.inflate(R.layout.activity_detail_report, container, false);
        resources = getResources();
        _context = getContext();

        //declare views
        MyTabs = root.findViewById(R.id.MyTabs);
        MyPage = root.findViewById(R.id.MyPage);

        contactName = root.findViewById(R.id.contact_name);
        customerType = root.findViewById(R.id.customer_type);
        customerMobile = root.findViewById(R.id.customer_mobile);
        customerAdress = root.findViewById(R.id.customer_adress);
        backBtn = root.findViewById(R.id.id_back);

        contactDbController = new ContactDbController(_context);
        contactDbController.open();


        Bundle args = getArguments();
        indexId = args.getInt("id", 0);
        Toast.makeText(_context, indexId + "", Toast.LENGTH_LONG).show();


        Contact contact = contactDbController.getCustomerById(indexId);


        Toolbar toolbar = root.findViewById(R.id.toolbar);
        toolbar.setTitle(resources.getString(R.string.string_view_contact));
        // Permet d'afficher le bouton de navigation up sur l'application


        contactName.setText(contact.getName());
        customerType.setText(contact.getType());
        customerMobile.setText(contact.getMobile());
        customerAdress.setText(contact.getAddress_line_1());


        backBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                replaceFragment(new ListContactFragment());
            }
        });

        initTabLayout();

        return root;
    }


    public void SetUpViewPager(ViewPager viewpage) {

        MyViewPageAdapter Adapter = new MyViewPageAdapter(((AppCompatActivity) _context).getSupportFragmentManager());
        Adapter.AddFragmentPage(new LedgerMain(indexId), "Ledger");
        Adapter.AddFragmentPage(new SaleMain(indexId), "Sales");
        Adapter.AddFragmentPage(new PayementMain(indexId), "Payement");
        /*
        You can add more Fragment Adapter
        But the minimum of the ViewPager is 3 index Page
         */
        // We Need Fragment class now
        viewpage.setAdapter(Adapter);
    }

    private void initTabLayout() {
        MyTabs.setupWithViewPager(MyPage);
        SetUpViewPager(MyPage);

        MyTabs.getTabAt(0).setIcon(tabIcons[0]).getIcon();
        MyTabs.getTabAt(1).setIcon(tabIcons[1]).getIcon();
        MyTabs.getTabAt(2).setIcon(tabIcons[2]).getIcon();
    }

    //Custom Adapter Here
    public class MyViewPageAdapter extends FragmentPagerAdapter {
        private List<Fragment> MyFragment = new ArrayList<>();
        private List<String> MyPageTittle = new ArrayList<>();

        public MyViewPageAdapter(FragmentManager manager) {
            super(manager);
        }

        public void AddFragmentPage(Fragment Frag, String Title) {
            MyFragment.add(Frag);
            MyPageTittle.add(Title);
        }

        @Override
        public Fragment getItem(int position) {
            return MyFragment.get(position);
        }

        @Override
        public CharSequence getPageTitle(int position) {
            return MyPageTittle.get(position);
        }

        @Override
        public int getCount() {
            return MyFragment.size();
        }
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        switch (item.getItemId()) {
            case android.R.id.home:
                ((AppCompatActivity) _context).onBackPressed();
                return true;
        }
        return super.onOptionsItemSelected(item);
    }


    public void replaceFragment(Fragment someFragment) {
        FragmentTransaction transaction = getFragmentManager().beginTransaction().remove(ViewContactMain.this);
        transaction.replace(R.id.nav_host_fragment, someFragment);
        transaction.addToBackStack(null);
        transaction.commit();
    }


}
