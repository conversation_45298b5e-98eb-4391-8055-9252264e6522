package com.rising.high.tech.bigultimatenavdraw.ui.customergroups;

import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.widget.AppCompatImageView;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.db.CategoryDbController;
import com.rising.high.tech.bigultimatenavdraw.db.CustomerGroupsDbController;
import com.rising.high.tech.bigultimatenavdraw.model.Category;
import com.rising.high.tech.bigultimatenavdraw.model.Customer_groups;
import com.rising.high.tech.bigultimatenavdraw.model.Transaction;
import com.rising.high.tech.bigultimatenavdraw.ui.category.adapter.CategoryAdapter;
import com.rising.high.tech.bigultimatenavdraw.ui.customergroups.adapter.CustomerGroupsAdapter;
import com.rising.high.tech.bigultimatenavdraw.ui.purchase.adapter.PurchasesListAdapter;
import com.rising.high.tech.bigultimatenavdraw.util.SessionManager;
import com.rising.high.tech.bigultimatenavdraw.util.serverInteraction;

import java.util.ArrayList;


import static com.rising.high.tech.bigultimatenavdraw.util.Constant.CONTACT_ADD;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.NO;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.SERVER_MASTER;

public class ListCustomerGroupsFragment extends Fragment {

    private static final String TAG = "ListCustomerGroupsFragment";
    private Context _context;

    EditText searchEdit;
    RecyclerView recycle_customer_groups;
    TextView noProductFound;
    Button btnAdd;
    Button btnFilter;
    SessionManager session;
    private Integer userId;

    CustomerGroupsDbController customerGroupsDbController;

    CustomerGroupsAdapter customerGroupsAdapter;

    public ListCustomerGroupsFragment() {
        // Required empty public constructor
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        // Inflate the layout for this fragment
        View root = inflater.inflate(R.layout.fragment_list_customer_groups, container, false);
        _context = getContext();
        session = new SessionManager(_context);

        customerGroupsDbController = new CustomerGroupsDbController(_context);
        customerGroupsDbController.open();

        customerGroupsAdapter = new CustomerGroupsAdapter();
        recycle_customer_groups.setAdapter(customerGroupsAdapter);
        recycle_customer_groups.setLayoutManager(new LinearLayoutManager(_context));

        showCustomerList(customerGroupsDbController.getAllCustomerGroups());
        initListners();

        checkRoles();
        userId=(int)session.getUserDetails().get(session.ID_USER);

        return root;
    }

    @Override
    public void onStart() {
        super.onStart();
    }

    private void checkRoles()
    {
        if (session.getBoolean(SERVER_MASTER) || !session.checkPermissionSubModule(CONTACT_ADD))
        {
            btnAdd.setVisibility(View.GONE);
        }
    }

    private void initListners() {
        btnAdd.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                addCustomerGroups();
            }
        });

        btnFilter.setOnClickListener(view -> {
            ArrayList<Customer_groups> arrayList = customerGroupsDbController.getCsutomerGroupsLike(searchEdit.getText().toString());
            showCustomerList(arrayList);
        });

        customerGroupsAdapter.setOnDataChangeListener(new CustomerGroupsAdapter.OnDataChangeListener() {
            @Override
            public void onDataDeleted() {
                setItemView();
            }
        });
    }

    private void showCustomerList(ArrayList<Customer_groups> arrayList){
        customerGroupsAdapter.setData(arrayList);
        recycle_customer_groups.setVisibility(arrayList.size() > 0 ? View.VISIBLE : View.GONE);
        noProductFound.setVisibility(arrayList.size() > 0 ? View.GONE : View.VISIBLE);
    }

    public void replaceFragment(Fragment someFragment) {
        FragmentTransaction transaction = getFragmentManager().beginTransaction();
        transaction.replace(R.id.nav_host_fragment, someFragment);
        transaction.addToBackStack(null);
        transaction.commit();
    }


    private void addCustomerGroups() {
        //Preparing views
        // get prompts.xml view
        LayoutInflater li = LayoutInflater.from(_context);
        View promptsView = li.inflate(R.layout.customer_groups_add_main, null);
        androidx.appcompat.app.AlertDialog.Builder alertDialogBuilder = new androidx.appcompat.app.AlertDialog.Builder(
                _context);

        alertDialogBuilder.setView(promptsView);

        final EditText customer_groups_name = promptsView.findViewById(R.id.customer_groups_name);
        final EditText amount = promptsView.findViewById(R.id.amount);

        final Button ButtonSave = promptsView.findViewById(R.id.btn_save);
        final AppCompatImageView ButtonClose = promptsView.findViewById(R.id.btn_close);


        final androidx.appcompat.app.AlertDialog mAlertDialog = alertDialogBuilder.create();
        if (mAlertDialog.getWindow() != null)
            mAlertDialog.getWindow().getAttributes().windowAnimations = R.style.SlidingDialogAnimation;
        mAlertDialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));

        ButtonClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mAlertDialog.dismiss();
            }
        });


        ButtonSave.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!customer_groups_name.getText().toString().equals("") && !amount.getText().toString().equals("")) {

                    Customer_groups customer_groups = new Customer_groups();
                    customer_groups.setName(customer_groups_name.getText().toString());
                    customer_groups.setAmount(amount.getText().toString());
                    customer_groups.setCreated_by(userId);
                    customer_groups.setBusiness_id(1);
                    customer_groups.setSync(NO);

                    int inserted = customerGroupsDbController.insertLocal(customer_groups);
                    if (inserted > 0) {
                        Toast.makeText(getContext(), getResources().getString(R.string.lbl_added_success), Toast.LENGTH_LONG).show();
                        showCustomerList(customerGroupsDbController.getAllCustomerGroups());
                        customerGroupsAdapter.notifyDataSetChanged();
                        mAlertDialog.dismiss();
                    } else {
                        Toast.makeText(getContext(), "Error while adding", Toast.LENGTH_LONG).show();
                    }
                } else {
                    Toast.makeText(getContext(), getResources().getString(R.string.label_all_field_required), Toast.LENGTH_LONG).show();
                }

            }
        });


        mAlertDialog.show();
    }
    public void setItemView()
    {
        if(customerGroupsAdapter.getItemCount() > 0)
        {
            recycle_customer_groups.setVisibility(View.VISIBLE);
            noProductFound.setVisibility(View.GONE);
        }
        else
        {
            recycle_customer_groups.setVisibility(View.GONE);
            noProductFound.setVisibility(View.VISIBLE);
        }
    }

}