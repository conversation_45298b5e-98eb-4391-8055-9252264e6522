package com.rising.high.tech.bigultimatenavdraw.ui.customergroups.adapter;

import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.widget.AppCompatImageView;
import androidx.recyclerview.widget.RecyclerView;

import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.db.CustomerGroupsDbController;
import com.rising.high.tech.bigultimatenavdraw.model.Customer_groups;

import java.util.ArrayList;


public class CustomerGroupsAdapter extends RecyclerView.Adapter<CustomerGroupsAdapter.Customer_groupsViewHolder> {

    private ArrayList<Customer_groups> dataList = new ArrayList<>();

    Context context;

    CustomerGroupsDbController customerGroupsDbController;

    @Override
    public Customer_groupsViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        context = parent.getContext();

        customerGroupsDbController = new CustomerGroupsDbController(context);
        customerGroupsDbController.open();
        return new Customer_groupsViewHolder(LayoutInflater.from(parent.getContext())
                .inflate(R.layout.customer_groups_item, parent, false));
    }

    @Override
    public void onBindViewHolder(Customer_groupsViewHolder holder, int position) {
        holder.name.setText(dataList.get(position).getName());
        holder.amount.setText(dataList.get(position).getAmount());
    }

    public void setData(ArrayList<Customer_groups> arrayList) {
        this.dataList = arrayList;
        notifyDataSetChanged();
    }


    @Override
    public int getItemCount() {
        return dataList.size();
    }

    public class Customer_groupsViewHolder extends RecyclerView.ViewHolder {

        TextView name, amount;
        Button btnDelete, btnEdit;

        public Customer_groupsViewHolder(View itemView) {
            super(itemView);


            name = itemView.findViewById(R.id.id_name);
            amount = itemView.findViewById(R.id.amount);

            btnEdit = itemView.findViewById(R.id.btn_edit);

            btnDelete = itemView.findViewById(R.id.btn_delete);

            btnDelete.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    deleteItem(getAdapterPosition());
                }
            });

            btnEdit.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    editCustomer_groups(getAdapterPosition());
                }
            });

        }
    }

    private void editCustomer_groups(int position) {
        //Preparing views
        // get prompts.xml view
        LayoutInflater li = LayoutInflater.from(context);
        View promptsView = li.inflate(R.layout.customer_groups_add_main, null);


        androidx.appcompat.app.AlertDialog.Builder alertDialogBuilder = new androidx.appcompat.app.AlertDialog.Builder(
                context);

        alertDialogBuilder.setView(promptsView);

        final EditText customer_groups_name = promptsView.findViewById(R.id.customer_groups_name);
        final EditText amount = promptsView.findViewById(R.id.amount);

        final Button ButtonSave = promptsView.findViewById(R.id.btn_save);
        final AppCompatImageView ButtonClose = promptsView.findViewById(R.id.btn_close);

        ButtonSave.setText(context.getResources().getString(R.string.label_updatee));

        customer_groups_name.setText(dataList.get(position).getName());
        amount.setText(dataList.get(position).getAmount());

        final androidx.appcompat.app.AlertDialog mAlertDialog = alertDialogBuilder.create();
        if (mAlertDialog.getWindow() != null)
            mAlertDialog.getWindow().getAttributes().windowAnimations = R.style.SlidingDialogAnimation;
        mAlertDialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));

        ButtonClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mAlertDialog.dismiss();
            }
        });


        ButtonSave.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!customer_groups_name.getText().toString().equals("") && !amount.getText().toString().equals("")) {

                    Customer_groups customer_groups = dataList.get(position);
                    customer_groups.setName(customer_groups_name.getText().toString());
                    customer_groups.setAmount(amount.getText().toString());

                    int inserted = customerGroupsDbController.editCustomer_groups(customer_groups);
                    if (inserted > 0) {
                        Toast.makeText(context, context.getResources().getString(R.string.lbl_added_success), Toast.LENGTH_LONG).show();
                        notifyItemChanged(position);
                        mAlertDialog.dismiss();
                    } else {
                        Toast.makeText(context, "Error while adding", Toast.LENGTH_LONG).show();
                    }
                } else {
                    Toast.makeText(context, context.getResources().getString(R.string.label_all_field_required), Toast.LENGTH_LONG).show();
                }

            }
        });


        mAlertDialog.show();
    }


    private void deleteItem(int postition) {
        //Preparing views
        // get prompts.xml view
        LayoutInflater li = LayoutInflater.from(context);
        View promptsView = li.inflate(R.layout.delete_dialog_main, null);

        androidx.appcompat.app.AlertDialog.Builder alertDialogBuilder = new androidx.appcompat.app.AlertDialog.Builder(
                context);

        alertDialogBuilder.setView(promptsView);

        final Button ButtonSave = promptsView.findViewById(R.id.btn_save);
        final Button ButtonClose = promptsView.findViewById(R.id.btn_close);

        final androidx.appcompat.app.AlertDialog mAlertDialog = alertDialogBuilder.create();
        if (mAlertDialog.getWindow() != null)
            mAlertDialog.getWindow().getAttributes().windowAnimations = R.style.SlidingDialogAnimation;
        mAlertDialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));

        ButtonClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mAlertDialog.dismiss();
            }
        });

        ButtonSave.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

                customerGroupsDbController.deleteCustomerGroups(dataList.get(postition).getId());
                dataList.remove(postition);
                mOnDataChangeListener.onDataDeleted();

                mAlertDialog.dismiss();
                notifyDataSetChanged();

            }
        });


        mAlertDialog.show();
    }

    public interface OnDataChangeListener {
        void onDataDeleted();
    }

    CustomerGroupsAdapter.OnDataChangeListener mOnDataChangeListener;

    public void setOnDataChangeListener(CustomerGroupsAdapter.OnDataChangeListener onDataChangeListener) {
        mOnDataChangeListener = onDataChangeListener;
    }
}
