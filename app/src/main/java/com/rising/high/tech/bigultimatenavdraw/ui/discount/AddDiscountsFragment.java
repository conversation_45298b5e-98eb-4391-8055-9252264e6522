package com.rising.high.tech.bigultimatenavdraw.ui.discount;

import android.app.DatePickerDialog;
import android.content.Context;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.DatePicker;
import android.widget.EditText;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.Spinner;
import android.widget.Toast;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;

import com.androidbuts.multispinnerfilter.KeyPairBoolData;
import com.androidbuts.multispinnerfilter.MultiSpinnerListener;
import com.androidbuts.multispinnerfilter.MultiSpinnerSearch;
import com.google.android.material.snackbar.Snackbar;
import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.db.BrandDbController;
import com.rising.high.tech.bigultimatenavdraw.db.CategoryDbController;
import com.rising.high.tech.bigultimatenavdraw.db.DiscountDbController;
import com.rising.high.tech.bigultimatenavdraw.db.DiscountVariationDbController;
import com.rising.high.tech.bigultimatenavdraw.db.ProductDbController;
import com.rising.high.tech.bigultimatenavdraw.db.BusinessLocationDbController;
import com.rising.high.tech.bigultimatenavdraw.model.Brand;
import com.rising.high.tech.bigultimatenavdraw.model.Category;
import com.rising.high.tech.bigultimatenavdraw.model.Discount;
import com.rising.high.tech.bigultimatenavdraw.model.Discount_variation;
import com.rising.high.tech.bigultimatenavdraw.model.Product;
import com.rising.high.tech.bigultimatenavdraw.model.Business_location;
import com.rising.high.tech.bigultimatenavdraw.ui.adapter.SpinBrandAdapter;
import com.rising.high.tech.bigultimatenavdraw.ui.adapter.SpinCategoryAdapter;
import com.rising.high.tech.bigultimatenavdraw.ui.adapter.SpinStationAdapter;
import com.rising.high.tech.bigultimatenavdraw.util.FileUtil;
import com.rising.high.tech.bigultimatenavdraw.util.StringFormat;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;

import static com.rising.high.tech.bigultimatenavdraw.util.Constant.NO;
import static com.rising.high.tech.bigultimatenavdraw.util.StringFormat.populateSetDate;

public class AddDiscountsFragment extends Fragment implements View.OnClickListener {
    private static final String TAG = "AddDiscountsFragment";
    private Context _context;
    final Calendar c = Calendar.getInstance();

    DiscountDbController discountDbController;
    DiscountVariationDbController discountVariationDbController;
    ProductDbController productDbController;
    BusinessLocationDbController businessLocationDbController;
    CategoryDbController categoryDbController;
    BrandDbController brandDbController;
    SpinStationAdapter spinStationAdapter;
    SpinBrandAdapter spinBrandAdapter;
    SpinCategoryAdapter spinCategoryAdapter;

    private ArrayList<Product> dataListProduct = null;

    @BindView(R.id.name_discount)
    EditText nameDiscount;
    @BindView(R.id.spinner_brand)
    Spinner spinnerBrand;
    @BindView(R.id.spinner_category)
    Spinner spinnerCategory;
    @BindView(R.id.spinner_location)
    Spinner spinnerLocation;
    @BindView(R.id.discount_type_spinner)
    Spinner spinnerDiscountType;
    @BindView(R.id.priority)
    EditText priority;
    @BindView(R.id.starts_at)
    EditText startsAt;
    @BindView(R.id.ends_at)
    EditText endsAt;
    @BindView(R.id.discount_amnt)
    EditText discountAmnt;
    @BindView(R.id.add_btn)
    Button addBtn;
    @BindView(R.id.id_back)
    Button backBtn;
    @BindView(R.id.check_spg)
    CheckBox checkSpg;
    @BindView(R.id.is_active)
    CheckBox checkActive;
    @BindView(R.id.multipleItemSelectionSpinner)
    MultiSpinnerSearch multiSelectSpinnerWithSearch;
    @BindView(R.id.spin_brand_container)
    FrameLayout spinBrandContainer;
    @BindView(R.id.spin_category_container)
    FrameLayout spinCategoryContainer;
    @BindView(R.id.linearLayout)
    RelativeLayout linearLayout;

    private ArrayList<Integer> productIds = new ArrayList<>();

    public AddDiscountsFragment() {
        // Required empty public constructor
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        // Inflate the layout for this fragment
        View root = inflater.inflate(R.layout.fragment_add_discount, container, false);
        ButterKnife.bind(this, root);
        _context = getContext();
        //searchEdit.setOnClickListener(this);

        initDb();
        initSpinners();
        initBtnClicks();
        initSerachEdit();
        initMultiSpinnerSearch();
        return root;
    }

    private void initBtnClicks() {
        startsAt.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                DatePickerDialog datePickerDialog = new DatePickerDialog(_context,
                        new DatePickerDialog.OnDateSetListener() {
                            @Override
                            public void onDateSet(DatePicker datePicker, int year, int month, int day) {
                                String myFormat = populateSetDate(year, month, day);
                                startsAt.setText(myFormat);
                            }
                        }, c.get(Calendar.YEAR), c.get(Calendar.MONTH), c.get(Calendar.DAY_OF_MONTH));
                datePickerDialog.show();
            }
        });

        endsAt.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                DatePickerDialog datePickerDialog = new DatePickerDialog(_context,
                        new DatePickerDialog.OnDateSetListener() {
                            @Override
                            public void onDateSet(DatePicker datePicker, int year, int month, int day) {
                                String myFormat = populateSetDate(year, month, day);
                                endsAt.setText(myFormat);
                            }
                        }, c.get(Calendar.YEAR), c.get(Calendar.MONTH), c.get(Calendar.DAY_OF_MONTH));
                datePickerDialog.show();
            }
        });

        backBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

                replaceFragment(new ListDiscountFragment());
            }
        });

        addBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                addDiscount();
            }
        });
    }

    private void initSpinners() {

        spinStationAdapter = new SpinStationAdapter(_context, android.R.layout.simple_spinner_item, businessLocationDbController.getAllStationSpinner());
        spinnerLocation.setAdapter(spinStationAdapter);

        spinCategoryAdapter = new SpinCategoryAdapter(_context, R.layout.custom_spinner_item, categoryDbController.getAllCategorySpinner());
        spinnerCategory.setAdapter(spinCategoryAdapter);

        spinBrandAdapter = new SpinBrandAdapter(_context, R.layout.custom_spinner_item, brandDbController.getAllBrandSpinner());
        spinnerBrand.setAdapter(spinBrandAdapter);

        startsAt.setText(StringFormat.populateSetDate(c.get(Calendar.YEAR), c.get(Calendar.MONTH), c.get(Calendar.DAY_OF_MONTH)));
        endsAt.setText(StringFormat.populateSetDate(c.get(Calendar.YEAR), c.get(Calendar.MONTH), c.get(Calendar.DAY_OF_MONTH)));

    }

    private void initMultiSpinnerSearch() {
        /**
         * Search MultiSelection Spinner (With Search/Filter Functionality)
         *
         *  Using MultiSpinnerSearch class
         */

        // Pass true If you want searchView above the list. Otherwise false. default = true.
        multiSelectSpinnerWithSearch.setSearchEnabled(true);

        // A text that will display in search hint.
        multiSelectSpinnerWithSearch.setSearchHint("Select product");

        // Set text that will display when search result not found...
        multiSelectSpinnerWithSearch.setEmptyTitle("Not Data Found!");

        // If you will set the limit, this button will not display automatically.
        multiSelectSpinnerWithSearch.setShowSelectAllButton(true);

        //A text that will display in clear text button
        multiSelectSpinnerWithSearch.setClearText("Close & Clear");

        // Removed second parameter, position. Its not required now..
        // If you want to pass preselected items, you can do it while making listArray,
        // pass true in setSelected of any item that you want to preselect

        ArrayList<KeyPairBoolData> listArray1 = new ArrayList<>();
        for (Product product : productDbController.getAllProduct()) {
            KeyPairBoolData keyPairBoolData = new KeyPairBoolData(product.getName(), false);
            keyPairBoolData.setId(product.getId());
            listArray1.add(keyPairBoolData);
        }

        multiSelectSpinnerWithSearch.setItems(listArray1, new MultiSpinnerListener() {
            @Override
            public void onItemsSelected(List<KeyPairBoolData> items) {
                productIds.clear();
                if(items.size()>0){
                    spinnerCategory.setEnabled(false);
                    spinnerCategory.setClickable(false);
                    spinnerCategory.setSelection(0);
                    spinCategoryContainer.setBackgroundColor(getResources().getColor(R.color.gray_white));

                    spinnerBrand.setEnabled(false);
                    spinnerBrand.setClickable(false);
                    spinnerBrand.setSelection(0);
                    spinBrandContainer.setBackgroundColor(getResources().getColor(R.color.gray_white));

                }else {
                    spinnerCategory.setEnabled(true);
                    spinnerCategory.setClickable(true);
                    spinCategoryContainer.setBackgroundColor(getResources().getColor(R.color.white));

                    spinnerBrand.setEnabled(true);
                    spinnerBrand.setClickable(true);
                    spinBrandContainer.setBackgroundColor(getResources().getColor(R.color.white));

                }

                for (int i = 0; i < items.size(); i++) {
                    if (items.get(i).isSelected()) {
                        productIds.add((int)items.get(i).getId());
                        Log.i(TAG, i + " : " + items.get(i).getName() + " : " + items.get(i).getId());
                    }
                }
            }
        });

        /**
         * If you want to set limit as maximum item should be selected is 2.
         * For No limit -1 or do not call this method.
         *
         */
        multiSelectSpinnerWithSearch.setLimit(2, new MultiSpinnerSearch.LimitExceedListener() {
            @Override
            public void onLimitListener(KeyPairBoolData data) {
                Toast.makeText(_context,
                        "Limit exceed ", Toast.LENGTH_LONG).show();
            }
        });
    }


    private void initDb() {
        discountDbController = new DiscountDbController(_context);

        discountVariationDbController = new DiscountVariationDbController(_context);

        productDbController = new ProductDbController(_context);

        businessLocationDbController = new BusinessLocationDbController(_context);

        categoryDbController = new CategoryDbController(_context);

        brandDbController = new BrandDbController(_context);
    }

    private void addDiscount() {


        if (nameDiscount.getText().toString().equals("")) {
            //     Toast.makeText(_context, _context.getResources().getString(R.string.label_select_ssupplier), Toast.LENGTH_LONG).show();
            Snackbar snackbar = Snackbar.make(linearLayout, _context.getResources().getString(R.string.label_select_namme), Snackbar.LENGTH_LONG);
            snackbar.show();
        }else if (spinnerLocation.getSelectedItemPosition() == 0) {
            Snackbar snackbar = Snackbar.make(linearLayout, _context.getResources().getString(R.string.lbl_please_select_station), Snackbar.LENGTH_LONG);
            snackbar.show();
        }else if (priority.getText().toString().equals("")) {
            Snackbar snackbar = Snackbar.make(linearLayout, _context.getResources().getString(R.string.label_plz_spriority), Snackbar.LENGTH_LONG);
            snackbar.show();
        }else if (spinnerDiscountType.getSelectedItemPosition()== 0){
            Snackbar snackbar = Snackbar.make(linearLayout, _context.getResources().getString(R.string.label_choose_seldect_discount_type), Snackbar.LENGTH_LONG);
            snackbar.show();
        }else if (discountAmnt.getText().toString().equals("")){
            Snackbar snackbar = Snackbar.make(linearLayout, _context.getResources().getString(R.string.label_selectamuuunt), Snackbar.LENGTH_LONG);
            snackbar.show();
        }else


        if ( !startsAt.getText().toString().equals("") && !endsAt.getText().toString().equals("")) {
            Discount discount = new Discount();

            discount.setName(nameDiscount.getText().toString());
            discount.setPriority(Integer.parseInt(priority.getText().toString()));
            discount.setDiscount_type(spinnerDiscountType.getSelectedItem().toString().toLowerCase());
            discount.setDiscount_amount(discountAmnt.getText().toString());
            discount.setStarts_at(startsAt.getText().toString());
            discount.setEnds_at(endsAt.getText().toString());
            discount.setApplicable_in_spg(checkSpg.isChecked() ? 1 : 0);
            discount.setIs_active(checkActive.isChecked() ? 1 : 0);
            discount.setApplicable_in_cg(0);

            Business_location businesslocation = (Business_location) spinnerLocation.getSelectedItem();
            discount.setLocation_id(businesslocation.getId());

            Category category = (Category) spinnerCategory.getSelectedItem();
            discount.setCategory_id(category.getId());

            Brand brand = (Brand) spinnerBrand.getSelectedItem();
            discount.setBrand_id(brand.getId());
            discount.setSync(NO);
            discount.setBusiness_id(1);

            int discount_id = discountDbController.insertLocal(discount);

            if (discount_id > 0) {
                if (productIds.size()>0){
                    for (int p : productIds){
                        Discount_variation discount_variation = new Discount_variation();
                        discount_variation.setDiscount_id(discount_id);
                        discount_variation.setVariation_id(p);
                        discountVariationDbController.insertLocal(discount_variation);
                    }
                }
            //    Toast.makeText(_context, getResources().getString(R.string.lbl_added_success), Toast.LENGTH_LONG).show();
                FileUtil.showDialog(_context, "Successful", getResources().getString(R.string.stock_transfer_success));

                replaceFragment(new ListDiscountFragment());
                // discountVariationDbController.insertLocal()
            }
        } else {
            Toast.makeText(_context, getResources().getString(R.string.label_all_field_required), Toast.LENGTH_LONG).show();
        }
    }


    public void replaceFragment(Fragment someFragment) {
        FragmentTransaction transaction = getFragmentManager().beginTransaction();
        transaction.replace(R.id.nav_host_fragment, someFragment);
        transaction.addToBackStack(null);
        transaction.commit();
    }

    @Override
    public void onClick(View v) {

    }

    private void initSerachEdit() {
        //TODO
        // IMPLEMENT Thread solution

//
//        dataListProduct = productDbController.getAllProduct();
//
//
//            String[] names = new String[dataListProduct.size()];
//            for (int i = 0; i < dataListProduct.size(); i++) {
//                names[i] = dataListProduct.get(i).getName();
//            }
//
//            final ArrayAdapter<String> adapter = new ArrayAdapter<String>(_context, android.R.layout.simple_list_item_1, names);
//            searchEdit.setAdapter(adapter);
//            searchEdit.setDropDownBackgroundDrawable(new ColorDrawable(_context.getResources().getColor(R.color.blue)));
//
//            searchEdit.setOnItemClickListener(new AdapterView.OnItemClickListener() {
//
//                @Override
//                public void onItemClick(AdapterView<?> parent, View arg1, int pos,
//                                        long id) {
//
//                  //  searchProduct(adapter.getItem(pos).toString());
//
//                }
//            });

    }


}