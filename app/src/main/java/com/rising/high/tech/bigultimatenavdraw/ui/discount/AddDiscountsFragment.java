package com.rising.high.tech.bigultimatenavdraw.ui.discount;

import android.app.DatePickerDialog;
import android.content.Context;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.DatePicker;
import android.widget.EditText;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.Spinner;
import android.widget.Toast;

import androidx.appcompat.widget.AppCompatSpinner;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;

import com.google.android.material.snackbar.Snackbar;
import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.db.BrandDbController;
import com.rising.high.tech.bigultimatenavdraw.db.CategoryDbController;
import com.rising.high.tech.bigultimatenavdraw.db.DiscountDbController;
import com.rising.high.tech.bigultimatenavdraw.db.DiscountVariationDbController;
import com.rising.high.tech.bigultimatenavdraw.db.ProductDbController;
import com.rising.high.tech.bigultimatenavdraw.db.BusinessLocationDbController;
import com.rising.high.tech.bigultimatenavdraw.model.Brand;
import com.rising.high.tech.bigultimatenavdraw.model.Category;
import com.rising.high.tech.bigultimatenavdraw.model.Discount;
import com.rising.high.tech.bigultimatenavdraw.model.Discount_variation;
import com.rising.high.tech.bigultimatenavdraw.model.Product;
import com.rising.high.tech.bigultimatenavdraw.model.Business_location;
import com.rising.high.tech.bigultimatenavdraw.ui.adapter.SpinBrandAdapter;
import com.rising.high.tech.bigultimatenavdraw.ui.adapter.SpinCategoryAdapter;
import com.rising.high.tech.bigultimatenavdraw.ui.adapter.SpinStationAdapter;
import com.rising.high.tech.bigultimatenavdraw.util.FileUtil;
import com.rising.high.tech.bigultimatenavdraw.util.StringFormat;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;


import static com.rising.high.tech.bigultimatenavdraw.util.Constant.NO;
import static com.rising.high.tech.bigultimatenavdraw.util.StringFormat.populateSetDate;

public class AddDiscountsFragment extends Fragment implements View.OnClickListener {
    private static final String TAG = "AddDiscountsFragment";
    private Context _context;
    final Calendar c = Calendar.getInstance();

    DiscountDbController discountDbController;
    DiscountVariationDbController discountVariationDbController;
    ProductDbController productDbController;
    BusinessLocationDbController businessLocationDbController;
    CategoryDbController categoryDbController;
    BrandDbController brandDbController;
    SpinStationAdapter spinStationAdapter;
    SpinBrandAdapter spinBrandAdapter;
    SpinCategoryAdapter spinCategoryAdapter;

    private ArrayList<Product> dataListProduct = null;

    EditText nameDiscount;
    Spinner spinnerBrand;
    Spinner spinnerCategory;
    Spinner spinnerLocation;
    Spinner spinnerDiscountType;
    EditText priority;
    EditText startsAt;
    EditText endsAt;
    EditText discountAmnt;
    Button addBtn;
    Button backBtn;
    CheckBox checkSpg;
    CheckBox checkActive;
    AppCompatSpinner multiSelectSpinnerWithSearch;
    FrameLayout spinBrandContainer;
    FrameLayout spinCategoryContainer;
    RelativeLayout linearLayout;

    private ArrayList<Integer> productIds = new ArrayList<>();

    public AddDiscountsFragment() {
        // Required empty public constructor
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        // Inflate the layout for this fragment
        View root = inflater.inflate(R.layout.fragment_add_discount, container, false);
        _context = getContext();
        //searchEdit.setOnClickListener(this);

        initDb();
        initSpinners();
        initBtnClicks();
        initSerachEdit();
        // initMultiSpinnerSearch(); // Commented out - replaced with simple spinner
        return root;
    }

    private void initBtnClicks() {
        startsAt.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                DatePickerDialog datePickerDialog = new DatePickerDialog(_context,
                        new DatePickerDialog.OnDateSetListener() {
                            @Override
                            public void onDateSet(DatePicker datePicker, int year, int month, int day) {
                                String myFormat = populateSetDate(year, month, day);
                                startsAt.setText(myFormat);
                            }
                        }, c.get(Calendar.YEAR), c.get(Calendar.MONTH), c.get(Calendar.DAY_OF_MONTH));
                datePickerDialog.show();
            }
        });

        endsAt.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                DatePickerDialog datePickerDialog = new DatePickerDialog(_context,
                        new DatePickerDialog.OnDateSetListener() {
                            @Override
                            public void onDateSet(DatePicker datePicker, int year, int month, int day) {
                                String myFormat = populateSetDate(year, month, day);
                                endsAt.setText(myFormat);
                            }
                        }, c.get(Calendar.YEAR), c.get(Calendar.MONTH), c.get(Calendar.DAY_OF_MONTH));
                datePickerDialog.show();
            }
        });

        backBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

                replaceFragment(new ListDiscountFragment());
            }
        });

        addBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                addDiscount();
            }
        });
    }

    private void initSpinners() {

        spinStationAdapter = new SpinStationAdapter(_context, android.R.layout.simple_spinner_item, businessLocationDbController.getAllStationSpinner());
        spinnerLocation.setAdapter(spinStationAdapter);

        spinCategoryAdapter = new SpinCategoryAdapter(_context, R.layout.custom_spinner_item, categoryDbController.getAllCategorySpinner());
        spinnerCategory.setAdapter(spinCategoryAdapter);

        spinBrandAdapter = new SpinBrandAdapter(_context, R.layout.custom_spinner_item, brandDbController.getAllBrandSpinner());
        spinnerBrand.setAdapter(spinBrandAdapter);

        startsAt.setText(StringFormat.populateSetDate(c.get(Calendar.YEAR), c.get(Calendar.MONTH), c.get(Calendar.DAY_OF_MONTH)));
        endsAt.setText(StringFormat.populateSetDate(c.get(Calendar.YEAR), c.get(Calendar.MONTH), c.get(Calendar.DAY_OF_MONTH)));

    }

    private void initMultiSpinnerSearch() {
        // TODO: Implement multi-selection functionality
        // Temporarily disabled due to missing MultiSpinnerSearch dependency
        // Simple implementation using regular spinner

        ArrayList<String> productNames = new ArrayList<>();
        productNames.add("Select Products");

        for (Product product : productDbController.getAllProduct()) {
            productNames.add(product.getName());
        }

        ArrayAdapter<String> adapter = new ArrayAdapter<>(_context,
            android.R.layout.simple_spinner_item, productNames);
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        multiSelectSpinnerWithSearch.setAdapter(adapter);
    }


    private void initDb() {
        discountDbController = new DiscountDbController(_context);

        discountVariationDbController = new DiscountVariationDbController(_context);

        productDbController = new ProductDbController(_context);

        businessLocationDbController = new BusinessLocationDbController(_context);

        categoryDbController = new CategoryDbController(_context);

        brandDbController = new BrandDbController(_context);
    }

    private void addDiscount() {


        if (nameDiscount.getText().toString().equals("")) {
            //     Toast.makeText(_context, _context.getResources().getString(R.string.label_select_ssupplier), Toast.LENGTH_LONG).show();
            Snackbar snackbar = Snackbar.make(linearLayout, _context.getResources().getString(R.string.label_select_namme), Snackbar.LENGTH_LONG);
            snackbar.show();
        }else if (spinnerLocation.getSelectedItemPosition() == 0) {
            Snackbar snackbar = Snackbar.make(linearLayout, _context.getResources().getString(R.string.lbl_please_select_station), Snackbar.LENGTH_LONG);
            snackbar.show();
        }else if (priority.getText().toString().equals("")) {
            Snackbar snackbar = Snackbar.make(linearLayout, _context.getResources().getString(R.string.label_plz_spriority), Snackbar.LENGTH_LONG);
            snackbar.show();
        }else if (spinnerDiscountType.getSelectedItemPosition()== 0){
            Snackbar snackbar = Snackbar.make(linearLayout, _context.getResources().getString(R.string.label_choose_seldect_discount_type), Snackbar.LENGTH_LONG);
            snackbar.show();
        }else if (discountAmnt.getText().toString().equals("")){
            Snackbar snackbar = Snackbar.make(linearLayout, _context.getResources().getString(R.string.label_selectamuuunt), Snackbar.LENGTH_LONG);
            snackbar.show();
        }else


        if ( !startsAt.getText().toString().equals("") && !endsAt.getText().toString().equals("")) {
            Discount discount = new Discount();

            discount.setName(nameDiscount.getText().toString());
            discount.setPriority(Integer.parseInt(priority.getText().toString()));
            discount.setDiscount_type(spinnerDiscountType.getSelectedItem().toString().toLowerCase());
            discount.setDiscount_amount(discountAmnt.getText().toString());
            discount.setStarts_at(startsAt.getText().toString());
            discount.setEnds_at(endsAt.getText().toString());
            discount.setApplicable_in_spg(checkSpg.isChecked() ? 1 : 0);
            discount.setIs_active(checkActive.isChecked() ? 1 : 0);
            discount.setApplicable_in_cg(0);

            Business_location businesslocation = (Business_location) spinnerLocation.getSelectedItem();
            discount.setLocation_id(businesslocation.getId());

            Category category = (Category) spinnerCategory.getSelectedItem();
            discount.setCategory_id(category.getId());

            Brand brand = (Brand) spinnerBrand.getSelectedItem();
            discount.setBrand_id(brand.getId());
            discount.setSync(NO);
            discount.setBusiness_id(1);

            int discount_id = discountDbController.insertLocal(discount);

            if (discount_id > 0) {
                if (productIds.size()>0){
                    for (int p : productIds){
                        Discount_variation discount_variation = new Discount_variation();
                        discount_variation.setDiscount_id(discount_id);
                        discount_variation.setVariation_id(p);
                        discountVariationDbController.insertLocal(discount_variation);
                    }
                }
            //    Toast.makeText(_context, getResources().getString(R.string.lbl_added_success), Toast.LENGTH_LONG).show();
                FileUtil.showDialog(_context, "Successful", getResources().getString(R.string.stock_transfer_success));

                replaceFragment(new ListDiscountFragment());
                // discountVariationDbController.insertLocal()
            }
        } else {
            Toast.makeText(_context, getResources().getString(R.string.label_all_field_required), Toast.LENGTH_LONG).show();
        }
    }


    public void replaceFragment(Fragment someFragment) {
        FragmentTransaction transaction = getFragmentManager().beginTransaction();
        transaction.replace(R.id.nav_host_fragment, someFragment);
        transaction.addToBackStack(null);
        transaction.commit();
    }

    @Override
    public void onClick(View v) {

    }

    private void initSerachEdit() {
        //TODO
        // IMPLEMENT Thread solution

//
//        dataListProduct = productDbController.getAllProduct();
//
//
//            String[] names = new String[dataListProduct.size()];
//            for (int i = 0; i < dataListProduct.size(); i++) {
//                names[i] = dataListProduct.get(i).getName();
//            }
//
//            final ArrayAdapter<String> adapter = new ArrayAdapter<String>(_context, android.R.layout.simple_list_item_1, names);
//            searchEdit.setAdapter(adapter);
//            searchEdit.setDropDownBackgroundDrawable(new ColorDrawable(_context.getResources().getColor(R.color.blue)));
//
//            searchEdit.setOnItemClickListener(new AdapterView.OnItemClickListener() {
//
//                @Override
//                public void onItemClick(AdapterView<?> parent, View arg1, int pos,
//                                        long id) {
//
//                  //  searchProduct(adapter.getItem(pos).toString());
//
//                }
//            });

    }


}