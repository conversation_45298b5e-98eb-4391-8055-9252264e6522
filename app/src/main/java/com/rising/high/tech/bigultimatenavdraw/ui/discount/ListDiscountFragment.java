package com.rising.high.tech.bigultimatenavdraw.ui.discount;

import android.content.Context;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.Spinner;
import android.widget.TextView;

import androidx.appcompat.widget.SearchView;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.gson.Gson;
import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.db.DiscountDbController;
import com.rising.high.tech.bigultimatenavdraw.model.Discount;
import com.rising.high.tech.bigultimatenavdraw.ui.discount.adapter.DiscountAdapter;
import com.rising.high.tech.bigultimatenavdraw.util.SessionManager;
import com.rising.high.tech.bigultimatenavdraw.util.serverInteraction;

import butterknife.BindView;
import butterknife.ButterKnife;

import static com.rising.high.tech.bigultimatenavdraw.util.Constant.SERVER_MASTER;

public class ListDiscountFragment extends Fragment {

    private static final String TAG = "ListDiscountFragment";
    private Context _context;


    @BindView(R.id.noItemFound)
    TextView noItemFound;

    RecyclerView recycleCustomer;
    SearchView searchEdit;
    Button btnAdd;
    Spinner spinnerType;
    DiscountDbController discountDbController;
    DiscountAdapter discountAdapter;
    SessionManager session;

    public ListDiscountFragment() {
        // Required empty public constructor
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        // Inflate the layout for this fragment
        View root = inflater.inflate(R.layout.fragment_discount_list_main, container, false);
        ButterKnife.bind(this, root);

        _context = getContext();
        session = new SessionManager(_context);

        recycleCustomer = root.findViewById(R.id.recycle_customers);
        searchEdit = root.findViewById(R.id.id_search_edit);
        spinnerType = root.findViewById(R.id.spinner_type);
        recycleCustomer = root.findViewById(R.id.recycle_customers);

        btnAdd = root.findViewById(R.id.id_add);
        discountDbController = new DiscountDbController(_context);
        discountDbController.open();
        discountAdapter = new DiscountAdapter();
        recycleCustomer.setAdapter(discountAdapter);
        recycleCustomer.setLayoutManager(new LinearLayoutManager(_context));
        discountAdapter.setData(discountDbController.getAllDiscount());

        btnAdd.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                replaceFragment(new AddDiscountsFragment());
            }
        });

        searchEdit.setQueryHint("Search Here");
        searchEdit.setOnQueryTextListener(new SearchView.OnQueryTextListener() {

            @Override
            public boolean onQueryTextSubmit(String query) {
                discountAdapter.setData(discountDbController.getDiscountLike(query));
                setItemView();
                return false;
            }

            @Override
            public boolean onQueryTextChange(String newText) {
                discountAdapter.setData(discountDbController.getDiscountLike(newText));
                setItemView();
                return false;
            }
        });

        discountAdapter.setOnDataChangeListener(new DiscountAdapter.OnDataChangeListener() {
            @Override
            public void onDataChanged(Discount transaction) {
                setItemView();
            }

            @Override
            public void onDataDeleted() {

            }
        });
        setItemView();

        checkRoles();

        return root;
    }

    private void checkRoles() {
        if (session.getBoolean(SERVER_MASTER)) {
            btnAdd.setVisibility(View.INVISIBLE);
        }
    }

    public void setItemView() {
        if (discountAdapter.getItemCount() > 0) {
            recycleCustomer.setVisibility(View.VISIBLE);
            noItemFound.setVisibility(View.GONE);
        } else {
            recycleCustomer.setVisibility(View.GONE);
            noItemFound.setVisibility(View.VISIBLE);
        }
    }

    public void replaceFragment(Fragment someFragment) {
        FragmentTransaction transaction = getFragmentManager().beginTransaction();
        transaction.replace(R.id.nav_host_fragment, someFragment);
        transaction.addToBackStack(null);
        transaction.commit();
    }

}