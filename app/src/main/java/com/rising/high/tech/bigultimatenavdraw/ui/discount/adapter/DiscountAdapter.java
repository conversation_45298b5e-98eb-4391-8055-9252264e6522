package com.rising.high.tech.bigultimatenavdraw.ui.discount.adapter;

import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.Button;
import android.widget.Spinner;
import android.widget.TextView;

import androidx.appcompat.app.AppCompatActivity;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.RecyclerView;

import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.db.BrandDbController;
import com.rising.high.tech.bigultimatenavdraw.db.CategoryDbController;
import com.rising.high.tech.bigultimatenavdraw.db.DiscountDbController;
import com.rising.high.tech.bigultimatenavdraw.db.BusinessLocationDbController;
import com.rising.high.tech.bigultimatenavdraw.db.DiscountVariationDbController;
import com.rising.high.tech.bigultimatenavdraw.db.ProductDbController;
import com.rising.high.tech.bigultimatenavdraw.model.Discount;
import com.rising.high.tech.bigultimatenavdraw.model.Discount_variation;
import com.rising.high.tech.bigultimatenavdraw.model.Product;
import com.rising.high.tech.bigultimatenavdraw.model.Product_location;
import com.rising.high.tech.bigultimatenavdraw.model.Variation_location_details;
import com.rising.high.tech.bigultimatenavdraw.ui.discount.EditDiscountsFragment;

import java.util.ArrayList;
import java.util.Collections;

public class DiscountAdapter extends RecyclerView.Adapter<DiscountAdapter.VenteViewHolder> {

    private final String TAG = this.getClass().getSimpleName();
    private ArrayList<Discount> dataList = new ArrayList<>();
    private BusinessLocationDbController businessLocationDbController;
    private CategoryDbController categoryDbController;
    private BrandDbController brandDbController;
    private DiscountDbController discountDbController;
    private DiscountVariationDbController discountVariationDbController;
    private ProductDbController productDbController;
    Context context;

    @Override
    public VenteViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        context = parent.getContext();
        businessLocationDbController = new BusinessLocationDbController(context);

        categoryDbController = new CategoryDbController(context);

        brandDbController = new BrandDbController(context);

        discountDbController = new DiscountDbController(context);
        discountVariationDbController = new DiscountVariationDbController(context);
        productDbController = new ProductDbController(context);

        return new VenteViewHolder(LayoutInflater.from(parent.getContext())
                .inflate(R.layout.discount_item_layout, parent, false));
    }

    @Override
    public void onBindViewHolder(VenteViewHolder holder, int position) {

        holder.name.setText(dataList.get(position).getName());
        holder.starts_at.setText(dataList.get(position).getStarts_at());
        holder.ends_at.setText(dataList.get(position).getEnds_at());
        holder.discount_amnt.setText(dataList.get(position).getDiscount_amount());
        holder.priority.setText(dataList.get(position).getPriority() + "");

        String brandName = brandDbController.getBrandById(dataList.get(position).getBrand_id()).getName();
        holder.brand.setText(brandName);

        String categoryName = categoryDbController.getCategoryById(dataList.get(position).getCategory_id()).getName();
        holder.category.setText(categoryName);

        ArrayList<Discount_variation> discount_variationArrayList= discountVariationDbController.getDiscountVarByDiscountId(dataList.get(position).getId());
        if (discount_variationArrayList.size()>0) {
            String product_names = "";
            for (Discount_variation discount_variation : discount_variationArrayList) {
                Product product = productDbController.getProductById(discount_variation.getVariation_id());
                product_names += product.getName() + " ,\n";
            }
            holder.products.setText(product_names.substring(0, product_names.length() - 2));
        }
        String stationName = businessLocationDbController.getStationById(dataList.get(position).getLocation_id()).getName();
        holder.location.setText(stationName);
        holder.discount_type.setText(dataList.get(position).getDiscount_type());

    }

    public void setData(ArrayList<Discount> arrayList) {
        Collections.reverse(arrayList);
        this.dataList = arrayList;
        notifyDataSetChanged();
    }

    @Override
    public int getItemCount() {
        return dataList.size();
    }

    public class VenteViewHolder extends RecyclerView.ViewHolder {
        TextView name, starts_at, ends_at, discount_amnt, priority, brand, category, products, location, discount_type;
        Button btnDelete, btnEdit;
        Spinner spinner_action;

        public VenteViewHolder(View itemView) {
            super(itemView);

            name = itemView.findViewById(R.id.name);
            starts_at = itemView.findViewById(R.id.starts_at);
            ends_at = itemView.findViewById(R.id.ends_at);
            discount_amnt = itemView.findViewById(R.id.discount_amnt);
            priority = itemView.findViewById(R.id.priority);
            brand = itemView.findViewById(R.id.brand);
            category = itemView.findViewById(R.id.category);
            products = itemView.findViewById(R.id.products);
            location = itemView.findViewById(R.id.location);
            btnDelete = itemView.findViewById(R.id.btn_delete);
            btnEdit = itemView.findViewById(R.id.btn_edit);
            discount_type = itemView.findViewById(R.id.discount_type);
            spinner_action = itemView.findViewById(R.id.spinner_action);

            spinner_action.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
                @Override
                public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                    switch (position) {
                        case 1: {
                            naviguateFragment(getAdapterPosition());
                            spinner_action.setSelection(0, true);
                            break;
                        }
                        case 2: {
                            deleteItem(getAdapterPosition());
                            spinner_action.setSelection(0, true);
                            break;
                        }
                    }
                }

                @Override
                public void onNothingSelected(AdapterView<?> parent) {

                }
            });



        }
    }

    public interface OnDataChangeListener {
        void onDataChanged(Discount transaction);
        void onDataDeleted();
    }

    OnDataChangeListener mOnDataChangeListener;

    public void setOnDataChangeListener(OnDataChangeListener onDataChangeListener) {
        mOnDataChangeListener = onDataChangeListener;
    }

    private void deleteItem(int postition) {
        //Preparing views
        // get prompts.xml view
        LayoutInflater li = LayoutInflater.from(context);
        View promptsView = li.inflate(R.layout.delete_dialog_main, null);

        androidx.appcompat.app.AlertDialog.Builder alertDialogBuilder = new androidx.appcompat.app.AlertDialog.Builder(
                context);

        alertDialogBuilder.setView(promptsView);

        final Button ButtonSave = promptsView.findViewById(R.id.btn_save);
        final Button ButtonClose = promptsView.findViewById(R.id.btn_close);

        final androidx.appcompat.app.AlertDialog mAlertDialog = alertDialogBuilder.create();
        if (mAlertDialog.getWindow() != null)
            mAlertDialog.getWindow().getAttributes().windowAnimations = R.style.SlidingDialogAnimation;
        mAlertDialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));

        ButtonClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mAlertDialog.dismiss();
            }
        });

        ButtonSave.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

                discountVariationDbController.deleteByDiscountId(dataList.get(postition).getId());
                discountDbController.deleteItem(dataList.get(postition).getId());
                dataList.remove(postition);
                mOnDataChangeListener.onDataDeleted();

                mAlertDialog.dismiss();
                notifyDataSetChanged();

            }
        });

        mAlertDialog.show();
    }

    private void naviguateFragment(int position) {
        int id= dataList.get(position).getId();
        AppCompatActivity activity = (AppCompatActivity) context;
        Bundle bundle = new Bundle();
        bundle.putInt("id", id);
        Fragment myFragment = new EditDiscountsFragment();
        myFragment.setArguments(bundle);
        activity.getSupportFragmentManager().beginTransaction().replace(R.id.nav_host_fragment, myFragment).addToBackStack(null).commit();
    }



}
