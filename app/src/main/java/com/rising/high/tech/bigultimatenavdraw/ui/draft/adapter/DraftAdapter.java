package com.rising.high.tech.bigultimatenavdraw.ui.draft.adapter;

import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.Spinner;
import android.widget.TextView;

import androidx.appcompat.app.AppCompatActivity;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.RecyclerView;

import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.db.ContactDbController;
import com.rising.high.tech.bigultimatenavdraw.db.BusinessLocationDbController;
import com.rising.high.tech.bigultimatenavdraw.db.TransactionDbController;
import com.rising.high.tech.bigultimatenavdraw.model.Contact;
import com.rising.high.tech.bigultimatenavdraw.model.Transaction;
import com.rising.high.tech.bigultimatenavdraw.ui.home.HomeFragment;
import com.rising.high.tech.bigultimatenavdraw.ui.product.EditProductFragment;

import java.util.ArrayList;
import java.util.Collections;

import static com.rising.high.tech.bigultimatenavdraw.util.Constant.ID;

public class DraftAdapter extends RecyclerView.Adapter<DraftAdapter.VenteViewHolder> {

    private final String TAG = this.getClass().getSimpleName();
    private ArrayList<Transaction> dataList = new ArrayList<>();
    private BusinessLocationDbController businessLocationDbController;
    private ContactDbController contactDbController;
    private TransactionDbController transactionDbController;
    Context context;

    @Override
    public VenteViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        context = parent.getContext();
        businessLocationDbController = new BusinessLocationDbController(context);
        businessLocationDbController.open();

        contactDbController= new ContactDbController(context);
        contactDbController.open();

        transactionDbController=new TransactionDbController(context);
        transactionDbController.open();

        return new VenteViewHolder(LayoutInflater.from(parent.getContext())
                .inflate(R.layout.draft_item_layout, parent, false));
    }

    @Override
    public void onBindViewHolder(VenteViewHolder holder, int position) {
        holder.dateTxt.setText(dataList.get(position).getTransaction_date());
        holder.factureNo.setText(dataList.get(position).getRef_no());
        String stationName = businessLocationDbController.getStationById(dataList.get(position).getLocation_id()).getName();
        holder.locationTxt.setText(stationName);
        Contact contact=contactDbController.getCustomerById(dataList.get(position).getContact_id());
        holder.id_customr_name.setText(contact.getName());
        holder.payementStatus.setText(dataList.get(position).getPayment_status());
        holder.montantTotal.setText(dataList.get(position).getFinal_total() + " $");
    }

    public void setData(ArrayList<Transaction> arrayList) {
        Collections.reverse(arrayList);
        this.dataList = arrayList;
        notifyDataSetChanged();
    }

    @Override
    public int getItemCount() {
        return dataList.size();
    }

    public class VenteViewHolder extends RecyclerView.ViewHolder {
        TextView dateTxt, factureNo, locationTxt, payementStatus, montantTotal, id_customr_name;
        Spinner spinner_action;
        LinearLayout  container;

        public VenteViewHolder(View itemView) {
            super(itemView);

            dateTxt = itemView.findViewById(R.id.id_date);
            factureNo = itemView.findViewById(R.id.facture_no);
            locationTxt = itemView.findViewById(R.id.location_txt);
            payementStatus = itemView.findViewById(R.id.payement_status);
            montantTotal = itemView.findViewById(R.id.montant_total);
            container = itemView.findViewById(R.id.container);
            id_customr_name = itemView.findViewById(R.id.id_customr_name);
            spinner_action = itemView.findViewById(R.id.spinner_action);

            spinner_action.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
                @Override
                public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                    switch (position) {
                        case 1: {
                            if (mOnDataChangeListener != null) {
                                mOnDataChangeListener.onDataChanged(dataList.get(getAdapterPosition()));
                            }
                            notifyItemChanged(getAdapterPosition());
                            break;
                        }

                        case 2: {
                            deleteItem(getAdapterPosition());
                            break;
                        }
                        case 3: {
                            edit(getAdapterPosition());
                            break;
                        }
                    }
                    spinner_action.setSelection(0, true);

                }

                @Override
                public void onNothingSelected(AdapterView<?> parent) {

                }
            });

        }
    }

    private void edit(Integer position){
        AppCompatActivity activity = (AppCompatActivity) context;
        Bundle bundle = new Bundle();
        bundle.putInt(ID, dataList.get(position).getId());
        Fragment myFragment = new HomeFragment();
        myFragment.setArguments(bundle);
        activity.getSupportFragmentManager().beginTransaction().replace(R.id.nav_host_fragment, myFragment).addToBackStack(null).commit();
    }

    private void deleteItem(int position) {
        //Preparing views
        // get prompts.xml view
        LayoutInflater li = LayoutInflater.from(context);
        View promptsView = li.inflate(R.layout.delete_dialog_main, null);

        androidx.appcompat.app.AlertDialog.Builder alertDialogBuilder = new androidx.appcompat.app.AlertDialog.Builder(
                context);

        alertDialogBuilder.setView(promptsView);

        final Button ButtonSave = promptsView.findViewById(R.id.btn_save);
        final Button ButtonClose = promptsView.findViewById(R.id.btn_close);

        final androidx.appcompat.app.AlertDialog mAlertDialog = alertDialogBuilder.create();
        if (mAlertDialog.getWindow() != null)
            mAlertDialog.getWindow().getAttributes().windowAnimations = R.style.SlidingDialogAnimation;
        mAlertDialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));


        ButtonClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mAlertDialog.dismiss();
            }
        });

        ButtonSave.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

                transactionDbController.deleteTransaction(dataList.get(position).getId());

                //                for (Purchase_line purchaseLine : purchaseLineDbController.getPurchaseLineByTransaction(dataList.get(position).getId())) {
//                    purchaseLineDbController.deletePurchase(purchaseLine.getId());
//                }
                dataList.remove(position);
  
                mAlertDialog.dismiss();
                notifyDataSetChanged();
            }
        });

        mAlertDialog.show();
    }

    public interface OnDataChangeListener {
        void onDataChanged(Transaction transaction);
    }

    OnDataChangeListener mOnDataChangeListener;

    public void setOnDataChangeListener(OnDataChangeListener onDataChangeListener) {
        mOnDataChangeListener = onDataChangeListener;
    }

}
