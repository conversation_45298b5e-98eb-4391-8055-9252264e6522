package com.rising.high.tech.bigultimatenavdraw.ui.draft.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.model.Sell_lines;

import java.util.ArrayList;

public class SubDraftAdapter extends RecyclerView.Adapter<SubDraftAdapter.sub_vente_view_holder> {
    private ArrayList<Sell_lines> dataList = new ArrayList<>();
    Context context;

    @Override
    public sub_vente_view_holder onCreateViewHolder(ViewGroup parent, int viewType) {
        context = parent.getContext();
        return new sub_vente_view_holder(LayoutInflater.from(parent.getContext())
                .inflate(R.layout.sub_vente_item_, parent, false));
    }

    @Override
    public void onBindViewHolder(sub_vente_view_holder holder, int position) {
        holder.product_name.setText(dataList.get(position).getProduct_name());
        holder.quantity.setText(dataList.get(position).getQuantity()+"");
        holder.unit_price.setText(dataList.get(position).getUnit_price());
        float total = dataList.get(position).getQuantity() * Float.parseFloat(dataList.get(position).getUnit_price());
        holder.total.setText(""+total);

      //  holder.quantity.setText("zakarya");

    }

    public void setData(ArrayList<Sell_lines> arrayList) {
        this.dataList = arrayList;
        notifyDataSetChanged();
    }

    @Override
    public int getItemCount() {
        return dataList.size();
    }

    public class sub_vente_view_holder extends RecyclerView.ViewHolder {
        TextView product_name, quantity, unit_price, total;
        public sub_vente_view_holder(View itemView) {
            super(itemView);
            product_name = itemView.findViewById(R.id.product_name);
            quantity = itemView.findViewById(R.id.quantity);
            unit_price = itemView.findViewById(R.id.unit_price);
            total = itemView.findViewById(R.id.total);
        }
    }
}
