package com.rising.high.tech.bigultimatenavdraw.ui.expense;

import android.app.DatePickerDialog;
import android.content.Context;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.CompoundButton;
import android.widget.DatePicker;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;

import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.db.ContactDbController;
import com.rising.high.tech.bigultimatenavdraw.db.ExpenseCategoriesDbController;
import com.rising.high.tech.bigultimatenavdraw.db.BusinessLocationDbController;
import com.rising.high.tech.bigultimatenavdraw.db.TaxRatesDbController;
import com.rising.high.tech.bigultimatenavdraw.db.TransactionDbController;
import com.rising.high.tech.bigultimatenavdraw.db.TransactionPayementDbController;
import com.rising.high.tech.bigultimatenavdraw.db.UserDbController;
import com.rising.high.tech.bigultimatenavdraw.model.Business_location;
import com.rising.high.tech.bigultimatenavdraw.model.Contact;
import com.rising.high.tech.bigultimatenavdraw.model.Expense_category;
import com.rising.high.tech.bigultimatenavdraw.model.Tax_rates;
import com.rising.high.tech.bigultimatenavdraw.model.Transaction;
import com.rising.high.tech.bigultimatenavdraw.model.User;
import com.rising.high.tech.bigultimatenavdraw.ui.adapter.SpinContactAdapter;
import com.rising.high.tech.bigultimatenavdraw.ui.adapter.SpinExpenseCategoryAdapter;
import com.rising.high.tech.bigultimatenavdraw.ui.adapter.SpinStationAdapter;
import com.rising.high.tech.bigultimatenavdraw.ui.adapter.SpinTaxRatesAdapter;
import com.rising.high.tech.bigultimatenavdraw.ui.adapter.SpinUserAdapter;
import com.rising.high.tech.bigultimatenavdraw.ui.stocktransfers.StockTransferFragment;
import com.rising.high.tech.bigultimatenavdraw.util.FileUtil;
import com.rising.high.tech.bigultimatenavdraw.util.SessionManager;
import com.rising.high.tech.bigultimatenavdraw.util.StringFormat;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Objects;

//import butterknife.BindView;
//import butterknife.ButterKnife;

import static com.rising.high.tech.bigultimatenavdraw.util.Constant.DUE;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.EXPENSE;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.EXPENSE_REFUND;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.FINAL;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.ID;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.NO;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.PAID;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.PARTIAL;

public class AddExpenseFragment extends Fragment {
    private static final String TAG = "AddExpenseFragment";
    private Context _context;
    final Calendar c = Calendar.getInstance();

    private BusinessLocationDbController businessLocationDbController;
    private ExpenseCategoriesDbController expenseCategoriesDbController;

    private TransactionDbController transactionDbController;
    private TransactionPayementDbController transactionPayementDbController;
    private TaxRatesDbController taxRatesDbController;

    SpinStationAdapter spinStationAdapter;
    private SpinContactAdapter spinContactAdapter;
    private SpinUserAdapter spinUserAdapter;
    private ContactDbController contactDbController;
    private UserDbController userDbController;

    private SpinExpenseCategoryAdapter spinExpenseCategoryAdapter;
    private SpinTaxRatesAdapter spinTaxRatesAdapter;

    @BindView(R.id.spinner_location)
    Spinner spinnerStation;
    @BindView(R.id.spinner_expense_category)
    Spinner spinnerExpenseCategory;
    @BindView(R.id.date_expense)
    EditText dateExpense;
    @BindView(R.id.spinner_expense_for_contact)
    Spinner spinnerExpenseForContact;
    @BindView(R.id.spinner_expense_for)
    Spinner spinnerExpenseFor;
    @BindView(R.id.add_btn)
    Button addBtn;
    @BindView(R.id.id_back)
    Button btnBack;
    @BindView(R.id.refund_container)
    LinearLayout refundContainer;
    @BindView(R.id.amount_txt)
    EditText amountTxt;
    @BindView(R.id.total_amount_txt)
    EditText totalAmountTxt;
    @BindView(R.id.expense_note)
    EditText expenseNote;
    @BindView(R.id.paid_on_txt)
    EditText paidOnTxt;
    @BindView(R.id.payement_note)
    EditText payementNote;
    @BindView(R.id.recurrent_interval)
    EditText recurrentInterval;
    @BindView(R.id.reference_no)
    EditText referenceNo;
    @BindView(R.id.spin_payment_method)
    Spinner spinPaymentMethod;
    @BindView(R.id.spinner_recu_interval_type)
    Spinner spinnerRecuIntervalType;
    @BindView(R.id.checbok_refund)
    CheckBox checkboxRefund;
    @BindView(R.id.checbok_is_reccuring)
    CheckBox checbokIsReccuring;
    @BindView(R.id.spinner_applicanle_tax)
    Spinner spinnerApplicanleTax;
    @BindView(R.id.recur_repitition)
    EditText recurRepitition;
    @BindView(R.id.total_due)
    TextView totalDue;
    @BindView(R.id.paymenent_container)
    LinearLayout paymenentContainer;
    private Integer userId;
    SessionManager session;
    private boolean isEdit = false;
    private Integer indexId = 0;


    public AddExpenseFragment(boolean isEdit) {
        // Required empty public constructor
        this.isEdit = isEdit;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        // Inflate the layout for this fragment
        View root = inflater.inflate(R.layout.activity_add_depense, container, false);
        ButterKnife.bind(this, root);
        _context = getContext();
        session = new SessionManager(_context);
        if (isEdit) {
            Bundle args = getArguments();
            indexId = args.getInt(ID, 0);
        }

        initDB();
        initSpinners();
        initListners();
        setData();

        return root;
    }

    private void setData() {
        if (isEdit) {
            Transaction transactionExpense = transactionDbController.getTransactionById(indexId);
            addBtn.setText(_context.getResources().getString(R.string.label_updatee));
            Business_location business_location = businessLocationDbController.getStationById(transactionExpense.getLocation_id());
            int spinPositionLocation = spinStationAdapter.getPosition(business_location);
            spinnerStation.setSelection(spinPositionLocation);

            if (transactionExpense.getExpense_category_id() > 0) {
                Expense_category expense_category = expenseCategoriesDbController.getExpense_categoryById(transactionExpense.getExpense_category_id());
                int spinPositionEC = spinExpenseCategoryAdapter.getPosition(expense_category);
                spinnerExpenseCategory.setSelection(spinPositionEC);
            }
            referenceNo.setText(transactionExpense.getRef_no());
            dateExpense.setText(transactionExpense.getTransaction_date());

            User user = userDbController.getUsersById(transactionExpense.getPayment_for());
            int spinPositionExpenseFor = spinUserAdapter.getPosition(user);
            spinnerExpenseFor.setSelection(spinPositionExpenseFor);

            Contact contact = contactDbController.getCustomerById(transactionExpense.getContact_id());
            int spinPositionContact = spinContactAdapter.getPosition(contact);
            spinnerExpenseForContact.setSelection(spinPositionContact);

            Tax_rates tax_rates = taxRatesDbController.getTax_ratesById(transactionExpense.getTax_id());
            int spinPositionTaxId = spinTaxRatesAdapter.getPosition(tax_rates);
            spinnerApplicanleTax.setSelection(spinPositionTaxId);

            totalAmountTxt.setText(transactionExpense.getFinal_total());
            expenseNote.setText(transactionExpense.getAdditional_notes());

            checkboxRefund.setChecked(transactionExpense.getType().matches(EXPENSE_REFUND) ? true : false);
            refundContainer.setVisibility(transactionExpense.getType().matches(EXPENSE_REFUND) ? View.GONE : View.VISIBLE);

            checbokIsReccuring.setChecked(transactionExpense.getIs_recurring() == 1 ? true : false);
            recurrentInterval.setText(transactionExpense.getRecur_interval() + "");

            if (transactionExpense.getRecur_interval_type() != null) {
                int indexP = Arrays.asList(getResources().getStringArray(R.array.array_duration)).indexOf(transactionExpense.getRecur_interval_type());
                spinnerRecuIntervalType.setSelection(indexP);
            }
            ;

            recurRepitition.setText(transactionExpense.getRecur_repetitions() + "");
            paymenentContainer.setVisibility(View.GONE);
        }
    }

    private void initListners() {
        userId = (int) session.getUserDetails().get(session.ID_USER);

        checkboxRefund.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                refundContainer.setVisibility(isChecked ? View.GONE : View.VISIBLE);
            }
        });

        dateExpense.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                DatePickerDialog datePickerDialog = new DatePickerDialog(_context,
                        new DatePickerDialog.OnDateSetListener() {
                            @Override
                            public void onDateSet(DatePicker datePicker, int year, int month, int day) {
                                String myFormat = StringFormat.populateSetFullDate(year, month, day, c.get(Calendar.HOUR), c.get(Calendar.MINUTE));
                                dateExpense.setText(myFormat);
                            }
                        }, c.get(Calendar.YEAR), c.get(Calendar.MONTH), c.get(Calendar.DAY_OF_MONTH));
                datePickerDialog.show();
            }
        });

        paidOnTxt.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                DatePickerDialog datePickerDialog = new DatePickerDialog(_context,
                        new DatePickerDialog.OnDateSetListener() {
                            @Override
                            public void onDateSet(DatePicker datePicker, int year, int month, int day) {
                                String myFormat = StringFormat.populateSetFullDate(year, month, day, c.get(Calendar.HOUR), c.get(Calendar.MINUTE));
                                paidOnTxt.setText(myFormat);
                            }
                        }, c.get(Calendar.YEAR), c.get(Calendar.MONTH), c.get(Calendar.DAY_OF_MONTH));
                datePickerDialog.show();
            }
        });

        btnBack.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                replaceFragment(new ListExpenseFragment());
            }
        });


        amountTxt.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                if (s.length() != 0) {
                    Float total_amount = Float.parseFloat(totalAmountTxt.getText().toString().equals("") ? "0" : totalAmountTxt.getText().toString());
                    Float amount = 0.f;
                    if (!amountTxt.getText().toString().matches("")) {
                        amount = Float.parseFloat(amountTxt.getText().toString());
                    }
                    totalDue.setText((total_amount - amount) + "");
                }
            }

            @Override
            public void afterTextChanged(Editable s) {

            }
        });

        totalAmountTxt.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                if (s.length() != 0) {
                    Float total_amount = Float.parseFloat(totalAmountTxt.getText().toString());
                    Float amount = Float.parseFloat(amountTxt.getText().toString().equals("") ? "0" : amountTxt.getText().toString());
                    totalDue.setText((total_amount - amount) + "");
                }
            }

            @Override
            public void afterTextChanged(Editable s) {

            }
        });

        addBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

                if (
                        spinnerStation.getSelectedItemPosition() != 0
                                && !dateExpense.getText().toString().equals("")
                                //    && !amountTxt.getText().toString().equals("")
                                //     && !recurrentInterval.getText().toString().equals("")
                                && !totalAmountTxt.getText().toString().equals("")
                    //  && !paidOnTxt.getText().toString().equals("")
                ) {
                    Transaction transaction = new Transaction();
                    transaction.setBusiness_id(1);
                    Business_location businesslocation = (Business_location) spinnerStation.getSelectedItem();
                    transaction.setLocation_id(businesslocation.getId());
                    transaction.setType(checkboxRefund.isChecked() ? EXPENSE_REFUND : EXPENSE);
                    transaction.setStatus(FINAL);
                    Float total_amount = Float.parseFloat(totalAmountTxt.getText().toString());

                    Float amount = Float.parseFloat(amountTxt.getText().toString().matches("") ? "0" : amountTxt.getText().toString());

                    transaction.setPayment_status(total_amount <= amount ? PAID : (amount == 0 ? DUE : PARTIAL));

                    Contact contact = (Contact) spinnerExpenseForContact.getSelectedItem();
                    transaction.setContact_id(contact.getId());

                    User user = (User) spinnerExpenseFor.getSelectedItem();
                    transaction.setPayment_for(user.getId());

                    transaction.setRef_no(referenceNo.getText().toString());
                    transaction.setTransaction_date(dateExpense.getText().toString());
                    transaction.setTotal_before_tax(totalAmountTxt.getText().toString());

                    Tax_rates tax_rates = (Tax_rates) spinnerApplicanleTax.getSelectedItem();
                    transaction.setTax_id(tax_rates.getId());
                    transaction.setTax_amount(tax_rates.getAmount());
                    transaction.setAdditional_notes(expenseNote.getText().toString());
                    transaction.setFinal_total(totalAmountTxt.getText().toString());

                    Expense_category expense_category = (Expense_category) spinnerExpenseCategory.getSelectedItem();
                    transaction.setExpense_category_id(expense_category.getId());

                    transaction.setCreated_by(userId);
                    transaction.setIs_recurring(checbokIsReccuring.isChecked() ? 1 : 0);
                    int recInt = Integer.parseInt(recurrentInterval.getText().toString().matches("") ? "0" : recurrentInterval.getText().toString());
                    transaction.setRecur_interval(recInt);
                    transaction.setRecur_interval_type(spinnerRecuIntervalType.getSelectedItemPosition() != 0 ? spinnerRecuIntervalType.getSelectedItem().toString() : null);
                    transaction.setRecur_repetitions(recurRepitition.getText().toString().equals("") ? 0 : Integer.parseInt(recurRepitition.getText().toString()));
                    transaction.setIs_sync(NO);
                    int idInserted=0;
                    if (isEdit) {
                        transaction.setId(indexId);
                        idInserted = transactionDbController.updateTransaction(transaction);
                    } else {
                        transaction.setRef_no(StringFormat.generateRefExpenseNo(_context));
                        idInserted = transactionDbController.insertLocal(transaction);
                        if (idInserted > 0) {
                            if (amount > 0) {
                                transaction.setTransaction_id(idInserted);
                                transaction.setAmount(Float.parseFloat(amountTxt.getText().toString()));
                                transaction.setMethod(spinPaymentMethod.getSelectedItem().toString());
                                transaction.setPaid_on(dateExpense.getText().toString());
                                Contact contact1 = (Contact) spinnerExpenseForContact.getSelectedItem();
                                transaction.setPayment_for(contact1.getId());
                                transaction.setNote(payementNote.getText().toString());
                                int idPayment = transactionPayementDbController.insertLocal(transaction);
//                                if (idPayment > 0) {
//                                    Toast.makeText(_context, getResources().getString(R.string.lbl_added_success), Toast.LENGTH_LONG).show();
//                                    replaceFragment(new ListExpenseFragment());
//                                } else {
//                                    Toast.makeText(_context, "Error insert ...", Toast.LENGTH_LONG).show();
//                                }
                            }
//                            else {
//                                Toast.makeText(_context, getResources().getString(R.string.lbl_added_success), Toast.LENGTH_LONG).show();
//                                replaceFragment(new ListExpenseFragment());
//                            }
                        } else {
                            Toast.makeText(_context, "Error insert ...", Toast.LENGTH_LONG).show();
                        }
                    }
                    if (idInserted > 0) {
                        FileUtil.showDialog(_context, "Successful", getResources().getString(R.string.stock_expense_success));
                        replaceFragment(new ListExpenseFragment());
                    }

                } else {
                    Toast.makeText(_context, _context.getResources().getString(R.string.label_all_field_required), Toast.LENGTH_LONG).show();
                }
            }
        });

        dateExpense.setText(StringFormat.populateSetFullDate(c.get(Calendar.YEAR), c.get(Calendar.MONTH), c.get(Calendar.DAY_OF_MONTH), c.get(Calendar.HOUR), c.get(Calendar.MINUTE)));
        paidOnTxt.setText(StringFormat.populateSetFullDate(c.get(Calendar.YEAR), c.get(Calendar.MONTH), c.get(Calendar.DAY_OF_MONTH), c.get(Calendar.HOUR), c.get(Calendar.MINUTE)));


    }

    private void initDB() {
        businessLocationDbController = new BusinessLocationDbController(_context);
        businessLocationDbController.open();

        transactionDbController = new TransactionDbController(_context);
        transactionDbController.open();

        expenseCategoriesDbController = new ExpenseCategoriesDbController(_context);
        expenseCategoriesDbController.open();

        contactDbController = new ContactDbController(_context);
        userDbController = new UserDbController(_context);

        transactionPayementDbController = new TransactionPayementDbController(_context);
        transactionPayementDbController.open();

        taxRatesDbController = new TaxRatesDbController(_context);
        taxRatesDbController.open();
    }

    private void initSpinners() {
        spinContactAdapter = new SpinContactAdapter(_context, android.R.layout.simple_spinner_item, contactDbController.getSpinContacts());
        spinnerExpenseForContact.setAdapter(spinContactAdapter);

        spinUserAdapter = new SpinUserAdapter(_context, android.R.layout.simple_spinner_item, userDbController.getSpinUsers());
        spinnerExpenseFor.setAdapter(spinUserAdapter);

        spinStationAdapter = new SpinStationAdapter(_context, android.R.layout.simple_spinner_item, businessLocationDbController.getAllStationSpinner());
        spinnerStation.setAdapter(spinStationAdapter);

        spinExpenseCategoryAdapter = new SpinExpenseCategoryAdapter(_context, android.R.layout.simple_spinner_item, expenseCategoriesDbController.getSpinExpenseCategories());
        spinnerExpenseCategory.setAdapter(spinExpenseCategoryAdapter);

        spinTaxRatesAdapter = new SpinTaxRatesAdapter(_context, android.R.layout.simple_spinner_item, taxRatesDbController.getAllTax_ratesSpinner());
        spinnerApplicanleTax.setAdapter(spinTaxRatesAdapter);

    }

    public void replaceFragment(Fragment someFragment) {
        FragmentTransaction transaction = getFragmentManager().beginTransaction();
        transaction.replace(R.id.nav_host_fragment, someFragment);
        transaction.addToBackStack(null);
        transaction.commit();
    }

}