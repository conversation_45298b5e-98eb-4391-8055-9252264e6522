package com.rising.high.tech.bigultimatenavdraw.ui.expense.adapter;

import android.app.DatePickerDialog;
import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.Button;
import android.widget.DatePicker;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.db.ContactDbController;
import com.rising.high.tech.bigultimatenavdraw.db.ExpenseCategoriesDbController;
import com.rising.high.tech.bigultimatenavdraw.db.BusinessLocationDbController;
import com.rising.high.tech.bigultimatenavdraw.db.TransactionDbController;
import com.rising.high.tech.bigultimatenavdraw.db.TransactionPayementDbController;
import com.rising.high.tech.bigultimatenavdraw.model.Contact;
import com.rising.high.tech.bigultimatenavdraw.model.Expense_category;
import com.rising.high.tech.bigultimatenavdraw.model.Business_location;
import com.rising.high.tech.bigultimatenavdraw.model.Transaction;
import com.rising.high.tech.bigultimatenavdraw.ui.expense.AddExpenseFragment;
import com.rising.high.tech.bigultimatenavdraw.ui.expense.EditExpenseFragment;
import com.rising.high.tech.bigultimatenavdraw.ui.purchase.adapter.SubPaymentAdapter;
import com.rising.high.tech.bigultimatenavdraw.util.SessionManager;
import com.rising.high.tech.bigultimatenavdraw.util.StringFormat;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.HashMap;

import static com.rising.high.tech.bigultimatenavdraw.util.Constant.DUE;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.PAID;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.PARTIAL;

public class DepenseAdapter extends RecyclerView.Adapter<DepenseAdapter.VenteViewHolder> {
    private static final String TAG = "DepenseAdapter";

    private ArrayList<Transaction> dataList = new ArrayList<>();
    private BusinessLocationDbController businessLocationDbController;
    private ExpenseCategoriesDbController expenseCategoriesDbController;
    private TransactionPayementDbController transactionPayementDbController;
    private ContactDbController contactDbController;
    private TransactionDbController transactionDbController;
    private SessionManager session;
    private HashMap<String, Object> user;
    Context context;
    private SubExpenseeAdapter subPaymentAdapter;

    final Calendar c = Calendar.getInstance();

    @Override
    public VenteViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        context = parent.getContext();

        session = new SessionManager(context);
        user = session.getUserDetails();

        businessLocationDbController = new BusinessLocationDbController(context);

        expenseCategoriesDbController = new ExpenseCategoriesDbController(context);

        transactionPayementDbController = new TransactionPayementDbController(context);

        contactDbController = new ContactDbController(context);

        transactionDbController = new TransactionDbController(context);

        subPaymentAdapter = new SubExpenseeAdapter();

        subPaymentAdapter.setOnDataChangeListener(new SubExpenseeAdapter.OnDataChangeListener() {
            @Override
            public void onDataChanged() {
                notifyDataSetChanged();
            }
        });


        return new VenteViewHolder(LayoutInflater.from(parent.getContext())
                .inflate(R.layout.depense_item_layout, parent, false));
    }

    @Override
    public void onBindViewHolder(VenteViewHolder holder, int position) {

        holder.dateTxt.setText(dataList.get(position).getTransaction_date());

        String stationName = businessLocationDbController.getStationById(dataList.get(position).getLocation_id()).getName();
        holder.locationTxt.setText(stationName);
        holder.payementStatus.setText(dataList.get(position).getPayment_status());
        if (dataList.get(position).getPayment_status().equals(PARTIAL)) {
            holder.payementStatus.setBackground(context.getResources().getDrawable(R.drawable.rounded_btn_wihte_blue));
            holder.payementStatus.setTextColor(Color.WHITE);
        } else if (dataList.get(position).getPayment_status().equals(PAID)) {
            holder.payementStatus.setBackground(context.getResources().getDrawable(R.drawable.rounded_green_bg));
            holder.payementStatus.setTextColor(Color.WHITE);
        } else if (dataList.get(position).getPayment_status().equals(DUE)) {
            holder.payementStatus.setBackground(context.getResources().getDrawable(R.drawable.rounded_orange_bg));
            holder.payementStatus.setTextColor(Color.WHITE);
        }

        holder.montantTotal.setText(dataList.get(position).getFinal_total() + " " + user.get(session.KEY_SYMBOL));
        holder.reference_no.setText(dataList.get(position).getRef_no());
        holder.reccuring_details.setText("");

        Expense_category expense_category = expenseCategoriesDbController.getExpense_categoryById(dataList.get(position).getExpense_category_id());
        holder.expense_catgeroy.setText(expense_category.getName());

        Float total_amount = 0.f;
        for (Transaction transaction : transactionPayementDbController.getAllTransactionById(dataList.get(position).getId())) {
            total_amount += transaction.getAmount();
        }
        holder.payment_due.setText(total_amount + " " + user.get(session.KEY_SYMBOL));

        Contact contact = contactDbController.getCustomerById(dataList.get(position).getContact_id());
        holder.contact_txt.setText(contact.getName());

        holder.expense_note.setText(dataList.get(position).getAdditional_notes());
        holder.added_by.setText(String.valueOf(dataList.get(position).getCreated_by()));

        //      holder.createdBy.setText(dataList.get(position).getCreated_by());
    }

    public void setData(ArrayList<Transaction> arrayList) {
        Collections.reverse(arrayList);
        this.dataList = arrayList;
        notifyDataSetChanged();
    }
    public ArrayList<Transaction> getData() {
        return this.dataList;
    }

    @Override
    public int getItemCount() {
        return dataList.size();
    }

    public class VenteViewHolder extends RecyclerView.ViewHolder {
        TextView dateTxt, locationTxt, payementStatus, montantTotal, reference_no, added_by, reccuring_details, expense_catgeroy, payment_due, expense_for, contact_txt, expense_note;
        LinearLayout container;
        Spinner spinnerAction;

        public VenteViewHolder(View itemView) {
            super(itemView);

            dateTxt = itemView.findViewById(R.id.id_date);
            reference_no = itemView.findViewById(R.id.reference_no);
            locationTxt = itemView.findViewById(R.id.location_txt);
            payementStatus = itemView.findViewById(R.id.payement_status);
            montantTotal = itemView.findViewById(R.id.montant_total);
            container = itemView.findViewById(R.id.container);
            reccuring_details = itemView.findViewById(R.id.reccuring_details);
            expense_catgeroy = itemView.findViewById(R.id.expense_catgeroy);
            payment_due = itemView.findViewById(R.id.payment_due);
            expense_for = itemView.findViewById(R.id.expense_for);
            expense_note = itemView.findViewById(R.id.expense_note);
            contact_txt = itemView.findViewById(R.id.contact_txt);
            added_by = itemView.findViewById(R.id.added_by);
            spinnerAction = itemView.findViewById(R.id.spinner_action);

            spinnerAction.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
                @Override
                public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                    switch (position) {
                        case 1: {
                            edit(dataList.get(getAdapterPosition()).getId());
                            break;
                        }
                        case 2: {
                            deleteItem(getAdapterPosition());
                            break;
                        }
                        case 3: {
                            addPayement(getAdapterPosition());
                            break;
                        }
                        case 4: {
                            viewPayement(getAdapterPosition());
                            break;
                        }
                    }
                    spinnerAction.setSelection(0, true);

                }

                @Override
                public void onNothingSelected(AdapterView<?> parent) {

                }
            });
        }
    }

    private void viewPayement(int position) {
        //Preparing views
        // get prompts.xml view

        LayoutInflater li = LayoutInflater.from(context);
        View promptsView = li.inflate(R.layout.view_payement_eexpensee_dialog_main, null);

        androidx.appcompat.app.AlertDialog.Builder alertDialogBuilder = new androidx.appcompat.app.AlertDialog.Builder(
                context);

        alertDialogBuilder.setView(promptsView);

     //   final Button ButtonSave = promptsView.findViewById(R.id.btn_save);
        final ImageView ButtonClose = promptsView.findViewById(R.id.btn_close);

        final TextView supplier_name = promptsView.findViewById(R.id.supplier_name);
        final TextView supplier_phone = promptsView.findViewById(R.id.supplier_phone);
        final TextView business_location = promptsView.findViewById(R.id.business_location);
        final RecyclerView recyclerView = promptsView.findViewById(R.id.recycle_sub_vente);
        final TextView label_reference_num = promptsView.findViewById(R.id.label_reference_num);
        final TextView purchase_date = promptsView.findViewById(R.id.purchase_date);
        final TextView purchase_status = promptsView.findViewById(R.id.purchase_status);
        final TextView payement_status = promptsView.findViewById(R.id.payement_status);

        recyclerView.setAdapter(subPaymentAdapter);
        recyclerView.setLayoutManager(new LinearLayoutManager(context));

        ArrayList<Transaction> purchaseLines = transactionPayementDbController.getAllTransactionById(dataList.get(position).getId());
        subPaymentAdapter.setData(purchaseLines);
        subPaymentAdapter.setOnDataChangeListener(new SubExpenseeAdapter.OnDataChangeListener() {
            @Override
            public void onDataChanged() {
                dataList.get(position).setPayment_status(transactionDbController.getTransactionById(dataList.get(position).getId()).getPayment_status());
                notifyItemChanged(position);
            }
        });

        Contact contact = contactDbController.getCustomerById(dataList.get(position).getContact_id());

        supplier_name.setText(contact.getName());
        supplier_phone.setText(contact.getMobile());
        Business_location businesslocation = businessLocationDbController.getStationById(dataList.get(position).getLocation_id());

        business_location.setText(businesslocation.getName() + " " + businesslocation.getCity() + " " + businesslocation.getCountry());
        label_reference_num.setText(dataList.get(position).getRef_no());
        purchase_date.setText(dataList.get(position).getTransaction_date());
        purchase_status.setText(dataList.get(position).getStatus());
        payement_status.setText(dataList.get(position).getPayment_status());

        //  final ImageView image_product = promptsView.findViewById(R.id.image_product);

        final androidx.appcompat.app.AlertDialog mAlertDialog = alertDialogBuilder.create();
        if (mAlertDialog.getWindow() != null)
            mAlertDialog.getWindow().getAttributes().windowAnimations = R.style.SlidingDialogAnimation;
        mAlertDialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));


        ButtonClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mAlertDialog.dismiss();
            }
        });


        mAlertDialog.show();
    }


    private void addPayement(int position) {
        LayoutInflater li = LayoutInflater.from(context);
        View promptsView = li.inflate(R.layout.add_payment_purchase_dialog_main, null);
        androidx.appcompat.app.AlertDialog.Builder alertDialogBuilder = new androidx.appcompat.app.AlertDialog.Builder(context);

        alertDialogBuilder.setView(promptsView);

        final Button ButtonSave = promptsView.findViewById(R.id.btn_save);
        final ImageView ButtonClose = promptsView.findViewById(R.id.btn_close);

        final TextView supplier_name = promptsView.findViewById(R.id.supplier_name);
        final TextView supplier_phone = promptsView.findViewById(R.id.supplier_phone);
        final TextView business_location = promptsView.findViewById(R.id.business_location);
        final TextView total_amount = promptsView.findViewById(R.id.total_amount);
        final TextView payment_note = promptsView.findViewById(R.id.payment_note);
        final TextView purchase_note = promptsView.findViewById(R.id.purchase_note);
        final EditText amount_txt = promptsView.findViewById(R.id.amount_txt);
        final EditText paid_on_txt = promptsView.findViewById(R.id.paid_on_txt);
        final Spinner spin_payment_method = promptsView.findViewById(R.id.spin_payment_method);

        Contact contact = contactDbController.getCustomerById(dataList.get(position).getContact_id());
        Transaction payment = transactionPayementDbController.getTransactionById(dataList.get(position).getId());
        Business_location businesslocation = businessLocationDbController.getStationById(dataList.get(position).getLocation_id());

        supplier_name.setText(contact.getName());
        supplier_phone.setText(contact.getMobile());
        business_location.setText(businesslocation.getName() + " " + businesslocation.getCity() + " " + businesslocation.getCountry());
        total_amount.setText(dataList.get(position).getFinal_total() + " " + user.get(session.KEY_SYMBOL));
     //   purchase_note.setText(dataList.get(position).getNote());
        Float final_total = Float.parseFloat(dataList.get(position).getFinal_total());


        /**
         * get amount from all payments
         */
        Float amount = 0.f;
        for (Transaction transaction : transactionPayementDbController.getAllTransactionById(dataList.get(position).getId())) {
            amount += transaction.getAmount();
        }

        Float due = (final_total - amount);
        amount_txt.setText(due + "");
       // paid_on_txt.setText(payment.getPaid_on());
        paid_on_txt.setText(StringFormat.populateSetFullDate(c.get(Calendar.YEAR), c.get(Calendar.MONTH), c.get(Calendar.DAY_OF_MONTH), c.get(Calendar.HOUR), c.get(Calendar.MINUTE)));

        //  payment_note.setText(payment.getNote());

        if (dataList.get(position).getMethod() != null) {
            int indexP = Arrays.asList(context.getResources().getStringArray(R.array.payment_method_array)).indexOf(dataList.get(position).getMethod());
            spin_payment_method.setSelection(indexP);
        }

        /**
         * init button click listner
         */
        paid_on_txt.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                DatePickerDialog datePickerDialog = new DatePickerDialog(context,
                        new DatePickerDialog.OnDateSetListener() {
                            @Override
                            public void onDateSet(DatePicker datePicker, int year, int month, int day) {
                                String myFormat = StringFormat.populateSetFullDate(year, month, day, c.get(Calendar.HOUR), c.get(Calendar.MINUTE));
                                paid_on_txt.setText(myFormat);
                            }
                        }, c.get(Calendar.YEAR), c.get(Calendar.MONTH), c.get(Calendar.DAY_OF_MONTH));
                datePickerDialog.show();
            }
        });

        final androidx.appcompat.app.AlertDialog mAlertDialog = alertDialogBuilder.create();
        if (mAlertDialog.getWindow() != null)
            mAlertDialog.getWindow().getAttributes().windowAnimations = R.style.SlidingDialogAnimation;
        mAlertDialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));


        ButtonClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mAlertDialog.dismiss();
            }
        });

        ButtonSave.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

                if (Float.parseFloat(amount_txt.getText().toString()) <= due) {

                    dataList.get(position).setAmount(Float.parseFloat(amount_txt.getText().toString()));
                    dataList.get(position).setMethod(spin_payment_method.getSelectedItem().toString());
                    dataList.get(position).setNote(payment_note.getText().toString());
                    dataList.get(position).setPaid_on(paid_on_txt.getText().toString());
                    dataList.get(position).setTransaction_id(dataList.get(position).getId());

                    if (Float.parseFloat(amount_txt.getText().toString()) == due) {
                        dataList.get(position).setPayment_status(PAID);
                    }else if(Float.parseFloat(amount_txt.getText().toString()) < due){
                        dataList.get(position).setPayment_status(PARTIAL);
                    }

                    transactionDbController.updateTransaction(dataList.get(position));
                    int index = transactionPayementDbController.insertLocal(dataList.get(position));
                    if (index > 0) {
                        Toast.makeText(context, context.getResources().getText(R.string.lbl_added_success), Toast.LENGTH_LONG).show();
                        notifyItemChanged(position);
                        mAlertDialog.dismiss();
                        if (mOnDataChangeListener != null) {
                            mOnDataChangeListener.onDataChanged(dataList.get(position));
                        }
                        notifyDataSetChanged();
                    } else {
                        Toast.makeText(context, "Error insert", Toast.LENGTH_LONG).show();
                    }
                } else {
                    Toast.makeText(context, context.getResources().getText(R.string.label_maximum_amount) + " " + due, Toast.LENGTH_LONG).show();
                }
            }
        });

        mAlertDialog.show();

    }

    private void edit(int id) {
        AppCompatActivity activity = (AppCompatActivity) context;
        Bundle bundle = new Bundle();
        bundle.putInt("id", id);
        Fragment myFragment = new AddExpenseFragment(true);
        myFragment.setArguments(bundle);
        activity.getSupportFragmentManager().beginTransaction().replace(R.id.nav_host_fragment, myFragment).addToBackStack(null).commit();
    }

    private void deleteItem(int position) {
        //Preparing views
        // get prompts.xml view
        LayoutInflater li = LayoutInflater.from(context);
        View promptsView = li.inflate(R.layout.delete_dialog_main, null);

        androidx.appcompat.app.AlertDialog.Builder alertDialogBuilder = new androidx.appcompat.app.AlertDialog.Builder(
                context);

        alertDialogBuilder.setView(promptsView);

        final Button ButtonSave = promptsView.findViewById(R.id.btn_save);
        final Button ButtonClose = promptsView.findViewById(R.id.btn_close);

        final androidx.appcompat.app.AlertDialog mAlertDialog = alertDialogBuilder.create();
        if (mAlertDialog.getWindow() != null)
            mAlertDialog.getWindow().getAttributes().windowAnimations = R.style.SlidingDialogAnimation;
        mAlertDialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));


        ButtonClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mAlertDialog.dismiss();
            }
        });

        ButtonSave.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

                transactionDbController.deleteTransaction(dataList.get(position).getId());
                transactionPayementDbController.deletePayment(dataList.get(position).getId());

                dataList.remove(position);
                if (mOnDataChangeListener != null) {
                    mOnDataChangeListener.onDataChanged(dataList.get(position));
                }
                mAlertDialog.dismiss();
                notifyDataSetChanged();
            }
        });


        mAlertDialog.show();
    }

    public interface OnDataChangeListener {
        void onDataChanged(Transaction transaction);
    }

    OnDataChangeListener mOnDataChangeListener;

    public void setOnDataChangeListener(OnDataChangeListener onDataChangeListener) {
        mOnDataChangeListener = onDataChangeListener;
    }

}
