package com.rising.high.tech.bigultimatenavdraw.ui.expense.adapter;

import android.app.DatePickerDialog;
import android.content.Context;
import android.content.res.Resources;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.DatePicker;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.widget.AppCompatImageView;
import androidx.recyclerview.widget.RecyclerView;

import com.google.gson.Gson;
import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.db.BusinessLocationDbController;
import com.rising.high.tech.bigultimatenavdraw.db.ContactDbController;
import com.rising.high.tech.bigultimatenavdraw.db.TransactionDbController;
import com.rising.high.tech.bigultimatenavdraw.db.TransactionPayementDbController;
import com.rising.high.tech.bigultimatenavdraw.model.Business_location;
import com.rising.high.tech.bigultimatenavdraw.model.Contact;
import com.rising.high.tech.bigultimatenavdraw.model.Transaction;
import com.rising.high.tech.bigultimatenavdraw.util.SessionManager;
import com.rising.high.tech.bigultimatenavdraw.util.StringFormat;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.HashMap;

import static com.rising.high.tech.bigultimatenavdraw.util.Constant.DUE;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.PAID;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.PARTIAL;

public class SubExpenseeAdapter extends RecyclerView.Adapter<SubExpenseeAdapter.sub_vente_view_holder> {
    private static final String TAG = "SubPaymentAdapter";
    private ArrayList<Transaction> dataList = new ArrayList<>();
    Context context;
    private TransactionPayementDbController transactionPayementDbController;
    private TransactionDbController transactionDbController;
    private ContactDbController contactDbController;
    private BusinessLocationDbController businessLocationDbController;
    private Resources resources;
    private SessionManager session;
    private HashMap<String, Object> user;
    final Calendar c = Calendar.getInstance();

    @Override
    public sub_vente_view_holder onCreateViewHolder(ViewGroup parent, int viewType) {
        context = parent.getContext();
        resources = context.getResources();

        session = new SessionManager(context);
        user = session.getUserDetails();

        transactionPayementDbController = new TransactionPayementDbController(context);
        transactionPayementDbController.open();

        transactionDbController = new TransactionDbController(context);
        transactionDbController.open();

        businessLocationDbController = new BusinessLocationDbController(context);
        businessLocationDbController.open();

        contactDbController = new ContactDbController(context);
        contactDbController.open();


        return new sub_vente_view_holder(LayoutInflater.from(parent.getContext())
                .inflate(R.layout.sub_pyment_item_, parent, false));
    }

    @Override
    public void onBindViewHolder(sub_vente_view_holder holder, int position) {
        holder.date.setText(dataList.get(position).getPaid_on());
        holder.reference_no.setText(dataList.get(position).getPayment_ref_no());
        holder.account.setText(dataList.get(position).getAmount() + "");
        holder.payement_method.setText(dataList.get(position).getMethod());
        holder.payment_note.setText(dataList.get(position).getNote());
    }

    public void setData(ArrayList<Transaction> arrayList) {
        this.dataList = arrayList;
        notifyDataSetChanged();
    }

    @Override
    public int getItemCount() {
        return dataList.size();
    }

    public class sub_vente_view_holder extends RecyclerView.ViewHolder {
        TextView date, reference_no, account, payement_method, payment_note;
        ImageView delete_payment, edit_payment, view_item;

        public sub_vente_view_holder(View itemView) {
            super(itemView);
            date = itemView.findViewById(R.id.date);
            reference_no = itemView.findViewById(R.id.reference_no);
            account = itemView.findViewById(R.id.account);
            payement_method = itemView.findViewById(R.id.payement_method);
            payment_note = itemView.findViewById(R.id.payment_note);
            delete_payment = itemView.findViewById(R.id.delete_payment);
            edit_payment = itemView.findViewById(R.id.edit_payment);
            view_item = itemView.findViewById(R.id.view_item);

            delete_payment.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    deleteItem(getAdapterPosition());
                }
            });

            edit_payment.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    edit(getAdapterPosition(), false);
                }
            });

            view_item.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    edit(getAdapterPosition(), true);
                }
            });
        }
    }

    private void edit(int position, boolean isView) {
        LayoutInflater li = LayoutInflater.from(context);
        View promptsView = li.inflate(R.layout.add_payment_purchase_dialog_main, null);
        androidx.appcompat.app.AlertDialog.Builder alertDialogBuilder = new androidx.appcompat.app.AlertDialog.Builder(context);

        alertDialogBuilder.setView(promptsView);

        final Button ButtonSave = promptsView.findViewById(R.id.btn_save);
        final AppCompatImageView ButtonClose = promptsView.findViewById(R.id.btn_close);

        final TextView supplier_name = promptsView.findViewById(R.id.supplier_name);
        final TextView supplier_phone = promptsView.findViewById(R.id.supplier_phone);
        final TextView business_location = promptsView.findViewById(R.id.business_location);
        final TextView total_amount = promptsView.findViewById(R.id.total_amount);
        final TextView payment_note = promptsView.findViewById(R.id.payment_note);
        final TextView purchase_note = promptsView.findViewById(R.id.purchase_note);
        final EditText amount_txt = promptsView.findViewById(R.id.amount_txt);
        final EditText paid_on_txt = promptsView.findViewById(R.id.paid_on_txt);
        final Spinner spin_payment_method = promptsView.findViewById(R.id.spin_payment_method);

        if (isView){
            amount_txt.setEnabled(false);
            payment_note.setEnabled(false);
            paid_on_txt.setEnabled(false);
            spin_payment_method.setEnabled(false);
            ButtonSave.setVisibility(View.GONE);
            payment_note.setText(dataList.get(position).getNote());
        }

        Log.d(TAG, " data iteem ## " + new Gson().toJson(dataList.get(position)));
        Transaction transaction = transactionDbController.getTransactionById(dataList.get(position).getTransaction_id());


        Contact contact = contactDbController.getCustomerById(transaction.getContact_id());
        Transaction payment = transactionPayementDbController.getTransactionById(transaction.getId());
        Business_location businesslocation = businessLocationDbController.getStationById(transaction.getLocation_id());

        supplier_name.setText(contact.getName());
        supplier_phone.setText(contact.getMobile());
        business_location.setText(businesslocation.getName() + " " + businesslocation.getCity() + " " + businesslocation.getCountry());
        total_amount.setText(transaction.getFinal_total() + " " + user.get(session.KEY_SYMBOL));
        purchase_note.setText(transaction.getNote());
        Float final_total = Float.parseFloat(transaction.getFinal_total());

        /**
         * get amount from all payments
         */
        Float amount = 0.f;
        for (Transaction transaction1 : transactionPayementDbController.getAllTransactionById(transaction.getId())) {
            amount += transaction1.getAmount();
        }


        Float due = (final_total - amount);

        amount_txt.setText(dataList.get(position).getAmount() + "");
        paid_on_txt.setText(dataList.get(position).getPaid_on());

        //  if (dataList.get(position).getMethod() != null) {
        int indexP = Arrays.asList(resources.getStringArray(R.array.payment_method_array)).indexOf(dataList.get(position).getMethod());

        Log.d(TAG, " indexP is ## " + indexP + " fucking item name ## " + transaction.getMethod());
        spin_payment_method.setSelection(indexP);

        /**
         * init button click listner
         */
        paid_on_txt.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                DatePickerDialog datePickerDialog = new DatePickerDialog(context,
                        new DatePickerDialog.OnDateSetListener() {
                            @Override
                            public void onDateSet(DatePicker datePicker, int year, int month, int day) {
                                String myFormat = StringFormat.populateSetFullDate(year, month, day, c.get(Calendar.HOUR), c.get(Calendar.MINUTE));
                                paid_on_txt.setText(myFormat);
                            }
                        }, c.get(Calendar.YEAR), c.get(Calendar.MONTH), c.get(Calendar.DAY_OF_MONTH));
                datePickerDialog.show();
            }
        });

        final androidx.appcompat.app.AlertDialog mAlertDialog = alertDialogBuilder.create();
        if (mAlertDialog.getWindow() != null)
            mAlertDialog.getWindow().getAttributes().windowAnimations = R.style.SlidingDialogAnimation;
        mAlertDialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));


        ButtonClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mAlertDialog.dismiss();
            }
        });

        ButtonSave.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

                //  if (Float.parseFloat(amount_txt.getText().toString()) <= due) {

                dataList.get(position).setAmount(Float.parseFloat(amount_txt.getText().toString()));
                dataList.get(position).setMethod(spin_payment_method.getSelectedItem().toString());
                dataList.get(position).setNote(payment_note.getText().toString());
                dataList.get(position).setPaid_on(paid_on_txt.getText().toString());
                dataList.get(position).setTransaction_id(transaction.getId());

                if (Float.parseFloat(amount_txt.getText().toString()) >= due) {
                    transaction.setPayment_status(PAID);
                } else if (Float.parseFloat(amount_txt.getText().toString()) < due) {
                    transaction.setPayment_status(PARTIAL);
                }
                transactionDbController.updateTransaction(transaction);
                int index = transactionPayementDbController.updatePayement(dataList.get(position));
                if (index > 0) {
                    Toast.makeText(context, context.getResources().getText(R.string.lbl_added_success), Toast.LENGTH_LONG).show();
                    notifyItemChanged(position);
                    mAlertDialog.dismiss();


                    if (mOnDataChangeListener != null) {
                        mOnDataChangeListener.onDataChanged();
                    }
                    notifyDataSetChanged();
                } else {
                    Toast.makeText(context, "Error insert", Toast.LENGTH_LONG).show();
                }

            }
        });

        mAlertDialog.show();

    }

    private void deleteItem(int position) {
        //Preparing views
        // get prompts.xml view
        LayoutInflater li = LayoutInflater.from(context);
        View promptsView = li.inflate(R.layout.delete_dialog_main, null);

        androidx.appcompat.app.AlertDialog.Builder alertDialogBuilder = new androidx.appcompat.app.AlertDialog.Builder(
                context);

        alertDialogBuilder.setView(promptsView);

        final Button ButtonSave = promptsView.findViewById(R.id.btn_save);
        final Button ButtonClose = promptsView.findViewById(R.id.btn_close);

        final androidx.appcompat.app.AlertDialog mAlertDialog = alertDialogBuilder.create();
        if (mAlertDialog.getWindow() != null)
            mAlertDialog.getWindow().getAttributes().windowAnimations = R.style.SlidingDialogAnimation;
        mAlertDialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));

        ButtonClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mAlertDialog.dismiss();
            }
        });

        ButtonSave.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

                Transaction transaction = transactionDbController.getTransactionById(dataList.get(position).getTransaction_id());
                transactionPayementDbController.deletePayment(dataList.get(position).getId());
                Float amount = 0.f;
                Float final_total = Float.parseFloat(transaction.getFinal_total());
                for (Transaction transaction1 : transactionPayementDbController.getAllTransactionById(dataList.get(position).getTransaction_id())) {
                    amount += transaction1.getAmount();
                }

                Float due = (final_total - amount);

                if (amount < final_total) {
                    transaction.setPayment_status(PARTIAL);
                } else if (amount == 0.f) {
                    transaction.setPayment_status(DUE);
                }

                int index = transactionDbController.updateTransaction(transaction);
                if (index > 0) {
                    Toast.makeText(context, "Deleted ", Toast.LENGTH_LONG).show();

                    if (mOnDataChangeListener != null) {
                        mOnDataChangeListener.onDataChanged();
                    }
                } else {
                    Toast.makeText(context, " error db ...", Toast.LENGTH_LONG).show();
                }
                dataList.remove(position);

                notifyDataSetChanged();
                mAlertDialog.dismiss();
                notifyDataSetChanged();
            }
        });

        mAlertDialog.show();
    }


    public interface OnDataChangeListener {
        void onDataChanged();
    }

    OnDataChangeListener mOnDataChangeListener;

    public void setOnDataChangeListener(OnDataChangeListener onDataChangeListener) {
        mOnDataChangeListener = onDataChangeListener;
    }

}
