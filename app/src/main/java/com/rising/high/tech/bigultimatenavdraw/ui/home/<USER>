package com.rising.high.tech.bigultimatenavdraw.ui.home;

import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.pdf.PdfDocument;
import android.os.Bundle;
import android.os.Environment;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.LinearLayout;

import androidx.appcompat.app.AppCompatActivity;

import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.util.PrinterTester;

import java.io.File;
import java.io.FileOutputStream;
import java.util.Date;


public class PrintPreview extends AppCompatActivity {
    private static final String TAG = "NaviMainActivity";



    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        getWindow().setFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN,
                WindowManager.LayoutParams.FLAG_FULLSCREEN);
        setContentView(R.layout.print_reciept_layout);

        takeScreenshot();
    }
    private void takeScreenshot() {
        Date now = new Date();
        android.text.format.DateFormat.format("yyyy-MM-dd_hh:mm:ss", now);

        try {
            // image naming and path  to include sd card  appending name you choose for file
            String mPath = Environment.getExternalStorageDirectory().toString() + "/" + now + ".jpg";

            // create bitmap screen capture
            View v1 = getWindow().getDecorView().getRootView();
            v1.setDrawingCacheEnabled(true);
            Bitmap bitmap = Bitmap.createBitmap(v1.getDrawingCache());
            v1.setDrawingCacheEnabled(false);

            File imageFile = new File(mPath);

            FileOutputStream outputStream = new FileOutputStream(imageFile);
            int quality = 100;
            bitmap.compress(Bitmap.CompressFormat.JPEG, quality, outputStream);
            outputStream.flush();
            outputStream.close();

            startPrintTicket(bitmap);
        } catch (Throwable e) {
            // Several error may come out with file handling or DOM
            e.printStackTrace();
        }
    }
    private void takeScreenshot1() {
        Date now = new Date();
        android.text.format.DateFormat.format("yyyy-MM-dd_hh:mm:ss", now);

        try {
            // image naming and path  to include sd card  appending name you choose for file
            LinearLayout linearLayout = findViewById(R.id.linearLayout);
            String mPath = Environment.getExternalStorageDirectory().toString() + "/" + now + ".jpg";
            int width = linearLayout.getWidth();
            int height = linearLayout.getHeight();
            Bitmap b = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888);
            Canvas c1 = new Canvas(b);
            linearLayout.draw(c1);
            PdfDocument pd = new PdfDocument();
            PdfDocument.PageInfo pi = new PdfDocument.PageInfo.Builder(width, height, 1).create();
            PdfDocument.Page p = pd.startPage(pi);
            Canvas c = p.getCanvas();
            c.drawBitmap(b, 0f, 0f, new Paint());
            pd.finishPage(p);
            // create bitmap screen capture
            View v1 =getWindow().getDecorView().getRootView();
            v1.setDrawingCacheEnabled(true);
            Bitmap bitmap = Bitmap.createBitmap(v1.getDrawingCache());
            v1.setDrawingCacheEnabled(false);

            File imageFile = new File(mPath);
            startPrintTicket(bitmap);


        } catch (Throwable e) {
            // Several error may come out with file handling or DOM
            e.printStackTrace();
        }
    }
    private void startPrintTicket(Bitmap bitmap) {

                try {

                    PrinterTester.getInstance(this).init();
                    PrinterTester.printBitmap(bitmap);

                    PrinterTester.getInstance(this).spaceSet(Byte.parseByte("0"),
                            Byte.parseByte("0"));
                    PrinterTester.getInstance(this).leftIndents(Short.parseShort("0"));
                    PrinterTester.getInstance(this).setGray(Integer.parseInt("1"));

                    PrinterTester.getInstance(this).setInvert(false);

                        PrinterTester.getInstance(this).printStr("", null);
                    PrinterTester.getInstance(this).step(Integer.parseInt("150"));

                    final String status = PrinterTester.getInstance(this).start();
                    String res = PrinterTester.getInstance(this).cutPaper(0);

                } catch (Exception e) {
                }
            }


}