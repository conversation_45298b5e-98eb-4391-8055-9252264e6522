package com.rising.high.tech.bigultimatenavdraw.ui.home.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.recyclerview.widget.RecyclerView;


import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.db.VariationLocationDetailDbController;
import com.rising.high.tech.bigultimatenavdraw.model.Product;
import com.rising.high.tech.bigultimatenavdraw.model.Variation_location_details;
import com.rising.high.tech.bigultimatenavdraw.util.SessionManager;

import java.util.ArrayList;
import java.util.HashMap;

public class DetailProductAdapter extends RecyclerView.Adapter<DetailProductAdapter.detailProductViewHolder> {

    private static final String TAG = "DetailProductAdapter";
    private ArrayList<Product> dataList = new ArrayList<>();
    Context context;
    // Session Manager Class
    SessionManager session;
    HashMap<String, Object> user;

    private VariationLocationDetailDbController variationLocationDetailDbController;

    @Override
    public detailProductViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        context = parent.getContext();
        session = new SessionManager(context);
        user = session.getUserDetails();

        variationLocationDetailDbController = new VariationLocationDetailDbController(context);
        variationLocationDetailDbController.open();

        return new detailProductViewHolder(LayoutInflater.from(parent.getContext())
                .inflate(R.layout.detail_product_item, parent, false));
    }

    @Override
    public void onBindViewHolder(detailProductViewHolder holder, int position) {
        holder.product_name.setText(dataList.get(position).getName());

        holder.id_price.setText(user.get(SessionManager.KEY_SYMBOL) + dataList.get(position).getDefault_sell_price());
        holder.id_quantity.setText(dataList.get(position).getSell_qte() + "");
        Float totalAmount = Float.parseFloat(dataList.get(position).getDefault_sell_price())* dataList.get(position).getSell_qte();
        holder.total_price.setText(user.get(SessionManager.KEY_SYMBOL) + String.format("%.2f", totalAmount));

        //get quantity from Variation_location_details table
        int id_station = (int) user.get(session.LOCATION_ID);
        int id_product = dataList.get(position).getId();
        Variation_location_details variation_location_details = variationLocationDetailDbController.getVariationLocationDetailsByStationIdProductId(id_station, id_product);
        Integer qtyAvailable = variation_location_details.getQty_available();
        dataList.get(position).setQty_available(String.valueOf(qtyAvailable));

        if (position % 2 == 1) {
            holder.linearLayout.setBackgroundColor(context.getResources().getColor(R.color.white));
        } else {
            holder.linearLayout.setBackgroundColor(context.getResources().getColor(R.color.lightGrey));

        }

    }

    public void setData(ArrayList<Product> arrayList) {
        this.dataList = arrayList;
        notifyDataSetChanged();
    }

    @Override
    public int getItemCount() {
        return dataList.size();
    }

    public class detailProductViewHolder extends RecyclerView.ViewHolder {

        TextView product_name, id_price, id_quantity, total_price;
        ImageButton imageButtonAdd, imageButtonRem, btnDelete;
        LinearLayout linearLayout;

        public detailProductViewHolder(View itemView) {
            super(itemView);

            product_name = itemView.findViewById(R.id.product_name);
            id_price = itemView.findViewById(R.id.id_price);
            id_quantity = itemView.findViewById(R.id.id_quantity);
            total_price = itemView.findViewById(R.id.total_price);
            imageButtonAdd = itemView.findViewById(R.id.image_button_add);
            imageButtonRem = itemView.findViewById(R.id.image_button_rem);
            btnDelete = itemView.findViewById(R.id.btn_delete);
            linearLayout = itemView.findViewById(R.id.linearLayout);

            imageButtonRem.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    Integer qtyAvailable = Integer.parseInt(dataList.get(getAdapterPosition()).getQty_available());

                    if (dataList.get(getAdapterPosition()).getSell_qte() > 1) {
                        dataList.get(getAdapterPosition()).setSell_qte(dataList.get(getAdapterPosition()).getSell_qte() - 1);
                        dataList.get(getAdapterPosition()).setQty_available((qtyAvailable + 1) + "");
                        if (mOnDataChangeListener != null) {
                            mOnDataChangeListener.onDataChanged(dataList);
                        }
                        notifyDataSetChanged();
                    }
                }
            });

            imageButtonAdd.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    Integer qtyAvailable = Integer.parseInt(dataList.get(getAdapterPosition()).getQty_available());
                    Integer qtySell = dataList.get(getAdapterPosition()).getSell_qte();
                    if (dataList.get(getAdapterPosition()).getSell_qte() > 0 && qtyAvailable > 0 && qtySell<qtyAvailable) {
                        dataList.get(getAdapterPosition()).setSell_qte(dataList.get(getAdapterPosition()).getSell_qte() + 1);
                        dataList.get(getAdapterPosition()).setQty_available((qtyAvailable - 1) + "");
                        if (mOnDataChangeListener != null) {
                            mOnDataChangeListener.onDataChanged(dataList);
                        }
                        notifyDataSetChanged();
                    } else {
                        Toast.makeText(context, context.getResources().getString(R.string.label_empty_product), Toast.LENGTH_LONG).show();
                    }
                }
            });

            btnDelete.setOnClickListener(v->{
                dataList.remove(dataList.get(getAdapterPosition()));
                if (mOnDataChangeListener != null) {
                    mOnDataChangeListener.onDataDeleted(dataList);
                }
                notifyDataSetChanged();
            });
        }
    }

    public ArrayList<Product> getDataList() {
        return this.dataList;
    }

    public interface OnDataChangeListener {
        void onDataChanged(ArrayList<Product> products);
        void onDataDeleted(ArrayList<Product> products);
    }

    OnDataChangeListener mOnDataChangeListener;

    public void setOnDataChangeListener(OnDataChangeListener onDataChangeListener) {
        mOnDataChangeListener = onDataChangeListener;
    }

}
