package com.rising.high.tech.bigultimatenavdraw.ui.home.adapter;

import android.text.Editable;
import android.text.TextWatcher;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.Spinner;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;


import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.model.Payement;

import java.util.ArrayList;

public class MultiPayAdapter extends RecyclerView.Adapter<MultiPayAdapter.multiPayViewHolder> {
    private ArrayList<Payement> dataListPayement = new ArrayList<>();

    @NonNull
    @Override
    public multiPayViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new multiPayViewHolder(LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_multy_pay, parent, false));
    }

    @Override
    public void onBindViewHolder(@NonNull multiPayViewHolder holder, int position) {
        holder.textAmount.setText(dataListPayement.get(position).getAmount() + "");
        holder.imageQuit.setVisibility(position==0? View.GONE: View.VISIBLE);
    }

    public ArrayList<Payement> getDataListPayement() {
        return this.dataListPayement;
    }

    @Override
    public int getItemCount() {
        return dataListPayement.size();
    }

    class multiPayViewHolder extends RecyclerView.ViewHolder {
        ImageView imageQuit;
        EditText textAmount, textNote;
        Spinner pymntMethod;

        public multiPayViewHolder(@NonNull View itemView) {

            super(itemView);
            imageQuit = itemView.findViewById(R.id.id_quit);
            textAmount = itemView.findViewById(R.id.id_amount);
            textNote = itemView.findViewById(R.id.id_text_note);
            pymntMethod = itemView.findViewById(R.id.id_spinner_product);

            pymntMethod.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
                @Override
                public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                    dataListPayement.get(getAdapterPosition()).setMethod(pymntMethod.getSelectedItem().toString());
                }

                @Override
                public void onNothingSelected(AdapterView<?> parent) {

                }
            });

            imageQuit.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    dataListPayement.remove(getAdapterPosition());
                    notifyDataSetChanged();
                //    mOnDataChangeListener.onDataChanged(dataListPayement);
                }
            });

            textAmount.addTextChangedListener(new TextWatcher() {
                @Override
                public void beforeTextChanged(CharSequence s, int start, int count, int after) {

                }

                @Override
                public void onTextChanged(CharSequence s, int start, int before, int count) {

                }

                @Override
                public void afterTextChanged(Editable s) {
                    if (!s.toString().isEmpty() && dataListPayement != null) {
                        dataListPayement.get(getAdapterPosition()).setAmount(s.toString());

                    }
                }
            });


            textNote.addTextChangedListener(new TextWatcher() {
                @Override
                public void beforeTextChanged(CharSequence s, int start, int count, int after) {

                }

                @Override
                public void onTextChanged(CharSequence s, int start, int before, int count) {

                }

                @Override
                public void afterTextChanged(Editable s) {

                    if (!s.toString().isEmpty() && dataListPayement != null) {
                        dataListPayement.get(getAdapterPosition()).setNote(s.toString());
                    }

                }
            });
        }
    }

    public void setDataAdapter(ArrayList<Payement> payements) {
        this.dataListPayement = payements;
        notifyDataSetChanged();
    }

    public interface OnDataChangeListener {
        void onDataChanged(ArrayList<Payement> payement);
    }

    OnDataChangeListener mOnDataChangeListener;
    public void setOnDataChangeListener(OnDataChangeListener onDataChangeListener) {
        mOnDataChangeListener = onDataChangeListener;
    }


}
