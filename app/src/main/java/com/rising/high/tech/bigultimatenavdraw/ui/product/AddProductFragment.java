package com.rising.high.tech.bigultimatenavdraw.ui.product;

import android.Manifest;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.net.Uri;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.Gallery;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.Spinner;

import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.core.app.ActivityCompat;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;

import com.google.android.material.textfield.TextInputLayout;
import com.google.zxing.integration.android.IntentIntegrator;
import com.google.zxing.integration.android.IntentResult;
import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.db.BrandDbController;
import com.rising.high.tech.bigultimatenavdraw.db.CategoryDbController;
import com.rising.high.tech.bigultimatenavdraw.db.ProductDbController;
import com.rising.high.tech.bigultimatenavdraw.db.ProductLocationDbController;
import com.rising.high.tech.bigultimatenavdraw.db.PurchaseLineDbController;
import com.rising.high.tech.bigultimatenavdraw.db.BusinessLocationDbController;
import com.rising.high.tech.bigultimatenavdraw.db.TaxRatesDbController;
import com.rising.high.tech.bigultimatenavdraw.db.TransactionDbController;
import com.rising.high.tech.bigultimatenavdraw.db.UnitDbController;
import com.rising.high.tech.bigultimatenavdraw.db.VariationLocationDetailDbController;
import com.rising.high.tech.bigultimatenavdraw.db.VariationsDbController;
import com.rising.high.tech.bigultimatenavdraw.model.Brand;
import com.rising.high.tech.bigultimatenavdraw.model.Category;
import com.rising.high.tech.bigultimatenavdraw.model.Product;
import com.rising.high.tech.bigultimatenavdraw.model.Product_location;
import com.rising.high.tech.bigultimatenavdraw.model.Business_location;
import com.rising.high.tech.bigultimatenavdraw.model.Tax_rates;
import com.rising.high.tech.bigultimatenavdraw.model.Variation;
import com.rising.high.tech.bigultimatenavdraw.model.Variation_location_details;
import com.rising.high.tech.bigultimatenavdraw.ui.adapter.ImageAdapter;
import com.rising.high.tech.bigultimatenavdraw.ui.adapter.SpinCategoryAdapter;
import com.rising.high.tech.bigultimatenavdraw.ui.adapter.SpinStationAdapter;
import com.rising.high.tech.bigultimatenavdraw.ui.adapter.SpinTaxRatesAdapter;
import com.rising.high.tech.bigultimatenavdraw.ui.adapter.SpinUnitAdapter;
import com.rising.high.tech.bigultimatenavdraw.ui.category.ListCategoryFragment;
import com.rising.high.tech.bigultimatenavdraw.ui.unit.ListUnitFragment;
import com.rising.high.tech.bigultimatenavdraw.util.FileUtil;
import com.rising.high.tech.bigultimatenavdraw.util.MultiSelectSpinner;
import com.rising.high.tech.bigultimatenavdraw.util.SessionManager;
import com.rising.high.tech.bigultimatenavdraw.util.StringFormat;

import java.io.FileNotFoundException;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

import butterknife.BindView;
import butterknife.ButterKnife;

import static android.app.Activity.RESULT_OK;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.EXCLUSIVE;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.INCLUSIVE;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.SINGLE;

public class AddProductFragment extends Fragment implements MultiSelectSpinner.OnMultipleItemsSelectedListener, AdapterView.OnItemSelectedListener {
    private static final String TAG = "AddProductFragment";
    private static final int PICK_FILE_REQUEST = 1;

    private Context _context;
    private SpinStationAdapter spinStationAdapter;
    private BusinessLocationDbController businessLocationDbController;
    private ProductLocationDbController productLocationDbController;
    private VariationLocationDetailDbController variationLocationDetailDbController;
    private VariationsDbController variationsDbController;
    private TransactionDbController transactionDbController;
    private PurchaseLineDbController purchaseLineDbController;
    private BrandDbController brandDbController;

    private Product product = new Product();

    Button btnBack, btnAdd;
    SpinCategoryAdapter spinCategoryAdapter,spinSubCategoryAdapter;
    SpinUnitAdapter spinUnitAdapter;
    SpinTaxRatesAdapter spinTaxRatesAdapter;
    CategoryDbController categoryDbController;
    UnitDbController unitDbController;
    TaxRatesDbController taxRatesDbController;
    Spinner spinnerCategory, subCategorySpinner,spinnerUnit, spinnerTaxRates,spinnerBrand,spinnerBarcodeType;
    EditText dspIncTax, dppExcTax, productName, dspExcTax, productDesc, dppIncTax, editMargin, alertQuantity,product_weight;
    TextInputLayout mStockContainer;
    ProductDbController productDbController;
    AppCompatTextView imageAddAttach;
    Gallery gallery;
    SessionManager session;
    String SkuValue="";
    Boolean isSelectedStation=false;
    RelativeLayout relativeView;
    private Integer userId;

    private ArrayList<Business_location> currentBusinesslocation = null;

    @BindView(R.id.id_seling_tax_price_spinner)
    Spinner spinnerSelingTaxPriceSpinner;
    @BindView(R.id.product_sku)
    EditText productSku;
    @BindView(R.id.spinner_station)
    MultiSelectSpinner spinnerStation;
    @BindView(R.id.id_add_unit)
    ImageView btnAddUnit;
    @BindView(R.id.id_add_category)
    ImageView btnAddCategory;

    CheckBox cbManageStock, cbNotForSelling;
    private static final String[] PERMISSIONS_STORAGE = {
            Manifest.permission.READ_EXTERNAL_STORAGE,
            Manifest.permission.WRITE_EXTERNAL_STORAGE
    };
    private static final int REQUEST_EXTERNAL_STORAGE = 1;

    public AddProductFragment() {
        // Required empty public constructor
    }
    private ArrayList<Brand> brandsList;
    private ArrayList<Tax_rates> taxList;
    private ArrayList<Category> categoryList;
    private ArrayList<Category> subCategoryList;
    int brandId=0,taxId=0;

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        // Inflate the layout for this fragment

        View root = inflater.inflate(R.layout.activity_add_product, container, false);
        ButterKnife.bind(this, root);

        _context = getContext();
        onBackPressed(root);
        session = new SessionManager(_context);
        btnBack = root.findViewById(R.id.id_back);
        spinnerCategory = root.findViewById(R.id.spinner_category);
        subCategorySpinner = root.findViewById(R.id.subCategorySpinner);
        spinnerBrand = root.findViewById(R.id.spinner_brand);
        cbManageStock = root.findViewById(R.id.id_manage_stock);
        cbNotForSelling = root.findViewById(R.id.id_not_for_selling);
        gallery = root.findViewById(R.id.gallery);
        alertQuantity = root.findViewById(R.id.edit_alert_quantity);
        productDesc = root.findViewById(R.id.produxt_desc);
        dspIncTax = root.findViewById(R.id.default_sell_inc_tax);
        dppExcTax = root.findViewById(R.id.id_exc_tax);
        dspExcTax = root.findViewById(R.id.id_default_sel_price_exc_tax);
        btnAdd = root.findViewById(R.id.add_btn);
        productName = root.findViewById(R.id.product_name);
        spinnerUnit = root.findViewById(R.id.spinner_unit);
        spinnerTaxRates = root.findViewById(R.id.tax_rate);
        mStockContainer = root.findViewById(R.id.manage_stock_container);
        dppIncTax = root.findViewById(R.id.id_inc_tax);
        editMargin = root.findViewById(R.id.id_margin);
        imageAddAttach = root.findViewById(R.id.id_image_add_attach);
        spinnerBarcodeType = root.findViewById(R.id.spinnerBarcodeType);
        product_weight = root.findViewById(R.id.product_weight);
        relativeView = root.findViewById(R.id.relativeView);

        initDB();
        initSpinners();
        initClickListeners();
        initCalculated();
        return root;
    }


    private void initDB() {
        categoryDbController = new CategoryDbController(_context);
        categoryDbController.open();

        unitDbController = new UnitDbController(_context);
        unitDbController.open();

        taxRatesDbController = new TaxRatesDbController(_context);
        taxRatesDbController.open();

        productDbController = new ProductDbController(_context);
        productDbController.open();

        businessLocationDbController = new BusinessLocationDbController(_context);
        businessLocationDbController.open();

        productLocationDbController = new ProductLocationDbController(_context);
        productLocationDbController.open();

        variationLocationDetailDbController = new VariationLocationDetailDbController(_context);
        variationLocationDetailDbController.open();

        variationsDbController = new VariationsDbController(_context);
        variationsDbController.open();

        transactionDbController = new TransactionDbController(_context);
        transactionDbController.open();

        purchaseLineDbController = new PurchaseLineDbController(_context);
        purchaseLineDbController.open();

        brandDbController = new BrandDbController(_context);
        brandDbController.open();

        ArrayList<Business_location> selectedBusinesslocations = new ArrayList<>();
    }

    private void initSpinners() {
        userId = (int) session.getUserDetails().get(session.ID_USER);

        categoryList= categoryDbController.getAllMainCategory();
        spinCategoryAdapter = new SpinCategoryAdapter(_context, android.R.layout.simple_spinner_item,categoryList);
        spinnerCategory.setAdapter(spinCategoryAdapter);
        spinnerCategory.setOnItemSelectedListener(this);

        spinUnitAdapter = new SpinUnitAdapter(_context, android.R.layout.simple_spinner_item, unitDbController.getUnits());
        spinnerUnit.setAdapter(spinUnitAdapter);

        taxList = new ArrayList<>();
        taxList.addAll(taxRatesDbController.getAllTax_ratesSpinner());
        spinTaxRatesAdapter = new SpinTaxRatesAdapter(_context, android.R.layout.simple_spinner_item, taxList);
        spinnerTaxRates.setAdapter(spinTaxRatesAdapter);
        spinnerTaxRates.setOnItemSelectedListener(this);

        currentBusinesslocation = businessLocationDbController.getAllStationSpinner();
        ArrayList<String> dataStation = new ArrayList<>();

        if (currentBusinesslocation.size() > 0) {
            for (Business_location businesslocation : currentBusinesslocation) {
                dataStation.add(businesslocation.getName());
            }
            setSpinnerStation(dataStation);
        }


        brandsList = new ArrayList<>();
        Brand brand = new Brand(0,_context.getResources().getString(R.string.please_select_brand_spin),"");
        brandsList.add(brand);
        brandsList.addAll(brandDbController.getAllBrands());
        ArrayList<String> brands = new ArrayList<>();
        for (int i = 0; i < brandsList.size(); i++) {
            brands.add(brandsList.get(i).getName());
        }
        ArrayAdapter<String> dataAdapter = new ArrayAdapter<>(_context, android.R.layout.simple_spinner_item, brands);
        dataAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        spinnerBrand.setAdapter(dataAdapter);
        spinnerBrand.setOnItemSelectedListener(this);
        editMargin.setText(session.getBusinessModel().getDefault_profit_percent());
    }
    @Override
    public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
        if (parent.getId() == R.id.spinner_brand) {
            brandId = brandsList.get(position).getId();
        }
        else if (parent.getId() == R.id.tax_rate) {
            taxId = taxList.get(position).getId();

            if (dppExcTax.getText().toString().length()!=0 && editMargin.getText().toString().length()!=0) {
                float selling_price_exc_tax;
                float selling_price_inc_tax;
                float inc_tax;
                float margin;
                // float margin = Float.parseFloat(editMargin.getText().toString());
                Tax_rates tax_rates = (Tax_rates) spinnerTaxRates.getSelectedItem();
                float tax_perc = Float.parseFloat(tax_rates.getAmount());
                float inc = Float.parseFloat(dppExcTax.getText().toString());
                inc_tax = inc + (inc * (tax_perc / 100));
                dppIncTax.setText(inc_tax + "");

                if (!editMargin.getText().toString().matches("")) {
                    margin = Float.parseFloat(editMargin.getText().toString());
                    selling_price_exc_tax = inc + (inc * (margin / 100));
                    dspExcTax.setText("" + selling_price_exc_tax);
                    selling_price_inc_tax = inc_tax + (inc_tax * (margin / 100));
                    dspIncTax.setText(selling_price_inc_tax + "");
                } else {
                    dspExcTax.setText(dppExcTax.getText().toString());
                    dspIncTax.setText(dppExcTax.getText().toString());
                }
            }

        }
        else if (parent.getId() == R.id.spinner_category) {
            subCategoryList=categoryDbController.getSubCategoryById(categoryList.get(position).getId());
            spinSubCategoryAdapter = new SpinCategoryAdapter(_context, android.R.layout.simple_spinner_item, subCategoryList);
            subCategorySpinner.setAdapter(spinSubCategoryAdapter);
        }
    }

    @Override
    public void onNothingSelected(AdapterView<?> parent) {

    }
    private void initClickListeners() {

        btnBack.setOnClickListener(v -> replaceFragment(new ListProductFragment()));

        btnAdd.setOnClickListener(v -> {
            for (Business_location businesslocation1 : currentBusinesslocation) {
                if (businesslocation1.getId() > 0 && businesslocation1.getSelected() == 1) {
                    isSelectedStation = true;
                    break;
                }
            }
            if (productName.getText().toString().isEmpty()) {
                productName.requestFocus();
                productName.setError(_context.getResources().getString(R.string.string_please_enter_product_name));
            }
            else if (spinnerUnit.getSelectedItemPosition() == 0) {
                StringFormat.showSnackBarF(requireView(),R.string.please_select_unit_name,true);
            }
            else if (spinnerCategory.getSelectedItemPosition() == 0) {
                StringFormat.showSnackBarF(requireView(),R.string.select_category_name,true);
            }
            else if (!isSelectedStation) {
                StringFormat.showSnackBarF(requireView(),R.string.lbl_please_select_station,true);
            }
            else if (editMargin.getText().toString().isEmpty()) {
                editMargin.requestFocus();
                editMargin.setError(_context.getResources().getString(R.string.string_please_enter_margin));
            }
            else if (dppExcTax.getText().toString().isEmpty()) {
                dppExcTax.requestFocus();
                dppExcTax.setError(_context.getResources().getString(R.string.string_please_exclusive_tax));
            }
            else if (dppIncTax.getText().toString().isEmpty()) {
                dppIncTax.requestFocus();
                dppIncTax.setError(_context.getResources().getString(R.string.string_please_einclusive_tax));
            }
            else
            {
                //TODO
                product.setBusiness_id(session.getBusinessModel().getId());
                product.setName(productName.getText().toString());
                product.setType(SINGLE);
                if(spinnerSelingTaxPriceSpinner.getSelectedItemPosition()==0){
                    product.setTax_type(EXCLUSIVE) ;
                }else if(spinnerSelingTaxPriceSpinner.getSelectedItemPosition()==1) {
                    product.setTax_type(INCLUSIVE);
                }
                product.setNot_for_selling(cbNotForSelling.isChecked() ? 1 : 0);
                product.setCreated_by(userId);
                product.setEnable_stock(cbManageStock.isChecked()? 1:0);
                product.setEnable_sr_no(0);
                product.setIs_inactive(0);

                product.setAlert_quantity(!alertQuantity.getText().toString().equals("") ? Integer.parseInt(alertQuantity.getText().toString()) : 0);
                product.setCategory_id(spinCategoryAdapter.getItem(spinnerCategory.getSelectedItemPosition()).getId());
                product.setSub_category_id(spinSubCategoryAdapter.getItem(subCategorySpinner.getSelectedItemPosition()).getId());
                product.setCategory_name(spinCategoryAdapter.getItem(spinnerCategory.getSelectedItemPosition()).getName());
                product.setUnit_id(spinUnitAdapter.getItem(spinnerUnit.getSelectedItemPosition()).getId());
                product.setUnit_actualname(spinUnitAdapter.getItem(spinnerUnit.getSelectedItemPosition()).getActual_name());
                product.setUnit_shortname(spinUnitAdapter.getItem(spinnerUnit.getSelectedItemPosition()).getShort_name());
                product.setProduct_description(productDesc.getText().toString());
                product.setBrand_id(brandId);
                product.setTax(taxId);
                product.setBarcode_type(spinnerBarcodeType.getSelectedItem().toString());
                product.setWeight(product_weight.getText().toString());

                if(productSku.getText().toString().isEmpty())
                {
                    SkuValue=String.format(Locale.ENGLISH,"%04d",productDbController.getLastProductID() + 1);
                    product.setSku(String.format(Locale.ENGLISH,"%04d",productDbController.getLastProductID() + 1));
                }
                else
                {
                    SkuValue=productSku.getText().toString();

                    product.setSku(productSku.getText().toString());
                }
                product.setIs_sync("no");
                int idProduct = productDbController.insertLocal(product);
                if (idProduct > 0) {

                    //insert prices to variation db
                    Variation variation = new Variation();
                    variation.setProduct_id(idProduct);
                    variation.setProduct_variation_id(idProduct);
                    variation.setName("DUMMY");
                    variation.setDefault_purchase_price(dppExcTax.getText().toString());
                    variation.setDpp_inc_tax(dppIncTax.getText().toString());
                    variation.setProfit_percent(editMargin.getText().toString());
                    variation.setDefault_sell_price(dspExcTax.getText().toString());
                    variation.setSell_price_inc_tax(dspIncTax.getText().toString());
                    variation.setSub_sku(SkuValue);
                    variationsDbController.insertLocal(variation);

                    for (Business_location businesslocation1 : currentBusinesslocation) {
                        if (businesslocation1.getId() > 0 && businesslocation1.getSelected() == 1) {
                            Variation_location_details variation_location_details = new Variation_location_details();
                            variation_location_details.setProduct_id(idProduct);
                            variation_location_details.setVariation_id(idProduct);
                            variation_location_details.setLocation_id(businesslocation1.getId());
                            variation_location_details.setQty_available(0);
                            variation_location_details.setOld_qty_available(0);
                            variationLocationDetailDbController.insertLocal(variation_location_details);

                            Product_location product_location = new Product_location(idProduct, businesslocation1.getId()+"");
                            productLocationDbController.insertLocal(product_location);
                        }
                    }
                    FileUtil.showDialog(_context, getString(R.string.success), getResources().getString(R.string.products_added_success));
                    replaceFragment(new ListProductFragment());
                }

            }

        });

        imageAddAttach.setOnClickListener(v -> addAttach());
        cbManageStock.setOnCheckedChangeListener((buttonView, isChecked) -> {
            if (isChecked) {
                mStockContainer.setVisibility(View.VISIBLE);
                // perform logic
            } else {
                mStockContainer.setVisibility(View.GONE);
                alertQuantity.setText("0");
            }
        });

        btnAddUnit.setOnClickListener(v -> {
            replaceFragment(new ListUnitFragment());
        });
        btnAddCategory.setOnClickListener(v -> {
            replaceFragment(new ListCategoryFragment());
        });
    }


    private void initCalculated() {
        dppExcTax.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

                if (!dppExcTax.getText().toString().matches("") && !editMargin.getText().toString().matches("")) {
                    float selling_price_exc_tax;
                    float selling_price_inc_tax;
                    float inc_tax;
                    float margin;
                    // float margin = Float.parseFloat(editMargin.getText().toString());
                    Tax_rates tax_rates = (Tax_rates) spinnerTaxRates.getSelectedItem();
                    float tax_perc = Float.parseFloat(tax_rates.getAmount());
                    float inc = Float.parseFloat(dppExcTax.getText().toString());
                    inc_tax = inc + (inc * (tax_perc / 100));
                    dppIncTax.setText(inc_tax + "");

                    if (!editMargin.getText().toString().matches("")) {
                        margin = Float.parseFloat(editMargin.getText().toString());
                        selling_price_exc_tax = inc + (inc * (margin / 100));
                        dspExcTax.setText("" + selling_price_exc_tax);
                        selling_price_inc_tax = inc_tax + (inc_tax * (margin / 100));
                        dspIncTax.setText(selling_price_inc_tax + "");
                    } else {
                        dspExcTax.setText(dppExcTax.getText().toString());
                        dspIncTax.setText(dppExcTax.getText().toString());
                    }
                }
            }

            @Override
            public void afterTextChanged(Editable s) {

            }
        });

        editMargin.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

                if (!dppExcTax.getText().toString().matches("") && !editMargin.getText().toString().matches("")) {
                    float selling_price_exc_tax;
                    float selling_price_inc_tax;
                    float inc_tax;
                    float margin;
                    // float tax_perc = initProduct.getTax_value();
                    Tax_rates tax_rates = (Tax_rates) spinnerTaxRates.getSelectedItem();
                    float tax_perc = Float.parseFloat(tax_rates.getAmount());
                    float inc = Float.parseFloat(dppExcTax.getText().toString());

                    inc_tax = inc + (inc * (tax_perc / 100));
                    dppIncTax.setText(inc_tax + "");
                    if (!editMargin.getText().toString().matches("")) {
                        margin = Float.parseFloat(editMargin.getText().toString());
                        selling_price_exc_tax = inc + (inc * (margin / 100));
                        selling_price_inc_tax = inc_tax + (inc_tax * (margin / 100));
                        dspExcTax.setText("" + selling_price_exc_tax);
                        dspIncTax.setText(selling_price_inc_tax + "");
                    } else {
                        dspExcTax.setText(dppExcTax.getText().toString());
                        dspIncTax.setText(dppExcTax.getText().toString());
                    }
                }
            }

            @Override
            public void afterTextChanged(Editable s) {

            }
        });
    }

    public void replaceFragment(Fragment someFragment) {
        FragmentTransaction transaction = getFragmentManager().beginTransaction();
        transaction.replace(R.id.nav_host_fragment, someFragment);
        transaction.addToBackStack(null);
        transaction.commit();
    }


    private void addAttach() {
        // Check if we have write permission
        int permission = ActivityCompat.checkSelfPermission(_context, Manifest.permission.WRITE_EXTERNAL_STORAGE);

        if (permission != PackageManager.PERMISSION_GRANTED) {
            // We don't have permission so prompt the user
            ActivityCompat.requestPermissions(
                    (AppCompatActivity) _context,
                    PERMISSIONS_STORAGE,
                    REQUEST_EXTERNAL_STORAGE
            );
        } else {
            // TODO Auto-generated method stub
//            Intent intent = new Intent(Intent.ACTION_PICK,
//                    MediaStore.Images.Media.EXTERNAL_CONTENT_URI);
//            //intent.putExtra(Intent.EXTRA_ALLOW_MULTIPLE,                       );
//            // intent.setAction(Intent.ACTION_GET_CONTENT);
//            startActivityForResult(Intent.createChooser(intent, "Choose images"), 1);

            Intent intent = new Intent();
            intent.setType("image/*");
            intent.setAction(Intent.ACTION_GET_CONTENT);
            startActivityForResult(Intent.createChooser(intent,"Choose File to Upload.."),PICK_FILE_REQUEST);
        }
    }


    public void onActivityResult(int requestCode, int resultCode, Intent intent) {
        // nous utilisons la classe IntentIntegrator et sa fonction parseActivityResult pour parser le résultat du scan
        IntentResult scanningResult = IntentIntegrator.parseActivityResult(requestCode, resultCode, intent);
        if (scanningResult == null) {
            // TODO Auto-generated method stub
            super.onActivityResult(requestCode, resultCode, intent);
            if (resultCode == RESULT_OK) {
                Uri targetUri = intent.getData();

                Bitmap bitmap;
                try {
                    bitmap = BitmapFactory.decodeStream(_context.getContentResolver().openInputStream(targetUri));

                    product.setImage_product(bitmap);
                    //targetImage.setImageBitmap(bitmap);
                    //listImages.add(bitmap);
                    List<Bitmap> temBitmap = new ArrayList<>();
                    temBitmap.add(bitmap);
                    ImageAdapter imageAdapter = new ImageAdapter(_context, temBitmap);

                    gallery.setAdapter(imageAdapter);

                } catch (FileNotFoundException e) {
                    // TODO Auto-generated catch block
                    e.printStackTrace();
                }
            }
        }
    }

    private void setSpinnerStation(ArrayList<String> data) {

        spinnerStation.setItems(data);
        spinnerStation.hasNoneOption(true);
        // spinnerStation.setSelection(new int[]{0});
        spinnerStation.setListener(this);

      /*
        ArrayAdapter<String> spinnerArrayAdapter = new ArrayAdapter<String>(
                this, R.layout.simple_spinner_items, data);
        spinnerStation.setAdapter(spinnerArrayAdapter);*/
    }

    @Override
    public void selectedIndices(List<Integer> indices) {
        // selectedStations.clear();


        for (int i = 0; i < currentBusinesslocation.size(); i++) {
            if (indices.contains(i) && i != 0) {
                currentBusinesslocation.get(i).setSelected(1);
                //    selectedStations.add(currentStation.get(i));
            } else {
                currentBusinesslocation.get(i).setSelected(0);
                // selectedStations.add(currentStation.get(i));
            }
        }

    }

    @Override
    public void selectedStrings(List<String> strings) {

    }

    public void onBackPressed(View view) {
        view.setFocusableInTouchMode(true);
        view.requestFocus();
        view.setOnKeyListener((v, keyCode, event) -> keyCode == KeyEvent.KEYCODE_BACK && event.getAction() == KeyEvent.ACTION_UP);
    }
}