package com.rising.high.tech.bigultimatenavdraw.ui.product;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;

import androidx.appcompat.widget.AppCompatButton;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.Spinner;
import android.widget.TextView;

import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.db.BrandDbController;
import com.rising.high.tech.bigultimatenavdraw.db.CategoryDbController;
import com.rising.high.tech.bigultimatenavdraw.db.ProductDbController;
import com.rising.high.tech.bigultimatenavdraw.db.BusinessLocationDbController;
import com.rising.high.tech.bigultimatenavdraw.db.TaxRatesDbController;
import com.rising.high.tech.bigultimatenavdraw.db.UnitDbController;
import com.rising.high.tech.bigultimatenavdraw.model.Brand;
import com.rising.high.tech.bigultimatenavdraw.model.Category;
import com.rising.high.tech.bigultimatenavdraw.model.Business_location;
import com.rising.high.tech.bigultimatenavdraw.model.Tax_rates;
import com.rising.high.tech.bigultimatenavdraw.model.Transaction;
import com.rising.high.tech.bigultimatenavdraw.model.Unit;
import com.rising.high.tech.bigultimatenavdraw.ui.adapter.SpinBrandAdapter;
import com.rising.high.tech.bigultimatenavdraw.ui.adapter.SpinCategoryAdapter;
import com.rising.high.tech.bigultimatenavdraw.ui.adapter.SpinStationAdapter;
import com.rising.high.tech.bigultimatenavdraw.ui.adapter.SpinTaxRatesAdapter;
import com.rising.high.tech.bigultimatenavdraw.ui.adapter.SpinUnitAdapter;
import com.rising.high.tech.bigultimatenavdraw.ui.businessetting.BusinessSettingsFragment;
import com.rising.high.tech.bigultimatenavdraw.ui.businesslocation.BusinessLocationFragment;
import com.rising.high.tech.bigultimatenavdraw.ui.product.adapter.ListItemProductAdapter;
import com.rising.high.tech.bigultimatenavdraw.util.SessionManager;
import com.rising.high.tech.bigultimatenavdraw.util.serverInteraction;

import java.util.ArrayList;

import butterknife.BindView;
import butterknife.ButterKnife;

import static com.rising.high.tech.bigultimatenavdraw.util.Constant.PRODUCT;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.PRODUCT_ADD;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.SERVER_MASTER;

public class ListProductFragment extends Fragment implements AdapterView.OnItemSelectedListener {
    private static final String TAG = "ListProductFragment";
    private Context _context;

    SpinUnitAdapter spinUnitAdapter;
    SpinBrandAdapter spinBrandAdapter;

    UnitDbController unitDbController;
    BrandDbController brandDbController;
    ListItemProductAdapter listItemProductAdapter;
    ProductDbController productDbController;
    CategoryDbController categoryDbController;
    BusinessLocationDbController businessLocationDbController;
    SpinCategoryAdapter spinCategoryAdapter;
    SpinStationAdapter spinStationAdapter;
    TaxRatesDbController taxRatesDbController;
    @BindView(R.id.spinner_brand)
    Spinner spinnerBrand;
    @BindView(R.id.filter_container)
    LinearLayout filterContainer;
    @BindView(R.id.filter_header)
    LinearLayout filterHeader;
    @BindView(R.id.id_add)
    Button addBtn;
    @BindView(R.id.recycler_purchases)
    RecyclerView recycle_products;
    @BindView(R.id.spinner_category)
    Spinner spinnerCategory;
    @BindView(R.id.spinner_station)
    Spinner spinnerStation;
    @BindView(R.id.spinner_unit)
    Spinner spinnerUnit;
    @BindView(R.id.id_filter)
    Button btnFilter;
    @BindView(R.id.filterArrow)
    ImageView filterArrow;
    @BindView(R.id.noProductFound)
    TextView noProductFound;
    @BindView(R.id.spinnerTaxRate)
    Spinner spinnerTaxRate;
    private ArrayList<Tax_rates> taxList;
    private ArrayList<Category> categoryList;
    private ArrayList<Unit> unitList;
    private ArrayList<Brand> brandList;
    private ArrayList<Business_location> businesslocationList;
    SpinTaxRatesAdapter spinTaxRatesAdapter;
    int taxId=0,brandID=0,categoryId=0,stationId=0,unitId=0;
    SessionManager session;
    public ListProductFragment() {
        // Required empty public constructor
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        // Inflate the layout for this fragment
        View root = inflater.inflate(R.layout.fragment_list_product, container, false);
        ButterKnife.bind(this, root);
        _context = getContext();
        session = new SessionManager(_context);

        checkRoles();
        listItemProductAdapter = new ListItemProductAdapter();
        recycle_products.setAdapter(listItemProductAdapter);
        recycle_products.setLayoutManager(new LinearLayoutManager(_context));

        initDB();
        initSpinners();
        initClickListners();

        listItemProductAdapter.setData(productDbController.getAllProduct());
        setItemView();


        return root;
    }

    private void checkRoles()
    {
        if (session.getBoolean(SERVER_MASTER) || !session.checkPermissionSubModule(PRODUCT_ADD))
        {
            addBtn.setVisibility(View.INVISIBLE);
        }

    }


    private void setItemView()
    {
        if(listItemProductAdapter.getItemCount() > 0)
        {
            recycle_products.setVisibility(View.VISIBLE);
            noProductFound.setVisibility(View.GONE);
        }
        else
        {
            recycle_products.setVisibility(View.GONE);
            noProductFound.setVisibility(View.VISIBLE);
        }
    }
    private void initClickListners() {
        btnFilter.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                filter();
            }
        });
        listItemProductAdapter.setOnDataChangeListener(new ListItemProductAdapter.OnDataChangeListener() {
            @Override
            public void onDataDeleted() {
                setItemView();
            }
        });

        addBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if(session.getBusinessModel().getId() == 0)
                {
                    showDialog(_context,"Setup business Settings", "Please Setup Business settings to Add your product",new BusinessSettingsFragment());
                }
                else if(businesslocationList.size() == 1)
                {
                    showDialog(_context,"Setup Business Location", "Please Add Your Business Location to Add your product",new BusinessLocationFragment());
                }
                else
                {
                    replaceFragment(new AddProductFragment());
                }

            }
        });

        filterHeader.setOnClickListener(new View.OnClickListener() {
            @SuppressLint("UseCompatLoadingForDrawables")
            @Override
            public void onClick(View v) {
                filterContainer.setVisibility(filterContainer.getVisibility() == View.VISIBLE ? View.GONE : View.VISIBLE);
             if(filterContainer.getVisibility() == View.VISIBLE)
             {
                 filterArrow.setImageDrawable(getResources().getDrawable(R.drawable.ic_baseline_expand_less_24));
                 filterContainer.setVisibility(View.GONE);
             }
             else
             {
                 filterArrow.setImageDrawable(getResources().getDrawable(R.drawable.ic_baseline_expand_more_24));
                 filterContainer.setVisibility(View.VISIBLE);
             }
            }
        });
    }

    private void initDB() {
        unitDbController = new UnitDbController(_context);

        brandDbController = new BrandDbController(_context);

        productDbController = new ProductDbController(_context);

        categoryDbController = new CategoryDbController(_context);

        businessLocationDbController = new BusinessLocationDbController(_context);

        taxRatesDbController = new TaxRatesDbController(_context);
    }

    private void initSpinners() {
        categoryList = new ArrayList<>();
        unitList = new ArrayList<>();
        businesslocationList = new ArrayList<>();
        brandList = new ArrayList<>();
        taxList = new ArrayList<>();

        categoryList=categoryDbController.getAllCategorySpinner();
        unitList=unitDbController.getAllUnitSpinner();
        businesslocationList = businessLocationDbController.getAllStationSpinner();
        brandList=brandDbController.getAllBrandSpinner();
        taxList=taxRatesDbController.getAllTax_ratesSpinner();

        spinCategoryAdapter = new SpinCategoryAdapter(_context, R.layout.custom_spinner_item, categoryList);
        spinnerCategory.setAdapter(spinCategoryAdapter);

        spinUnitAdapter = new SpinUnitAdapter(_context, android.R.layout.simple_spinner_item, unitList);
        spinnerUnit.setAdapter(spinUnitAdapter);

        spinStationAdapter = new SpinStationAdapter(_context, android.R.layout.simple_spinner_item, businesslocationList);
        spinnerStation.setAdapter(spinStationAdapter);

        spinBrandAdapter = new SpinBrandAdapter(_context, R.layout.custom_spinner_item, brandList);
        spinnerBrand.setAdapter(spinBrandAdapter);

        spinTaxRatesAdapter = new SpinTaxRatesAdapter(_context, android.R.layout.simple_spinner_item, taxList);
        spinnerTaxRate.setAdapter(spinTaxRatesAdapter);

        spinnerTaxRate.setOnItemSelectedListener(this);
        spinnerBrand.setOnItemSelectedListener(this);
        spinnerStation.setOnItemSelectedListener(this);
        spinnerUnit.setOnItemSelectedListener(this);
        spinnerCategory.setOnItemSelectedListener(this);

    }
    @Override
    public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {

        if (parent.getId() == R.id.spinner_station) {
            stationId = businesslocationList.get(position).getId();
        }
        if (parent.getId() == R.id.spinner_category) {
            categoryId = categoryList.get(position).getId();
        }
        if (parent.getId() == R.id.spinner_unit) {
            unitId = unitList.get(position).getId();
        }
        if (parent.getId() == R.id.spinnerTaxRate) {
            taxId = taxList.get(position).getId();
        }
        if (parent.getId() == R.id.spinner_brand) {
            brandID = brandList.get(position).getId();
        }

    }

    @Override
    public void onNothingSelected(AdapterView<?> parent) {

    }
    private void filter() {

        listItemProductAdapter.setData(productDbController.searchProductList(categoryId,unitId,stationId,taxId,brandID));
        listItemProductAdapter.notifyDataSetChanged();
        setItemView();
    }

    public void replaceFragment(Fragment someFragment) {
        FragmentTransaction transaction = getFragmentManager().beginTransaction();
        transaction.replace(R.id.nav_host_fragment, someFragment);
        transaction.addToBackStack(null);
        transaction.commit();
    }


    private void showDetail(Transaction transaction) {
        //Preparing views
        // get prompts.xml view
//        LayoutInflater li = LayoutInflater.from(_context);
//        View promptsView = li.inflate(R.layout.dialog_filtre_main, null);
//        SubVenteAdapter subVenteAdapter = new SubVenteAdapter();
//
//        androidx.appcompat.app.AlertDialog.Builder alertDialogBuilder = new androidx.appcompat.app.AlertDialog.Builder(
//                _context);
//
//        // set prompts.xml to alertdialog builder
//        alertDialogBuilder.setView(promptsView);
//        ArrayList<Sell_lines> sell_lines = sellLineDbController.getSellLineByTransaction(transaction.getId());
//        final RecyclerView recyclerView = promptsView.findViewById(R.id.recycle_sub_vente);
//        recyclerView.setAdapter(subVenteAdapter);
//        recyclerView.setLayoutManager(new LinearLayoutManager(_context));
//
//        subVenteAdapter.setData(sell_lines);
//        Log.d(TAG, "transactionns sells  ### " + new Gson().toJson(sell_lines));
//        final androidx.appcompat.app.AlertDialog mAlertDialog = alertDialogBuilder.create();
//        if (mAlertDialog.getWindow() != null)
//            mAlertDialog.getWindow().getAttributes().windowAnimations = R.style.SlidingDialogAnimation;
//        mAlertDialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
//
//        mAlertDialog.show();
    }

    public void showDialog(Context context, String title, String msg, Fragment fragment) {
        LayoutInflater li = LayoutInflater.from(context);
        View promptsView = li.inflate(R.layout.dialog_confirm_layout, null);
        androidx.appcompat.app.AlertDialog.Builder alertDialogBuilder = new androidx.appcompat.app.AlertDialog.Builder(context);
        alertDialogBuilder.setView(promptsView);
        final AppCompatTextView titleTxt = promptsView.findViewById(R.id.title);
        final AppCompatTextView messageTxt = promptsView.findViewById(R.id.message);
        final AppCompatButton noBtn = promptsView.findViewById(R.id.noBtn);
        final AppCompatButton yesBtn = promptsView.findViewById(R.id.yesBtn);
        titleTxt.setText(title);
        messageTxt.setText(msg);
        final androidx.appcompat.app.AlertDialog mAlertDialog = alertDialogBuilder.create();
        if (mAlertDialog.getWindow() != null) mAlertDialog.getWindow().getAttributes().windowAnimations = R.style.SlidingDialogAnimation;
        mAlertDialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        noBtn.setOnClickListener(v -> mAlertDialog.dismiss());

        yesBtn.setOnClickListener(v ->
        {
            mAlertDialog.dismiss();
            replaceFragment(fragment);
        });
        mAlertDialog.show();
        mAlertDialog.getWindow().setLayout(600, ViewGroup.LayoutParams.WRAP_CONTENT);
    }

}