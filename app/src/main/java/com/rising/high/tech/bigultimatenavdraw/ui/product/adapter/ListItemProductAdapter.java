package com.rising.high.tech.bigultimatenavdraw.ui.product.adapter;

import android.content.Context;
import android.content.res.Resources;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.gson.Gson;
import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.db.CategoryDbController;
import com.rising.high.tech.bigultimatenavdraw.db.ProductDbController;
import com.rising.high.tech.bigultimatenavdraw.db.ProductLocationDbController;
import com.rising.high.tech.bigultimatenavdraw.db.PurchaseLineDbController;
import com.rising.high.tech.bigultimatenavdraw.db.BusinessLocationDbController;
import com.rising.high.tech.bigultimatenavdraw.db.TaxRatesDbController;
import com.rising.high.tech.bigultimatenavdraw.db.TransactionDbController;
import com.rising.high.tech.bigultimatenavdraw.db.TransactionSellLineDbController;
import com.rising.high.tech.bigultimatenavdraw.db.VariationLocationDetailDbController;
import com.rising.high.tech.bigultimatenavdraw.db.VariationsDbController;
import com.rising.high.tech.bigultimatenavdraw.model.Discount;
import com.rising.high.tech.bigultimatenavdraw.model.Product;
import com.rising.high.tech.bigultimatenavdraw.model.Product_location;
import com.rising.high.tech.bigultimatenavdraw.model.Purchase_line;
import com.rising.high.tech.bigultimatenavdraw.model.Transaction;
import com.rising.high.tech.bigultimatenavdraw.model.Variation;
import com.rising.high.tech.bigultimatenavdraw.model.Variation_location_details;
import com.rising.high.tech.bigultimatenavdraw.ui.adapter.VariationProductLocationDetailsAdapter;
import com.rising.high.tech.bigultimatenavdraw.ui.discount.adapter.DiscountAdapter;
import com.rising.high.tech.bigultimatenavdraw.ui.product.EditProductFragment;
import com.rising.high.tech.bigultimatenavdraw.util.Constant;
import com.rising.high.tech.bigultimatenavdraw.util.SessionManager;
import com.rising.high.tech.bigultimatenavdraw.util.StringFormat;
import com.squareup.picasso.Callback;
import com.squareup.picasso.Picasso;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Locale;

import static com.rising.high.tech.bigultimatenavdraw.util.Constant.PRODUCT_DELETE;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.PRODUCT_EDIT;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.SERVER_MASTER;


public class ListItemProductAdapter extends RecyclerView.Adapter<ListItemProductAdapter.ListProductViewHolder> {

    private static final String TAG = "ListItemProductAdapter";
    private ArrayList<Product> dataList = new ArrayList<>();
    private Resources resources;
    private ProductDbController productDbController;
    private CategoryDbController categoryDbController;
    private BusinessLocationDbController businessLocationDbController;
    private VariationLocationDetailDbController variationLocationDetailDbController;
    private VariationsDbController variationsDbController;
    private ProductLocationDbController productLocationDbController;
    private PurchaseLineDbController purchaseLineDbController;
    private TransactionDbController transactionDbController;
    private TaxRatesDbController taxRatesDbController;
    private TransactionSellLineDbController transactionSellLineDbController;
    private SessionManager session;
    private HashMap<String, Object> user;
    private Context context;
    private Integer userId;

    @Override
    public ListProductViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        context = parent.getContext();
        resources = context.getResources();
        session = new SessionManager(context);
        user = session.getUserDetails();
        userId = (int) session.getUserDetails().get(session.ID_USER);

        productDbController = new ProductDbController(context);

        categoryDbController = new CategoryDbController(context);

        businessLocationDbController = new BusinessLocationDbController(context);

        productLocationDbController = new ProductLocationDbController(context);

        variationLocationDetailDbController = new VariationLocationDetailDbController(context);

        variationsDbController = new VariationsDbController(context);

        purchaseLineDbController = new PurchaseLineDbController(context);

        transactionDbController = new TransactionDbController(context);

        taxRatesDbController = new TaxRatesDbController(context);

        transactionSellLineDbController = new TransactionSellLineDbController(context);

        return new ListProductViewHolder(LayoutInflater.from(parent.getContext())
                .inflate(R.layout.list_product_item, parent, false));
    }

    @Override
    public void onBindViewHolder(ListProductViewHolder holder, int position) {

        holder.id_sku.setText(dataList.get(position).getSku());
        holder.product_name.setText(dataList.get(position).getName());

        Variation variation = variationsDbController.getVariationByProductId(dataList.get(position).getId());
        holder.purchase_price.setText(variation.getDpp_inc_tax() + user.get(session.KEY_SYMBOL));
        holder.selling_price.setText(variation.getSell_price_inc_tax() + user.get(session.KEY_SYMBOL));
        holder.category_text.setText(categoryDbController.getCategoryById(dataList.get(position).getCategory_id()).getName());
        String taxAmount = taxRatesDbController.getTax_ratesById(dataList.get(position).getTax()).getAmount();
        if (taxAmount == null || taxAmount.isEmpty()) {
            holder.taxAmount.setText("0");
        } else {
            holder.taxAmount.setText(taxAmount);
        }

        int qty_available = 0;
        String stations_names = "";
        for (Product_location product_location : productLocationDbController.getProductLocationByProductId(dataList.get(position).getId())) {
            Variation_location_details variation_location_details = variationLocationDetailDbController.getVariationLocationDetailsByStationIdProductId(Integer.parseInt(product_location.getLocation_id()), dataList.get(position).getId());
            qty_available += variation_location_details.getQty_available();
            stations_names += businessLocationDbController.getStationById(Integer.parseInt(product_location.getLocation_id())).getName() + " ,\n";
        }


        if (stations_names.length() > 0)
            stations_names = stations_names.substring(0, stations_names.length() - 2);
        holder.business_location.setText(stations_names);

        holder.stock_actual.setText((qty_available + " " + dataList.get(position).getUnit_actualname()));

        holder.imageProduct.setImageDrawable(resources.getDrawable(R.drawable.image_notavailable));
        if (dataList.get(position).getIs_sync().equals("no")) {
            if (dataList.get(position).getImage_product() != null)
                holder.imageProduct.setImageBitmap(dataList.get(position).getImage_product());
        } else {
            if (dataList.get(position).getImage_url() != null) {
                Picasso.get().load(dataList.get(position).getImage_url()).resize(100, 100).into(holder.imageProduct, new Callback() {
                    @Override
                    public void onSuccess() {
                    }

                    @Override
                    public void onError(Exception e) {
                        holder.imageProduct.setImageDrawable(resources.getDrawable(R.drawable.image_notavailable));
                    }
                });
            }
        }
        if (position % 2 == 1) {
            holder.linearLayout.setBackgroundColor(context.getResources().getColor(R.color.white));
        } else {
            holder.linearLayout.setBackgroundColor(context.getResources().getColor(R.color.lightGrey));
        }

        ArrayList<String> spinnerArray = new ArrayList<>();
        String[] list = context.getResources().getStringArray(R.array.array_action_product);
        spinnerArray.add(list[0]);
        spinnerArray.add(list[1]);
        if (!session.getBoolean(SERVER_MASTER)) {
            spinnerArray.add(list[2]);
            spinnerArray.add(list[3]);
            if (dataList.get(position).getEnable_stock() == 1) {
                spinnerArray.add(list[4]);
            }
        }
        ArrayAdapter<String> spinnerArrayAdapter = new ArrayAdapter<String>
                (context, android.R.layout.simple_spinner_item,
                        spinnerArray); //selected item will look like a spinner set from XML
        spinnerArrayAdapter.setDropDownViewResource(android.R.layout
                .simple_spinner_dropdown_item);
        holder.spinnerAction.setAdapter(spinnerArrayAdapter);


        holder.spinnerAction.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int i, long id) {
                holder.spinnerAction.setSelection(0, true);
                switch (i) {
                    case 1: {
                        viewDetail(position);
                        break;
                    }
                    case 2: {
                        if (session.getBoolean(SERVER_MASTER) || !session.checkPermissionSubModule(PRODUCT_EDIT)) {
                            Toast.makeText(context, resources.getString(R.string.label_not_autorized), Toast.LENGTH_LONG).show();
                        } else {
                            naviguateFragment(dataList.get(position).getId());
                        }
                        break;
                    }
                    case 3: {
                        if (!session.checkPermissionSubModule(PRODUCT_DELETE)) {
                            Toast.makeText(context, resources.getString(R.string.label_not_autorized), Toast.LENGTH_LONG).show();
                        } else if (transactionSellLineDbController.checkIfSellsByProduct(dataList.get(position).getId())) {
                            Toast.makeText(context, resources.getString(R.string.label_cannot_delete_produ), Toast.LENGTH_LONG).show();
                        } else {
                            deleteItem(position);
                        }
                        break;
                    }
                    case 4: {
                        editOpeningStockProduct(position);
                        break;
                    }
                }
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {

            }
        });


    }

    public void setData(ArrayList<Product> arrayList) {
        Collections.reverse(arrayList);
        this.dataList = arrayList;
        notifyDataSetChanged();
    }


    @Override
    public int getItemCount() {
        return dataList.size();
    }

    public class ListProductViewHolder extends RecyclerView.ViewHolder {
        ImageView imageProduct;
        TextView product_name, id_sku, category_text, stock_actual, selling_price, business_location, purchase_price, taxAmount;
        LinearLayout linearLayout;
        Spinner spinnerAction;

        public ListProductViewHolder(View itemView) {
            super(itemView);

            linearLayout = itemView.findViewById(R.id.linearLayout);
            imageProduct = itemView.findViewById(R.id.image_product);
            product_name = itemView.findViewById(R.id.product_name);
            id_sku = itemView.findViewById(R.id.id_sku);
            stock_actual = itemView.findViewById(R.id.stock_actual);
            category_text = itemView.findViewById(R.id.category_text);
            stock_actual = itemView.findViewById(R.id.stock_actual);
            selling_price = itemView.findViewById(R.id.selling_price);
            spinnerAction = itemView.findViewById(R.id.spinner_action);
            business_location = itemView.findViewById(R.id.business_location);
            purchase_price = itemView.findViewById(R.id.purchase_price);
            taxAmount = itemView.findViewById(R.id.taxAmount);

        }
    }

    private void editOpeningStockProduct(int postition) {
        //Preparing views
        // get prompts.xml view
        VariationProductLocationDetailsAdapter variationLocationDetailsAdapter = new VariationProductLocationDetailsAdapter();
        LayoutInflater li = LayoutInflater.from(context);
        View promptsView = li.inflate(R.layout.edit_product_dialog_main, null);
        androidx.appcompat.app.AlertDialog.Builder alertDialogBuilder = new androidx.appcompat.app.AlertDialog.Builder(
                context);

        alertDialogBuilder.setView(promptsView);

        final Button ButtonSave = promptsView.findViewById(R.id.btn_save);
        final Button ButtonClose = promptsView.findViewById(R.id.btn_close);
        final TextView product_name = promptsView.findViewById(R.id.product_name);
        final TextView subtotal_product = promptsView.findViewById(R.id.subtotal_product);
        final EditText qntity_remaining = promptsView.findViewById(R.id.qntity_remaining);
        final EditText unit_cost = promptsView.findViewById(R.id.unit_cost);
        final RecyclerView recycler_variation_location = promptsView.findViewById(R.id.recycler_variation_location);

        final androidx.appcompat.app.AlertDialog mAlertDialog = alertDialogBuilder.create();
        if (mAlertDialog.getWindow() != null)
            mAlertDialog.getWindow().getAttributes().windowAnimations = R.style.SlidingDialogAnimation;
        mAlertDialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));

        recycler_variation_location.setAdapter(variationLocationDetailsAdapter);
        recycler_variation_location.setLayoutManager(new LinearLayoutManager(context));

        variationLocationDetailsAdapter.setData(variationLocationDetailDbController.getVariationLocationDetailsByProductId(dataList.get(postition).getId()));
        Variation variation = variationsDbController.getVariationByProductId(dataList.get(postition).getId());

        unit_cost.setText(variation.getDefault_purchase_price());
        unit_cost.setEnabled(false);
        qntity_remaining.setText(dataList.get(postition).getQty_available() != null ? dataList.get(postition).getQty_available() : "00.0");
        subtotal_product.setText(dataList.get(postition).getQty_available() != null ? dataList.get(postition).getQty_available() : "00.0");
        product_name.setText(dataList.get(postition).getName());

        ButtonClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mAlertDialog.dismiss();
            }
        });

        ButtonSave.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                // categoryDbController.deleteItem(dataList.get(postition).getName());


                for (Variation_location_details variation_location_details : variationLocationDetailsAdapter.getData()) {
                    Transaction transaction = transactionDbController.getTransactionOpeningStockById(variation_location_details.getProduct_id(), variation_location_details.getLocation_id());

                    int idTransaction;
                    Float final_total = Float.parseFloat(variation.getDefault_purchase_price()) * variation_location_details.getQty_available();
                    Transaction transactionOpeningStock = new Transaction();
                    transactionOpeningStock.setBusiness_id(session.getBusinessModel().getId());
                    transactionOpeningStock.setLocation_id(variation_location_details.getLocation_id());
                    transactionOpeningStock.setType(Constant.OPENING_STOCK);
                    transactionOpeningStock.setStatus(Constant.RECEIVED);
                    transactionOpeningStock.setPayment_status(Constant.PAID);
                    transactionOpeningStock.setTransaction_date(StringFormat.actualTime());
                    transactionOpeningStock.setFinal_total(final_total + "");
                    transactionOpeningStock.setTotal_before_tax(final_total + "");
                    transactionOpeningStock.setOpening_stock_product_id(dataList.get(postition).getId());
                    transactionOpeningStock.setCreated_by(userId);
                    transactionOpeningStock.setIs_sync("no");

                    if (transaction.getId() == 0) {
                        idTransaction = transactionDbController.insertLocal(transactionOpeningStock);
                    } else {
                        transactionDbController.updateTransaction(transaction);
                        idTransaction = transaction.getId();
                    }
                    System.out.println("idTransaction.. " + idTransaction);
                    if (idTransaction > 0) {
                        System.out.println("idTransaction " + idTransaction);
                        System.out.println("idTransaction 2 " + transaction.getId());

                        Purchase_line purchase_lineData = purchaseLineDbController.getPurchaseLineTransaction(idTransaction);

                        Purchase_line purchase_line = new Purchase_line();
                        purchase_line.setTransaction_id(idTransaction);
                        purchase_line.setProduct_id(dataList.get(postition).getId());
                        purchase_line.setVariation_id(variation.getId());
                        purchase_line.setQuantity(variation_location_details.getQty_available());
                        purchase_line.setPp_without_discount(variation.getDefault_purchase_price());
                        purchase_line.setPurchase_price(variation.getDefault_purchase_price());
                        purchase_line.setPurchase_price_inc_tax(variation.getDpp_inc_tax());
                        purchase_line.setQuantity_sold(0);
                        purchase_line.setQuantity_returned(0);

                        double taxAmount = Double.parseDouble(variation.getDpp_inc_tax()) - Double.parseDouble(variation.getDefault_purchase_price());
                        purchase_line.setItem_tax(String.format(Locale.ENGLISH, "%.2f", taxAmount));

                        purchase_line.setTax_id(dataList.get(postition).getTax());
                        if (purchase_lineData.getId() == 0) {
                            purchaseLineDbController.insertLocal(purchase_line);
                        } else {
                            purchaseLineDbController.updatePurchaseLine(purchase_line);
                        }
                    }
                }

                for (Variation_location_details variation_location_details : variationLocationDetailsAdapter.getData()) {
                    variationLocationDetailDbController.update(variation_location_details);
                    //  if()
                }


                notifyItemChanged(postition);
                mAlertDialog.dismiss();
                notifyDataSetChanged();

            }
        });


        mAlertDialog.show();
    }

    private void viewDetail(int postition) {
        //Preparing views
        // get prompts.xml view
        LayoutInflater li = LayoutInflater.from(context);
        View promptsView = li.inflate(R.layout.view_product_dialog_main, null);

        androidx.appcompat.app.AlertDialog.Builder alertDialogBuilder = new androidx.appcompat.app.AlertDialog.Builder(
                context);

        alertDialogBuilder.setView(promptsView);

        final Button ButtonSave = promptsView.findViewById(R.id.btn_save);
        final Button ButtonClose = promptsView.findViewById(R.id.btn_close);
        final TextView product_name = promptsView.findViewById(R.id.product_name);
        final TextView qntity_remaining = promptsView.findViewById(R.id.qntity_remaining);
        final TextView unit_cost = promptsView.findViewById(R.id.unit_cost);
        final TextView sku = promptsView.findViewById(R.id.id_sku);
        final TextView catgeroy_name = promptsView.findViewById(R.id.catgeroy_name);
        final TextView product_unit = promptsView.findViewById(R.id.product_unit);
        final TextView product_barcode_type = promptsView.findViewById(R.id.product_barcode_type);
        final TextView product_type = promptsView.findViewById(R.id.product_type);

        final TextView id_exc_tax = promptsView.findViewById(R.id.id_exc_tax);
        final TextView id_inc_tax = promptsView.findViewById(R.id.id_inc_tax);
        final TextView id_margin = promptsView.findViewById(R.id.id_margin);
        final TextView id_default_sel_price_exc_tax = promptsView.findViewById(R.id.id_default_sel_price_exc_tax);
        final TextView default_sell_inc_tax = promptsView.findViewById(R.id.default_sell_inc_tax);


        final ImageView image_product = promptsView.findViewById(R.id.image_product);

        final androidx.appcompat.app.AlertDialog mAlertDialog = alertDialogBuilder.create();
        if (mAlertDialog.getWindow() != null)
            mAlertDialog.getWindow().getAttributes().windowAnimations = R.style.SlidingDialogAnimation;
        mAlertDialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));


        image_product.setImageDrawable(resources.getDrawable(R.drawable.image_notavailable));
        if (dataList.get(postition).getIs_sync().equals("no")) {
            if (dataList.get(postition).getImage_product() != null)
                image_product.setImageBitmap(dataList.get(postition).getImage_product());
        } else {
            if (dataList.get(postition).getImage_url() != null) {
                Picasso.get().load(dataList.get(postition).getImage_url()).resize(100, 100).into(image_product, new Callback() {
                    @Override
                    public void onSuccess() {
                    }

                    @Override
                    public void onError(Exception e) {
                        image_product.setImageDrawable(resources.getDrawable(R.drawable.image_notavailable));
                    }
                });
            }
        }

        Variation variation = variationsDbController.getVariationByProductId(dataList.get(postition).getId());
        unit_cost.setText(variation.getDefault_purchase_price());

        int qty_available = 0;
        for (Product_location product_location : productLocationDbController.getProductLocationByProductId(dataList.get(postition).getId())) {
            Variation_location_details variation_location_details = variationLocationDetailDbController.getVariationLocationDetailsByStationIdProductId(Integer.parseInt(product_location.getLocation_id()), dataList.get(postition).getId());
            qty_available += variation_location_details.getQty_available();
        }

        qntity_remaining.setText(qty_available +"");
        product_name.setText(dataList.get(postition).getName());
        sku.setText(dataList.get(postition).getSku());
        catgeroy_name.setText(dataList.get(postition).getCategory_name());
        product_unit.setText(dataList.get(postition).getUnit_actualname() + " " + dataList.get(postition).getUnit_shortname());
        product_barcode_type.setText(dataList.get(postition).getBarcode_type());
        product_type.setText(dataList.get(postition).getType());
        id_exc_tax.setText(variation.getDefault_purchase_price());
        id_inc_tax.setText(variation.getDpp_inc_tax());
        id_margin.setText(variation.getProfit_percent());
        id_default_sel_price_exc_tax.setText(variation.getDefault_sell_price());
        default_sell_inc_tax.setText(variation.getSell_price_inc_tax());

        ButtonClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mAlertDialog.dismiss();
            }
        });


        mAlertDialog.show();
        //  mAlertDialog.getWindow().setLayout(1600, 800);
    }

    private void deleteItem(int postition) {
        //Preparing views
        // get prompts.xml view
        LayoutInflater li = LayoutInflater.from(context);
        View promptsView = li.inflate(R.layout.delete_dialog_main, null);

        androidx.appcompat.app.AlertDialog.Builder alertDialogBuilder = new androidx.appcompat.app.AlertDialog.Builder(
                context);

        alertDialogBuilder.setView(promptsView);

        final Button ButtonSave = promptsView.findViewById(R.id.btn_save);
        final Button ButtonClose = promptsView.findViewById(R.id.btn_close);

        final androidx.appcompat.app.AlertDialog mAlertDialog = alertDialogBuilder.create();
        if (mAlertDialog.getWindow() != null)
            mAlertDialog.getWindow().getAttributes().windowAnimations = R.style.SlidingDialogAnimation;
        mAlertDialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));

        ButtonClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mAlertDialog.dismiss();
            }
        });

        ButtonSave.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                //check if transactions made with this product

                productDbController.deleteProduct(dataList.get(postition).getId());
                dataList.remove(postition);

                mOnDataChangeListener.onDataDeleted();
                mAlertDialog.dismiss();
                notifyDataSetChanged();
            }
        });


        mAlertDialog.show();
    }

    private void naviguateFragment(int id) {
        AppCompatActivity activity = (AppCompatActivity) context;
        Bundle bundle = new Bundle();
        bundle.putInt("id", id);
        Fragment myFragment = new EditProductFragment();
        myFragment.setArguments(bundle);
        activity.getSupportFragmentManager().beginTransaction().replace(R.id.nav_host_fragment, myFragment).addToBackStack(null).commit();
    }

    public interface OnDataChangeListener {
        void onDataDeleted();
    }

    ListItemProductAdapter.OnDataChangeListener mOnDataChangeListener;

    public void setOnDataChangeListener(ListItemProductAdapter.OnDataChangeListener onDataChangeListener) {
        mOnDataChangeListener = onDataChangeListener;
    }

}
