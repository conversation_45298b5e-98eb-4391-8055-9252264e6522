package com.rising.high.tech.bigultimatenavdraw.ui.profitloss;

import android.content.Context;
import android.content.res.Resources;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.fragment.app.Fragment;

import com.google.gson.Gson;
import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.db.ProfitLossDbController;
import com.rising.high.tech.bigultimatenavdraw.model.ProfitLossReport;
import com.rising.high.tech.bigultimatenavdraw.util.SessionManager;

import java.util.HashMap;

public class ProfitLossFragment extends Fragment {
    private static final String TAG = "ProfitLossFragment";
    private Context _context;

    private ProfitLossReport profitLossReport;
    private Resources resources;

    ProfitLossDbController profitLossDbController;
    TextView benficeBruteTxt, beneficeNetTxt, openingStockTxt, openingStockBySp , closingStock, closingStockBySpTxt, totalSellTxt, totalExpenseTxt;

    private SessionManager session;
    private HashMap<String, Object> user;

    public ProfitLossFragment() {
        // Required empty public constructor
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        // Inflate the layout for this fragment
        View root = inflater.inflate(R.layout.fragment_profit_loss, container, false);
        _context = getContext();
        resources = getResources();

        session = new SessionManager(_context);
        user = session.getUserDetails();

        profitLossDbController = new ProfitLossDbController(_context);
        profitLossDbController.open();
        profitLossReport =  profitLossDbController.getAllProfitLossReport();


        if(profitLossReport.getClosing_stock()!=null) {

            benficeBruteTxt = root.findViewById(R.id.benfice_brute_txt);
            beneficeNetTxt = root.findViewById(R.id.benefice_net_txt);
            openingStockTxt = root.findViewById(R.id.opening_stock_txt);
            openingStockBySp = root.findViewById(R.id.opening_stock_by_sp_txt);
            closingStock = root.findViewById(R.id.closing_stock_txt);
            closingStockBySpTxt = root.findViewById(R.id.closing_stock_by_sp_txt);
            totalSellTxt = root.findViewById(R.id.total_sell_txt);
            totalExpenseTxt = root.findViewById(R.id.total_expense_txt);

            benficeBruteTxt.setText(profitLossReport.getGross_profit());
            beneficeNetTxt.setText(profitLossReport.getNet_profit());
            openingStockTxt.setText(profitLossReport.getOpening_stock() + " " + user.get(session.KEY_SYMBOL));
            openingStockBySp.setText(profitLossReport.getOpening_stock_by_sp() + " " + user.get(session.KEY_SYMBOL));
            closingStock.setText(profitLossReport.getClosing_stock() + " " + user.get(session.KEY_SYMBOL));
            closingStockBySpTxt.setText(profitLossReport.getClosing_stock_by_sp() + " " + user.get(session.KEY_SYMBOL));
            totalSellTxt.setText(profitLossReport.getTotal_sell() + " " + user.get(session.KEY_SYMBOL));
            totalExpenseTxt.setText(profitLossReport.getTotal_expense() + " " + user.get(session.KEY_SYMBOL));

        }
        return root;
    }

}