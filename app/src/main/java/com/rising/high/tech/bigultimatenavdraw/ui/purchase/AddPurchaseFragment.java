package com.rising.high.tech.bigultimatenavdraw.ui.purchase;

import android.app.DatePickerDialog;
import android.content.Context;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.AutoCompleteTextView;
import android.widget.Button;
import android.widget.DatePicker;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.material.snackbar.Snackbar;
import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.db.ContactDbController;
import com.rising.high.tech.bigultimatenavdraw.db.ProductDbController;
import com.rising.high.tech.bigultimatenavdraw.db.PurchaseLineDbController;
import com.rising.high.tech.bigultimatenavdraw.db.BusinessLocationDbController;
import com.rising.high.tech.bigultimatenavdraw.db.TaxRatesDbController;
import com.rising.high.tech.bigultimatenavdraw.db.TransactionDbController;
import com.rising.high.tech.bigultimatenavdraw.db.TransactionPayementDbController;
import com.rising.high.tech.bigultimatenavdraw.db.VariationLocationDetailDbController;
import com.rising.high.tech.bigultimatenavdraw.db.VariationsDbController;
import com.rising.high.tech.bigultimatenavdraw.model.Business_location;
import com.rising.high.tech.bigultimatenavdraw.model.Contact;
import com.rising.high.tech.bigultimatenavdraw.model.Product;
import com.rising.high.tech.bigultimatenavdraw.model.Purchase_line;
import com.rising.high.tech.bigultimatenavdraw.model.Tax_rates;
import com.rising.high.tech.bigultimatenavdraw.model.Transaction;
import com.rising.high.tech.bigultimatenavdraw.model.Variation;
import com.rising.high.tech.bigultimatenavdraw.model.Variation_location_details;
import com.rising.high.tech.bigultimatenavdraw.ui.adapter.DShortProductAdapter;
import com.rising.high.tech.bigultimatenavdraw.ui.adapter.SpinContactAdapter;
import com.rising.high.tech.bigultimatenavdraw.ui.adapter.SpinStationAdapter;
import com.rising.high.tech.bigultimatenavdraw.ui.adapter.SpinTaxRatesAdapter;
import com.rising.high.tech.bigultimatenavdraw.util.FileUtil;
import com.rising.high.tech.bigultimatenavdraw.util.ProductUtil;
import com.rising.high.tech.bigultimatenavdraw.util.SessionManager;
import com.rising.high.tech.bigultimatenavdraw.util.StringFormat;

import java.util.ArrayList;
import java.util.Calendar;

import butterknife.BindView;
import butterknife.ButterKnife;

import static com.rising.high.tech.bigultimatenavdraw.util.Constant.DUE;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.FIXED;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.ORDERED;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.PAID;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.PARTIAL;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.PENDING;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.PERCENTAGE;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.PURCHASE;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.RECEIVED;

public class AddPurchaseFragment extends Fragment {
    private static final String TAG = "AddPurchaseFragment";
    private Context _context;
    final Calendar c = Calendar.getInstance();

    private SpinStationAdapter spinStationAdapter;
    private SpinContactAdapter spinContactAdapter;
    private BusinessLocationDbController businessLocationDbController;
    private ProductDbController productDbController;
    private TransactionDbController transactionDbController;
    private TransactionPayementDbController transactionPayementDbController;
    private PurchaseLineDbController purchaseLineDbController;
    private VariationsDbController variationsDbController;
    private VariationLocationDetailDbController variationLocationDetailDbController;
    private SpinTaxRatesAdapter spinTaxRatesAdapter;
    private TaxRatesDbController taxRatesDbController;

    private ContactDbController contactDbController;
    private ArrayList<Integer> productIds = new ArrayList<>();

//    ImageView btn_more_product;
    LinearLayout container_product_search, container_sub_product;

    @BindView(R.id.id_back)
    Button addBack;
    @BindView(R.id.add_btn)
    Button addBtn;
    @BindView(R.id.purchase_date)
    EditText purchaseDate;
    @BindView(R.id.spinner_location)
    Spinner spinnerLocation;
    @BindView(R.id.spin_payment_method)
    Spinner spinnerPaymentMethod;
    @BindView(R.id.spinner_discount_type)
    Spinner spinnerDiscountType;
    @BindView(R.id.spinner_supplier)
    Spinner spinnerSupplier;
    @BindView(R.id.spinner_purchase_status)
    Spinner spinnerPurchaseStatus;
    @BindView(R.id.amount_txt)
    EditText amountTxt;
    @BindView(R.id.pay_term_number)
    EditText payTermNumber;
    @BindView(R.id.paid_on_txt)
    EditText paidOnTxt;
    @BindView(R.id.total_items)
    TextView totalItemsTxt;
    @BindView(R.id.additional_shiping_ch)
    EditText additionalShipingTxt;
    @BindView(R.id.net_total_amount)
    TextView netTotalAmountTxt;
    @BindView(R.id.purchase_total)
    TextView purchaseTotal;
    @BindView(R.id.discount_amnt_txt)
    TextView discountAmntTxt;
    @BindView(R.id.total_due)
    TextView totalDue;
    @BindView(R.id.recycle_product)
    RecyclerView recycle_product;
    @BindView(R.id.search_edit)
    AutoCompleteTextView searchEdit;
    @BindView(R.id.spinner_term_duration)
    Spinner spinnerTermDuration;
    @BindView(R.id.shipping_detail_txt)
    EditText shippingDetailTxt;
    @BindView(R.id.payement_note)
    EditText payementNote;
    @BindView(R.id.linearLayout)
    LinearLayout linearLayout;
    @BindView(R.id.spinner_tax)
    Spinner spinnerTaxRates;
    @BindView(R.id.id_tax_amount)
    EditText taxAmount;
    @BindView(R.id.id_discount_amount)
    EditText discountAmount;

    @BindView(R.id.additional_note_id)
    EditText additionalNote;

    DShortProductAdapter dShortProductAdapter;
    SessionManager session;
    private Integer userId;
    public AddPurchaseFragment() {
        // Required empty public constructor
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        // Inflate the layout for this fragment
        View root = inflater.inflate(R.layout.activity_add_purchase, container, false);
        _context = getContext();
        ButterKnife.bind(this, root);
        session = new SessionManager(_context);

        container_product_search = root.findViewById(R.id.container_product_search);

        container_sub_product = root.findViewById(R.id.container_sub_product);

        dShortProductAdapter = new DShortProductAdapter();
        recycle_product.setAdapter(dShortProductAdapter);
        recycle_product.setLayoutManager(new LinearLayoutManager(_context));


        initDb();
        initSpinners();
        initClickListners();

        setProductSearchDapter(productDbController.getAllProduct());

        return root;
    }

    private void initDb() {
        businessLocationDbController = new BusinessLocationDbController(_context);
        contactDbController = new ContactDbController(_context);
        productDbController = new ProductDbController(_context);
        transactionDbController = new TransactionDbController(_context);
        transactionPayementDbController = new TransactionPayementDbController(_context);
        purchaseLineDbController = new PurchaseLineDbController(_context);
        variationsDbController = new VariationsDbController(_context);
        variationLocationDetailDbController = new VariationLocationDetailDbController(_context);
        taxRatesDbController = new TaxRatesDbController(_context);

    }

    private void initSpinners() {

        spinStationAdapter = new SpinStationAdapter(_context, android.R.layout.simple_spinner_item, businessLocationDbController.getAllStationSpinner());
        spinnerLocation.setAdapter(spinStationAdapter);

        spinContactAdapter = new SpinContactAdapter(_context, android.R.layout.simple_spinner_item, contactDbController.getSpinSupplier());
        spinnerSupplier.setAdapter(spinContactAdapter);

        spinTaxRatesAdapter = new SpinTaxRatesAdapter(_context, android.R.layout.simple_spinner_item, taxRatesDbController.getAllTax_ratesSpinner());
        spinnerTaxRates.setAdapter(spinTaxRatesAdapter);

        spinnerDiscountType.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                setViewAount();
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {

            }
        });

        spinnerTaxRates.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                //   setViewAount();

//                Float discount = Float.parseFloat(discountAmntTxt.getText().toString());
//                Float totalnet = Float.parseFloat(netTotalAmountTxt.getText().toString());
//                Float taxamount = Float.parseFloat(taxAmount.getText().toString());
//                Float shippingcharge = Float.parseFloat(additionalShipingTxt.getText().toString());
//
//                Tax_rates tax_rates = (Tax_rates) spinnerTaxRates.getSelectedItem();
//                float taxpercentage = Float.parseFloat(tax_rates.getAmount());
//
//                Float orderTaxAmount = 0.f;
//
//                if (spinnerDiscountType.getSelectedItemPosition() == 1) {
//                    orderTaxAmount = ((totalnet - discount) * taxpercentage / 100);
//                } else if (spinnerDiscountType.getSelectedItemPosition() == 2) {
//                    orderTaxAmount = ((totalnet) * taxpercentage / 100);
//                }
//
//                taxAmount.setText(orderTaxAmount + "");
//                purchaseTotal.setText((totalnet - discount + orderTaxAmount + shippingcharge) + "");

                setViewAount();

            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {

            }
        });

    }


    private void initClickListners() {
        userId=(int)session.getUserDetails().get(session.ID_USER);

        addBack.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                replaceFragment(new PurchaseFragment());
            }
        });

        addBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                addPurchase();
            }
        });

        paidOnTxt.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                DatePickerDialog datePickerDialog = new DatePickerDialog(_context,
                        new DatePickerDialog.OnDateSetListener() {
                            @Override
                            public void onDateSet(DatePicker datePicker, int year, int month, int day) {
                                String myFormat = StringFormat.populateSetFullDate(year, month, day, c.get(Calendar.HOUR), c.get(Calendar.MINUTE));
                                paidOnTxt.setText(myFormat);
                            }
                        }, c.get(Calendar.YEAR), c.get(Calendar.MONTH), c.get(Calendar.DAY_OF_MONTH));
                datePickerDialog.show();
            }
        });

        purchaseDate.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                DatePickerDialog datePickerDialog = new DatePickerDialog(_context,
                        new DatePickerDialog.OnDateSetListener() {
                            @Override
                            public void onDateSet(DatePicker datePicker, int year, int month, int day) {
                                String myFormat = StringFormat.populateSetFullDate(year, month, day, c.get(Calendar.HOUR), c.get(Calendar.MINUTE));
                                purchaseDate.setText(myFormat);
                            }
                        }, c.get(Calendar.YEAR), c.get(Calendar.MONTH), c.get(Calendar.DAY_OF_MONTH));
                datePickerDialog.show();
            }
        });

        additionalShipingTxt.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                if (s.length() != 0) {
//                    Float total_purchase = Float.parseFloat(purchaseTotal.getText().toString());
//
//                    Float total = Float.parseFloat(netTotalAmountTxt.getText().toString()) + Float.parseFloat(additionalShipingTxt.getText().toString());
//                    if (spinnerDiscountType.getSelectedItemPosition() == 1) {
//                        total = total - Float.parseFloat(discountAmntTxt.getText().toString());
//                    } else if (spinnerDiscountType.getSelectedItemPosition() == 2) {
//                        total = (total * (1 - Float.parseFloat(discountAmntTxt.getText().toString()) / 100));
//                    }
//                    purchaseTotal.setText(total + "");
//                    totalDue.setText((Float.parseFloat(purchaseTotal.getText().toString()) - Float.parseFloat(amountTxt.getText().toString())) + "");

                    setViewAount();

                }

            }

            @Override
            public void afterTextChanged(Editable s) {

            }

        });

//        btn_more_product.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                btn_more_product.animate().rotationBy(180f).start();
//                container_product_search.setVisibility(container_product_search.getVisibility() == View.VISIBLE ? View.GONE : View.VISIBLE);
//            }
//        });

        amountTxt.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                if (s.length() != 0) {
                    totalDue.setText((Float.parseFloat(purchaseTotal.getText().toString()) - Float.parseFloat(amountTxt.getText().toString())) + "");
                }
            }

            @Override
            public void afterTextChanged(Editable s) {

            }
        });

        discountAmntTxt.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                if (s.length() != 0) {
                    Float discount = 0.0f;
//                    if (spinnerDiscountType.getSelectedItemPosition() == 1) {
//                        discount = Float.parseFloat(netTotalAmountTxt.getText().toString()) - Float.parseFloat(discountAmntTxt.getText().toString());
//                        purchaseTotal.setText(discount + "");
//                        totalDue.setText(((Float.parseFloat(purchaseTotal.getText().toString()) - Float.parseFloat(amountTxt.getText().toString()) + "")));
//
//                    } else if (spinnerDiscountType.getSelectedItemPosition() == 2) {
//                        discount = (Float.parseFloat(netTotalAmountTxt.getText().toString()) * ((1 - Float.parseFloat(discountAmntTxt.getText().toString()) / 100)));
//                        purchaseTotal.setText(discount + "");
//                        totalDue.setText(((Float.parseFloat(purchaseTotal.getText().toString()) - Float.parseFloat(amountTxt.getText().toString()) + "")));
//                    }
//                    purchaseTotal.setText(discount + "");

                    setViewAount();
                }
            }

            @Override
            public void afterTextChanged(Editable s) {

            }
        });

        dShortProductAdapter.setOnDataChangeListener(new DShortProductAdapter.OnDataChangeListener() {
            @Override
            public void onDataChanged(ArrayList<Product> products) {

                netTotalAmountTxt.setText(ProductUtil.getTotalAmountDpp(products, _context));
                totalItemsTxt.setText(products.size() + "");

//                totalDue.setText((Float.parseFloat(netTotalAmountTxt.getText().toString()) - Float.parseFloat(amountTxt.getText().toString()) + ""));
//                purchaseTotal.setText(getPurchaseTotal());

                setViewAount();
            }
        });

        purchaseDate.setText(StringFormat.populateSetFullDate(c.get(Calendar.YEAR), c.get(Calendar.MONTH), c.get(Calendar.DAY_OF_MONTH), c.get(Calendar.HOUR), c.get(Calendar.MINUTE)));
        paidOnTxt.setText(StringFormat.populateSetFullDate(c.get(Calendar.YEAR), c.get(Calendar.MONTH), c.get(Calendar.DAY_OF_MONTH), c.get(Calendar.HOUR), c.get(Calendar.MINUTE)));


    }

    private void addPurchase() {

        if (spinnerSupplier.getSelectedItemPosition() == 0) {
            //     Toast.makeText(_context, _context.getResources().getString(R.string.label_select_ssupplier), Toast.LENGTH_LONG).show();
            Snackbar snackbar = Snackbar.make(linearLayout, _context.getResources().getString(R.string.label_select_ssupplier), Snackbar.LENGTH_LONG);
            snackbar.show();
        }else if (spinnerPurchaseStatus.getSelectedItemPosition() == 0) {
            Snackbar snackbar = Snackbar.make(linearLayout, _context.getResources().getString(R.string.label_select_statuus), Snackbar.LENGTH_LONG);
            snackbar.show();
        }else if (spinnerLocation.getSelectedItemPosition() == 0) {
            Snackbar snackbar = Snackbar.make(linearLayout, _context.getResources().getString(R.string.label_plz_select_locations), Snackbar.LENGTH_LONG);
            snackbar.show();
        }else if ( dShortProductAdapter.getItemCount()== 0){
            Snackbar snackbar = Snackbar.make(linearLayout, _context.getResources().getString(R.string.label_choose_product), Snackbar.LENGTH_LONG);
            snackbar.show();
        }else if (
                 !purchaseDate.getText().toString().equals("")
                && !amountTxt.getText().toString().equals("")
                && !paidOnTxt.getText().toString().equals("")
        ) {
            Transaction transaction = new Transaction();
            Float due_total = Float.parseFloat(totalDue.getText().toString());
            Float purchase_total = Float.parseFloat(purchaseTotal.getText().toString());

            // TODO implement session bussines id to be recuperate
            transaction.setBusiness_id(1);

            Business_location businesslocation = (Business_location) spinnerLocation.getSelectedItem();
            transaction.setLocation_id(businesslocation.getId());

            transaction.setType(PURCHASE);
            transaction.setRef_no(StringFormat.generateInvoicePurchaseNo(_context));

            switch (spinnerPurchaseStatus.getSelectedItemPosition()){
                case 1:{
                    transaction.setStatus(RECEIVED);
                }
                case 2:{
                    transaction.setStatus(PENDING);
                }
                case 3:{
                    transaction.setStatus(ORDERED);
                }
            }

            if (due_total <= 0) {
                transaction.setPayment_status(PAID);
            } else if (due_total > 0 && due_total < purchase_total) {
                transaction.setPayment_status(PARTIAL);
            } else {
                transaction.setPayment_status(DUE);
            }

            Contact contact = (Contact) spinnerSupplier.getSelectedItem();
            transaction.setContact_id(contact.getId());

            transaction.setTransaction_date(purchaseDate.getText().toString());
            transaction.setTotal_before_tax(netTotalAmountTxt.getText().toString());

            if (spinnerTaxRates.getSelectedItemPosition() != 0) {
                Tax_rates tax_rates = (Tax_rates) spinnerTaxRates.getSelectedItem();
                transaction.setTax_id(tax_rates.getId());
            }
            transaction.setDiscount_type(spinnerDiscountType.getSelectedItemPosition() == 1 ? FIXED : spinnerDiscountType.getSelectedItemPosition() == 2 ? PERCENTAGE : null);
            transaction.setDiscount_amount(discountAmntTxt.getText().toString());
            transaction.setShipping_charges(additionalShipingTxt.getText().toString());
            transaction.setFinal_total(purchaseTotal.getText().toString());
            transaction.setCreated_by(userId);
            transaction.setPay_term_number(payTermNumber.getText().toString());
            transaction.setPay_term_type(spinnerTermDuration.getSelectedItem().toString());
            transaction.setShipping_details(shippingDetailTxt.getText().toString());
            transaction.setAdditional_notes(additionalNote.getText().toString());


            int idInsert = transactionDbController.insertLocal(transaction);
            if (idInsert > 0) {

                transaction.setTransaction_id(idInsert);
                transaction.setAmount(Float.parseFloat(amountTxt.getText().toString()));
                transaction.setMethod(spinnerPaymentMethod.getSelectedItem().toString().toLowerCase());
                transaction.setNote(payementNote.getText().toString());
                transaction.setPaid_on(paidOnTxt.getText().toString());

                transactionPayementDbController.insertLocal(transaction);

                for (Product product : dShortProductAdapter.getData()) {

                    Purchase_line purchaseLine = new Purchase_line();
                    purchaseLine.setTransaction_id(idInsert);
                    purchaseLine.setProduct_id(product.getId());
                    purchaseLine.setVariation_id(product.getId());
                    purchaseLine.setQuantity(product.getSell_qte());
                    Variation variation = variationsDbController.getVariationByProductId(product.getId());
                    purchaseLine.setPp_without_discount(variation.getDefault_purchase_price());
                    purchaseLine.setPurchase_price(variation.getDefault_purchase_price());
                    purchaseLine.setPurchase_price_inc_tax(variation.getDpp_inc_tax());

                    purchaseLineDbController.insertLocal(purchaseLine);

                    if (spinnerPurchaseStatus.getSelectedItemPosition()==1){
                        Variation_location_details variation_location_details=variationLocationDetailDbController.getVariationLocationDetailsByStationIdProductId(businesslocation.getId() ,product.getId());
                        variation_location_details.setOld_qty_available(variation_location_details.getQty_available());
                        Integer qty = variation_location_details.getQty_available() + product.getSell_qte();
                        variation_location_details.setQty_available(qty);
                        variationLocationDetailDbController.update(variation_location_details);
                    }

                }

           //    Toast.makeText(_context, getResources().getString(R.string.lbl_added_success), Toast.LENGTH_LONG).show();
                FileUtil.showDialog(_context, "Successful", getResources().getString(R.string.stock_purchase_success));

                replaceFragment(new PurchaseFragment());

            } else {
                Toast.makeText(_context, "Error insert ", Toast.LENGTH_LONG).show();
            }
        } else {
            Toast.makeText(_context, getResources().getString(R.string.label_all_field_required), Toast.LENGTH_LONG).show();
        }
    }

    private String getPurchaseTotal() {

        Float purchaseTotal = 0.f;
        purchaseTotal = Float.parseFloat(netTotalAmountTxt.getText().toString()) + Float.parseFloat(additionalShipingTxt.getText().toString());

        return purchaseTotal + "";
    }

    private void setProductSearchDapter(ArrayList<Product> arrayListTemp) {
        ArrayAdapter<Product> adapter = new ArrayAdapter<Product>(_context, android.R.layout.simple_list_item_1, arrayListTemp);
        searchEdit.setAdapter(adapter);
        searchEdit.setDropDownBackgroundDrawable(new ColorDrawable(_context.getResources().getColor(R.color.colorPrimary)));
        searchEdit.setOnItemClickListener(new AdapterView.OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> parent, View arg1, int pos, long id) {
                Business_location businesslocation = (Business_location) spinnerLocation.getSelectedItem();
                Product selected = (Product) parent.getAdapter().getItem(pos);
                searchEdit.setText("");

                if(spinnerLocation.getSelectedItemPosition()==0){
                    Snackbar snackbar = Snackbar.make(linearLayout, _context.getResources().getString(R.string.lbl_please_select_station), Snackbar.LENGTH_LONG);
                    snackbar.show();
                }

                else if(!variationLocationDetailDbController.isProductHasVariationInStation(businesslocation.getId(), selected.getId())){
                    Snackbar snackbar = Snackbar.make(linearLayout, _context.getResources().getString(R.string.label_empty_add_product_to) + " " + businesslocation.getName(), Snackbar.LENGTH_LONG);
                    snackbar.show();

                }else {
                    dShortProductAdapter.updateData(selected);
                    container_sub_product.setVisibility(View.VISIBLE);
                }

                //    inputManager.hideSoftInputFromWindow(getCurrentFocus().getWindowToken(), InputMethodManager.HIDE_NOT_ALWAYS);
            }
        });

    }




    public void replaceFragment(Fragment someFragment) {
        FragmentTransaction transaction = getFragmentManager().beginTransaction();
        transaction.replace(R.id.nav_host_fragment, someFragment);
        transaction.addToBackStack(null);
        transaction.commit();
    }

    private void setViewAount() {
        Float discount = Float.parseFloat(discountAmntTxt.getText().toString());
        Float totalnet = Float.parseFloat(netTotalAmountTxt.getText().toString());
        Float taxamount = Float.parseFloat(taxAmount.getText().toString());
        Float shippingcharge = Float.parseFloat(additionalShipingTxt.getText().toString());
        Tax_rates tax_rates = (Tax_rates) spinnerTaxRates.getSelectedItem();
        float taxpercentage = Float.parseFloat(tax_rates.getAmount());
        taxAmount.setText((taxpercentage * (totalnet + discount) / 100) + "");
        switch (spinnerDiscountType.getSelectedItemPosition()) {
            case 0: {
                discountAmount.setText("00");
                discount = 0.f;
                purchaseTotal.setText((totalnet - discount + taxamount + shippingcharge) + "");
                totalDue.setText((Float.parseFloat(purchaseTotal.getText().toString()) - Float.parseFloat(amountTxt.getText().toString()) + ""));

                break;
            }
            case 1: {

                totalnet = Float.parseFloat(netTotalAmountTxt.getText().toString());
                taxamount = Float.parseFloat(taxAmount.getText().toString());
                shippingcharge = shippingcharge;
                tax_rates = (Tax_rates) spinnerTaxRates.getSelectedItem();
                taxpercentage = Float.parseFloat(tax_rates.getAmount());


                discount = Float.parseFloat(discountAmntTxt.getText().toString());
                Float orderTaxAmount = ((totalnet - discount) * taxpercentage / 100);
                discountAmount.setText(discount + "");

                taxAmount.setText(orderTaxAmount + "");
                purchaseTotal.setText((totalnet - discount + orderTaxAmount + shippingcharge) + "");
                totalDue.setText((Float.parseFloat(purchaseTotal.getText().toString()) - Float.parseFloat(amountTxt.getText().toString()) + ""));


                //   totalPayable.setText((totalnet - discount + taxamount + shippingcharge) + "");
                break;
            }
            case 2: {

                totalnet = Float.parseFloat(netTotalAmountTxt.getText().toString());
                taxamount = Float.parseFloat(taxAmount.getText().toString());
                shippingcharge = shippingcharge;
                tax_rates = (Tax_rates) spinnerTaxRates.getSelectedItem();
                taxpercentage = Float.parseFloat(tax_rates.getAmount());


                discount = (Float.parseFloat(discountAmntTxt.getText().toString()) * totalnet) / 100;
                Float orderTaxAmount = 0.f;
                if (spinnerDiscountType.getSelectedItemPosition() == 1) {
                    orderTaxAmount = ((totalnet - discount) * taxpercentage / 100);
                } else if (spinnerDiscountType.getSelectedItemPosition() == 2) {
                    orderTaxAmount = ((totalnet) * taxpercentage / 100);
                }
                discountAmount.setText(discount + "");

                taxAmount.setText(orderTaxAmount + "");
                purchaseTotal.setText((totalnet - discount + orderTaxAmount + shippingcharge) + "");
                totalDue.setText((Float.parseFloat(purchaseTotal.getText().toString()) - Float.parseFloat(amountTxt.getText().toString()) + ""));


                //   discountAmount.setText(((totalnet * discount / 100) + taxamount + shippingcharge) + "");
                //    totalPayable.setText((totalnet * (1 - (discount / 100)) + ""));
                break;
            }
        }
    }


}