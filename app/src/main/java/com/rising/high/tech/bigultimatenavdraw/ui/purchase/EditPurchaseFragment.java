package com.rising.high.tech.bigultimatenavdraw.ui.purchase;

import android.app.DatePickerDialog;
import android.content.Context;
import android.content.res.Resources;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.AutoCompleteTextView;
import android.widget.Button;
import android.widget.DatePicker;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;

import androidx.cardview.widget.CardView;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.material.snackbar.Snackbar;
import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.db.ContactDbController;
import com.rising.high.tech.bigultimatenavdraw.db.ProductDbController;
import com.rising.high.tech.bigultimatenavdraw.db.PurchaseLineDbController;
import com.rising.high.tech.bigultimatenavdraw.db.BusinessLocationDbController;
import com.rising.high.tech.bigultimatenavdraw.db.TaxRatesDbController;
import com.rising.high.tech.bigultimatenavdraw.db.TransactionDbController;
import com.rising.high.tech.bigultimatenavdraw.db.TransactionPayementDbController;
import com.rising.high.tech.bigultimatenavdraw.db.VariationLocationDetailDbController;
import com.rising.high.tech.bigultimatenavdraw.db.VariationsDbController;
import com.rising.high.tech.bigultimatenavdraw.model.Business_location;
import com.rising.high.tech.bigultimatenavdraw.model.Contact;
import com.rising.high.tech.bigultimatenavdraw.model.Product;
import com.rising.high.tech.bigultimatenavdraw.model.Purchase_line;
import com.rising.high.tech.bigultimatenavdraw.model.Tax_rates;
import com.rising.high.tech.bigultimatenavdraw.model.Transaction;
import com.rising.high.tech.bigultimatenavdraw.model.Variation;
import com.rising.high.tech.bigultimatenavdraw.ui.adapter.DShortProductAdapter;
import com.rising.high.tech.bigultimatenavdraw.ui.adapter.SpinContactAdapter;
import com.rising.high.tech.bigultimatenavdraw.ui.adapter.SpinStationAdapter;
import com.rising.high.tech.bigultimatenavdraw.ui.adapter.SpinTaxRatesAdapter;
import com.rising.high.tech.bigultimatenavdraw.util.FileUtil;
import com.rising.high.tech.bigultimatenavdraw.util.ProductUtil;
import com.rising.high.tech.bigultimatenavdraw.util.SessionManager;
import com.rising.high.tech.bigultimatenavdraw.util.StringFormat;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;


import static com.rising.high.tech.bigultimatenavdraw.util.Constant.DUE;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.FIXED;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.ID;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.ORDERED;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.PAID;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.PARTIAL;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.PENDING;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.PERCENTAGE;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.PURCHASE;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.RECEIVED;

public class EditPurchaseFragment extends Fragment {
    private static final String TAG = "AddPurchaseFragment";
    private Context _context;
    final Calendar c = Calendar.getInstance();

    private SpinStationAdapter spinStationAdapter;
    private SpinContactAdapter spinContactAdapter;
    private BusinessLocationDbController businessLocationDbController;
    private ProductDbController productDbController;
    private TransactionDbController transactionDbController;
    private TransactionPayementDbController transactionPayementDbController;
    private PurchaseLineDbController purchaseLineDbController;

    private VariationsDbController variationsDbController;
    private VariationLocationDetailDbController variationLocationDetailDbController;

    private ContactDbController contactDbController;
    private ArrayList<Integer> productIds = new ArrayList<>();
    private int indexId = 0;
    private Resources resources;
    private SessionManager session;
    private Integer userId;
    Button addBack;
    Button addBtn;
    EditText purchaseDate;
    Spinner spinnerLocation;
    Spinner spinnerPaymentMethod;
    Spinner spinnerDiscountType;
    Spinner spinnerSupplier;
    Spinner spinnerPurchaseStatus;
    EditText amountTxt;
    EditText paidOnTxt;
    TextView totalItemsTxt;
    EditText additionalShipingTxt;
    TextView netTotalAmountTxt;
    TextView purchaseTotal;
    TextView discountAmntTxt;
    TextView totalDue;

    RecyclerView recycle_product;
    AutoCompleteTextView searchEdit;
    TextView titleTxt;
    EditText payTermNumber;
    Spinner spinnerTermDuration;
    EditText shippingDetailTxt;
    EditText payementNote;
    CardView cardPayment;
    Spinner spinnerTaxRates;
    EditText taxAmount;
    EditText discountAmount;
    EditText additionalNote;
    LinearLayout container_sub_product;
    LinearLayout linearLayout;

    private SpinTaxRatesAdapter spinTaxRatesAdapter;
    private TaxRatesDbController taxRatesDbController;
    DShortProductAdapter dShortProductAdapter;

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        // Inflate the layout for this fragment
        View root = inflater.inflate(R.layout.activity_add_purchase, container, false);
        _context = getContext();
        resources = getResources();
        session = new SessionManager(_context);

        userId = (int) session.getUserDetails().get(session.ID_USER);


        Bundle args = getArguments();
        indexId = args.getInt(ID, 0);

        dShortProductAdapter = new DShortProductAdapter();
        recycle_product.setAdapter(dShortProductAdapter);
        recycle_product.setLayoutManager(new LinearLayoutManager(_context));

        initDb();
        initSpinners();
        initForm();
        initListners();
        // initMultiSpinnerSearch();

        setProductSearchDapter(productDbController.getAllProduct());
        //  dShortProductAdapter.setData(productDbController.getAllProduct());

        return root;
    }

    private void initDb() {

        businessLocationDbController = new BusinessLocationDbController(_context);

        contactDbController = new ContactDbController(_context);

        productDbController = new ProductDbController(_context);

        transactionDbController = new TransactionDbController(_context);

        transactionPayementDbController = new TransactionPayementDbController(_context);

        purchaseLineDbController = new PurchaseLineDbController(_context);
        taxRatesDbController = new TaxRatesDbController(_context);

        variationsDbController = new VariationsDbController(_context);
        variationLocationDetailDbController = new VariationLocationDetailDbController(_context);

    }

    private void initForm() {
        Transaction transaction = transactionDbController.getTransactionById(indexId);
        Transaction payement = transactionPayementDbController.getTransactionById(indexId);

        Contact contact = contactDbController.getCustomerById(transaction.getContact_id());
        if (contact.getId() > 0)
            spinnerSupplier.setSelection(spinContactAdapter.getPosition(contact));

        Tax_rates tax_rates = taxRatesDbController.getTax_ratesById(transaction.getTax_id());
        spinnerTaxRates.setSelection(spinTaxRatesAdapter.getPosition(tax_rates));

        purchaseDate.setText(transaction.getTransaction_date());

        if (transaction.getStatus() != null) {
            switch (transaction.getStatus()) {
                case RECEIVED: {
                    spinnerPurchaseStatus.setSelection(1);
                }
                case PENDING: {
                    spinnerPurchaseStatus.setSelection(2);
                }
                case ORDERED: {
                    spinnerPurchaseStatus.setSelection(3);
                }
            }
            int indexP = Arrays.asList(resources.getStringArray(R.array.array_purchase_status)).indexOf(transaction.getStatus());
            // spinnerPurchaseStatus.setSelection(indexP);
        }

        Business_location businesslocation = businessLocationDbController.getStationById(transaction.getLocation_id());
        spinnerLocation.setSelection(spinStationAdapter.getPosition(businesslocation));

        payTermNumber.setText(transaction.getPay_term_number());
        if (transaction.getPay_term_number() != null) {
            int indexT = Arrays.asList(resources.getStringArray(R.array.array_duration)).indexOf(transaction.getPay_term_type());
            spinnerTermDuration.setSelection(indexT);
        }

        ArrayList<Product> arrayListProduct = new ArrayList<>();
        for (Purchase_line purchaseLine : purchaseLineDbController.getPurchaseLineByTransaction(indexId)) {
            Product product = productDbController.getProductById(purchaseLine.getProduct_id());
            product.setSell_qte(purchaseLine.getQuantity());
            arrayListProduct.add(product);
        }

        dShortProductAdapter.setData(arrayListProduct);
        dShortProductAdapter.notifyDataSetChanged();

        // set ui params
        netTotalAmountTxt.setText(ProductUtil.getTotalAmountDpp(dShortProductAdapter.getData(), _context));
        totalItemsTxt.setText(dShortProductAdapter.getData().size() + "");
        purchaseTotal.setText(transaction.getFinal_total());
        additionalNote.setText(transaction.getAdditional_notes());

        if (transaction.getStatus() != null) {
            int indexM = Arrays.asList(resources.getStringArray(R.array.discount_type)).indexOf(transaction.getDiscount_type());
            spinnerDiscountType.setSelection(indexM);
        }
        if (transaction.getDiscount_type() != null) {
            spinnerDiscountType.setSelection(transaction.getDiscount_type().matches(FIXED) ? 1 : 2);
        }
        discountAmntTxt.setText(transaction.getDiscount_amount());
        shippingDetailTxt.setText(transaction.getShipping_details());
        additionalShipingTxt.setText(transaction.getShipping_charges());

        amountTxt.setText(payement.getAmount() + "");
        paidOnTxt.setText(transaction.getTransaction_date());
        if (transaction.getStatus() != null) {
            int indexP = Arrays.asList(resources.getStringArray(R.array.payment_method_array)).indexOf(payement.getMethod());
            spinnerPaymentMethod.setSelection(indexP);
        }
        payementNote.setText(payement.getNote());

    }

    private void initSpinners() {

        spinStationAdapter = new SpinStationAdapter(_context, android.R.layout.simple_spinner_item, businessLocationDbController.getAllStationSpinner());
        spinnerLocation.setAdapter(spinStationAdapter);
        spinContactAdapter = new SpinContactAdapter(_context, android.R.layout.simple_spinner_item, contactDbController.getSpinSupplier());
        spinnerSupplier.setAdapter(spinContactAdapter);
        spinTaxRatesAdapter = new SpinTaxRatesAdapter(_context, android.R.layout.simple_spinner_item, taxRatesDbController.getAllTax_ratesSpinner());
        spinnerTaxRates.setAdapter(spinTaxRatesAdapter);
        spinnerDiscountType.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                setViewAount();

            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {

            }
        });

        spinnerTaxRates.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                //   setViewAount();

//                Float discount = Float.parseFloat(discountAmntTxt.getText().toString());
//                Float totalnet = Float.parseFloat(netTotalAmountTxt.getText().toString());
//                Float taxamount = Float.parseFloat(taxAmount.getText().toString());
//                Float shippingcharge = Float.parseFloat(additionalShipingTxt.getText().toString());
//
//                Tax_rates tax_rates = (Tax_rates) spinnerTaxRates.getSelectedItem();
//                float taxpercentage = Float.parseFloat(tax_rates.getAmount());
//
//                Float orderTaxAmount = 0.f;
//
//                if (spinnerDiscountType.getSelectedItemPosition() == 1) {
//                    orderTaxAmount = ((totalnet - discount) * taxpercentage / 100);
//                } else if (spinnerDiscountType.getSelectedItemPosition() == 2) {
//                    orderTaxAmount = ((totalnet) * taxpercentage / 100);
//                }
//
//                taxAmount.setText(orderTaxAmount + "");
//                purchaseTotal.setText((totalnet - discount + orderTaxAmount + shippingcharge) + "");

                setViewAount();

            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {

            }
        });
    }


    private void initListners() {
        titleTxt.setText(getResources().getString(R.string.label_edit_purchase));
        addBtn.setText(getResources().getString(R.string.label_updatee));
        cardPayment.setVisibility(View.GONE);
        addBack.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                replaceFragment(new PurchaseFragment());
            }
        });

        addBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                addPurchase();
            }
        });

        paidOnTxt.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                DatePickerDialog datePickerDialog = new DatePickerDialog(_context,
                        new DatePickerDialog.OnDateSetListener() {
                            @Override
                            public void onDateSet(DatePicker datePicker, int year, int month, int day) {
                                String myFormat = StringFormat.populateSetFullDate(year, month, day, c.get(Calendar.HOUR), c.get(Calendar.MINUTE));
                                paidOnTxt.setText(myFormat);
                            }
                        }, c.get(Calendar.YEAR), c.get(Calendar.MONTH), c.get(Calendar.DAY_OF_MONTH));
                datePickerDialog.show();
            }
        });

        purchaseDate.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                DatePickerDialog datePickerDialog = new DatePickerDialog(_context,
                        new DatePickerDialog.OnDateSetListener() {
                            @Override
                            public void onDateSet(DatePicker datePicker, int year, int month, int day) {
                                String myFormat = StringFormat.populateSetFullDate(year, month, day, c.get(Calendar.HOUR), c.get(Calendar.MINUTE));
                                purchaseDate.setText(myFormat);
                            }
                        }, c.get(Calendar.YEAR), c.get(Calendar.MONTH), c.get(Calendar.DAY_OF_MONTH));
                datePickerDialog.show();
            }
        });

        additionalShipingTxt.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                if (s.length() != 0) {

                    Float total = Float.parseFloat(netTotalAmountTxt.getText().toString()) + Float.parseFloat(additionalShipingTxt.getText().toString());
                    if (spinnerDiscountType.getSelectedItemPosition() == 1) {
                        total = total - Float.parseFloat(discountAmntTxt.getText().toString());
                    } else if (spinnerDiscountType.getSelectedItemPosition() == 2) {
                        total = (total * (1 - Float.parseFloat(discountAmntTxt.getText().toString()) / 100));
                    }
                    purchaseTotal.setText(total + "");
                    totalDue.setText((Float.parseFloat(purchaseTotal.getText().toString()) - Float.parseFloat(amountTxt.getText().toString())) + "");
                }

            }

            @Override
            public void afterTextChanged(Editable s) {

            }

        });

        amountTxt.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                if (s.length() != 0) {
                    totalDue.setText((Float.parseFloat(purchaseTotal.getText().toString()) - Float.parseFloat(amountTxt.getText().toString())) + "");
                }

            }

            @Override
            public void afterTextChanged(Editable s) {

            }
        });

        discountAmntTxt.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                if (s.length() != 0) {
                    Float discount = 0.0f;
                    if (spinnerDiscountType.getSelectedItemPosition() == 1) {
                        discount = Float.parseFloat(netTotalAmountTxt.getText().toString()) - Float.parseFloat(discountAmntTxt.getText().toString());
                        purchaseTotal.setText(discount + "");
                        totalDue.setText(((Float.parseFloat(purchaseTotal.getText().toString()) - Float.parseFloat(amountTxt.getText().toString()) + "")));

                    } else if (spinnerDiscountType.getSelectedItemPosition() == 2) {
                        discount = (Float.parseFloat(netTotalAmountTxt.getText().toString()) * ((1 - Float.parseFloat(discountAmntTxt.getText().toString()) / 100)));
                        purchaseTotal.setText(discount + "");
                        totalDue.setText(((Float.parseFloat(purchaseTotal.getText().toString()) - Float.parseFloat(amountTxt.getText().toString()) + "")));
                    }
                    purchaseTotal.setText(discount + "");
                }
            }

            @Override
            public void afterTextChanged(Editable s) {

            }
        });

        dShortProductAdapter.setOnDataChangeListener(new DShortProductAdapter.OnDataChangeListener() {
            @Override
            public void onDataChanged(ArrayList<Product> products) {

                netTotalAmountTxt.setText(ProductUtil.getTotalAmountDpp(products, _context));
                totalItemsTxt.setText(products.size() + "");

                setViewAount();
            }
        });

    }

    private void addPurchase() {

        if (spinnerSupplier.getSelectedItemPosition() == 0) {
            //     Toast.makeText(_context, _context.getResources().getString(R.string.label_select_ssupplier), Toast.LENGTH_LONG).show();
            Snackbar snackbar = Snackbar.make(linearLayout, _context.getResources().getString(R.string.label_select_ssupplier), Snackbar.LENGTH_LONG);
            snackbar.show();
        }else if (spinnerPurchaseStatus.getSelectedItemPosition() == 0) {
            Snackbar snackbar = Snackbar.make(linearLayout, _context.getResources().getString(R.string.label_select_statuus), Snackbar.LENGTH_LONG);
            snackbar.show();
        }else if (spinnerLocation.getSelectedItemPosition() == 0) {
            Snackbar snackbar = Snackbar.make(linearLayout, _context.getResources().getString(R.string.label_plz_select_locations), Snackbar.LENGTH_LONG);
            snackbar.show();
        }else if ( dShortProductAdapter.getItemCount()== 0){
            Snackbar snackbar = Snackbar.make(linearLayout, _context.getResources().getString(R.string.label_choose_product), Snackbar.LENGTH_LONG);
            snackbar.show();
        }else if (
                !purchaseDate.getText().toString().equals("")
                        && !amountTxt.getText().toString().equals("")
                        && !paidOnTxt.getText().toString().equals("")
        ) {
            Transaction transaction = transactionDbController.getTransactionById(indexId);
            Float due_total = Float.parseFloat(totalDue.getText().toString());
            Float purchase_total = Float.parseFloat(purchaseTotal.getText().toString());

            // TODO implement session bussines id to be recuperate
            transaction.setBusiness_id(1);

            Business_location businesslocation = (Business_location) spinnerLocation.getSelectedItem();
            transaction.setLocation_id(businesslocation.getId());

            transaction.setType(PURCHASE);
            switch (spinnerPurchaseStatus.getSelectedItemPosition()) {
                case 1: {
                    transaction.setStatus(RECEIVED);
                }
                case 2: {
                    transaction.setStatus(PENDING);
                }
                case 3: {
                    transaction.setStatus(ORDERED);
                }
            }

            if (due_total <= 0) {
                transaction.setPayment_status(PAID);
            } else if (purchase_total == due_total) {
                transaction.setPayment_status(DUE);
            } else if (due_total < purchase_total && due_total > 0) {
                transaction.setPayment_status(PARTIAL);
            }

            Contact contact = (Contact) spinnerSupplier.getSelectedItem();
            transaction.setContact_id(contact.getId());

            transaction.setTransaction_date(purchaseDate.getText().toString());
            transaction.setTotal_before_tax(netTotalAmountTxt.getText().toString());
            if (spinnerTaxRates.getSelectedItemPosition() != 0) {
                Tax_rates tax_rates = (Tax_rates) spinnerTaxRates.getSelectedItem();
                transaction.setTax_id(tax_rates.getId());
            }
            transaction.setDiscount_type(spinnerDiscountType.getSelectedItemPosition() == 1 ? FIXED : spinnerDiscountType.getSelectedItemPosition() == 2 ? PERCENTAGE : null);
            transaction.setDiscount_amount(discountAmntTxt.getText().toString());
            transaction.setShipping_charges(additionalShipingTxt.getText().toString());
            transaction.setFinal_total(purchaseTotal.getText().toString());
            transaction.setCreated_by(userId);
            transaction.setPay_term_number(payTermNumber.getText().toString());
            transaction.setPay_term_type(spinnerTermDuration.getSelectedItem().toString());
            transaction.setShipping_details(shippingDetailTxt.getText().toString());
            transaction.setAdditional_notes(additionalNote.getText().toString());
            int idInsert = transactionDbController.updateTransaction(transaction);
            if (idInsert > 0) {

                purchaseLineDbController.deletePurchaseByTransaction(indexId);
                for (Product product : dShortProductAdapter.getData()) {
                    Purchase_line purchaseLine = new Purchase_line();
                    purchaseLine.setTransaction_id(indexId);
                    purchaseLine.setProduct_id(product.getId());
                    purchaseLine.setVariation_id(product.getId());
                    purchaseLine.setQuantity(product.getSell_qte());
                    Variation variation = variationsDbController.getVariationByProductId(product.getId());
                    purchaseLine.setPp_without_discount(variation.getDefault_purchase_price());
                    purchaseLine.setPurchase_price(variation.getDefault_purchase_price());
                    purchaseLine.setPurchase_price_inc_tax(variation.getDpp_inc_tax());

                    purchaseLineDbController.insertLocal(purchaseLine);

                    if (spinnerPurchaseStatus.getSelectedItemPosition() == 1) {
//                        Variation_location_details variation_location_details=variationLocationDetailDbController.getVariationLocationDetailsByStationIdProductId(businesslocation.getId() ,product.getId());
//                        variation_location_details.setOld_qty_available(variation_location_details.getQty_available());
//                        Integer qty = variation_location_details.getQty_available() + product.getSell_qte();
//                        variation_location_details.setQty_available(qty);
//                        variationLocationDetailDbController.update(variation_location_details);
                        variationLocationDetailDbController.updatePurchaseQty(product.getId(), businesslocation.getId(), product.getSell_qte());

                    }

                }
                FileUtil.showDialog(_context, "Successful", getResources().getString(R.string.stock_purchase_success));

                replaceFragment(new PurchaseFragment());

            } else {
                Toast.makeText(_context, " Error insert ", Toast.LENGTH_LONG).show();
            }
        } else {
            Toast.makeText(_context, getResources().getString(R.string.label_all_field_required), Toast.LENGTH_LONG).show();
        }
    }

    private String getPurchaseTotal() {

        Float purchaseTotal = 0.f;
        purchaseTotal = Float.parseFloat(netTotalAmountTxt.getText().toString()) + Float.parseFloat(additionalShipingTxt.getText().toString());

        return purchaseTotal + "";
    }

    private void setProductSearchDapter(ArrayList<Product> arrayListTemp) {


        ArrayAdapter<Product> adapter = new ArrayAdapter<Product>(_context, android.R.layout.simple_list_item_1, arrayListTemp);
        searchEdit.setAdapter(adapter);
        searchEdit.setDropDownBackgroundDrawable(new ColorDrawable(_context.getResources().getColor(R.color.colorPrimary)));
        searchEdit.setOnItemClickListener(new AdapterView.OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> parent, View arg1, int pos, long id) {

                searchEdit.setText("");

                Business_location businesslocation = (Business_location) spinnerLocation.getSelectedItem();
                Product selected = (Product) parent.getAdapter().getItem(pos);

                if (spinnerLocation.getSelectedItemPosition() == 0) {
                    Snackbar snackbar = Snackbar.make(linearLayout, _context.getResources().getString(R.string.lbl_please_select_station), Snackbar.LENGTH_LONG);
                    snackbar.show();
                } else if (!variationLocationDetailDbController.isProductHasVariationInStation(businesslocation.getId(), selected.getId())) {
                    Snackbar snackbar = Snackbar.make(linearLayout, _context.getResources().getString(R.string.label_empty_add_product_to) + " " + businesslocation.getName(), Snackbar.LENGTH_LONG);
                    snackbar.show();
                } else {
                    dShortProductAdapter.updateData(selected);
                    container_sub_product.setVisibility(View.VISIBLE);
                }


            }
        });

    }


    public void replaceFragment(Fragment someFragment) {
        FragmentTransaction transaction = getFragmentManager().beginTransaction();
        transaction.replace(R.id.nav_host_fragment, someFragment);
        transaction.addToBackStack(null);
        transaction.commit();
    }

    private void setViewAount() {
        Float discount = Float.parseFloat(discountAmntTxt.getText().toString());
        Float totalnet = Float.parseFloat(netTotalAmountTxt.getText().toString());
        Float taxamount = Float.parseFloat(taxAmount.getText().toString());
        Float shippingcharge = Float.parseFloat(additionalShipingTxt.getText().toString());
        Tax_rates tax_rates = (Tax_rates) spinnerTaxRates.getSelectedItem();
        float taxpercentage = Float.parseFloat(tax_rates.getAmount());
        taxAmount.setText((taxpercentage * (totalnet + discount) / 100) + "");
        switch (spinnerDiscountType.getSelectedItemPosition()) {
            case 0: {
                discountAmount.setText("00");
                discount = 0.f;
                purchaseTotal.setText((totalnet - discount + taxamount + shippingcharge) + "");
                totalDue.setText((Float.parseFloat(purchaseTotal.getText().toString()) - Float.parseFloat(amountTxt.getText().toString()) + ""));

                break;
            }
            case 1: {

                totalnet = Float.parseFloat(netTotalAmountTxt.getText().toString());
                taxamount = Float.parseFloat(taxAmount.getText().toString());
                shippingcharge = shippingcharge;
                tax_rates = (Tax_rates) spinnerTaxRates.getSelectedItem();
                taxpercentage = Float.parseFloat(tax_rates.getAmount());


                discount = Float.parseFloat(discountAmntTxt.getText().toString());
                Float orderTaxAmount = ((totalnet - discount) * taxpercentage / 100);
                discountAmount.setText(discount + "");

                taxAmount.setText(orderTaxAmount + "");
                purchaseTotal.setText((totalnet - discount + orderTaxAmount + shippingcharge) + "");
                totalDue.setText((Float.parseFloat(purchaseTotal.getText().toString()) - Float.parseFloat(amountTxt.getText().toString()) + ""));


                //   totalPayable.setText((totalnet - discount + taxamount + shippingcharge) + "");
                break;
            }
            case 2: {

                totalnet = Float.parseFloat(netTotalAmountTxt.getText().toString());
                taxamount = Float.parseFloat(taxAmount.getText().toString());
                shippingcharge = shippingcharge;
                tax_rates = (Tax_rates) spinnerTaxRates.getSelectedItem();
                taxpercentage = Float.parseFloat(tax_rates.getAmount());


                discount = (Float.parseFloat(discountAmntTxt.getText().toString()) * totalnet) / 100;
                Float orderTaxAmount = 0.f;
                if (spinnerDiscountType.getSelectedItemPosition() == 1) {
                    orderTaxAmount = ((totalnet - discount) * taxpercentage / 100);
                } else if (spinnerDiscountType.getSelectedItemPosition() == 2) {
                    orderTaxAmount = ((totalnet) * taxpercentage / 100);
                }
                discountAmount.setText(discount + "");

                taxAmount.setText(orderTaxAmount + "");
                purchaseTotal.setText((totalnet - discount + orderTaxAmount + shippingcharge) + "");
                totalDue.setText((Float.parseFloat(purchaseTotal.getText().toString()) - Float.parseFloat(amountTxt.getText().toString()) + ""));


                //   discountAmount.setText(((totalnet * discount / 100) + taxamount + shippingcharge) + "");
                //    totalPayable.setText((totalnet * (1 - (discount / 100)) + ""));
                break;
            }
        }
    }
}