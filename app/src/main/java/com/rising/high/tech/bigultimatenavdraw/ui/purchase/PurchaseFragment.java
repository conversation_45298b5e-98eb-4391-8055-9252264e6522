package com.rising.high.tech.bigultimatenavdraw.ui.purchase;

import android.app.DatePickerDialog;
import android.content.Context;
import android.os.AsyncTask;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.DatePicker;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.Spinner;
import android.widget.TextView;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.material.textfield.TextInputLayout;
import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.db.ContactDbController;
import com.rising.high.tech.bigultimatenavdraw.db.PurchaseLineDbController;
import com.rising.high.tech.bigultimatenavdraw.db.BusinessLocationDbController;
import com.rising.high.tech.bigultimatenavdraw.db.TransactionDbController;
import com.rising.high.tech.bigultimatenavdraw.model.Contact;
import com.rising.high.tech.bigultimatenavdraw.model.Product;
import com.rising.high.tech.bigultimatenavdraw.model.Purchase_line;
import com.rising.high.tech.bigultimatenavdraw.model.Business_location;
import com.rising.high.tech.bigultimatenavdraw.model.StockReport;
import com.rising.high.tech.bigultimatenavdraw.model.Transaction;
import com.rising.high.tech.bigultimatenavdraw.model.Variation;
import com.rising.high.tech.bigultimatenavdraw.model.Variation_location_details;
import com.rising.high.tech.bigultimatenavdraw.ui.adapter.SpinContactAdapter;
import com.rising.high.tech.bigultimatenavdraw.ui.adapter.SpinStationAdapter;
import com.rising.high.tech.bigultimatenavdraw.ui.purchase.adapter.PurchasesListAdapter;
import com.rising.high.tech.bigultimatenavdraw.util.DbUtil;
import com.rising.high.tech.bigultimatenavdraw.util.SessionManager;
import com.rising.high.tech.bigultimatenavdraw.util.serverInteraction;

import java.util.ArrayList;
import java.util.Calendar;


import static com.rising.high.tech.bigultimatenavdraw.util.Constant.PRODUCT_ADD;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.PURCHASE;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.PURCHASE_ADD;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.SERVER_MASTER;
import static com.rising.high.tech.bigultimatenavdraw.util.StringFormat.populateSetDate;

public class PurchaseFragment extends Fragment {
    private static final String TAG = "PurchaseFragment";
    private Context _context;
    private PurchaseLineDbController purchaseLineDbController;
    private TransactionDbController transactionDbController;
    private ArrayList<Purchase_line> purchasesList = new ArrayList<>();
    private PurchasesListAdapter purchasesListAdapter;
    RecyclerView recyclerPurchases;
    Spinner spinnerCategory, spinnerStation, spinnerUnit;
    Button btnAdd;
    private SessionManager session;

    Button addBtn;
    Spinner spinnerSupplier;
    LinearLayout filterHeader;
    LinearLayout filterContainer;
    EditText startDate;
    EditText endDate;
    TextView purchaseGrandTotal;
    LinearLayout containerReceived;
    TextView receivedCount;
    LinearLayout containerPending;
    TextView pendingCount;
    LinearLayout containerPartial;
    TextView partialCount;
    LinearLayout containerDue;
    TextView dueCount;
    LinearLayout containerOrdered;
    TextView orderedCount;
    LinearLayout containerPaid;
    TextView paidCount;
    Button btnFilter;
    Spinner spinnerPurchaseStatus;
    Spinner spinnerPayementStatus;
    LinearLayout totalContainer;
    TextView noItemFound;
    private BusinessLocationDbController businessLocationDbController;
    private SpinStationAdapter spinStationAdapter;
    private SpinContactAdapter spinContactAdapter;
    private ContactDbController contactDbController;
    final Calendar c = Calendar.getInstance();

    public PurchaseFragment() {
        // Required empty public constructor
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        // Inflate the layout for this fragment
        View root = inflater.inflate(R.layout.fragment_list_purchase, container, false);
        _context = getContext();
        session = new SessionManager(_context);

        //  purchaseSummaryLayout.setVisibility(View.GONE);
        recyclerPurchases = root.findViewById(R.id.recycler_purchases);
        spinnerCategory = root.findViewById(R.id.spinner_category);
        spinnerStation = root.findViewById(R.id.spinner_station);
        spinnerUnit = root.findViewById(R.id.spinner_unit);
        btnAdd = root.findViewById(R.id.id_add);

        RecyclerView.LayoutManager linearLayoutManager = new LinearLayoutManager(_context);
        recyclerPurchases.setLayoutManager(linearLayoutManager);

        initDb();
        purchasesListAdapter = new PurchasesListAdapter();
        recyclerPurchases.setAdapter(purchasesListAdapter);
        ArrayList<Transaction> transactionArrayList=transactionDbController.getTransactionType(PURCHASE);

        purchasesListAdapter.setData(transactionArrayList);

     //   getReportStock(root);
        initClickListners();
        initSpinner();
        getResume();
        checkRoles();
        setItemView();
        return root;
    }

//    private void getReportStock(View view) {
//      //  showProgress();
//        AsyncTask.execute(new Runnable() {
//            @Override
//            public void run() {
//                ArrayList<Transaction> transactionArrayList=transactionDbController.getTransactionType(PURCHASE);
//
//                view.post(new Runnable() {
//                    public void run() {
//                        /* the desired UI update */
//                        purchasesListAdapter.setData(transactionArrayList);
//
//                    }
//                });
//            }
//        });
//    }


    private void initSyncData(){

    }

    private void checkRoles()
    {
   //     if (session.getBoolean(SERVER_MASTER) || !session.checkPermissionSubModule(PURCHASE_ADD))
        if (!session.checkPermissionSubModule(PURCHASE_ADD))
        {
            addBtn.setVisibility(View.INVISIBLE);
        }
    }

    public void setItemView()
    {
        if(purchasesListAdapter.getItemCount() > 0)
        {
            recyclerPurchases.setVisibility(View.VISIBLE);
            totalContainer.setVisibility(View.VISIBLE);
            noItemFound.setVisibility(View.GONE);
        }
        else
        {
            recyclerPurchases.setVisibility(View.GONE);
            recyclerPurchases.setVisibility(View.GONE);
            totalContainer.setVisibility(View.GONE);
        }
    }
    private void getResume() {
        if (purchasesListAdapter.getData().size() > 0) {
            totalContainer.setVisibility(View.VISIBLE);

            Float total_grand = 0.f;
            int ordered = 0;
            int pending = 0;
            int received = 0;
            int partial = 0;
            int due = 0;
            int paid = 0;

            for (Transaction transaction : purchasesListAdapter.getData()) {
                total_grand = total_grand + Float.parseFloat(transaction.getFinal_total());
                switch (transaction.getStatus()) {
                    case "ordered":
                        ordered += 1;
                        break;
                    case "pending":
                        pending += +1;
                        break;
                    case "received":
                        received += +1;
                        break;
                }

                if (transaction.getPayment_status() != null) {
                    switch (transaction.getPayment_status()) {
                        case "partial":
                            partial += +1;
                            break;
                        case "due":
                            due += +1;
                            break;
                        case "paid":
                            paid += +1;
                            break;
                    }
                }
            }


            containerReceived.setVisibility(received > 0 ? View.VISIBLE : View.GONE);
            receivedCount.setText(received + "");


            containerPending.setVisibility(pending > 0 ? View.VISIBLE : View.GONE);
            pendingCount.setText(pending + "");

            containerPartial.setVisibility(partial > 0 ? View.VISIBLE : View.GONE);
            partialCount.setText(partial + "");

            containerDue.setVisibility(due > 0 ? View.VISIBLE : View.GONE);
            dueCount.setText(due + "");

            containerOrdered.setVisibility(ordered > 0 ? View.VISIBLE : View.GONE);
            orderedCount.setText(ordered + "");

            containerPaid.setVisibility(paid > 0 ? View.VISIBLE : View.GONE);
            paidCount.setText(paid + "");

            purchaseGrandTotal.setText(total_grand + " " +session.getUserDetails().get(session.KEY_SYMBOL));
        } else {
            totalContainer.setVisibility(View.GONE);
        }
    }

    private void initSpinner() {
        spinContactAdapter = new SpinContactAdapter(_context, android.R.layout.simple_spinner_item, contactDbController.getSpinSupplier());
        spinnerSupplier.setAdapter(spinContactAdapter);

        spinStationAdapter = new SpinStationAdapter(_context, android.R.layout.simple_spinner_item, businessLocationDbController.getAllStationSpinner());
        spinnerStation.setAdapter(spinStationAdapter);

    }

    private void initDb() {

        purchaseLineDbController = new PurchaseLineDbController(_context);

        purchasesList = purchaseLineDbController.getAllPurchaseLine();

        businessLocationDbController = new BusinessLocationDbController(_context);

        contactDbController = new ContactDbController(_context);

        transactionDbController = new TransactionDbController(_context);
    }

    private void initClickListners() {
        filterHeader.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                filterContainer.setVisibility(filterContainer.getVisibility() == View.VISIBLE ? View.GONE : View.VISIBLE);
            }
        });

        addBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                replaceFragment(new AddPurchaseFragment());
            }
        });

        startDate.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                DatePickerDialog datePickerDialog = new DatePickerDialog(_context,
                        new DatePickerDialog.OnDateSetListener() {
                            @Override
                            public void onDateSet(DatePicker datePicker, int year, int month, int day) {
                                String myFormat = populateSetDate(year, month, day);
                                startDate.setText(myFormat);
                              //  startDate.setTextSize(10);
                            }
                        }, c.get(Calendar.YEAR), c.get(Calendar.MONTH), c.get(Calendar.DAY_OF_MONTH));
                datePickerDialog.show();
            }
        });

        endDate.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                DatePickerDialog datePickerDialog = new DatePickerDialog(_context,
                        new DatePickerDialog.OnDateSetListener() {
                            @Override
                            public void onDateSet(DatePicker datePicker, int year, int month, int day) {
                                String myFormat = populateSetDate(year, month, day);
                                endDate.setText(myFormat);
                            }
                        }, c.get(Calendar.YEAR), c.get(Calendar.MONTH), c.get(Calendar.DAY_OF_MONTH));
                datePickerDialog.show();
            }
        });

        btnFilter.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                filterData();
            }
        });

        purchasesListAdapter.setOnDataChangeListener(new PurchasesListAdapter.OnDataChangeListener() {
            @Override
            public void onDataChanged(Transaction transaction) {
                getResume();
            }
        });

    }

    private void filterData() {

        Contact contact = (Contact) spinnerSupplier.getSelectedItem();
        Business_location businesslocation = (Business_location) spinnerStation.getSelectedItem();

        purchasesListAdapter.setData(
                transactionDbController.filterPurchaseTransaction(
                        businesslocation.getId(),
                        spinnerPurchaseStatus.getSelectedItemPosition() != 0 ? spinnerPurchaseStatus.getSelectedItem().toString().toLowerCase() : null,
                        spinnerPayementStatus.getSelectedItemPosition() != 0 ? spinnerPayementStatus.getSelectedItem().toString().toLowerCase() : null,
                        contact.getId(), startDate.getText().toString(), endDate.getText().toString()));

        purchasesListAdapter.notifyDataSetChanged();

        getResume();
    }

    public void replaceFragment(Fragment someFragment) {
        FragmentTransaction transaction = getFragmentManager().beginTransaction();
        transaction.replace(R.id.nav_host_fragment, someFragment);
        transaction.addToBackStack(null);
        transaction.commit();
    }

}