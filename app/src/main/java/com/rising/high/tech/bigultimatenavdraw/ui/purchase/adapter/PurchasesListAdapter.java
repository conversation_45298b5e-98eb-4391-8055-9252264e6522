package com.rising.high.tech.bigultimatenavdraw.ui.purchase.adapter;

import android.app.DatePickerDialog;
import android.content.Context;
import android.content.res.Resources;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.Button;
import android.widget.DatePicker;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.AppCompatImageView;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.gson.Gson;
import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.db.ContactDbController;
import com.rising.high.tech.bigultimatenavdraw.db.PurchaseLineDbController;
import com.rising.high.tech.bigultimatenavdraw.db.BusinessLocationDbController;
import com.rising.high.tech.bigultimatenavdraw.db.TransactionDbController;
import com.rising.high.tech.bigultimatenavdraw.db.TransactionPayementDbController;
import com.rising.high.tech.bigultimatenavdraw.db.VariationLocationDetailDbController;
import com.rising.high.tech.bigultimatenavdraw.model.Business_location;
import com.rising.high.tech.bigultimatenavdraw.model.Contact;
import com.rising.high.tech.bigultimatenavdraw.model.Purchase_line;
import com.rising.high.tech.bigultimatenavdraw.model.Transaction;
import com.rising.high.tech.bigultimatenavdraw.model.Variation_location_details;
import com.rising.high.tech.bigultimatenavdraw.ui.contact.EditContactFragment;
import com.rising.high.tech.bigultimatenavdraw.ui.purchase.EditPurchaseFragment;
import com.rising.high.tech.bigultimatenavdraw.util.SessionManager;
import com.rising.high.tech.bigultimatenavdraw.util.StringFormat;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.HashMap;

import static com.rising.high.tech.bigultimatenavdraw.util.Constant.CONTACT_DELETE;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.CONTACT_EDIT;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.DUE;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.ORDERED;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.PAID;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.PARTIAL;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.PENDING;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.PURCHASE_DELETE;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.PURCHASE_EDIT;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.RECEIVED;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.SERVER_MASTER;


public class PurchasesListAdapter extends RecyclerView.Adapter<PurchasesListAdapter.ListPurchaseViewHolder> {

    private static final String TAG = "PurchasesListAdapter";

    private ArrayList<Transaction> dataList = new ArrayList<>();
    private Resources resources;
    private PurchaseLineDbController purchaseLineDbController;
    private TransactionDbController transactionDbController;
    private ContactDbController contactDbController;
    private TransactionPayementDbController transactionPayementDbController;
    private BusinessLocationDbController businessLocationDbController;
    private VariationLocationDetailDbController variationLocationDetailDbController;
    private SubPaymentAdapter subPaymentAdapter;
    final Calendar c = Calendar.getInstance();
    private Context context;

    private SessionManager session;
    private HashMap<String, Object> user;

    public PurchasesListAdapter() {

   //     this.context = context;


    }

    @Override
    public ListPurchaseViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        context = parent.getContext();
        resources = context.getResources();

        session = new SessionManager(this.context);
        user = session.getUserDetails();
        purchaseLineDbController = new PurchaseLineDbController(context);
        purchaseLineDbController.open();

        transactionDbController = new TransactionDbController(context);
        transactionDbController.open();

        contactDbController = new ContactDbController(context);
        contactDbController.open();

        businessLocationDbController = new BusinessLocationDbController(context);
        businessLocationDbController.open();

        transactionPayementDbController = new TransactionPayementDbController(context);
        transactionPayementDbController.open();

        variationLocationDetailDbController = new VariationLocationDetailDbController(context);
        variationLocationDetailDbController.open();

        subPaymentAdapter = new SubPaymentAdapter();

        subPaymentAdapter.setOnDataChangeListener(new SubPaymentAdapter.OnDataChangeListener() {
            @Override
            public void onDataChanged() {
                notifyDataSetChanged();
            }
        });

        return new ListPurchaseViewHolder(LayoutInflater.from(parent.getContext())
                .inflate(R.layout.list_purchase_item, parent, false));
    }

    @Override
    public void onBindViewHolder(ListPurchaseViewHolder holder, int position) {

        holder.purchaseDate.setText(dataList.get(position).getTransaction_date());
        holder.purchaseRef.setText(dataList.get(position).getRef_no());
        Contact contact = contactDbController.getCustomerById(dataList.get(position).getContact_id());
        holder.purchaseSupplier.setText(contact.getName());
        Business_location businesslocation = businessLocationDbController.getStationById(dataList.get(position).getLocation_id());
        holder.purchaseLocation.setText(businesslocation.getName());

        holder.purchaseStatus.setText(dataList.get(position).getStatus());
        holder.purchaseStatus.setTextColor(Color.BLACK);

        if (dataList.get(position).getStatus().equals(RECEIVED) ||dataList.get(position).getStatus().equals("reçu") ) {
            holder.purchaseStatus.setBackground(context.getResources().getDrawable(R.drawable.rounded_green_bg));
            holder.purchaseStatus.setTextColor(Color.WHITE);
        } else if (dataList.get(position).getStatus().equals(ORDERED) || dataList.get(position).getStatus().equals("commandé")) {
            holder.purchaseStatus.setBackground(context.getResources().getDrawable(R.drawable.rounded_btn_wihte_blue));
            holder.purchaseStatus.setTextColor(Color.WHITE);
        } else if (dataList.get(position).getStatus().equals(PENDING)||dataList.get(position).getStatus().equals("en attendant")) {
            holder.purchaseStatus.setBackground(context.getResources().getDrawable(R.drawable.rounded_orange_bg));
            holder.purchaseStatus.setTextColor(Color.WHITE);
        }

        holder.purchasePaymentStatus.setText(dataList.get(position).getPayment_status());
        holder.purchasePaymentStatus.setTextColor(Color.BLACK);
        if (dataList.get(position).getPayment_status().equals(PARTIAL)) {
            holder.purchasePaymentStatus.setBackground(context.getResources().getDrawable(R.drawable.rounded_btn_wihte_blue));
            holder.purchasePaymentStatus.setTextColor(Color.WHITE);
        } else if (dataList.get(position).getPayment_status().equals(PAID)) {
            holder.purchasePaymentStatus.setBackground(context.getResources().getDrawable(R.drawable.rounded_green_bg));
            holder.purchasePaymentStatus.setTextColor(Color.WHITE);
        } else if (dataList.get(position).getPayment_status().equals(DUE)) {
            holder.purchasePaymentStatus.setBackground(context.getResources().getDrawable(R.drawable.rounded_orange_bg));
            holder.purchasePaymentStatus.setTextColor(Color.WHITE);
        }

        holder.purchaseGrandTotal.setText(dataList.get(position).getFinal_total() + user.get(session.KEY_SYMBOL));
        Float final_total = Float.parseFloat(dataList.get(position).getFinal_total());
        Float total_amount = 0.f;
        for (Transaction transaction : transactionPayementDbController.getAllTransactionById(dataList.get(position).getId())) {
            total_amount += transaction.getAmount();
        }
        holder.purchasePaymentDue.setText((final_total - total_amount) + " " + user.get(session.KEY_SYMBOL));
        if (position % 2 == 1) {
            holder.linearLayout.setBackgroundColor(context.getResources().getColor(R.color.white));
        } else {
            holder.linearLayout.setBackgroundColor(context.getResources().getColor(R.color.lightGrey));
        }

    }

    public void setData(ArrayList<Transaction> arrayList) {
        this.dataList = arrayList;
        Collections.reverse(this.dataList);
   //     notifyDataSetChanged();
    }

    public ArrayList<Transaction> getData() {
        return this.dataList;
    }


    @Override
    public int getItemCount() {
        return dataList.size();
    }

    public class ListPurchaseViewHolder extends RecyclerView.ViewHolder {

        TextView purchaseDate, purchaseRef, purchaseLocation, purchaseSupplier,
                purchaseAddedBy, purchaseStatus, purchasePaymentStatus, purchaseGrandTotal, purchasePaymentDue;
        Button btn_edit, btn_view;
        Spinner spinnerAction;
        LinearLayout linearLayout;

        public ListPurchaseViewHolder(View itemView) {
            super(itemView);

            purchaseDate = itemView.findViewById(R.id.purchase_date);
            purchaseRef = itemView.findViewById(R.id.purchase_ref);
            purchaseLocation = itemView.findViewById(R.id.purchase_location);
            purchaseSupplier = itemView.findViewById(R.id.purchase_supplier);
            purchaseAddedBy = itemView.findViewById(R.id.purchase_added_by);
            purchaseStatus = itemView.findViewById(R.id.purchase_status);
            purchasePaymentStatus = itemView.findViewById(R.id.purchase_payment_status);
            purchaseGrandTotal = itemView.findViewById(R.id.purchase_grand_total);
            purchasePaymentDue = itemView.findViewById(R.id.purchase_payment_due);
            spinnerAction = itemView.findViewById(R.id.spinner_action);
            linearLayout = itemView.findViewById(R.id.linearLayout);

            spinnerAction.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
                @Override
                public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                    spinnerAction.setSelection(0, true);

                    switch (position) {
                        case 1: {
                            viewDetail(getAdapterPosition());
                            break;
                        }
                        case 2: {
                            if (session.getBoolean(SERVER_MASTER) || !session.checkPermissionSubModule(PURCHASE_EDIT)) {
                                Toast.makeText(context, context.getResources().getString(R.string.label_not_autorized), Toast.LENGTH_LONG).show();
                            } else {
                                if (dataList.get(getAdapterPosition()).getStatus().equals(RECEIVED)) {
                                    Toast.makeText(context, context.getResources().getString(R.string.label_not_autorized_purchase_edit), Toast.LENGTH_SHORT).show();
                                } else {
                                    navigateFragment(dataList.get(getAdapterPosition()).getId());
                                }
                            }
                            break;
                        }
                        case 3: {
                            if (!session.checkPermissionSubModule(PURCHASE_DELETE)) {
                                Toast.makeText(context, context.getResources().getString(R.string.label_not_autorized), Toast.LENGTH_LONG).show();
                            } else {
                                deleteItem(getAdapterPosition());
                            }
                            break;
                        }
                        case 4: {
                            if (!dataList.get(getAdapterPosition()).getPayment_status().equals(PAID)) {
                                addPayement(getAdapterPosition());
                            }
                            break;
                        }
                        case 5: {
                            viewPayement(getAdapterPosition());
                            break;
                        }
                        case 6: {
                            updtaeStatus(getAdapterPosition());
                            break;
                        }
                    }
                }

                @Override
                public void onNothingSelected(AdapterView<?> parent) {

                }
            });

            purchaseStatus.setOnClickListener(v ->
            {
                updtaeStatus(getAdapterPosition());
            });
        }
    }

    private void updtaeStatus(int position) {

        if (dataList.get(position).getStatus().equals(RECEIVED)) {
            Toast.makeText(context, context.getResources().getString(R.string.label_not_autorized_purchase), Toast.LENGTH_SHORT).show();
        } else {
            //Preparing views
            // get prompts.xml view
            LayoutInflater li = LayoutInflater.from(context);
            View promptsView = li.inflate(R.layout.update_status_dialog_main, null);

            androidx.appcompat.app.AlertDialog.Builder alertDialogBuilder = new androidx.appcompat.app.AlertDialog.Builder(
                    context);

            alertDialogBuilder.setView(promptsView);

            final Button ButtonSave = promptsView.findViewById(R.id.btn_save);
            final AppCompatImageView ButtonClose = promptsView.findViewById(R.id.btn_close);
            final Spinner spinner_purchase_status = promptsView.findViewById(R.id.spinner_purchase_status);

            if (dataList.get(position).getStatus() != null) {
                switch (dataList.get(position).getStatus()){
                    case RECEIVED:{
                        spinner_purchase_status.setSelection(1);
                    }
                    case PENDING:{
                        spinner_purchase_status.setSelection(2);
                    }
                    case ORDERED:{
                        spinner_purchase_status.setSelection(3);
                    }
                }

                int indexP = Arrays.asList(resources.getStringArray(R.array.array_purchase_status)).indexOf(dataList.get(position).getStatus());
            //    spinner_purchase_status.setSelection(indexP);
            }

            final androidx.appcompat.app.AlertDialog mAlertDialog = alertDialogBuilder.create();
            if (mAlertDialog.getWindow() != null)
                mAlertDialog.getWindow().getAttributes().windowAnimations = R.style.SlidingDialogAnimation;
            mAlertDialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));

            ButtonClose.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    mAlertDialog.dismiss();
                }
            });

            ButtonSave.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                  //  dataList.get(position).setStatus(spinner_purchase_status.getSelectedItem().toString().toLowerCase());

                    switch (spinner_purchase_status.getSelectedItemPosition()){
                        case 1:{
                            dataList.get(position).setStatus(RECEIVED);
                        }
                        case 2:{
                            dataList.get(position).setStatus(PENDING);
                        }
                        case 3:{
                            dataList.get(position).setStatus(ORDERED);
                        }
                    }

                    transactionDbController.updateTransaction(dataList.get(position));
                    // get old stock product quantity and update in case status is received
                    Transaction transaction = dataList.get(position);
                    ArrayList<Purchase_line> purchase_lines = purchaseLineDbController.getPurchaseLineByTransaction(transaction.getId());
                    if (spinner_purchase_status.getSelectedItemPosition()==1) {
                        for (Purchase_line purchaseLine : purchaseLineDbController.getPurchaseLineByTransaction(dataList.get(position).getId())) {
                            variationLocationDetailDbController.updatePurchaseQty(purchaseLine.getProduct_id(), dataList.get(position).getLocation_id(), purchaseLine.getQuantity());
                        }
                    }

//                    if (spinner_purchase_status.getSelectedItemPosition() == 1 && !isPurchaseStatusReceived) {
////                        for (Purchase_line purchase_line : purchase_lines) {
////                            Variation_location_details variation_location_details = variationLocationDetailDbController.getVariationLocationDetailsByStationIdProductId(transaction.getLocation_id(), purchase_line.getProduct_id());
////                            variation_location_details.setOld_qty_available(variation_location_details.getQty_available());
////                            int qty = variation_location_details.getQty_available() + purchase_line.getQuantity();
////                            variation_location_details.setQty_available(qty);
////                            variationLocationDetailDbController.update(variation_location_details);
////                        }
//                    } else if (isPurchaseStatusReceived) {
//
//                        for (Purchase_line purchase_line : purchase_lines) {
//                            Variation_location_details variation_location_details = variationLocationDetailDbController.getVariationLocationDetailsByStationIdProductId(transaction.getLocation_id(), purchase_line.getProduct_id());
//                            variation_location_details.setOld_qty_available(variation_location_details.getQty_available());
//                            int qty = variation_location_details.getQty_available() - purchase_line.getQuantity();
//                            variation_location_details.setQty_available(qty);
//                            variationLocationDetailDbController.update(variation_location_details);
//                        }
//                    }

                    if (mOnDataChangeListener != null && dataList.size() > 0) {
                        mOnDataChangeListener.onDataChanged(dataList.get(position));
                    }
                    mAlertDialog.dismiss();
                    notifyDataSetChanged();
                }
            });


            mAlertDialog.show();
        }

    }

    private void addPayement(int position) {
        LayoutInflater li = LayoutInflater.from(context);
        View promptsView = li.inflate(R.layout.add_payment_purchase_dialog_main, null);
        androidx.appcompat.app.AlertDialog.Builder alertDialogBuilder = new androidx.appcompat.app.AlertDialog.Builder(context);

        alertDialogBuilder.setView(promptsView);

        final Button ButtonSave = promptsView.findViewById(R.id.btn_save);
        final AppCompatImageView ButtonClose = promptsView.findViewById(R.id.btn_close);

        final TextView supplier_name = promptsView.findViewById(R.id.supplier_name);
        final TextView supplier_phone = promptsView.findViewById(R.id.supplier_phone);
        final TextView business_location = promptsView.findViewById(R.id.business_location);
        final TextView total_amount = promptsView.findViewById(R.id.total_amount);
        final TextView payment_note = promptsView.findViewById(R.id.payment_note);
        final TextView purchase_note = promptsView.findViewById(R.id.purchase_note);
        final EditText amount_txt = promptsView.findViewById(R.id.amount_txt);
        final EditText paid_on_txt = promptsView.findViewById(R.id.paid_on_txt);
        final Spinner spin_payment_method = promptsView.findViewById(R.id.spin_payment_method);

        Contact contact = contactDbController.getCustomerById(dataList.get(position).getContact_id());
        Transaction payment = transactionPayementDbController.getTransactionById(dataList.get(position).getId());
        Business_location businesslocation = businessLocationDbController.getStationById(dataList.get(position).getLocation_id());

        supplier_name.setText(contact.getName());
        supplier_phone.setText(contact.getMobile());
        business_location.setText(businesslocation.getName() + " " + businesslocation.getCity() + " " + businesslocation.getCountry());
        total_amount.setText(dataList.get(position).getFinal_total() + " " + user.get(session.KEY_SYMBOL));
        purchase_note.setText(dataList.get(position).getNote());
        Float final_total = Float.parseFloat(dataList.get(position).getFinal_total());

        /**
         * get amount from all payments
         */
        Float amount = 0.f;
        for (Transaction transaction : transactionPayementDbController.getAllTransactionById(dataList.get(position).getId())) {
            amount += transaction.getAmount();
        }

        Float due = (final_total - amount);
        amount_txt.setText(due + "");
        paid_on_txt.setText(payment.getPaid_on());
        payment_note.setText(payment.getNote());

        if (dataList.get(position).getMethod() != null) {
            int indexP = Arrays.asList(resources.getStringArray(R.array.payment_method_array)).indexOf(dataList.get(position).getMethod());
            spin_payment_method.setSelection(indexP);
        }

        /**
         * init button click listner
         */
        paid_on_txt.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                DatePickerDialog datePickerDialog = new DatePickerDialog(context,
                        new DatePickerDialog.OnDateSetListener() {
                            @Override
                            public void onDateSet(DatePicker datePicker, int year, int month, int day) {
                                String myFormat = StringFormat.populateSetFullDate(year, month, day, c.get(Calendar.HOUR), c.get(Calendar.MINUTE));
                                paid_on_txt.setText(myFormat);
                            }
                        }, c.get(Calendar.YEAR), c.get(Calendar.MONTH), c.get(Calendar.DAY_OF_MONTH));
                datePickerDialog.show();
            }
        });

        final androidx.appcompat.app.AlertDialog mAlertDialog = alertDialogBuilder.create();
        if (mAlertDialog.getWindow() != null)
            mAlertDialog.getWindow().getAttributes().windowAnimations = R.style.SlidingDialogAnimation;
        mAlertDialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));


        ButtonClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mAlertDialog.dismiss();
            }
        });

        ButtonSave.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

                if (Float.parseFloat(amount_txt.getText().toString()) <= due) {

                    dataList.get(position).setAmount(Float.parseFloat(amount_txt.getText().toString()));
                    dataList.get(position).setMethod(spin_payment_method.getSelectedItem().toString());
                    dataList.get(position).setNote(payment_note.getText().toString());
                    dataList.get(position).setPaid_on(paid_on_txt.getText().toString());
                    dataList.get(position).setTransaction_id(dataList.get(position).getId());

                    if (Float.parseFloat(amount_txt.getText().toString()) == due) {
                        dataList.get(position).setPayment_status(PAID);
                    } else if (Float.parseFloat(amount_txt.getText().toString()) < due) {
                        dataList.get(position).setPayment_status(PARTIAL);
                    }

                    transactionDbController.updateTransaction(dataList.get(position));
                    int index = transactionPayementDbController.insertLocal(dataList.get(position));
                    if (index > 0) {
                        Toast.makeText(context, context.getResources().getText(R.string.lbl_added_success), Toast.LENGTH_LONG).show();
                        notifyItemChanged(position);
                        mAlertDialog.dismiss();
                        if (mOnDataChangeListener != null) {
                            mOnDataChangeListener.onDataChanged(dataList.get(position));
                        }
                        notifyDataSetChanged();
                    } else {
                        Toast.makeText(context, "Error insert", Toast.LENGTH_LONG).show();
                    }
                } else {
                    Toast.makeText(context, context.getResources().getText(R.string.label_maximum_amount) + " " + due, Toast.LENGTH_LONG).show();
                }
            }
        });

        mAlertDialog.show();

    }

    private void viewDetail(int position) {
        //Preparing views
        // get prompts.xml view

        LayoutInflater li = LayoutInflater.from(context);
        View promptsView = li.inflate(R.layout.view_purchase_dialog_main, null);

        androidx.appcompat.app.AlertDialog.Builder alertDialogBuilder = new androidx.appcompat.app.AlertDialog.Builder(
                context);

        alertDialogBuilder.setView(promptsView);
        SubPurchaseAdapter subPurchaseAdapter = new SubPurchaseAdapter();

        final Button ButtonSave = promptsView.findViewById(R.id.btn_save);
        final AppCompatImageView ButtonClose = promptsView.findViewById(R.id.btn_close);

        final TextView supplier_name = promptsView.findViewById(R.id.supplier_name);
        final TextView supplier_phone = promptsView.findViewById(R.id.supplier_phone);
        final TextView business_location = promptsView.findViewById(R.id.business_location);
        final RecyclerView recyclerView = promptsView.findViewById(R.id.recycle_sub_vente);
        final TextView label_reference_num = promptsView.findViewById(R.id.label_reference_num);
        final TextView purchase_date = promptsView.findViewById(R.id.purchase_date);
        final TextView purchase_status = promptsView.findViewById(R.id.purchase_status);
        final TextView payement_status = promptsView.findViewById(R.id.payement_status);

        recyclerView.setAdapter(subPurchaseAdapter);
        recyclerView.setLayoutManager(new LinearLayoutManager(context));

        ArrayList<Purchase_line> purchaseLines = purchaseLineDbController.getPurchaseLineByTransaction(dataList.get(position).getId());
        subPurchaseAdapter.setData(purchaseLines);

        Contact contact = contactDbController.getCustomerById(dataList.get(position).getContact_id());

        supplier_name.setText(contact.getName());
        supplier_phone.setText(contact.getMobile());
        Business_location businesslocation = businessLocationDbController.getStationById(dataList.get(position).getLocation_id());

        business_location.setText(businesslocation.getName() + " " + businesslocation.getCity() + " " + businesslocation.getCountry());
        label_reference_num.setText(dataList.get(position).getRef_no());
        purchase_date.setText(dataList.get(position).getTransaction_date());
        purchase_status.setText(dataList.get(position).getStatus());
        payement_status.setText(dataList.get(position).getPayment_status());

        //  final ImageView image_product = promptsView.findViewById(R.id.image_product);

        final androidx.appcompat.app.AlertDialog mAlertDialog = alertDialogBuilder.create();
        if (mAlertDialog.getWindow() != null)
            mAlertDialog.getWindow().getAttributes().windowAnimations = R.style.SlidingDialogAnimation;
        mAlertDialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));


        ButtonClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mAlertDialog.dismiss();
            }
        });


        mAlertDialog.show();
    }

    private void viewPayement(int position) {
        //Preparing views
        // get prompts.xml view

        LayoutInflater li = LayoutInflater.from(context);
        View promptsView = li.inflate(R.layout.view_payement_purchase_dialog_main, null);

        androidx.appcompat.app.AlertDialog.Builder alertDialogBuilder = new androidx.appcompat.app.AlertDialog.Builder(
                context);

        alertDialogBuilder.setView(promptsView);

        final Button ButtonSave = promptsView.findViewById(R.id.btn_save);
        final AppCompatImageView ButtonClose = promptsView.findViewById(R.id.btn_close);

        final TextView supplier_name = promptsView.findViewById(R.id.supplier_name);
        final TextView supplier_phone = promptsView.findViewById(R.id.supplier_phone);
        final TextView business_location = promptsView.findViewById(R.id.business_location);
        final RecyclerView recyclerView = promptsView.findViewById(R.id.recycle_sub_vente);
        final TextView label_reference_num = promptsView.findViewById(R.id.label_reference_num);
        final TextView purchase_date = promptsView.findViewById(R.id.purchase_date);
        final TextView purchase_status = promptsView.findViewById(R.id.purchase_status);
        final TextView payement_status = promptsView.findViewById(R.id.payement_status);

        recyclerView.setAdapter(subPaymentAdapter);
        recyclerView.setLayoutManager(new LinearLayoutManager(context));

        ArrayList<Transaction> purchaseLines = transactionPayementDbController.getAllTransactionById(dataList.get(position).getId());
        subPaymentAdapter.setData(purchaseLines);
        subPaymentAdapter.setOnDataChangeListener(new SubPaymentAdapter.OnDataChangeListener() {
            @Override
            public void onDataChanged() {
                dataList.get(position).setPayment_status(transactionDbController.getTransactionById(dataList.get(position).getId()).getPayment_status());
                notifyItemChanged(position);
            }
        });

        Contact contact = contactDbController.getCustomerById(dataList.get(position).getContact_id());

        supplier_name.setText(contact.getName());
        supplier_phone.setText(contact.getMobile());
        Business_location businesslocation = businessLocationDbController.getStationById(dataList.get(position).getLocation_id());

        business_location.setText(businesslocation.getName() + " " + businesslocation.getCity() + " " + businesslocation.getCountry());
        label_reference_num.setText(dataList.get(position).getRef_no());
        purchase_date.setText(dataList.get(position).getTransaction_date());
        purchase_status.setText(dataList.get(position).getStatus());
        payement_status.setText(dataList.get(position).getPayment_status());

        //  final ImageView image_product = promptsView.findViewById(R.id.image_product);

        final androidx.appcompat.app.AlertDialog mAlertDialog = alertDialogBuilder.create();
        if (mAlertDialog.getWindow() != null)
            mAlertDialog.getWindow().getAttributes().windowAnimations = R.style.SlidingDialogAnimation;
        mAlertDialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));


        ButtonClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mAlertDialog.dismiss();
            }
        });


        mAlertDialog.show();
    }

    private void deleteItem(int position) {
        //Preparing views
        // get prompts.xml view

        LayoutInflater li = LayoutInflater.from(context);
        View promptsView = li.inflate(R.layout.delete_dialog_main, null);

        androidx.appcompat.app.AlertDialog.Builder alertDialogBuilder = new androidx.appcompat.app.AlertDialog.Builder(
                context);

        alertDialogBuilder.setView(promptsView);

        final Button ButtonSave = promptsView.findViewById(R.id.btn_save);
        final Button ButtonClose = promptsView.findViewById(R.id.btn_close);

        final androidx.appcompat.app.AlertDialog mAlertDialog = alertDialogBuilder.create();
        if (mAlertDialog.getWindow() != null)
            mAlertDialog.getWindow().getAttributes().windowAnimations = R.style.SlidingDialogAnimation;
        mAlertDialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));


        ButtonClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mAlertDialog.dismiss();
            }
        });

        ButtonSave.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

                if (dataList.get(position).getStatus().equals(RECEIVED)) {


                    for (Purchase_line purchaseLine : purchaseLineDbController.getPurchaseLineByTransaction(dataList.get(position).getId())) {
                        //   purchaseLineDbController.deletePurchase(purchaseLine.getId());
                        Log.d(TAG, " /// received purchases " + new Gson().toJson(purchaseLine));
                        Log.d(TAG, " /// received purchases " + dataList.get(position).getLocation_id());

                        variationLocationDetailDbController.updateSellQty(purchaseLine.getProduct_id(), dataList.get(position).getLocation_id(), purchaseLine.getQuantity());
                    }
                }
                purchaseLineDbController.deletePurchaseByTransaction(dataList.get(position).getId());
                transactionPayementDbController.deletePayment(dataList.get(position).getId());
                transactionDbController.deleteTransaction(dataList.get(position).getId());

                dataList.remove(position);
//                if (mOnDataChangeListener != null && dataList.size() > 0) {
//                    mOnDataChangeListener.onDataChanged(dataList.get(position));
//                } else {
                mOnDataChangeListener.onDataChanged(null);
                //  }
                mAlertDialog.dismiss();
                notifyDataSetChanged();
            }
        });


        mAlertDialog.show();
    }

    private void navigateFragment(int id) {
        AppCompatActivity activity = (AppCompatActivity) context;
        Bundle bundle = new Bundle();
        bundle.putInt("id", id);
        Fragment myFragment = new EditPurchaseFragment();
        myFragment.setArguments(bundle);
        activity.getSupportFragmentManager().beginTransaction().replace(R.id.nav_host_fragment, myFragment).addToBackStack(null).commit();
    }

    public interface OnDataChangeListener {
        void onDataChanged(Transaction transaction);
    }

    OnDataChangeListener mOnDataChangeListener;

    public void setOnDataChangeListener(OnDataChangeListener onDataChangeListener) {
        mOnDataChangeListener = onDataChangeListener;
    }


}
