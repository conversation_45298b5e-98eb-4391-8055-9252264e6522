package com.rising.high.tech.bigultimatenavdraw.ui.purchase.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.db.ProductDbController;
import com.rising.high.tech.bigultimatenavdraw.model.Product;
import com.rising.high.tech.bigultimatenavdraw.model.Purchase_line;

import java.util.ArrayList;

public class SubPurchaseAdapter extends RecyclerView.Adapter<SubPurchaseAdapter.sub_vente_view_holder> {
    private ArrayList<Purchase_line> dataList = new ArrayList<>();
    private ProductDbController productDbController;
    Context context;

    @Override
    public sub_vente_view_holder onCreateViewHolder(ViewGroup parent, int viewType) {
        context = parent.getContext();


        productDbController=new ProductDbController(context);
        productDbController.open();

        return new sub_vente_view_holder(LayoutInflater.from(parent.getContext())
                .inflate(R.layout.sub_vente_item_, parent, false));

    }

    @Override
    public void onBindViewHolder(sub_vente_view_holder holder, int position) {
        Product product=productDbController.getProductById(dataList.get(position).getProduct_id());
        holder.product_name.setText(product.getName());
        holder.quantity.setText(dataList.get(position).getQuantity() + "");
        holder.unit_price.setText(dataList.get(position).getPurchase_price());
        float total = dataList.get(position).getQuantity() * Float.parseFloat(dataList.get(position).getPurchase_price());
        holder.selling_price.setVisibility(View.VISIBLE);
        holder.selling_price.setText(dataList.get(position).getSellingPrice());

        holder.total.setText("" + total);

        //  holder.quantity.setText("zakarya");

    }

    public void setData(ArrayList<Purchase_line> arrayList) {
        this.dataList = arrayList;
        notifyDataSetChanged();
    }

    @Override
    public int getItemCount() {
        return dataList.size();
    }

    public class sub_vente_view_holder extends RecyclerView.ViewHolder {
        TextView product_name, quantity, unit_price, total, selling_price;

        public sub_vente_view_holder(View itemView) {
            super(itemView);
            product_name = itemView.findViewById(R.id.product_name);
            quantity = itemView.findViewById(R.id.quantity);
            unit_price = itemView.findViewById(R.id.unit_price);
            total = itemView.findViewById(R.id.total);
            selling_price = itemView.findViewById(R.id.selling_price);
        }
    }
}
