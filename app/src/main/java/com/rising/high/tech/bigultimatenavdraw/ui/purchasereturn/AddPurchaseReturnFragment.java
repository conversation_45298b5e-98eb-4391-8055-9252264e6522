package com.rising.high.tech.bigultimatenavdraw.ui.purchasereturn;


import android.app.DatePickerDialog;
import android.content.Context;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.AutoCompleteTextView;
import android.widget.Button;
import android.widget.DatePicker;
import android.widget.EditText;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.db.ContactDbController;
import com.rising.high.tech.bigultimatenavdraw.db.ProductDbController;
import com.rising.high.tech.bigultimatenavdraw.db.BusinessLocationDbController;
import com.rising.high.tech.bigultimatenavdraw.db.PurchaseLineDbController;
import com.rising.high.tech.bigultimatenavdraw.db.TransactionDbController;
import com.rising.high.tech.bigultimatenavdraw.db.VariationLocationDetailDbController;
import com.rising.high.tech.bigultimatenavdraw.model.Business_location;
import com.rising.high.tech.bigultimatenavdraw.model.Contact;
import com.rising.high.tech.bigultimatenavdraw.model.Product;
import com.rising.high.tech.bigultimatenavdraw.model.Purchase_line;
import com.rising.high.tech.bigultimatenavdraw.model.Transaction;
import com.rising.high.tech.bigultimatenavdraw.model.Variation_location_details;
import com.rising.high.tech.bigultimatenavdraw.ui.adapter.DShortProductAdapter;
import com.rising.high.tech.bigultimatenavdraw.ui.adapter.SpinContactAdapter;
import com.rising.high.tech.bigultimatenavdraw.ui.adapter.SpinStationAdapter;
import com.rising.high.tech.bigultimatenavdraw.util.FileUtil;
import com.rising.high.tech.bigultimatenavdraw.util.ProductUtil;
import com.rising.high.tech.bigultimatenavdraw.util.SessionManager;
import com.rising.high.tech.bigultimatenavdraw.util.StringFormat;

import java.util.ArrayList;
import java.util.Calendar;


import static com.rising.high.tech.bigultimatenavdraw.util.Constant.ID;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.PURCHASE_RETURN;

public class AddPurchaseReturnFragment extends Fragment {
    private static final String TAG = "AddPurchaseReturnFragme";
    private Context _context;
    final Calendar c = Calendar.getInstance();

    private SpinStationAdapter spinStationAdapter;
    private SpinContactAdapter spinContactAdapter;
    private BusinessLocationDbController businessLocationDbController;
    private ProductDbController productDbController;
    private DShortProductAdapter dShortProductAdapter;
    private TransactionDbController transactionDbController;
    private VariationLocationDetailDbController variationLocationDetailDbController;
    private ContactDbController contactDbController;
    private PurchaseLineDbController purchaseLineDbController;
    private ArrayList<Integer> productIds = new ArrayList<>();

    Button addBack;
    TextView totalAmount;
    TextView totalItems;
    EditText dateTxt;
    EditText referenceNo;
    Button addBtn;
    Spinner spinnerLocation;
    Spinner spinnerSupplier;
    RecyclerView recycle_product;
    AutoCompleteTextView searchEdit;

    SessionManager session;
    private Integer userId;
    private Boolean isEdit = false;
    private Integer indexId = 0;

    public AddPurchaseReturnFragment(boolean isEdit) {
        // Required empty public constructor
        this.isEdit = isEdit;

    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        // Inflate the layout for this fragment
        View root = inflater.inflate(R.layout.activity_add_purchase_return, container, false);
        _context = getContext();
        session = new SessionManager(_context);

        if (isEdit) {
            Bundle args = getArguments();
            indexId = args.getInt(ID, 0);
        }

        dShortProductAdapter = new DShortProductAdapter();
        recycle_product.setAdapter(dShortProductAdapter);
        recycle_product.setLayoutManager(new LinearLayoutManager(_context));

        initDb();
        initSpinners();
        initClickListners();
        setProductSearchDapter(productDbController.getAllProduct());
        setData();
        return root;
    }

    private void initDb() {

        businessLocationDbController = new BusinessLocationDbController(_context);
        businessLocationDbController.open();
        contactDbController = new ContactDbController(_context);
        productDbController = new ProductDbController(_context);
        purchaseLineDbController = new PurchaseLineDbController(_context);
        transactionDbController = new TransactionDbController(_context);
        variationLocationDetailDbController = new VariationLocationDetailDbController(_context);
    }

    private void setData() {
        if (isEdit) {

            Transaction transactionPurchaseReturn = transactionDbController.getTransactionById(indexId);
            addBtn.setText(getResources().getString(R.string.label_updatee));
            referenceNo.setText(transactionPurchaseReturn.getRef_no());

            // getting station from
            Transaction transaction = transactionDbController.getTransactionById(transactionPurchaseReturn.getId());
            Business_location business_locationFrom = businessLocationDbController.getStationById(transaction.getLocation_id());
            int spinnerPositionFrom = spinStationAdapter.getPosition(business_locationFrom);
            spinnerLocation.setSelection(spinnerPositionFrom);

            Contact contact = contactDbController.getCustomerById(transactionPurchaseReturn.getContact_id());
            int spinnerSupplierId = spinContactAdapter.getPosition(contact);
            spinnerSupplier.setSelection(spinnerSupplierId);

            //getting sell line to get transfered products
            ArrayList<Purchase_line> purchase_lines = purchaseLineDbController.getPurchaseLineByTransaction(indexId);
            ArrayList<Product> productArrayList = new ArrayList<>();
            for (Purchase_line purchase_line : purchase_lines) {
                Product product = productDbController.getProductById(purchase_line.getProduct_id());
                product.setSell_qte(purchase_line.getQuantity());
                productArrayList.add(product);
            }
            dShortProductAdapter.setData(productArrayList);
        }
    }

    private void initSpinners() {
        spinStationAdapter = new SpinStationAdapter(_context, android.R.layout.simple_spinner_item, businessLocationDbController.getAllStationSpinner());
        spinnerLocation.setAdapter(spinStationAdapter);

        spinContactAdapter = new SpinContactAdapter(_context, android.R.layout.simple_spinner_item, contactDbController.getSpinSupplier());
        spinnerSupplier.setAdapter(spinContactAdapter);
    }


    private void initClickListners() {
        userId = (int) session.getUserDetails().get(session.ID_USER);
        referenceNo.setText(StringFormat.generateInvoicePurchaseNo(_context));

        addBack.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                replaceFragment(new PurchasesReturnFragment());
            }
        });

        addBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                addPurchaseReturn();
            }
        });

        dateTxt.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                DatePickerDialog datePickerDialog = new DatePickerDialog(_context,
                        new DatePickerDialog.OnDateSetListener() {
                            @Override
                            public void onDateSet(DatePicker datePicker, int year, int month, int day) {
                                String myFormat = StringFormat.populateSetFullDate(year, month, day, c.get(Calendar.HOUR), c.get(Calendar.MINUTE));
                                dateTxt.setText(myFormat);
                            }
                        }, c.get(Calendar.YEAR), c.get(Calendar.MONTH), c.get(Calendar.DAY_OF_MONTH));
                datePickerDialog.show();
            }
        });

        dShortProductAdapter.setOnDataChangeListener(new DShortProductAdapter.OnDataChangeListener() {
            @Override
            public void onDataChanged(ArrayList<Product> products) {
                totalAmount.setText(ProductUtil.getTotalAmountDpp(products, _context));
                totalItems.setText(products.size() + "");
            }
        });

        dateTxt.setText(StringFormat.populateSetFullDate(c.get(Calendar.YEAR), c.get(Calendar.MONTH), c.get(Calendar.DAY_OF_MONTH), c.get(Calendar.HOUR), c.get(Calendar.MINUTE)));


    }

    private void addPurchaseReturn() {

        if (spinnerSupplier.getSelectedItemPosition() != 0
                && spinnerLocation.getSelectedItemPosition() != 0
                && !dateTxt.getText().toString().equals("")
                && dShortProductAdapter.getData().size() > 0) {

            Transaction transaction = new Transaction();

            // TODO set bussines auto
            transaction.setBusiness_id(1);
            Business_location businesslocation = (Business_location) spinnerLocation.getSelectedItem();
            transaction.setLocation_id(businesslocation.getId());
            transaction.setType(PURCHASE_RETURN);


            transaction.setPayment_status("due");
            Contact contact = (Contact) spinnerSupplier.getSelectedItem();
            transaction.setContact_id(contact.getId());
            transaction.setRef_no(!referenceNo.getText().toString().equals("") ? referenceNo.getText().toString() : "");
            transaction.setTransaction_date(dateTxt.getText().toString());
            transaction.setTotal_before_tax(totalAmount.getText().toString());

            transaction.setFinal_total(totalAmount.getText().toString());
            transaction.setCreated_by(userId);

            int inserted;
            if (isEdit) {
                transaction.setId(indexId);
                inserted = transactionDbController.updateTransaction(transaction);

                if (inserted > 0) {
                    ArrayList<Purchase_line> purchase_lines = purchaseLineDbController.getPurchaseLineByTransaction(indexId);

                    // disabling the old purchase line and adjust old quantity of product in variation location detail
                    for (Purchase_line purchase_line : purchase_lines) {
                        variationLocationDetailDbController.updatePurchaseQty(purchase_line.getProduct_id(), businesslocation.getId(), purchase_line.getQuantity());
                    }

                    purchaseLineDbController.deletePurchaseByTransaction(indexId);
                    for (Product product : dShortProductAdapter.getData()) {
                        Purchase_line purchaseLine = new Purchase_line();
                        purchaseLine.setTransaction_id(indexId);
                        purchaseLine.setProduct_id(product.getId());
                        purchaseLine.setVariation_id(product.getId());
                        purchaseLine.setQuantity(product.getSell_qte());
                        //      purchaseLine.setPurchase_price(variation.getDefault_purchase_price());
                        //    purchaseLine.setPurchase_price_inc_tax(variation.getDpp_inc_tax());
                        purchaseLineDbController.insertLocal(purchaseLine);
                        variationLocationDetailDbController.updateSellQty(product.getId(), businesslocation.getId(), product.getSell_qte());
                    }
                    FileUtil.showDialog(_context, "Successful", getResources().getString(R.string.purchase_return_success));
                    replaceFragment(new PurchasesReturnFragment());
                }


            } else {
                inserted = transactionDbController.insertLocal(transaction);
                for (Product product : dShortProductAdapter.getData()) {
                    Purchase_line purchaseLine = new Purchase_line();
                    purchaseLine.setTransaction_id(inserted);
                    purchaseLine.setProduct_id(product.getId());
                    purchaseLine.setVariation_id(product.getId());
                    purchaseLine.setQuantity(product.getSell_qte());
                    //      purchaseLine.setPurchase_price(variation.getDefault_purchase_price());
                    //    purchaseLine.setPurchase_price_inc_tax(variation.getDpp_inc_tax());
                    purchaseLineDbController.insertLocal(purchaseLine);
                    variationLocationDetailDbController.updateSellQty(product.getId(), businesslocation.getId(), product.getSell_qte());
                }
                FileUtil.showDialog(_context, "Successful", getResources().getString(R.string.purchase_return_success));
                replaceFragment(new PurchasesReturnFragment());
            }

        } else {
            Toast.makeText(_context, getResources().getString(R.string.label_all_field_required), Toast.LENGTH_LONG).show();
        }
    }

    private void setProductSearchDapter(ArrayList<Product> arrayListTemp) {

        ArrayAdapter<Product> adapter = new ArrayAdapter<Product>(_context, android.R.layout.simple_list_item_1, arrayListTemp);
        searchEdit.setAdapter(adapter);
        searchEdit.setDropDownBackgroundDrawable(new ColorDrawable(_context.getResources().getColor(R.color.colorPrimary)));

        searchEdit.setOnItemClickListener(new AdapterView.OnItemClickListener() {

            @Override
            public void onItemClick(AdapterView<?> parent, View arg1, int pos, long id) {
                if (spinnerLocation.getSelectedItemPosition() != 0) {
                    Product selected = (Product) parent.getAdapter().getItem(pos);
                    Business_location businesslocation = (Business_location) spinnerLocation.getSelectedItem();
                    Variation_location_details variation_location_details = variationLocationDetailDbController.getVariationLocationDetailsByStationIdProductId(businesslocation.getId(), selected.getId());
                    if (variation_location_details.getQty_available() == 0) {
                        Toast.makeText(_context, _context.getResources().getString(R.string.label_empty_product), Toast.LENGTH_LONG).show();
                    } else {
                        dShortProductAdapter.updateData(selected);
                        searchEdit.setText("");
                    }
                } else {
                    Toast.makeText(_context, getResources().getString(R.string.lbl_please_select_station_first), Toast.LENGTH_LONG).show();
                    searchEdit.setText("");
                }
            }
        });

    }


    public void replaceFragment(Fragment someFragment) {
        FragmentTransaction transaction = getFragmentManager().beginTransaction();
        transaction.replace(R.id.nav_host_fragment, someFragment);
        transaction.addToBackStack(null);
        transaction.commit();
    }

}