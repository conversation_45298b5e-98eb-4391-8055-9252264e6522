package com.rising.high.tech.bigultimatenavdraw.ui.purchasereturn;


import android.app.DatePickerDialog;
import android.content.Context;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.AutoCompleteTextView;
import android.widget.Button;
import android.widget.DatePicker;
import android.widget.EditText;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.db.ContactDbController;
import com.rising.high.tech.bigultimatenavdraw.db.ProductDbController;
import com.rising.high.tech.bigultimatenavdraw.db.BusinessLocationDbController;
import com.rising.high.tech.bigultimatenavdraw.db.TransactionDbController;
import com.rising.high.tech.bigultimatenavdraw.model.Contact;
import com.rising.high.tech.bigultimatenavdraw.model.Product;
import com.rising.high.tech.bigultimatenavdraw.model.Business_location;
import com.rising.high.tech.bigultimatenavdraw.model.Transaction;
import com.rising.high.tech.bigultimatenavdraw.ui.adapter.DShortProductAdapter;
import com.rising.high.tech.bigultimatenavdraw.ui.adapter.SpinContactAdapter;
import com.rising.high.tech.bigultimatenavdraw.ui.adapter.SpinStationAdapter;
import com.rising.high.tech.bigultimatenavdraw.util.ProductUtil;
import com.rising.high.tech.bigultimatenavdraw.util.SessionManager;
import com.rising.high.tech.bigultimatenavdraw.util.StringFormat;

import java.util.ArrayList;
import java.util.Calendar;

import butterknife.BindView;
import butterknife.ButterKnife;

import static com.rising.high.tech.bigultimatenavdraw.util.Constant.PURCHASE_RETURN;

public class EditPurchaseReturnFragment extends Fragment {
    private static final String TAG = "AddPurchaseReturnFragme";

    private Context _context;
    final Calendar c = Calendar.getInstance();

    private SpinStationAdapter spinStationAdapter;
    private SpinContactAdapter spinContactAdapter;
    private BusinessLocationDbController businessLocationDbController;
    private ProductDbController productDbController;
    private DShortProductAdapter dShortProductAdapter;
    private TransactionDbController transactionDbController;

    private ContactDbController contactDbController;
    private ArrayList<Integer> productIds = new ArrayList<>();

    @BindView(R.id.id_back)
    Button addBack;
    @BindView(R.id.total_amount)
    TextView totalAmount;
    @BindView(R.id.total_items)
    TextView totalItems;
    @BindView(R.id.date_txt)
    EditText dateTxt;
    @BindView(R.id.reference_no)
    EditText referenceNo;
    @BindView(R.id.add_btn)
    Button addBtn;
    @BindView(R.id.spinner_location)
    Spinner spinnerLocation;
    @BindView(R.id.spinner_supplier)
    Spinner spinnerSupplier;
    @BindView(R.id.recycle_product)
    RecyclerView recycle_product;
    @BindView(R.id.search_edit)
    AutoCompleteTextView searchEdit;

    SessionManager session;
    private Integer userId;

    public EditPurchaseReturnFragment() {
        // Required empty public constructor
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        // Inflate the layout for this fragment
        View root = inflater.inflate(R.layout.activity_add_purchase_return, container, false);
        _context = getContext();
        ButterKnife.bind(this, root);


        dShortProductAdapter = new DShortProductAdapter();
        recycle_product.setAdapter(dShortProductAdapter);
        recycle_product.setLayoutManager(new LinearLayoutManager(_context));

        session = new SessionManager(_context);

        initDb();
        initSpinners();
        initClickListners();
        setProductSearchDapter(productDbController.getAllProduct());

        userId=(int)session.getUserDetails().get(session.ID_USER);

        return root;
    }

    private void initDb() {

        businessLocationDbController = new BusinessLocationDbController(_context);
        businessLocationDbController.open();

        contactDbController = new ContactDbController(_context);
        contactDbController.open();

        productDbController = new ProductDbController(_context);
        productDbController.open();

        transactionDbController = new TransactionDbController(_context);
        transactionDbController.open();
    }

    private void initSpinners() {
        spinStationAdapter = new SpinStationAdapter(_context, android.R.layout.simple_spinner_item, businessLocationDbController.getAllStationSpinner());
        spinnerLocation.setAdapter(spinStationAdapter);

        spinContactAdapter = new SpinContactAdapter(_context, android.R.layout.simple_spinner_item, contactDbController.getSpinSupplier());
        spinnerSupplier.setAdapter(spinContactAdapter);


    }


    private void initClickListners() {
        addBack.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                replaceFragment(new PurchasesReturnFragment());
            }
        });

        addBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                addPurchaseReturn();
            }
        });

        dateTxt.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                DatePickerDialog datePickerDialog = new DatePickerDialog(_context,
                        new DatePickerDialog.OnDateSetListener() {
                            @Override
                            public void onDateSet(DatePicker datePicker, int year, int month, int day) {
                                String myFormat = StringFormat.populateSetFullDate(year, month, day, c.get(Calendar.HOUR), c.get(Calendar.MINUTE));
                                dateTxt.setText(myFormat);
                            }
                        }, c.get(Calendar.YEAR), c.get(Calendar.MONTH), c.get(Calendar.DAY_OF_MONTH));
                datePickerDialog.show();
            }
        });

        dShortProductAdapter.setOnDataChangeListener(new DShortProductAdapter.OnDataChangeListener() {
            @Override
            public void onDataChanged(ArrayList<Product> products) {

                totalAmount.setText(ProductUtil.getTotalAmountDpp(products, _context));
                totalItems.setText(products.size() + "");
            }
        });

    }

    private void addPurchaseReturn() {

        if (spinnerSupplier.getSelectedItemPosition() != 0
                && spinnerLocation.getSelectedItemPosition() != 0
                && !dateTxt.getText().toString().equals("")
                && dShortProductAdapter.getData().size() > 0) {

            Transaction transaction = new Transaction();

            // TODO set bussines auto
            transaction.setBusiness_id(1);

            Business_location businesslocation = (Business_location) spinnerLocation.getSelectedItem();
            transaction.setLocation_id(businesslocation.getId());
            transaction.setType(PURCHASE_RETURN);

            //TODO
            transaction.setPayment_status("due");

            Contact contact = (Contact) spinnerSupplier.getSelectedItem();
            transaction.setContact_id(contact.getId());
            transaction.setRef_no(!referenceNo.getText().toString().equals("") ? referenceNo.getText().toString() : "");
            transaction.setTransaction_date(dateTxt.getText().toString());
            transaction.setTotal_before_tax(totalAmount.getText().toString());

            //TODO transaction.setTax_amount();
            transaction.setFinal_total(totalAmount.getText().toString());
            //TODO
            transaction.setCreated_by(userId);

            int inserted = transactionDbController.insertLocal(transaction);
            if (inserted > 0) {
                Toast.makeText(_context, getResources().getString(R.string.lbl_added_success), Toast.LENGTH_LONG).show();
                replaceFragment(new PurchasesReturnFragment());
            }

        } else {
            Toast.makeText(_context, getResources().getString(R.string.label_all_field_required), Toast.LENGTH_LONG).show();
        }
    }

    private void setProductSearchDapter(ArrayList<Product> arrayListTemp) {

        ArrayAdapter<Product> adapter = new ArrayAdapter<Product>(_context, android.R.layout.simple_list_item_1, arrayListTemp);
        searchEdit.setAdapter(adapter);
        searchEdit.setDropDownBackgroundDrawable(new ColorDrawable(_context.getResources().getColor(R.color.colorPrimary)));

        searchEdit.setOnItemClickListener(new AdapterView.OnItemClickListener() {

            @Override
            public void onItemClick(AdapterView<?> parent, View arg1, int pos, long id) {

                Product selected = (Product) parent.getAdapter().getItem(pos);
                Toast.makeText(_context,
                        "Clicked " + pos + " name: " + selected.getName(),
                        Toast.LENGTH_SHORT).show();

                dShortProductAdapter.updateData(selected);
                searchEdit.setText("");

                // ((Activity)_context)inputManager.hideSoftInputFromWindow(getCurrentFocus().getWindowToken(), InputMethodManager.HIDE_NOT_ALWAYS);
            }
        });

    }


    public void replaceFragment(Fragment someFragment) {
        FragmentTransaction transaction = getFragmentManager().beginTransaction();
        transaction.replace(R.id.nav_host_fragment, someFragment);
        transaction.addToBackStack(null);
        transaction.commit();
    }

}