package com.rising.high.tech.bigultimatenavdraw.ui.purchasereturn;

import android.app.DatePickerDialog;
import android.content.Context;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.DatePicker;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.Spinner;
import android.widget.TextView;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.db.BusinessLocationDbController;
import com.rising.high.tech.bigultimatenavdraw.db.TransactionDbController;
import com.rising.high.tech.bigultimatenavdraw.model.Transaction;
import com.rising.high.tech.bigultimatenavdraw.ui.adapter.SpinStationAdapter;
import com.rising.high.tech.bigultimatenavdraw.ui.purchasereturn.adapter.PurchaseReturnListAdapter;
import com.rising.high.tech.bigultimatenavdraw.util.SessionManager;
import com.rising.high.tech.bigultimatenavdraw.util.serverInteraction;

import java.util.Calendar;

import butterknife.BindView;
import butterknife.ButterKnife;

import static com.rising.high.tech.bigultimatenavdraw.util.Constant.PURCHASE_ADD;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.PURCHASE_RETURN;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.SERVER_MASTER;
import static com.rising.high.tech.bigultimatenavdraw.util.StringFormat.populateSetDate;

public class PurchasesReturnFragment extends Fragment {

    private static final String TAG = "PurchasesReturnFragment";
    private Context _context;


    @BindView(R.id.spinner_station)
    Spinner spinnerStation;
    @BindView(R.id.id_add)
    Button addBtn;
    @BindView(R.id.recycler_purchases)
    RecyclerView recyclerPurchases;
    @BindView(R.id.purchase_grand_total)
    TextView purchaseGrandTotal;
    @BindView(R.id.new_start_date)
    EditText startDate;
    @BindView(R.id.new_end_date)
    EditText endDate;
    @BindView(R.id.filter_header)
    LinearLayout filterHeader;
    @BindView(R.id.filter_container)
    LinearLayout filterContainer;
    @BindView(R.id.total_container)
    LinearLayout totalContainer;
    @BindView(R.id.pending_count)
    TextView pendingCount;
    @BindView(R.id.received_count)
    TextView receivedCount;
    @BindView(R.id.container_received)
    LinearLayout containerReceived;
    @BindView(R.id.paid_count)
    TextView paidCount;
    @BindView(R.id.ordered_count)
    TextView orderedCount;
    @BindView(R.id.container_partial)
    LinearLayout containerPartial;
    @BindView(R.id.partial_count)
    TextView partialCount;
    @BindView(R.id.container_due)
    LinearLayout containerDue;
    @BindView(R.id.due_count)
    TextView dueCount;
    @BindView(R.id.container_ordered)
    LinearLayout containerOrdered;
    @BindView(R.id.container_pending)
    LinearLayout containerPending;
    @BindView(R.id.container_paid)
    LinearLayout containerPaid;
    @BindView(R.id.id_filter)
    Button btnFilter;
    @BindView(R.id.noItemFound)
    TextView noItemFound;
    BusinessLocationDbController businessLocationDbController;
    SpinStationAdapter spinStationAdapter;
    private TransactionDbController transactionDbController;
    private PurchaseReturnListAdapter purchaseReturnListAdapter;
    final Calendar c = Calendar.getInstance();
    SessionManager session;

    public PurchasesReturnFragment() {
        // Required empty public constructor
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        // Inflate the layout for this fragment
        View root = inflater.inflate(R.layout.fragment_list_achat_retourn, container, false);
        ButterKnife.bind(this, root);

        _context = getContext();
        session = new SessionManager(_context);

        initDb();
        initSpinners();
        initClickListners();

        recyclerPurchases.setLayoutManager(new LinearLayoutManager(_context));
        purchaseReturnListAdapter = new PurchaseReturnListAdapter(_context, transactionDbController.getTransactionType(PURCHASE_RETURN));
        getResume();

        recyclerPurchases.setAdapter(purchaseReturnListAdapter);
        setItemView();
        purchaseReturnListAdapter.setOnDataChangeListener(new PurchaseReturnListAdapter.OnDataChangeListener() {
            @Override
            public void onDataChanged() {
                getResume();
            }
        });
        checkRoles();

        return root;
    }
    private void checkRoles()
    {
        if (session.getBoolean(SERVER_MASTER)|| !session.checkPermissionSubModule(PURCHASE_ADD))
        {
            addBtn.setVisibility(View.INVISIBLE);
        }
    }
    public void setItemView()
    {
        if(purchaseReturnListAdapter.getItemCount() > 0)
        {
            recyclerPurchases.setVisibility(View.VISIBLE);
            totalContainer.setVisibility(View.VISIBLE);
            noItemFound.setVisibility(View.GONE);
        }
        else
        {
            recyclerPurchases.setVisibility(View.GONE);
            recyclerPurchases.setVisibility(View.GONE);
            totalContainer.setVisibility(View.GONE);
        }
    }
    private void getResume() {
        if (purchaseReturnListAdapter.getData().size() > 0) {
            totalContainer.setVisibility(View.VISIBLE);

            Float total_grand = 0.f;
            int ordered = 0;
            int pending = 0;
            int received = 0;
            int partial = 0;
            int due = 0;
            int paid = 0;

            for (Transaction transaction : purchaseReturnListAdapter.getData()) {
                total_grand = total_grand + Float.parseFloat(transaction.getFinal_total());
                if (transaction.getStatus() != null) {
                    switch (transaction.getStatus()) {
                        case "ordered":
                            ordered += 1;
                            break;
                        case "pending":
                            pending += +1;
                            break;
                        case "received":
                            received += +1;
                            break;
                    }
                }

                if (transaction.getPayment_status() != null) {
                    switch (transaction.getPayment_status()) {
                        case "partial":
                            partial += +1;
                            break;
                        case "due":
                            due += +1;
                            break;
                        case "paid":
                            paid += +1;
                            break;
                    }
                }
            }


            containerReceived.setVisibility(received > 0 ? View.VISIBLE : View.GONE);
            receivedCount.setText(received + "");


            containerPending.setVisibility(pending > 0 ? View.VISIBLE : View.GONE);
            pendingCount.setText(pending + "");

            containerPartial.setVisibility(partial > 0 ? View.VISIBLE : View.GONE);
            partialCount.setText(partial + "");

            containerDue.setVisibility(due > 0 ? View.VISIBLE : View.GONE);
            dueCount.setText(due + "");

            containerOrdered.setVisibility(ordered > 0 ? View.VISIBLE : View.GONE);
            orderedCount.setText(ordered + "");

            containerPaid.setVisibility(paid > 0 ? View.VISIBLE : View.GONE);
            paidCount.setText(paid + "");

            purchaseGrandTotal.setText(total_grand + "");
        } else {
            totalContainer.setVisibility(View.GONE);
        }
    }


    private void initSpinners() {

        spinStationAdapter = new SpinStationAdapter(_context, android.R.layout.simple_spinner_item, businessLocationDbController.getAllStationSpinner());
        spinnerStation.setAdapter(spinStationAdapter);

    }

    private void initDb() {
        businessLocationDbController = new BusinessLocationDbController(_context);
        businessLocationDbController.open();

        transactionDbController = new TransactionDbController(_context);
        transactionDbController.open();

    }

    private void initClickListners() {
        filterHeader.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                filterContainer.setVisibility(filterContainer.getVisibility() == View.VISIBLE ? View.GONE : View.VISIBLE);
            }
        });


        addBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                replaceFragment(new AddPurchaseReturnFragment(false));
            }
        });

        startDate.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                DatePickerDialog datePickerDialog = new DatePickerDialog(_context,
                        new DatePickerDialog.OnDateSetListener() {
                            @Override
                            public void onDateSet(DatePicker datePicker, int year, int month, int day) {
                                String myFormat = populateSetDate(year, month, day);
                                startDate.setText(myFormat);
                            }
                        }, c.get(Calendar.YEAR), c.get(Calendar.MONTH), c.get(Calendar.DAY_OF_MONTH));
                datePickerDialog.show();
            }
        });

        endDate.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                DatePickerDialog datePickerDialog = new DatePickerDialog(_context,
                        new DatePickerDialog.OnDateSetListener() {
                            @Override
                            public void onDateSet(DatePicker datePicker, int year, int month, int day) {
                                String myFormat = populateSetDate(year, month, day);
                                endDate.setText(myFormat);
                            }
                        }, c.get(Calendar.YEAR), c.get(Calendar.MONTH), c.get(Calendar.DAY_OF_MONTH));
                datePickerDialog.show();
            }
        });


        btnFilter.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                filterData();
            }
        });


    }

    private void filterData() {
        purchaseReturnListAdapter.setData(transactionDbController.filterPurchaseReturnTransaction(startDate.getText().toString(), endDate.getText().toString()));

        purchaseReturnListAdapter.notifyDataSetChanged();

        getResume();
    }

    public void replaceFragment(Fragment someFragment) {
        FragmentTransaction transaction = getFragmentManager().beginTransaction();
        transaction.replace(R.id.nav_host_fragment, someFragment);
        transaction.addToBackStack(null);
        transaction.commit();
    }

}