package com.rising.high.tech.bigultimatenavdraw.ui.purchasereturn.adapter;

import android.app.DatePickerDialog;
import android.content.Context;
import android.content.res.Resources;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.Button;
import android.widget.DatePicker;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.AppCompatImageView;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.gson.Gson;
import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.db.ContactDbController;
import com.rising.high.tech.bigultimatenavdraw.db.PurchaseLineDbController;
import com.rising.high.tech.bigultimatenavdraw.db.BusinessLocationDbController;
import com.rising.high.tech.bigultimatenavdraw.db.TransactionDbController;
import com.rising.high.tech.bigultimatenavdraw.db.TransactionPayementDbController;
import com.rising.high.tech.bigultimatenavdraw.db.VariationLocationDetailDbController;
import com.rising.high.tech.bigultimatenavdraw.model.Business_location;
import com.rising.high.tech.bigultimatenavdraw.model.Contact;
import com.rising.high.tech.bigultimatenavdraw.model.Purchase_line;
import com.rising.high.tech.bigultimatenavdraw.model.Transaction;
import com.rising.high.tech.bigultimatenavdraw.ui.purchase.adapter.SubPaymentAdapter;
import com.rising.high.tech.bigultimatenavdraw.ui.purchase.adapter.SubPurchaseAdapter;
import com.rising.high.tech.bigultimatenavdraw.ui.purchasereturn.AddPurchaseReturnFragment;
import com.rising.high.tech.bigultimatenavdraw.ui.purchasereturn.EditPurchaseReturnFragment;
import com.rising.high.tech.bigultimatenavdraw.util.SessionManager;
import com.rising.high.tech.bigultimatenavdraw.util.StringFormat;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.HashMap;

import static com.rising.high.tech.bigultimatenavdraw.util.Constant.ORDERED;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.PAID;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.PARTIAL;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.PENDING;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.RECEIVED;


public class PurchaseReturnListAdapter extends RecyclerView.Adapter<PurchaseReturnListAdapter.ListPurchaseViewHolder> {

    private static final String TAG = "PurchasesListAdapter";

    private ArrayList<Transaction> dataList = new ArrayList<>();
    private Resources resources;
    private PurchaseLineDbController purchaseLineDbController;
    private TransactionDbController transactionDbController;
    private ContactDbController contactDbController;
    private TransactionPayementDbController transactionPayementDbController;
    private BusinessLocationDbController businessLocationDbController;
    private VariationLocationDetailDbController variationLocationDetailDbController;
    private SubPaymentAdapter subPaymentAdapter;
    final Calendar c = Calendar.getInstance();
    private Context context;

    private SessionManager session;
    private HashMap<String, Object> user;

    public PurchaseReturnListAdapter(Context context, ArrayList<Transaction> dataList) {
        Collections.reverse(dataList);
        this.dataList = dataList;
        this.context = context;

        session = new SessionManager(context);
        user = session.getUserDetails();

    }

    @Override
    public ListPurchaseViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {

        context = parent.getContext();
        resources = context.getResources();

        purchaseLineDbController = new PurchaseLineDbController(context);
        purchaseLineDbController.open();

        transactionDbController = new TransactionDbController(context);

        contactDbController = new ContactDbController(context);

        businessLocationDbController = new BusinessLocationDbController(context);

        transactionPayementDbController = new TransactionPayementDbController(context);
        variationLocationDetailDbController = new VariationLocationDetailDbController(context);

        subPaymentAdapter = new SubPaymentAdapter();

        subPaymentAdapter.setOnDataChangeListener(new SubPaymentAdapter.OnDataChangeListener() {
            @Override
            public void onDataChanged() {
                notifyDataSetChanged();
            }
        });

        return new ListPurchaseViewHolder(LayoutInflater.from(parent.getContext())
                .inflate(R.layout.list_purchase_return_item, parent, false));
    }

    @Override
    public void onBindViewHolder(ListPurchaseViewHolder holder, int position) {

        holder.purchaseDate.setText(dataList.get(position).getTransaction_date());
        holder.purchaseRef.setText(dataList.get(position).getRef_no());
        Contact contact = contactDbController.getCustomerById(dataList.get(position).getContact_id());
        holder.purchaseSupplier.setText(contact.getName());
        Business_location businesslocation = businessLocationDbController.getStationById(dataList.get(position).getLocation_id());
        holder.purchaseLocation.setText(businesslocation.getName());
        /**
         * TODO  holder.purchaseStatus.setText(dataList.get(position).getStatus().equals(PAID) ? RECEIVED : dataList.get(position).getStatus());
         */
        holder.purchasePaymentStatus.setText(dataList.get(position).getPayment_status());
        holder.purchaseGrandTotal.setText(dataList.get(position).getFinal_total());
        Float final_total = Float.parseFloat(dataList.get(position).getFinal_total());
        Float amount = transactionPayementDbController.getTransactionById(dataList.get(position).getId()).getAmount();

        Float total_amount = 0.f;
        for (Transaction transaction : transactionPayementDbController.getAllTransactionById(dataList.get(position).getId())) {
            total_amount += transaction.getAmount();
        }
        holder.purchasePaymentDue.setText((final_total - total_amount) + "");
        if (position % 2 == 1) {
            holder.linearLayout.setBackgroundColor(context.getResources().getColor(R.color.white));
        } else {
            holder.linearLayout.setBackgroundColor(context.getResources().getColor(R.color.lightGrey));

        }
    }

    public void setData(ArrayList<Transaction> arrayList) {
        Collections.reverse(arrayList);
        this.dataList = arrayList;
        notifyDataSetChanged();
    }

    public ArrayList<Transaction> getData() {
        return this.dataList;
    }


    @Override
    public int getItemCount() {
        return dataList.size();
    }

    public class ListPurchaseViewHolder extends RecyclerView.ViewHolder {

        TextView purchaseDate, purchaseRef, purchaseLocation, purchaseSupplier,
                purchaseAddedBy, purchaseStatus, purchasePaymentStatus, purchaseGrandTotal, purchasePaymentDue;
        Button btn_edit, btn_view;
        Spinner spinnerAction;
        LinearLayout linearLayout;

        public ListPurchaseViewHolder(View itemView) {
            super(itemView);

            linearLayout = itemView.findViewById(R.id.linearLayout);
            purchaseDate = itemView.findViewById(R.id.purchase_date);
            purchaseRef = itemView.findViewById(R.id.purchase_ref);
            purchaseLocation = itemView.findViewById(R.id.purchase_location);
            purchaseSupplier = itemView.findViewById(R.id.purchase_supplier);
            purchaseAddedBy = itemView.findViewById(R.id.purchase_added_by);
            purchaseStatus = itemView.findViewById(R.id.purchase_status);
            purchasePaymentStatus = itemView.findViewById(R.id.purchase_payment_status);
            purchaseGrandTotal = itemView.findViewById(R.id.purchase_grand_total);
            purchasePaymentDue = itemView.findViewById(R.id.purchase_payment_due);
            spinnerAction = itemView.findViewById(R.id.spinner_action);

            spinnerAction.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
                @Override
                public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                    spinnerAction.setSelection(0, true);

                    switch (position) {
                        case 1: {
                            navigateFragment(dataList.get(getAdapterPosition()).getId());
                            break;
                        }
                        case 2: {
                            deleteItem(getAdapterPosition());

//                            if (!dataList.get(getAdapterPosition()).getPayment_status().equals(PAID))
//                            {
//                                addPayement(getAdapterPosition());
//                            }
                            break;
                        }
                        case 3: {
                            viewPayement(getAdapterPosition());
                            break;
                        }
//                        case 4: {
//                            deleteItem(getAdapterPosition());
//                            break;
//                        }
//                        case 5: {
//                            break;
//                        }
//                        case 7: {
//                            updtaeStatus(getAdapterPosition());
//                            break;
//                        }
                    }
                }

                @Override
                public void onNothingSelected(AdapterView<?> parent) {

                }
            });
        }
    }

    private void updtaeStatus(int position){
        //Preparing views
        // get prompts.xml view
        LayoutInflater li = LayoutInflater.from(context);
        View promptsView = li.inflate(R.layout.update_status_dialog_main, null);

        androidx.appcompat.app.AlertDialog.Builder alertDialogBuilder = new androidx.appcompat.app.AlertDialog.Builder(
                context);

        alertDialogBuilder.setView(promptsView);

        final Button ButtonSave = promptsView.findViewById(R.id.btn_save);
        final Button ButtonClose = promptsView.findViewById(R.id.btn_close);
        final Spinner spinner_purchase_status = promptsView.findViewById(R.id.spinner_purchase_status);

        if (dataList.get(position).getStatus() != null) {
            switch (dataList.get(position).getStatus()){
                case RECEIVED:{
                    spinner_purchase_status.setSelection(1);
                }
                case PENDING:{
                    spinner_purchase_status.setSelection(2);
                }
                case ORDERED:{
                    spinner_purchase_status.setSelection(3);
                }
            }

            int indexP = Arrays.asList(resources.getStringArray(R.array.array_purchase_status)).indexOf(dataList.get(position).getStatus());
         //   spinner_purchase_status.setSelection(indexP);
        }

        final androidx.appcompat.app.AlertDialog mAlertDialog = alertDialogBuilder.create();
        if (mAlertDialog.getWindow() != null)
            mAlertDialog.getWindow().getAttributes().windowAnimations = R.style.SlidingDialogAnimation;
        mAlertDialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));

        ButtonClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mAlertDialog.dismiss();
            }
        });

        ButtonSave.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

                dataList.get(position).setStatus(spinner_purchase_status.getSelectedItem().toString().toLowerCase());
                transactionDbController.updateTransaction(dataList.get(position));

                if (mOnDataChangeListener != null && dataList.size() > 0) {
                    mOnDataChangeListener.onDataChanged();
                }
                mAlertDialog.dismiss();
                notifyDataSetChanged();
            }
        });


        mAlertDialog.show();
    }
    private void addPayement(int position) {
        LayoutInflater li = LayoutInflater.from(context);
        View promptsView = li.inflate(R.layout.add_payment_purchase_dialog_main, null);
        androidx.appcompat.app.AlertDialog.Builder alertDialogBuilder = new androidx.appcompat.app.AlertDialog.Builder(context);

        alertDialogBuilder.setView(promptsView);

        final Button ButtonSave = promptsView.findViewById(R.id.btn_save);
        final AppCompatImageView ButtonClose = promptsView.findViewById(R.id.btn_close);

        final TextView supplier_name = promptsView.findViewById(R.id.supplier_name);
        final TextView supplier_phone = promptsView.findViewById(R.id.supplier_phone);
        final TextView business_location = promptsView.findViewById(R.id.business_location);
        final TextView total_amount = promptsView.findViewById(R.id.total_amount);
        final TextView payment_note = promptsView.findViewById(R.id.payment_note);
        final TextView purchase_note = promptsView.findViewById(R.id.purchase_note);
        final EditText amount_txt = promptsView.findViewById(R.id.amount_txt);
        final EditText paid_on_txt = promptsView.findViewById(R.id.paid_on_txt);
        final Spinner spin_payment_method = promptsView.findViewById(R.id.spin_payment_method);

        Contact contact = contactDbController.getCustomerById(dataList.get(position).getContact_id());
        Transaction payment = transactionPayementDbController.getTransactionById(dataList.get(position).getId());
        Business_location businesslocation = businessLocationDbController.getStationById(dataList.get(position).getLocation_id());

        supplier_name.setText(contact.getName());
        supplier_phone.setText(contact.getMobile());
        business_location.setText(businesslocation.getName() + " " + businesslocation.getCity() + " " + businesslocation.getCountry());
        total_amount.setText(dataList.get(position).getFinal_total() + " " + user.get(session.KEY_SYMBOL));
        purchase_note.setText(dataList.get(position).getNote());
        Float final_total = Float.parseFloat(dataList.get(position).getFinal_total());


        /**
         * get amount from all payments
         */
        Float amount = 0.f;
        for (Transaction transaction : transactionPayementDbController.getAllTransactionById(dataList.get(position).getId())) {
            amount += transaction.getAmount();
        }

        Float due = (final_total - amount);
        amount_txt.setText(due + "");
        paid_on_txt.setText(payment.getPaid_on());
        payment_note.setText(payment.getNote());

        if (dataList.get(position).getMethod() != null) {
            int indexP = Arrays.asList(resources.getStringArray(R.array.payment_method_array)).indexOf(dataList.get(position).getMethod());
            spin_payment_method.setSelection(indexP);
        }

        /**
         * init button click listner
         */
        paid_on_txt.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                DatePickerDialog datePickerDialog = new DatePickerDialog(context,
                        new DatePickerDialog.OnDateSetListener() {
                            @Override
                            public void onDateSet(DatePicker datePicker, int year, int month, int day) {
                                String myFormat = StringFormat.populateSetFullDate(year, month, day, c.get(Calendar.HOUR), c.get(Calendar.MINUTE));
                                paid_on_txt.setText(myFormat);
                            }
                        }, c.get(Calendar.YEAR), c.get(Calendar.MONTH), c.get(Calendar.DAY_OF_MONTH));
                datePickerDialog.show();
            }
        });

        final androidx.appcompat.app.AlertDialog mAlertDialog = alertDialogBuilder.create();
        if (mAlertDialog.getWindow() != null)
            mAlertDialog.getWindow().getAttributes().windowAnimations = R.style.SlidingDialogAnimation;
        mAlertDialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));


        ButtonClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mAlertDialog.dismiss();
            }
        });

        ButtonSave.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

                if (Float.parseFloat(amount_txt.getText().toString()) <= due) {

                    dataList.get(position).setAmount(Float.parseFloat(amount_txt.getText().toString()));
                    dataList.get(position).setMethod(spin_payment_method.getSelectedItem().toString());
                    dataList.get(position).setNote(payment_note.getText().toString());
                    dataList.get(position).setPaid_on(paid_on_txt.getText().toString());
                    dataList.get(position).setTransaction_id(dataList.get(position).getId());

                    if (Float.parseFloat(amount_txt.getText().toString()) == due) {
                        dataList.get(position).setPayment_status(PAID);
                    }else if(Float.parseFloat(amount_txt.getText().toString()) < due){
                        dataList.get(position).setPayment_status(PARTIAL);
                    }
                    transactionDbController.updateTransaction(dataList.get(position));
                    int index = transactionPayementDbController.insertLocal(dataList.get(position));
                    if (index > 0) {
                        Toast.makeText(context, context.getResources().getText(R.string.lbl_added_success), Toast.LENGTH_LONG).show();
                        notifyItemChanged(position);
                        mAlertDialog.dismiss();
                        if (mOnDataChangeListener != null) {
                            mOnDataChangeListener.onDataChanged();
                        }
                        notifyDataSetChanged();
                    } else {
                        Toast.makeText(context, "Error insert", Toast.LENGTH_LONG).show();
                    }
                } else {
                    Toast.makeText(context, context.getResources().getText(R.string.label_maximum_amount) + " " + due, Toast.LENGTH_LONG).show();
                }
            }
        });

        mAlertDialog.show();

    }

    private void viewDetail(int position) {
        //Preparing views
        // get prompts.xml view

        LayoutInflater li = LayoutInflater.from(context);
        View promptsView = li.inflate(R.layout.view_purchase_dialog_main, null);

        androidx.appcompat.app.AlertDialog.Builder alertDialogBuilder = new androidx.appcompat.app.AlertDialog.Builder(
                context);

        alertDialogBuilder.setView(promptsView);
        SubPurchaseAdapter subPurchaseAdapter = new SubPurchaseAdapter();

        final Button ButtonSave = promptsView.findViewById(R.id.btn_save);
        final Button ButtonClose = promptsView.findViewById(R.id.btn_close);

        final TextView supplier_name = promptsView.findViewById(R.id.supplier_name);
        final TextView supplier_phone = promptsView.findViewById(R.id.supplier_phone);
        final TextView business_location = promptsView.findViewById(R.id.business_location);
        final RecyclerView recyclerView = promptsView.findViewById(R.id.recycle_sub_vente);
        final TextView label_reference_num = promptsView.findViewById(R.id.label_reference_num);
        final TextView purchase_date = promptsView.findViewById(R.id.purchase_date);
        final TextView purchase_status = promptsView.findViewById(R.id.purchase_status);
        final TextView payement_status = promptsView.findViewById(R.id.payement_status);

        recyclerView.setAdapter(subPurchaseAdapter);
        recyclerView.setLayoutManager(new LinearLayoutManager(context));

        ArrayList<Purchase_line> purchaseLines = purchaseLineDbController.getPurchaseLineByTransaction(dataList.get(position).getId());
        subPurchaseAdapter.setData(purchaseLines);

        Contact contact = contactDbController.getCustomerById(dataList.get(position).getContact_id());

        supplier_name.setText(contact.getName());
        supplier_phone.setText(contact.getMobile());
        Business_location businesslocation = businessLocationDbController.getStationById(dataList.get(position).getLocation_id());

        business_location.setText(businesslocation.getName() + " " + businesslocation.getCity() + " " + businesslocation.getCountry());
        label_reference_num.setText(dataList.get(position).getRef_no());
        purchase_date.setText(dataList.get(position).getTransaction_date());
        purchase_status.setText(dataList.get(position).getStatus());
        payement_status.setText(dataList.get(position).getPayment_status());

        //  final ImageView image_product = promptsView.findViewById(R.id.image_product);

        final androidx.appcompat.app.AlertDialog mAlertDialog = alertDialogBuilder.create();
        if (mAlertDialog.getWindow() != null)
            mAlertDialog.getWindow().getAttributes().windowAnimations = R.style.SlidingDialogAnimation;
        mAlertDialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));


        ButtonClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mAlertDialog.dismiss();
            }
        });


        mAlertDialog.show();
    }

    private void viewPayement(int position) {
        //Preparing views
        // get prompts.xml view

        LayoutInflater li = LayoutInflater.from(context);
        View promptsView = li.inflate(R.layout.view_payement_purchase_dialog_main, null);

        androidx.appcompat.app.AlertDialog.Builder alertDialogBuilder = new androidx.appcompat.app.AlertDialog.Builder(
                context);

        alertDialogBuilder.setView(promptsView);

      //  final Button ButtonSave = promptsView.findViewById(R.id.btn_save);
     //   final Button ButtonClose = promptsView.findViewById(R.id.btn_close);

        final TextView supplier_name = promptsView.findViewById(R.id.supplier_name);
        final TextView supplier_phone = promptsView.findViewById(R.id.supplier_phone);
        final TextView business_location = promptsView.findViewById(R.id.business_location);
        final RecyclerView recyclerView = promptsView.findViewById(R.id.recycle_sub_vente);
        final TextView label_reference_num = promptsView.findViewById(R.id.label_reference_num);
        final TextView purchase_date = promptsView.findViewById(R.id.purchase_date);
        final TextView purchase_status = promptsView.findViewById(R.id.purchase_status);
        final TextView payement_status = promptsView.findViewById(R.id.payement_status);

        recyclerView.setAdapter(subPaymentAdapter);
        recyclerView.setLayoutManager(new LinearLayoutManager(context));

        ArrayList<Transaction> purchaseLines = transactionPayementDbController.getAllTransactionById(dataList.get(position).getId());
        subPaymentAdapter.setData(purchaseLines);
        subPaymentAdapter.setOnDataChangeListener(new SubPaymentAdapter.OnDataChangeListener() {
            @Override
            public void onDataChanged() {

                dataList.get(position).setPayment_status(transactionDbController.getTransactionById(dataList.get(position).getId()).getPayment_status());
                notifyItemChanged(position);
            }
        });

        Contact contact = contactDbController.getCustomerById(dataList.get(position).getContact_id());

        supplier_name.setText(contact.getName());
        supplier_phone.setText(contact.getMobile());
        Business_location businesslocation = businessLocationDbController.getStationById(dataList.get(position).getLocation_id());

        business_location.setText(businesslocation.getName() + " " + businesslocation.getCity() + " " + businesslocation.getCountry());
        label_reference_num.setText(dataList.get(position).getRef_no());
        purchase_date.setText(dataList.get(position).getTransaction_date());
        purchase_status.setText(dataList.get(position).getStatus());
        payement_status.setText(dataList.get(position).getPayment_status());

        //  final ImageView image_product = promptsView.findViewById(R.id.image_product);

        final androidx.appcompat.app.AlertDialog mAlertDialog = alertDialogBuilder.create();
        if (mAlertDialog.getWindow() != null)
            mAlertDialog.getWindow().getAttributes().windowAnimations = R.style.SlidingDialogAnimation;
        mAlertDialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));

//
//        ButtonClose.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                mAlertDialog.dismiss();
//            }
//        });


        mAlertDialog.show();
    }

    private void deleteItem(int position) {
        //Preparing views
        // get prompts.xml view
        LayoutInflater li = LayoutInflater.from(context);
        View promptsView = li.inflate(R.layout.delete_dialog_main, null);

        androidx.appcompat.app.AlertDialog.Builder alertDialogBuilder = new androidx.appcompat.app.AlertDialog.Builder(
                context);

        alertDialogBuilder.setView(promptsView);

        final Button ButtonSave = promptsView.findViewById(R.id.btn_save);
        final Button ButtonClose = promptsView.findViewById(R.id.btn_close);

        final androidx.appcompat.app.AlertDialog mAlertDialog = alertDialogBuilder.create();
        if (mAlertDialog.getWindow() != null)
            mAlertDialog.getWindow().getAttributes().windowAnimations = R.style.SlidingDialogAnimation;
        mAlertDialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));


        ButtonClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mAlertDialog.dismiss();
            }
        });

        ButtonSave.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

                transactionDbController.deleteTransaction(dataList.get(position).getId());
                transactionPayementDbController.deletePayment(dataList.get(position).getId());
                for (Purchase_line purchaseLine : purchaseLineDbController.getPurchaseLineByTransaction(dataList.get(position).getId())) {
                    variationLocationDetailDbController.updatePurchaseQty(purchaseLine.getProduct_id(),dataList.get(position).getLocation_id(), purchaseLine.getQuantity());
                    purchaseLineDbController.deletePurchase(purchaseLine.getId());
                }
                dataList.remove(position);
                if (mOnDataChangeListener != null) {
                    mOnDataChangeListener.onDataChanged();
                }
                mAlertDialog.dismiss();
                notifyDataSetChanged();
            }
        });

        mAlertDialog.show();
    }

    private void navigateFragment(int id) {
        AppCompatActivity activity = (AppCompatActivity) context;
        Bundle bundle = new Bundle();
        bundle.putInt("id", id);
        Fragment myFragment = new AddPurchaseReturnFragment(true);
        myFragment.setArguments(bundle);
        activity.getSupportFragmentManager().beginTransaction().replace(R.id.nav_host_fragment, myFragment).addToBackStack(null).commit();
    }

    public interface OnDataChangeListener {
        void onDataChanged();
    }

    OnDataChangeListener mOnDataChangeListener;

    public void setOnDataChangeListener(OnDataChangeListener onDataChangeListener) {
        mOnDataChangeListener = onDataChangeListener;
    }


}
