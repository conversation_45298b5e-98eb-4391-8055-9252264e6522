package com.rising.high.tech.bigultimatenavdraw.ui.quotation.Adapter;

import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.Button;
import android.widget.Spinner;
import android.widget.TextView;

import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.AppCompatImageView;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.gson.Gson;
import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.db.ContactDbController;
import com.rising.high.tech.bigultimatenavdraw.db.BusinessLocationDbController;
import com.rising.high.tech.bigultimatenavdraw.db.TransactionDbController;
import com.rising.high.tech.bigultimatenavdraw.db.TransactionSellLineDbController;
import com.rising.high.tech.bigultimatenavdraw.model.Contact;
import com.rising.high.tech.bigultimatenavdraw.model.Discount;
import com.rising.high.tech.bigultimatenavdraw.model.Business_location;
import com.rising.high.tech.bigultimatenavdraw.model.Sell_lines;
import com.rising.high.tech.bigultimatenavdraw.model.Transaction;
import com.rising.high.tech.bigultimatenavdraw.ui.quotation.EditQuotationFragment;
import com.rising.high.tech.bigultimatenavdraw.ui.sell.adapter.SubVenteAdapter;

import org.w3c.dom.Text;

import java.util.ArrayList;
import java.util.Collections;

public class QuotationAdapter extends RecyclerView.Adapter<QuotationAdapter.VenteViewHolder> {

    private static final String TAG = "QuotationAdapter";
    private ArrayList<Transaction> dataList = new ArrayList<>();
    private BusinessLocationDbController businessLocationDbController;
    private TransactionDbController transactionDbController;
    private ContactDbController contactDbController;
    Context context;
    private TransactionSellLineDbController transactionSellLineDbController;

    @Override
    public VenteViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        context = parent.getContext();
        businessLocationDbController = new BusinessLocationDbController(context);
        businessLocationDbController.open();

        transactionDbController = new TransactionDbController(context);
        transactionDbController.open();
        transactionSellLineDbController = new TransactionSellLineDbController(context);
        transactionSellLineDbController.open();
        contactDbController = new ContactDbController(context);
        contactDbController.open();
        return new VenteViewHolder(LayoutInflater.from(parent.getContext())
                .inflate(R.layout.quotaiont_item_layout, parent, false));
    }

    @Override
    public void onBindViewHolder(VenteViewHolder holder, int position) {

        holder.date.setText(dataList.get(position).getTransaction_date());
        holder.reference_no.setText(dataList.get(position).getPayment_ref_no());
        Contact contact = contactDbController.getCustomerById(dataList.get(position).getContact_id());
        holder.customer_name.setText(contact.getName());
        Business_location businesslocation = businessLocationDbController.getStationById(dataList.get(position).getLocation_id());
        holder.location.setText(businesslocation.getName());

//        holder.name.setText(dataList.get(position).getName());
//        holder.starts_at.setText(dataList.get(position).getStarts_at());
//        holder.ends_at.setText(dataList.get(position).getEnds_at());
//        holder.discount_amnt.setText(dataList.get(position).getDiscount_amount());
//        holder.priority.setText(dataList.get(position).getPriority() + "");
//
//        String brandName = brandDbController.getBrandById(dataList.get(position).getBrand_id()).getName();
//        holder.brand.setText(brandName);
//
//        String categoryName = categoryDbController.getCategoryById(dataList.get(position).getCategory_id()).getName();
//        holder.category.setText(categoryName);

        //  holder.products.setText("...");

//        String stationName = stationDbController.getStationById(dataList.get(position).getLocation_id()).getName();
//        holder.location.setText(stationName);
//        holder.discount_type.setText(dataList.get(position).getDiscount_type());

    }

    public void setData(ArrayList<Transaction> arrayList) {
        Collections.reverse(arrayList);
        this.dataList = arrayList;
        notifyDataSetChanged();
    }

    @Override
    public int getItemCount() {
        return dataList.size();
    }

    public class VenteViewHolder extends RecyclerView.ViewHolder {
        TextView date, reference_no, customer_name, location;
        Button btnDelete, btnEdit;

        Spinner spinner_action;

        public VenteViewHolder(View itemView) {
            super(itemView);

            date = itemView.findViewById(R.id.date);
            reference_no = itemView.findViewById(R.id.reference_no);
            customer_name = itemView.findViewById(R.id.customer_name);
            location = itemView.findViewById(R.id.location);
            location = itemView.findViewById(R.id.location);
            btnDelete = itemView.findViewById(R.id.btn_delete);
            btnEdit = itemView.findViewById(R.id.btn_edit);
            spinner_action = itemView.findViewById(R.id.spinner_action);

            spinner_action.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
                @Override
                public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                    switch (position) {
                        case 1: {
                            showDetail(dataList.get(getAdapterPosition()));
                            spinner_action.setSelection(0, true);
                            break;
                        }
                        case 2: {
                            deleteItem(getAdapterPosition());
                            spinner_action.setSelection(0, true);
                            break;
                        }
                        case 3: {
                            naviguateFragment(getAdapterPosition());
                            spinner_action.setSelection(0, true);
                            break;
                        }
                    }
                }

                @Override
                public void onNothingSelected(AdapterView<?> parent) {

                }
            });

            // subVenteAdapter.setData(.getSell_lines());

        }
    }


    public interface OnDataChangeListener {
        void onDataChanged(Discount transaction);
    }

    OnDataChangeListener mOnDataChangeListener;

    public void setOnDataChangeListener(OnDataChangeListener onDataChangeListener) {
        mOnDataChangeListener = onDataChangeListener;
    }

    private void editAction() {

    }


    private void deleteItem(int postition) {
        //Preparing views
        // get prompts.xml view
        LayoutInflater li = LayoutInflater.from(context);
        View promptsView = li.inflate(R.layout.delete_dialog_main, null);

        androidx.appcompat.app.AlertDialog.Builder alertDialogBuilder = new androidx.appcompat.app.AlertDialog.Builder(
                context);

        alertDialogBuilder.setView(promptsView);

        final Button ButtonSave = promptsView.findViewById(R.id.btn_save);
        final Button ButtonClose = promptsView.findViewById(R.id.btn_close);

        final androidx.appcompat.app.AlertDialog mAlertDialog = alertDialogBuilder.create();
        if (mAlertDialog.getWindow() != null)
            mAlertDialog.getWindow().getAttributes().windowAnimations = R.style.SlidingDialogAnimation;
        mAlertDialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));

        ButtonClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mAlertDialog.dismiss();
            }
        });

        ButtonSave.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

                transactionDbController.deleteTransaction(dataList.get(postition).getId());
                dataList.remove(postition);
                mAlertDialog.dismiss();
                notifyDataSetChanged();

            }
        });


        mAlertDialog.show();
    }

    private void showDetail(Transaction transaction) {
        //Preparing views
        // get prompts.xml view
        LayoutInflater li = LayoutInflater.from(context);
        View promptsView = li.inflate(R.layout.dialog_liste_vente_detail_main, null);
        SubVenteAdapter subVenteAdapter = new SubVenteAdapter();

        androidx.appcompat.app.AlertDialog.Builder alertDialogBuilder = new androidx.appcompat.app.AlertDialog.Builder(
                context);

        // set prompts.xml to alertdialog builder

        ArrayList<Sell_lines> sell_lines = transactionSellLineDbController.getSellLineByTransaction(transaction.getId());
        final RecyclerView recyclerView = promptsView.findViewById(R.id.recycle_sub_vente);
        final TextView status = promptsView.findViewById(R.id.status);
        final TextView payment_status = promptsView.findViewById(R.id.payment_status);
        final TextView date = promptsView.findViewById(R.id.date);
        final TextView customer_name = promptsView.findViewById(R.id.customer_name);
        final TextView phone = promptsView.findViewById(R.id.phone);
        final AppCompatImageView btn_close = promptsView.findViewById(R.id.btn_close);
        final TextView title_txt = promptsView.findViewById(R.id.title_txt);

        title_txt.setText(context.getResources().getString(R.string.label_quotation_details));
        status.setText(transaction.getStatus());
        payment_status.setText(transaction.getPayment_status());
        date.setText(transaction.getTransaction_date());

        Contact contact = contactDbController.getCustomerById(transaction.getContact_id());

        customer_name.setText(contact.getName() + ", " + contact.getState() + " " + contact.getCountry());
        phone.setText(contact.getMobile());


        recyclerView.setAdapter(subVenteAdapter);
        recyclerView.setLayoutManager(new LinearLayoutManager(context));

        subVenteAdapter.setData(sell_lines);
        subVenteAdapter.notifyDataSetChanged();
        alertDialogBuilder.setView(promptsView);
        final androidx.appcompat.app.AlertDialog mAlertDialog = alertDialogBuilder.create();
        if (mAlertDialog.getWindow() != null)
            mAlertDialog.getWindow().getAttributes().windowAnimations = R.style.SlidingDialogAnimation;
        mAlertDialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));

        btn_close.setOnClickListener(v -> {
            mAlertDialog.dismiss();
        });
        mAlertDialog.show();
    }

    private void naviguateFragment(int position) {
        int id = dataList.get(position).getId();
        AppCompatActivity activity = (AppCompatActivity) context;
        Bundle bundle = new Bundle();
        bundle.putInt("id", id);
        Fragment myFragment = new EditQuotationFragment();
        myFragment.setArguments(bundle);
        activity.getSupportFragmentManager().beginTransaction().replace(R.id.nav_host_fragment, myFragment).addToBackStack(null).commit();
    }


}
