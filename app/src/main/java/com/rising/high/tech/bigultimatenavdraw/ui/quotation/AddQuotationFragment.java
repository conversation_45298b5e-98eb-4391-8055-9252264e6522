package com.rising.high.tech.bigultimatenavdraw.ui.quotation;

import android.app.DatePickerDialog;
import android.app.TimePickerDialog;
import android.content.Context;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.AutoCompleteTextView;
import android.widget.Button;
import android.widget.DatePicker;
import android.widget.EditText;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.TimePicker;
import android.widget.Toast;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.db.ContactDbController;
import com.rising.high.tech.bigultimatenavdraw.db.ProductDbController;
import com.rising.high.tech.bigultimatenavdraw.db.BusinessLocationDbController;
import com.rising.high.tech.bigultimatenavdraw.db.TaxRatesDbController;
import com.rising.high.tech.bigultimatenavdraw.db.TransactionDbController;
import com.rising.high.tech.bigultimatenavdraw.db.TransactionSellLineDbController;
import com.rising.high.tech.bigultimatenavdraw.db.VariationsDbController;
import com.rising.high.tech.bigultimatenavdraw.model.Business_location;
import com.rising.high.tech.bigultimatenavdraw.model.Contact;
import com.rising.high.tech.bigultimatenavdraw.model.Product;
import com.rising.high.tech.bigultimatenavdraw.model.Sell_lines;
import com.rising.high.tech.bigultimatenavdraw.model.Tax_rates;
import com.rising.high.tech.bigultimatenavdraw.model.Transaction;
import com.rising.high.tech.bigultimatenavdraw.model.Variation;
import com.rising.high.tech.bigultimatenavdraw.ui.adapter.QuickSellQuotationAdapter;
import com.rising.high.tech.bigultimatenavdraw.ui.adapter.SpinContactAdapter;
import com.rising.high.tech.bigultimatenavdraw.ui.adapter.SpinStationAdapter;
import com.rising.high.tech.bigultimatenavdraw.ui.adapter.SpinTaxRatesAdapter;
import com.rising.high.tech.bigultimatenavdraw.util.ProductUtil;
import com.rising.high.tech.bigultimatenavdraw.util.SessionManager;

import java.util.ArrayList;
import java.util.Calendar;

//import butterknife.BindView;
//import butterknife.ButterKnife;

import static com.rising.high.tech.bigultimatenavdraw.util.Constant.DRAFT;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.NO;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.SELL;
import static com.rising.high.tech.bigultimatenavdraw.util.StringFormat.populateSetFullDate;

public class AddQuotationFragment extends Fragment {
    private static final String TAG = "AddQuotationFragment";
    private Context _context;

    private BusinessLocationDbController businessLocationDbController;
    private ContactDbController contactDbController;
    private SpinStationAdapter spinStationAdapter;
    private SpinContactAdapter spinContactAdapter;
    private QuickSellQuotationAdapter quickSellQuotationAdapter;
    private ProductDbController productDbController;
    private TransactionDbController transactionDbController;
    private TransactionSellLineDbController transactionSellLineDbController;
    private VariationsDbController variationsDbController;
    private SpinTaxRatesAdapter spinTaxRatesAdapter;
    private TaxRatesDbController taxRatesDbController;

    @BindView(R.id.spinner_station)
    Spinner spinnerStation;
    @BindView(R.id.spinner_customer)
    Spinner spinnerCustomer;
    @BindView(R.id.spinner_discount_type)
    Spinner spinnerDiscountType;
    @BindView(R.id.spinner_term_duration)
    Spinner spinnerTermDuration;
    @BindView(R.id.spinner_purchase_status)
    Spinner spinnerPurchaseStatus;
    @BindView(R.id.id_back)
    Button btnBack;
    @BindView(R.id.add_btn)
    Button btnAdd;
    @BindView(R.id.quotation_date)
    EditText quotationDate;
    @BindView(R.id.recycle_product)
    RecyclerView recycle_product;
    @BindView(R.id.search_edit)
    AutoCompleteTextView searchEdit;
    @BindView(R.id.total_items)
    TextView totalItemsTxt;
    @BindView(R.id.net_total_amount)
    TextView netTotalAmountTxt;
    @BindView(R.id.purchase_total)
    TextView purchaseTotal;
    @BindView(R.id.shipping_charge)
    EditText shippingCharge;
    @BindView(R.id.discount_amnt_txt)
    EditText discountAmntTxt;
    @BindView(R.id.id_tax_amount)
    EditText taxAmount;
    @BindView(R.id.id_discount_amount)
    EditText discountAmount;
    @BindView(R.id.shipping_detail_txt)
    EditText shippingDetailTxt;
    @BindView(R.id.shipping_adress_txt)
    EditText shippingAdressTxt;
    @BindView(R.id.id_sell_note)
    EditText sellNote;
    @BindView(R.id.id_delivered_to)
    EditText deliveredTo;
    @BindView(R.id.pay_term_number)
    EditText payTermNumber;
    @BindView(R.id.label_total_payable)
    TextView totalPayable;
    @BindView(R.id.spinner_tax)
    Spinner spinnerTaxRates;
    @BindView(R.id.net_total_amount_curr)
    TextView net_total_amount_curr;
    @BindView(R.id.label_total_payable_curr)
    TextView label_total_payable_curr;
    final Calendar c = Calendar.getInstance();
    private SessionManager session;

    public AddQuotationFragment() {
        // Required empty public constructor
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        // Inflate the layout for this fragment
        View root = inflater.inflate(R.layout.activity_add_quotation, container, false);
        ButterKnife.bind(this, root);
        _context = getContext();
        session = new SessionManager(_context);

        quickSellQuotationAdapter = new QuickSellQuotationAdapter();
        recycle_product.setAdapter(quickSellQuotationAdapter);
        recycle_product.setLayoutManager(new LinearLayoutManager(_context));

        initDB();
        initSpin();
        initListners();

        return root;
    }

    private void initListners() {
        btnBack.setOnClickListener(v -> {
            replaceFragment(new ListQuotationFragment());
        });
        net_total_amount_curr.setText(" " + session.getUserDetails().get(session.KEY_SYMBOL));
        label_total_payable_curr.setText(" " + session.getUserDetails().get(session.KEY_SYMBOL));
        quotationDate.setOnClickListener(v ->
                {
                    DatePickerDialog datePickerDialog = new DatePickerDialog(_context,
                            new DatePickerDialog.OnDateSetListener() {
                                @Override
                                public void onDateSet(DatePicker datePicker, int year, int month, int day) {
                                    // select hours and minute
                                    TimePickerDialog timepick = new TimePickerDialog(_context, new TimePickerDialog.OnTimeSetListener() {
                                        @Override
                                        public void onTimeSet(TimePicker view, int hourOfDay, int minute) {
                                            // String myFormat =populateSetDate(year,month,day);
                                            String myFormat = populateSetFullDate(year, month, day, hourOfDay, minute);
                                            quotationDate.setText(myFormat);
                                            //    quotationDate.setBackgroundColor(Color.GRAY);
                                            //    quotationDate.setTextSize(8);
                                            // Time results here
                                        }
                                    }, c.get(Calendar.HOUR), c.get(Calendar.MINUTE), true);
                                    timepick.setTitle("select time");
                                    timepick.show();
                                }
                            }, c.get(Calendar.YEAR), c.get(Calendar.MONTH), c.get(Calendar.DAY_OF_MONTH));
                    datePickerDialog.show();
                }
        );

        quickSellQuotationAdapter.setOnDataChangeListener(new QuickSellQuotationAdapter.OnDataChangeListener() {
            @Override
            public void onDataChanged(ArrayList<Product> products) {
                netTotalAmountTxt.setText(ProductUtil.getTotalAmountDefaultSellPrice(products, _context));
                totalPayable.setText(ProductUtil.getTotalAmountDefaultSellPrice(products, _context));
                totalPayable.setText((Float.parseFloat(netTotalAmountTxt.getText().toString()) - Float.parseFloat(discountAmount.getText().toString()) + Float.parseFloat(shippingCharge.getText().toString()) + ""));
                totalItemsTxt.setText(products.size() + "");
                setViewAount();
            }
        });

        discountAmntTxt.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                if (s.toString().length() != 0) {
                    setViewAount();
                }
            }

            @Override
            public void afterTextChanged(Editable s) {

            }
        });

        shippingCharge.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                if (s.toString().length() != 0) {
                    setViewAount();
                }
            }

            @Override
            public void afterTextChanged(Editable s) {

            }
        });

        btnAdd.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                // Toast.makeText(getActivity().getApplicationContext(), _context.getResources().getString(R.string.label_all_field_required), Toast.LENGTH_LONG).show();

                if (spinnerStation.getSelectedItemPosition() != 0 && spinnerCustomer.getSelectedItemPosition() != 0 && !quotationDate.getText().toString().equals("")) {
                    Transaction transaction = new Transaction();
                    /**
                     * TODO to ,ake general business_id
                     */
                    transaction.setBusiness_id(1);
                    Business_location businesslocation = (Business_location) spinnerStation.getSelectedItem();
                    transaction.setLocation_id(businesslocation.getId());
                    transaction.setType(SELL);
                    transaction.setStatus(DRAFT);
                    transaction.setIs_quotation(1);
                    Contact contact = (Contact) spinnerCustomer.getSelectedItem();
                    transaction.setContact_id(contact.getId());
                    transaction.setTransaction_date(quotationDate.getText().toString());
                    // transaction.setTotal_before_tax();??
                    transaction.setTax_amount(taxAmount.getText().toString());
                    if (spinnerDiscountType.getSelectedItemPosition() != 0)
                        transaction.setDiscount_type(spinnerDiscountType.getSelectedItem().toString());
                    transaction.setDiscount_amount(discountAmntTxt.getText().toString());
                    transaction.setShipping_details(shippingDetailTxt.getText().toString());
                    transaction.setShipping_address(shippingAdressTxt.getText().toString());
                    if (spinnerPurchaseStatus.getSelectedItemPosition() != 0)
                        transaction.setShipping_status(spinnerPurchaseStatus.getSelectedItem().toString());
                    transaction.setDelivered_to(deliveredTo.getText().toString());
                    transaction.setAdditional_notes(sellNote.getText().toString());
                    transaction.setShipping_charges(shippingCharge.getText().toString());
                    transaction.setFinal_total(totalPayable.getText().toString());
                    transaction.setCreated_by(1);
                    transaction.setIs_sync(NO);
                    transaction.setPay_term_number(!payTermNumber.getText().toString().equals("") ? payTermNumber.getText().toString() : "0");
                    if (spinnerTermDuration.getSelectedItemPosition() != 0)
                        transaction.setPay_term_type(spinnerTermDuration.getSelectedItem().toString());
                    if (spinnerTaxRates.getSelectedItemPosition() != 0) {
                        Tax_rates tax_rates = (Tax_rates) spinnerTaxRates.getSelectedItem();
                        transaction.setTax_id(tax_rates.getId());
                    }
                    int idInsert = transactionDbController.insertLocal(transaction);

                    if (idInsert > 0) {

                        for (Product product : quickSellQuotationAdapter.getData()) {
                            Sell_lines sell_line = new Sell_lines();
                            sell_line.setTransaction_id(idInsert);
                            sell_line.setProduct_id(product.getId());
                            sell_line.setVariation_id(product.getId());
                            sell_line.setQuantity(product.getSell_qte());
                            Variation variation = variationsDbController.getVariationByProductId(product.getId());
                            sell_line.setUnit_price(variation.getDefault_sell_price());
                            sell_line.setUnit_price_inc_tax(variation.getSell_price_inc_tax());
                            transactionSellLineDbController.insertLocal(sell_line);
                        }

                        replaceFragment(new ListQuotationFragment());
                    }
                } else {
                    Toast.makeText(_context, getResources().getString(R.string.label_all_field_required), Toast.LENGTH_LONG).show();
                }

            }
        });

    }

    private void setViewAount() {
        Float discount = Float.parseFloat(discountAmntTxt.getText().toString());
        Float totalnet = Float.parseFloat(netTotalAmountTxt.getText().toString());
        Float taxamount = Float.parseFloat(taxAmount.getText().toString());
        Float shippingcharge = Float.parseFloat(shippingCharge.getText().toString());
        Tax_rates tax_rates = (Tax_rates) spinnerTaxRates.getSelectedItem();
        float taxpercentage = Float.parseFloat(tax_rates.getAmount());
        taxAmount.setText((taxpercentage * (totalnet + discount) / 100) + "");
        switch (spinnerDiscountType.getSelectedItemPosition()) {
            case 0: {
                discountAmount.setText("00");
                discount = 0.f;
                totalPayable.setText((totalnet - discount + taxamount + shippingcharge) + "");
                break;
            }
            case 1: {

                totalnet = Float.parseFloat(netTotalAmountTxt.getText().toString());
                taxamount = Float.parseFloat(taxAmount.getText().toString());
                shippingcharge = Float.parseFloat(shippingCharge.getText().toString());
                tax_rates = (Tax_rates) spinnerTaxRates.getSelectedItem();
                taxpercentage = Float.parseFloat(tax_rates.getAmount());


                discount = Float.parseFloat(discountAmntTxt.getText().toString());
                Float orderTaxAmount = ((totalnet - discount) * taxpercentage / 100);
                discountAmount.setText(discount + "");

                taxAmount.setText(orderTaxAmount + "");
                totalPayable.setText((totalnet - discount + orderTaxAmount + shippingcharge) + "");


                //   totalPayable.setText((totalnet - discount + taxamount + shippingcharge) + "");
                break;
            }
            case 2: {

                totalnet = Float.parseFloat(netTotalAmountTxt.getText().toString());
                taxamount = Float.parseFloat(taxAmount.getText().toString());
                shippingcharge = Float.parseFloat(shippingCharge.getText().toString());
                tax_rates = (Tax_rates) spinnerTaxRates.getSelectedItem();
                taxpercentage = Float.parseFloat(tax_rates.getAmount());


                discount = (Float.parseFloat(discountAmntTxt.getText().toString()) * totalnet) / 100;
                Float orderTaxAmount = 0.f;
                if (spinnerDiscountType.getSelectedItemPosition() == 1) {
                    orderTaxAmount = ((totalnet - discount) * taxpercentage / 100);
                } else if (spinnerDiscountType.getSelectedItemPosition() == 2) {
                    orderTaxAmount = ((totalnet) * taxpercentage / 100);
                }
                discountAmount.setText(discount + "");

                taxAmount.setText(orderTaxAmount + "");
                totalPayable.setText((totalnet - discount + orderTaxAmount + shippingcharge) + "");


                //   discountAmount.setText(((totalnet * discount / 100) + taxamount + shippingcharge) + "");
                //    totalPayable.setText((totalnet * (1 - (discount / 100)) + ""));
                break;
            }
        }
    }

    private String getPurchaseTotal() {
        Float purchaseTotal = 0.f;
        purchaseTotal = Float.parseFloat(netTotalAmountTxt.getText().toString()) + Float.parseFloat(shippingCharge.getText().toString());
        return purchaseTotal + "";
    }

    private void initSpin() {
        spinStationAdapter = new SpinStationAdapter(_context, android.R.layout.simple_spinner_item, businessLocationDbController.getAllStationSpinner());
        spinnerStation.setAdapter(spinStationAdapter);

        spinContactAdapter = new SpinContactAdapter(_context, android.R.layout.simple_spinner_item, contactDbController.getSpinnerCustomer());
        spinnerCustomer.setAdapter(spinContactAdapter);

        spinTaxRatesAdapter = new SpinTaxRatesAdapter(_context, android.R.layout.simple_spinner_item, taxRatesDbController.getAllTax_ratesSpinner());
        spinnerTaxRates.setAdapter(spinTaxRatesAdapter);

        spinnerDiscountType.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                setViewAount();
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {

            }
        });

        spinnerTaxRates.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                //   setViewAount();

                Float discount = Float.parseFloat(discountAmntTxt.getText().toString());
                Float totalnet = Float.parseFloat(netTotalAmountTxt.getText().toString());
                Float taxamount = Float.parseFloat(taxAmount.getText().toString());
                Float shippingcharge = Float.parseFloat(shippingCharge.getText().toString());

                Tax_rates tax_rates = (Tax_rates) spinnerTaxRates.getSelectedItem();
                float taxpercentage = Float.parseFloat(tax_rates.getAmount());

                Float orderTaxAmount = ((totalnet - discount) * taxpercentage / 100);


                taxAmount.setText(orderTaxAmount + "");
                totalPayable.setText((totalnet - discount + orderTaxAmount + shippingcharge) + "");
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {

            }
        });
    }

    private void initDB() {
        businessLocationDbController = new BusinessLocationDbController(_context);
        contactDbController = new ContactDbController(_context);
        productDbController = new ProductDbController(_context);
        transactionDbController = new TransactionDbController(_context);
        transactionSellLineDbController = new TransactionSellLineDbController(_context);
        variationsDbController = new VariationsDbController(_context);
        taxRatesDbController = new TaxRatesDbController(_context);
        setProductSearchDapter(productDbController.getAllProduct());

    }


    private void setProductSearchDapter(ArrayList<Product> arrayList) {
        ArrayAdapter<Product> adapter = new ArrayAdapter<Product>(_context, android.R.layout.simple_list_item_1, arrayList);
        searchEdit.setAdapter(adapter);
        searchEdit.setDropDownBackgroundDrawable(new ColorDrawable(_context.getResources().getColor(R.color.colorPrimary)));
        searchEdit.setOnItemClickListener(new AdapterView.OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> parent, View arg1, int pos, long id) {
                Product selected = (Product) parent.getAdapter().getItem(pos);
                quickSellQuotationAdapter.updateData(selected);
                searchEdit.setText("");
            }
        });

    }


    public void replaceFragment(Fragment someFragment) {
        FragmentTransaction transaction = getFragmentManager().beginTransaction();
        transaction.replace(R.id.nav_host_fragment, someFragment);
        transaction.addToBackStack(null);
        transaction.commit();
    }

}