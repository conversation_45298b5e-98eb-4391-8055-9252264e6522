package com.rising.high.tech.bigultimatenavdraw.ui.quotation;

import android.app.DatePickerDialog;
import android.app.TimePickerDialog;
import android.content.Context;
import android.graphics.Color;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.DatePicker;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.TimePicker;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.db.ContactDbController;
import com.rising.high.tech.bigultimatenavdraw.db.TransactionSellLineDbController;
import com.rising.high.tech.bigultimatenavdraw.db.BusinessLocationDbController;
import com.rising.high.tech.bigultimatenavdraw.db.TransactionDbController;
import com.rising.high.tech.bigultimatenavdraw.model.Business_location;
import com.rising.high.tech.bigultimatenavdraw.model.Contact;
import com.rising.high.tech.bigultimatenavdraw.model.Transaction;
import com.rising.high.tech.bigultimatenavdraw.ui.adapter.SpinContactAdapter;
import com.rising.high.tech.bigultimatenavdraw.ui.adapter.SpinStationAdapter;
import com.rising.high.tech.bigultimatenavdraw.ui.quotation.Adapter.QuotationAdapter;
import com.rising.high.tech.bigultimatenavdraw.util.SessionManager;
import com.rising.high.tech.bigultimatenavdraw.util.serverInteraction;

import java.util.Calendar;
import java.util.HashMap;


import static com.rising.high.tech.bigultimatenavdraw.util.Constant.DRAFT;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.SERVER_MASTER;
import static com.rising.high.tech.bigultimatenavdraw.util.StringFormat.populateSetFullDate;

public class ListQuotationFragment extends Fragment {

    private static final String TAG = "ListQuotationFragment";
    private Context _context;
    Spinner spinnerPayementStatus;

    RecyclerView recycle_vente;
    TransactionDbController transactionDbController;
    QuotationAdapter quotationAdapter;
    private TransactionSellLineDbController transactionSellLineDbController;
    SpinStationAdapter spinStationAdapter;
    SpinContactAdapter spinContactAdapter;
    BusinessLocationDbController businessLocationDbController;
    ContactDbController contactDbController;
    final Calendar c = Calendar.getInstance();
    private HashMap<String, String> currentMap = new HashMap<>();
    SessionManager session;

    LinearLayout filterContainer;
    LinearLayout filterMain;
    Spinner spinnerStation;
    Spinner spinnerCustomer;
    EditText editStartDate;
    EditText editEndDate;
    Button btnAdd;
    Button btnSearch;
    TextView noItemFound;

    public ListQuotationFragment() {
        // Required empty public constructor
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        // Inflate the layout for this fragment
        View root = inflater.inflate(R.layout.fragment_quotation_main, container, false);

        _context = getContext();
        session = new SessionManager(_context);

        spinnerPayementStatus = root.findViewById(R.id.spinner_payment_status);

        recycle_vente = root.findViewById(R.id.recycle_vente);
        transactionDbController = new TransactionDbController(_context);
        transactionDbController.open();
        quotationAdapter = new QuotationAdapter();
        recycle_vente.setAdapter(quotationAdapter);
        recycle_vente.setLayoutManager(new LinearLayoutManager(_context));


        initDb();
        initSpinners();
        initClickListner();
        quotationAdapter.setData(transactionDbController.getQutationList());
        setItemView();

        checkRoles();

        return root;
    }
    private void checkRoles()
    {
        if (session.getBoolean(SERVER_MASTER))
        {
            btnAdd.setVisibility(View.INVISIBLE);
        }
    }
    private void initClickListner() {

        editStartDate.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                DatePickerDialog datePickerDialog = new DatePickerDialog(_context,
                        new DatePickerDialog.OnDateSetListener() {
                            @Override
                            public void onDateSet(DatePicker datePicker, int year, int month, int day) {
                                // select hours and minute
                                TimePickerDialog timepick = new TimePickerDialog(_context, new TimePickerDialog.OnTimeSetListener() {
                                    @Override
                                    public void onTimeSet(TimePicker view, int hourOfDay, int minute) {
                                        // String myFormat =populateSetDate(year,month,day);
                                        String myFormat = populateSetFullDate(year, month, day, hourOfDay, minute);
                                        editStartDate.setText(myFormat);
                                        currentMap.put("date_debut", myFormat);
//                                        editStartDate.setBackgroundColor(Color.GRAY);
//                                        editStartDate.setTextSize(8);
                                        // Time results here
                                    }
                                }, c.get(Calendar.HOUR), c.get(Calendar.MINUTE), true);
                                timepick.setTitle("select time");
                                timepick.show();
                            }
                        }, c.get(Calendar.YEAR), c.get(Calendar.MONTH), c.get(Calendar.DAY_OF_MONTH));
                datePickerDialog.show();
            }
        });

        editEndDate.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                DatePickerDialog datePickerDialog = new DatePickerDialog(_context,
                        new DatePickerDialog.OnDateSetListener() {
                            @Override
                            public void onDateSet(DatePicker datePicker, int year, int month, int day) {
                                TimePickerDialog timepick = new TimePickerDialog(_context, new TimePickerDialog.OnTimeSetListener() {
                                    @Override
                                    public void onTimeSet(TimePicker view, int hourOfDay, int minute) {
                                        //String myFormat =populateSetDate(year,month,day);
                                        String myFormat = populateSetFullDate(year, month, day, hourOfDay, minute);
                                        editEndDate.setText(myFormat);
                                        currentMap.put("date_fin", myFormat);
//                                        editEndDate.setBackgroundColor(Color.GRAY);
//                                        editEndDate.setTextSize(8);
                                        // Time results here
                                    }
                                }, c.get(Calendar.HOUR), c.get(Calendar.MINUTE), true);
                                timepick.setTitle("select time");
                                timepick.show();

                            }
                        }, c.get(Calendar.YEAR), c.get(Calendar.MONTH), c.get(Calendar.DAY_OF_MONTH));
                datePickerDialog.show();
            }
        });

//        quotationAdapter.setOnDataChangeListener(new QuotationAdapter.OnDataChangeListener() {
//            @Override
//            public void onDataChanged(Transaction transaction) {
//                Log.d(TAG, "click here ### " + new Gson().toJson(transaction));
//                showDetail(transaction);
//            }
//        });

        filterContainer.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                filterMain.setVisibility(filterMain.getVisibility() == View.VISIBLE ? View.GONE : View.VISIBLE);
            }
        });

        btnAdd.setOnClickListener(v -> {
            replaceFragment(new AddQuotationFragment());
        });

        btnSearch.setOnClickListener(v -> {
            Business_location businesslocation = (Business_location) spinnerStation.getSelectedItem();
            Contact contact = (Contact) spinnerCustomer.getSelectedItem();

            quotationAdapter.setData(transactionDbController.filterTransaction(DRAFT, businesslocation.getId(), contact.getId(), currentMap.get("date_debut"), currentMap.get("date_fin"), 1));
            quotationAdapter.notifyDataSetChanged();
            setItemView();
        });

    }

    private void initSpinners() {
        spinStationAdapter = new SpinStationAdapter(_context, android.R.layout.simple_spinner_item, businessLocationDbController.getAllStationSpinner());
        spinnerStation.setAdapter(spinStationAdapter);

        spinContactAdapter = new SpinContactAdapter(_context, android.R.layout.simple_spinner_item, contactDbController.getSpinnerCustomer());
        spinnerCustomer.setAdapter(spinContactAdapter);
    }

    private void initDb() {
        transactionSellLineDbController = new TransactionSellLineDbController(_context);
        transactionSellLineDbController.open();
        businessLocationDbController = new BusinessLocationDbController(_context);
        businessLocationDbController.open();
        contactDbController = new ContactDbController(_context);
        contactDbController.open();
    }

    public void replaceFragment(Fragment someFragment) {
        FragmentTransaction transaction = getFragmentManager().beginTransaction();
        transaction.replace(R.id.nav_host_fragment, someFragment);
        transaction.addToBackStack(null);
        transaction.commit();
    }

    private void showDetail(Transaction transaction) {
        //Preparing views
        // get prompts.xml view
//        LayoutInflater li = LayoutInflater.from(_context);
//        View promptsView = li.inflate(R.layout.dialog_filtre_main, null);
//        SubQuotationAdapter subQuotationAdapter = new SubQuotationAdapter();
//
//        androidx.appcompat.app.AlertDialog.Builder alertDialogBuilder = new androidx.appcompat.app.AlertDialog.Builder(_context);
//
//        // set prompts.xml to alertdialog builder
//        alertDialogBuilder.setView(promptsView);
//        ArrayList<Sell_lines> sell_lines = sellLineDbController.getSellLineByTransaction(transaction.getId());
//        final RecyclerView recyclerView = promptsView.findViewById(R.id.recycle_sub_vente);
//        recyclerView.setAdapter(subQuotationAdapter);
//        recyclerView.setLayoutManager(new LinearLayoutManager(_context));
//
//        subQuotationAdapter.setData(sell_lines);
//        Log.d(TAG, "transactionns sells  ### " + new Gson().toJson(sell_lines));
//
//        final androidx.appcompat.app.AlertDialog mAlertDialog = alertDialogBuilder.create();
//        if (mAlertDialog.getWindow() != null)
//            mAlertDialog.getWindow().getAttributes().windowAnimations = R.style.SlidingDialogAnimation;
//        mAlertDialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
//
//        mAlertDialog.show();
    }


    public void setItemView() {
        if (quotationAdapter.getItemCount() > 0) {
            recycle_vente.setVisibility(View.VISIBLE);
            noItemFound.setVisibility(View.GONE);
        } else {
            recycle_vente.setVisibility(View.GONE);
            noItemFound.setVisibility(View.VISIBLE);
        }
    }


}