package com.rising.high.tech.bigultimatenavdraw.ui.reportstock.adapter;

import android.content.Context;
import android.content.res.Resources;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.recyclerview.widget.RecyclerView;
import com.google.gson.Gson;
import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.model.StockReport;
import com.rising.high.tech.bigultimatenavdraw.util.SessionManager;

import java.util.ArrayList;
import java.util.HashMap;


public class ReportStockProductAdapter extends RecyclerView.Adapter<ReportStockProductAdapter.ListProductViewHolder> {

    private static final String TAG = "ReportStockProductAdapt";

    private ArrayList<StockReport> dataList = new ArrayList<>();

    Context context;

    SessionManager session;
    HashMap<String, Object> user;
    @Override
    public ListProductViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        context = parent.getContext();

        session = new SessionManager(context);
        user = session.getUserDetails();

        return new ListProductViewHolder(LayoutInflater.from(parent.getContext())
                .inflate(R.layout.list_product_stock_item, parent, false));
    }

    @Override
    public void onBindViewHolder(ListProductViewHolder holder, int position) {
        holder.sku_id.setText(dataList.get(position).getSku());
        holder.product_name.setText(dataList.get(position).getProduct());
        holder.location.setText(dataList.get(position).getLocation());
        holder.unit_price.setText(dataList.get(position).getUnit_price() + user.get(session.KEY_SYMBOL));
        holder.current_stock.setText(dataList.get(position).getCurrent_stock());
        holder.stcok_value_bpp.setText(dataList.get(position).getCurrent_stock_value_bpp()  + user.get(session.KEY_SYMBOL));
        holder.stcok_value_bcp.setText(dataList.get(position).getCurrent_stock_value_bsp() + user.get(session.KEY_SYMBOL));
        holder.potential_profit.setText(dataList.get(position).getPotential_profit() + user.get(session.KEY_SYMBOL));
        holder.total_unit_solde.setText(dataList.get(position).getTotal_unit_sold());
        holder.total_unit_transfered.setText(dataList.get(position).getTotal_unit_transfered());
        holder.total_unit_adjusted.setText(dataList.get(position).getTotal_unit_adjusted());
    }

    public void setData(ArrayList<StockReport> arrayList) {
        this.dataList = arrayList;
        notifyDataSetChanged();
    }


    @Override
    public int getItemCount() {
        return dataList.size();
    }

    public class ListProductViewHolder extends RecyclerView.ViewHolder {

        TextView product_name, location, unit_price, current_stock, sku_id, stcok_value_bpp, stcok_value_bcp, potential_profit, total_unit_solde, total_unit_transfered, total_unit_adjusted;
        public ListProductViewHolder(View itemView) {
            super(itemView);

            sku_id = itemView.findViewById(R.id.sku_id);
            product_name = itemView.findViewById(R.id.product_name);
            location = itemView.findViewById(R.id.location);
            unit_price = itemView.findViewById(R.id.unit_price);
            current_stock = itemView.findViewById(R.id.current_stock);
            stcok_value_bpp = itemView.findViewById(R.id.stcok_value_bpp);
            stcok_value_bcp = itemView.findViewById(R.id.stcok_value_bcp);
            potential_profit = itemView.findViewById(R.id.potential_profit);
            total_unit_solde = itemView.findViewById(R.id.total_unit_solde);
            total_unit_transfered = itemView.findViewById(R.id.total_unit_transfered);
            total_unit_adjusted = itemView.findViewById(R.id.total_unit_adjusted);

        }
    }
}
