package com.rising.high.tech.bigultimatenavdraw.ui.rightmenu;

import android.content.Context;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;

import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.ui.brand.ListBrandFragment;
import com.rising.high.tech.bigultimatenavdraw.ui.category.ListCategoryFragment;
import com.rising.high.tech.bigultimatenavdraw.ui.categoryexpense.CategoryExpenseFragment;
import com.rising.high.tech.bigultimatenavdraw.ui.contact.ListContactFragment;
import com.rising.high.tech.bigultimatenavdraw.ui.discount.ListDiscountFragment;
import com.rising.high.tech.bigultimatenavdraw.ui.draft.ListDraftFragment;
import com.rising.high.tech.bigultimatenavdraw.ui.expense.ListExpenseFragment;
import com.rising.high.tech.bigultimatenavdraw.ui.home.HomeFragment;
import com.rising.high.tech.bigultimatenavdraw.ui.product.ListProductFragment;
import com.rising.high.tech.bigultimatenavdraw.ui.purchase.PurchaseFragment;
import com.rising.high.tech.bigultimatenavdraw.ui.purchasereturn.PurchasesReturnFragment;
import com.rising.high.tech.bigultimatenavdraw.ui.quotation.ListQuotationFragment;
import com.rising.high.tech.bigultimatenavdraw.ui.reportstock.ReportStockFragment;
import com.rising.high.tech.bigultimatenavdraw.ui.sell.ListVenteFragment;
import com.rising.high.tech.bigultimatenavdraw.ui.stockadjustement.StockAdjustementFragment;
import com.rising.high.tech.bigultimatenavdraw.ui.stocktransfers.StockTransferFragment;
import com.rising.high.tech.bigultimatenavdraw.ui.unit.ListUnitFragment;
import com.rising.high.tech.bigultimatenavdraw.ui.variation.ListVariationFragment;
import com.rising.high.tech.bigultimatenavdraw.ui.warranty.ListWarrantyFragment;


public class RightMenuFragment extends Fragment {
    private static final String TAG = "ListVenteFragment";
    private Context _context;

//    LinearLayout filterContainer;
//    LinearLayout filterHeader;
//    EditText btnStartDate;
//    EditText btnEndDate;
//    TextView noItemFound;

    public RightMenuFragment() {
        // Required empty public constructor
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        // Inflate the layout for this fragment
        View root = inflater.inflate(R.layout.fragment_right_menu_main, container, false);
        _context = getContext();

        final LinearLayout id_home = root.findViewById(R.id.id_home);
        final LinearLayout id_stock = root.findViewById(R.id.id_stock);
        final LinearLayout id_prudcts = root.findViewById(R.id.id_prudcts);
        final LinearLayout id_purchaes_products = root.findViewById(R.id.id_purchaes_products);
        final LinearLayout id_sales = root.findViewById(R.id.id_sales);
        final LinearLayout id_stock_transfer = root.findViewById(R.id.id_stock_transfer);
        final LinearLayout id_stock_adjustement = root.findViewById(R.id.id_stock_adjustement);
        final LinearLayout id_expenses = root.findViewById(R.id.id_expenses);
        final LinearLayout id_reports = root.findViewById(R.id.id_reports);

        // container main
        final LinearLayout main_container = root.findViewById(R.id.main_container);
        final LinearLayout product_container = root.findViewById(R.id.product_container);
        final LinearLayout id_list_product = root.findViewById(R.id.id_list_product);
        final LinearLayout sell_container = root.findViewById(R.id.sell_container);
        final LinearLayout id_variation = root.findViewById(R.id.id_variation);
        final LinearLayout id_units = root.findViewById(R.id.id_units);
        final LinearLayout id_categories = root.findViewById(R.id.id_categories);
        final LinearLayout id_warranties = root.findViewById(R.id.id_warranties);
        final LinearLayout id_brands = root.findViewById(R.id.id_brands);
        final LinearLayout id_all_sell = root.findViewById(R.id.id_all_sell);
        final LinearLayout id_draft = root.findViewById(R.id.id_draft);
        final LinearLayout id_quotation = root.findViewById(R.id.id_quotation);
        final LinearLayout id_sell_return = root.findViewById(R.id.id_sell_return);
        final LinearLayout id_discount = root.findViewById(R.id.id_discount);
        final LinearLayout id_list_expenses = root.findViewById(R.id.id_list_expenses);
        final LinearLayout id_expense_categories = root.findViewById(R.id.id_expense_categories);
        final LinearLayout id_purchase_container = root.findViewById(R.id.id_purchase_container);

        final LinearLayout expense_container = root.findViewById(R.id.expense_container);
        final LinearLayout id_list_purchase = root.findViewById(R.id.id_list_purchase);
        final LinearLayout id_purchase_return = root.findViewById(R.id.id_purchase_return);

        id_home.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                replaceFragment(new HomeFragment());
                  
            }
        });

        id_stock.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                replaceFragment(new ListContactFragment());
                  
            }
        });

        id_discount.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                replaceFragment(new ListDiscountFragment());
                  
            }
        });

        id_quotation.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                replaceFragment(new ListQuotationFragment());
                  
            }
        });

        id_sell_return.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                replaceFragment(new PurchasesReturnFragment());
                  
            }
        });

        id_prudcts.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                // replaceFragment(new ListProductFragment());
                main_container.setVisibility(View.GONE);
                product_container.setVisibility(View.VISIBLE);
                //       
            }
        });

        id_list_product.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                replaceFragment(new ListProductFragment());
                  
            }
        });


        id_purchase_return.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                replaceFragment(new PurchasesReturnFragment());
                  
            }
        });


        id_draft.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                replaceFragment(new ListDraftFragment());
                  
            }
        });

        id_variation.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                replaceFragment(new ListVariationFragment());
                  
            }
        });
        id_brands.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                replaceFragment(new ListBrandFragment());
                  
            }
        });
        id_all_sell.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                replaceFragment(new ListVenteFragment());
                  
            }
        });
        id_expense_categories.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                replaceFragment(new CategoryExpenseFragment());
                  
            }
        });

        id_categories.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                replaceFragment(new ListCategoryFragment());
                  
            }
        });
        id_warranties.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                replaceFragment(new ListWarrantyFragment());
                  
            }
        });

        id_units.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                replaceFragment(new ListUnitFragment());
                  
            }
        });

        id_purchaes_products.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

                id_purchase_container.setVisibility(View.VISIBLE);
                main_container.setVisibility(View.GONE);
            }
        });

        id_list_purchase.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                replaceFragment(new PurchaseFragment());

                  
            }
        });
        id_list_expenses.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                replaceFragment(new ListExpenseFragment());
                  
            }
        });


        id_sales.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                //    replaceFragment(new ListVenteFragment());
                sell_container.setVisibility(View.VISIBLE);
                main_container.setVisibility(View.GONE);
                //    
            }
        });

        id_stock_transfer.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                replaceFragment(new StockTransferFragment());
                  
            }
        });

        id_stock_adjustement.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                replaceFragment(new StockAdjustementFragment());
                  
            }
        });

        id_expenses.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                //  replaceFragment(new ListExpenseFragment());
                expense_container.setVisibility(View.VISIBLE);
                main_container.setVisibility(View.GONE);
                //    
            }
        });
        id_reports.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                replaceFragment(new ReportStockFragment());
                  
            }
        });
        

        return root;
    }


    public void replaceFragment(Fragment someFragment) {
        FragmentTransaction transaction = getFragmentManager().beginTransaction();
        transaction.replace(R.id.nav_host_fragment, someFragment);
        transaction.addToBackStack(null);
        transaction.commit();
    }
}