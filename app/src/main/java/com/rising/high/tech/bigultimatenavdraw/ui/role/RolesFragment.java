package com.rising.high.tech.bigultimatenavdraw.ui.role;

import android.content.Context;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.TextView;

import androidx.appcompat.widget.SearchView;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.db.RoleDbController;
import com.rising.high.tech.bigultimatenavdraw.db.UserDbController;
import com.rising.high.tech.bigultimatenavdraw.ui.role.adapter.RoleAdapter;
import com.rising.high.tech.bigultimatenavdraw.ui.usermanagement.AddUserFragment;
import com.rising.high.tech.bigultimatenavdraw.util.SessionManager;

//import butterknife.BindView;
//import butterknife.ButterKnife;

import static com.rising.high.tech.bigultimatenavdraw.util.Constant.ROLES_ADD;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.SERVER_MASTER;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.USER_ADD;

public class RolesFragment extends Fragment {

    private static final String TAG = "UsersFragment";
    private Context _context;
    SessionManager session;

    @BindView(R.id.noItemFound)
    TextView noItemFound;
    Button addBtn;
    RecyclerView recycle_taxe_rates;
    RoleAdapter roleAdapter;
    RoleDbController roleDbController;

    @BindView(R.id.id_filter)
    Button btnFilter;
    @BindView(R.id.id_search_edit)
    SearchView search_edit;

    public RolesFragment() {
        // Required empty public constructor
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        // Inflate the layout for this fragment
        View root = inflater.inflate(R.layout.fragment_roles, container, false);
        ButterKnife.bind(this, root);
        _context = getContext();
        session = new SessionManager(_context);

        recycle_taxe_rates = root.findViewById(R.id.recycle_taxe_rates);
        addBtn = root.findViewById(R.id.id_add);

        roleDbController = new RoleDbController(_context);
        roleDbController.open();

        roleAdapter = new RoleAdapter();
        recycle_taxe_rates.setAdapter(roleAdapter);
        recycle_taxe_rates.setLayoutManager(new LinearLayoutManager(_context));

        roleAdapter.setData(roleDbController.getAllRoles());

        addBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                addUser();
            }
        });

        search_edit.setQueryHint("Search Here");
        search_edit.setOnQueryTextListener(new SearchView.OnQueryTextListener() {

            @Override
            public boolean onQueryTextSubmit(String query) {
                roleAdapter.setData(roleDbController.getRolesLike(query));
                setItemView();
                return false;
            }

            @Override
            public boolean onQueryTextChange(String newText) {
                roleAdapter.setData(roleDbController.getRolesLike(newText));
                setItemView();
                return false;
            }
        });

        setItemView();
        checkRoles();
        return root;
    }

    private void checkRoles() {
        if (session.getBoolean(SERVER_MASTER) || !session.checkPermissionSubModule(ROLES_ADD)) {
            addBtn.setVisibility(View.INVISIBLE);
        }
    }

    public void setItemView() {
        if (roleAdapter.getItemCount() > 0) {
            recycle_taxe_rates.setVisibility(View.VISIBLE);
            noItemFound.setVisibility(View.GONE);
        } else {
            recycle_taxe_rates.setVisibility(View.GONE);
            noItemFound.setVisibility(View.VISIBLE);
        }
    }


    public void replaceFragment(Fragment someFragment) {
        FragmentTransaction transaction = getFragmentManager().beginTransaction();
        transaction.replace(R.id.nav_host_fragment, someFragment);
        transaction.addToBackStack(null);
        transaction.commit();
    }


    private void addUser() {
        //Preparing views
        // get prompts.xml view
        replaceFragment(new AddRoleFragment());
    }


}