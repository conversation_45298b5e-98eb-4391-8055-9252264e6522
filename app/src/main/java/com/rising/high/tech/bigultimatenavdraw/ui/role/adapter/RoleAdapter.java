package com.rising.high.tech.bigultimatenavdraw.ui.role.adapter;

import android.content.Context;
import android.content.res.Resources;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.TextView;

import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.AppCompatImageView;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.RecyclerView;

import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.db.RoleDbController;
import com.rising.high.tech.bigultimatenavdraw.model.Role;
import com.rising.high.tech.bigultimatenavdraw.model.Role;
import com.rising.high.tech.bigultimatenavdraw.ui.contact.EditContactFragment;
import com.rising.high.tech.bigultimatenavdraw.ui.role.EditRoleFragment;
import com.rising.high.tech.bigultimatenavdraw.util.SessionManager;

import java.util.ArrayList;

import static com.rising.high.tech.bigultimatenavdraw.util.Constant.ROLES_DELETE;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.ROLES_EDIT;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.SERVER_MASTER;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.USER_DELETE;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.USER_EDIT;


public class RoleAdapter extends RecyclerView.Adapter<RoleAdapter.ListRoleViewHolder> {
    private static final String TAG = "ListRoleAdapter";
    private ArrayList<Role> dataList = new ArrayList<>();
    private Resources resources;
    Context context;
    SessionManager session;

    private RoleDbController roleDbController;

    @Override
    public ListRoleViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        context = parent.getContext();
        resources = context.getResources();
        session = new SessionManager(context);

        roleDbController = new RoleDbController(context);
        roleDbController.open();

        return new ListRoleViewHolder(LayoutInflater.from(parent.getContext())
                .inflate(R.layout.roles_item, parent, false));
    }

    @Override
    public void onBindViewHolder(ListRoleViewHolder holder, int position) {
        if (session.getBoolean(SERVER_MASTER) || !session.checkPermissionSubModule(ROLES_EDIT)) {
            holder.btnEdit.setVisibility(View.GONE);
        }
        if (session.getBoolean(SERVER_MASTER) || !session.checkPermissionSubModule(ROLES_DELETE)) {
            holder.btnDelete.setVisibility(View.GONE);
        }
        holder.id_name.setText(dataList.get(position).getName());
        if (position==0)
        {
            holder.btnDelete.setEnabled(false);
         //   holder.btnEdit.setEnabled(false);
        }

    }

    public void setData(ArrayList<Role> arrayList) {
        this.dataList = arrayList;
        notifyDataSetChanged();
    }


    @Override
    public int getItemCount() {
        return dataList.size();
    }

    public class ListRoleViewHolder extends RecyclerView.ViewHolder {

        TextView id_name, name, role, email;
        AppCompatImageView btnDelete, btnEdit;

        public ListRoleViewHolder(View itemView) {
            super(itemView);

            id_name = itemView.findViewById(R.id.id_name);

            btnDelete = itemView.findViewById(R.id.btn_delete);
            btnEdit = itemView.findViewById(R.id.btn_edit);

            btnDelete.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    deleteItem(getAdapterPosition());
                }
            });

            btnEdit.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    //  editRole(getAdapterPosition());
                    naviguateFragment(new EditRoleFragment(), dataList.get(getAdapterPosition()).getId());
                }
            });


        }
    }
    private void naviguateFragment(Fragment myFragment, Integer id) {
        AppCompatActivity activity = (AppCompatActivity) context;
        if (id != null) {
            Bundle bundle = new Bundle();
            bundle.putInt("id", id);
            myFragment.setArguments(bundle);
        }
        activity.getSupportFragmentManager().beginTransaction().replace(R.id.nav_host_fragment, myFragment).addToBackStack(null).commit();
    }

    private void deleteItem(int postition) {
        //Preparing views
        // get prompts.xml view
        LayoutInflater li = LayoutInflater.from(context);
        View promptsView = li.inflate(R.layout.delete_dialog_main, null);

        androidx.appcompat.app.AlertDialog.Builder alertDialogBuilder = new androidx.appcompat.app.AlertDialog.Builder(
                context);

        alertDialogBuilder.setView(promptsView);

        final Button ButtonSave = promptsView.findViewById(R.id.btn_save);
        final Button ButtonClose = promptsView.findViewById(R.id.btn_close);

        final androidx.appcompat.app.AlertDialog mAlertDialog = alertDialogBuilder.create();
        if (mAlertDialog.getWindow() != null)
            mAlertDialog.getWindow().getAttributes().windowAnimations = R.style.SlidingDialogAnimation;
        mAlertDialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));

        ButtonClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mAlertDialog.dismiss();
            }
        });

        ButtonSave.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

                roleDbController.deleteItem(dataList.get(postition).getId());
                dataList.remove(postition);
                mAlertDialog.dismiss();
                notifyDataSetChanged();

            }
        });


        mAlertDialog.show();
    }


}
