package com.rising.high.tech.bigultimatenavdraw.ui.sell;

import android.app.DatePickerDialog;
import android.app.TimePickerDialog;
import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.DatePicker;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.TimePicker;

import androidx.appcompat.widget.AppCompatImageView;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.gson.Gson;
import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.db.ContactDbController;
import com.rising.high.tech.bigultimatenavdraw.db.TransactionPayementDbController;
import com.rising.high.tech.bigultimatenavdraw.db.TransactionSellLineDbController;
import com.rising.high.tech.bigultimatenavdraw.db.BusinessLocationDbController;
import com.rising.high.tech.bigultimatenavdraw.db.TransactionDbController;
import com.rising.high.tech.bigultimatenavdraw.model.Business_location;
import com.rising.high.tech.bigultimatenavdraw.model.Contact;
import com.rising.high.tech.bigultimatenavdraw.model.Sell_lines;
import com.rising.high.tech.bigultimatenavdraw.model.Transaction;
import com.rising.high.tech.bigultimatenavdraw.ui.adapter.SpinStationAdapter;
import com.rising.high.tech.bigultimatenavdraw.ui.sell.adapter.PayementSellAdapter;
import com.rising.high.tech.bigultimatenavdraw.ui.sell.adapter.SubVenteAdapter;
import com.rising.high.tech.bigultimatenavdraw.ui.sell.adapter.VenteAdapter;
import com.rising.high.tech.bigultimatenavdraw.ui.sellreturn.AddSellReturnFragment;
import com.rising.high.tech.bigultimatenavdraw.util.Constant;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.HashMap;


import static com.rising.high.tech.bigultimatenavdraw.util.Constant.SELL;
import static com.rising.high.tech.bigultimatenavdraw.util.StringFormat.populateSetFullDate;

public class ListVenteFragment extends Fragment {
    private static final String TAG = "ListVenteFragment";
    private Context _context;

    LinearLayout filterContainer;
    LinearLayout filterHeader;
    EditText btnStartDate;
    EditText btnEndDate;
    TextView noItemFound;

    Spinner spinnerStation;
    Spinner spinnerPayementStatus;
    Button btnFilter;
    RecyclerView recycle_vente;
    TransactionDbController transactionDbController;
    VenteAdapter venteAdapter;
    private TransactionSellLineDbController transactionSellLineDbController;
    SpinStationAdapter spinStationAdapter;
    BusinessLocationDbController businessLocationDbController;
    ContactDbController contactDbController;
    TransactionPayementDbController transactionPayementDbController;
    final Calendar c = Calendar.getInstance();
    private HashMap<String, String> currentMap = new HashMap<>();

    public ListVenteFragment() {
        // Required empty public constructor
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        // Inflate the layout for this fragment
        View root = inflater.inflate(R.layout.fragment_vente_main, container, false);
        _context = getContext();

        spinnerStation = root.findViewById(R.id.spinnerStation);
        spinnerPayementStatus = root.findViewById(R.id.spinner_payment_status);
        btnFilter = root.findViewById(R.id.id_filter);
        recycle_vente = root.findViewById(R.id.recycle_vente);
        venteAdapter = new VenteAdapter();
        recycle_vente.setAdapter(venteAdapter);
        recycle_vente.setLayoutManager(new LinearLayoutManager(_context));

        initDB();
        venteAdapter.setData(transactionDbController.getSellTransaction(SELL));
        initSpinners();
        initListners();
        return root;
    }

    private void initDB() {
        transactionSellLineDbController = new TransactionSellLineDbController(_context);
        businessLocationDbController = new BusinessLocationDbController(_context);
        transactionDbController = new TransactionDbController(_context);
        contactDbController = new ContactDbController(_context);
        transactionPayementDbController = new TransactionPayementDbController(_context);
    }

    private void initSpinners() {
        spinStationAdapter = new SpinStationAdapter(_context, android.R.layout.simple_spinner_item, businessLocationDbController.getAllStationSpinner());
        spinnerStation.setAdapter(spinStationAdapter);
    }

    private void initListners() {
        filterHeader.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                filterContainer.setVisibility(filterContainer.getVisibility() == View.VISIBLE ? View.GONE : View.VISIBLE);
            }
        });

        btnStartDate.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                DatePickerDialog datePickerDialog = new DatePickerDialog(_context,
                        new DatePickerDialog.OnDateSetListener() {
                            @Override
                            public void onDateSet(DatePicker datePicker, int year, int month, int day) {

                                // select hours and minute
                                TimePickerDialog timepick = new TimePickerDialog(_context, new TimePickerDialog.OnTimeSetListener() {
                                    @Override
                                    public void onTimeSet(TimePicker view, int hourOfDay, int minute) {
                                        // String myFormat =populateSetDate(year,month,day);
                                        String myFormat = populateSetFullDate(year, month, day, hourOfDay, minute);
                                        btnStartDate.setText(myFormat);
                                        currentMap.put("date_debut", myFormat);
                                    }
                                }, c.get(Calendar.HOUR), c.get(Calendar.MINUTE), true);
                                timepick.setTitle("select time");
                                timepick.show();
                            }
                        }, c.get(Calendar.YEAR), c.get(Calendar.MONTH), c.get(Calendar.DAY_OF_MONTH));
                datePickerDialog.show();
            }
        });
        btnEndDate.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                DatePickerDialog datePickerDialog = new DatePickerDialog(_context,
                        new DatePickerDialog.OnDateSetListener() {
                            @Override
                            public void onDateSet(DatePicker datePicker, int year, int month, int day) {
                                TimePickerDialog timepick = new TimePickerDialog(_context, new TimePickerDialog.OnTimeSetListener() {
                                    @Override
                                    public void onTimeSet(TimePicker view, int hourOfDay, int minute) {
                                        //String myFormat =populateSetDate(year,month,day);
                                        String myFormat = populateSetFullDate(year, month, day, hourOfDay, minute);
                                        btnEndDate.setText(myFormat);
                                        currentMap.put("date_fin", myFormat);

                                        // Time results here
                                    }
                                }, c.get(Calendar.HOUR), c.get(Calendar.MINUTE), true);
                                timepick.setTitle("select time");
                                timepick.show();

                            }
                        }, c.get(Calendar.YEAR), c.get(Calendar.MONTH), c.get(Calendar.DAY_OF_MONTH));
                datePickerDialog.show();
            }
        });

        btnFilter.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Business_location businesslocation = (Business_location) spinnerStation.getSelectedItem();
                int status_pay = spinnerPayementStatus.getSelectedItemPosition();
                String sts = spinnerPayementStatus.getSelectedItem().toString().toLowerCase();
              //  if (status_pay == 1) sts = PAID;

                venteAdapter.setData(transactionDbController.filterTransaction(businesslocation.getId(), sts, currentMap.get("date_debut"), currentMap.get("date_fin"), SELL));
                venteAdapter.notifyDataSetChanged();
                setItemView();

            }
        });

        venteAdapter.setOnDataChangeListener(new VenteAdapter.OnDataChangeListener() {
            @Override
            public void onDataChanged(Transaction transaction) {
                showDetail(transaction);
            }
            @Override
            public void onSellDeleted() {
                venteAdapter.setData(transactionDbController.getSellTransaction(SELL));
                venteAdapter.notifyDataSetChanged();
            }

            @Override
            public void onSellReturn(Transaction transaction_) {
                AddSellReturnFragment addSellReturnFragment= new AddSellReturnFragment();
                Bundle bundle = new Bundle();
                bundle.putInt(Constant.SELL_RETURN_PARENT_ID, transaction_.getId());
                bundle.putString(Constant.SCREEN_FROM, SELL);
                System.out.println("Constant.SELL_RETURN_PARENT_ID "+transaction_.getId());
                addSellReturnFragment.setArguments(bundle);
                replaceFragment(addSellReturnFragment);
            }
        });

        setItemView();


    }

    private void showDetail(Transaction transaction) {
        //Preparing views
        // get prompts.xml view
        LayoutInflater li = LayoutInflater.from(_context);
        View promptsView = li.inflate(R.layout.dialog_liste_sell_detail_main, null);
        SubVenteAdapter subVenteAdapter = new SubVenteAdapter();
        PayementSellAdapter payementSellAdapter = new PayementSellAdapter();

        androidx.appcompat.app.AlertDialog.Builder alertDialogBuilder = new androidx.appcompat.app.AlertDialog.Builder(
                _context);

        // set prompts.xml to alertdialog builder

        ArrayList<Sell_lines> sell_lines = transactionSellLineDbController.getSellLineByTransaction(transaction.getId());
        final RecyclerView recyclerView = promptsView.findViewById(R.id.recycle_sub_vente);
        final RecyclerView recycle_sub_payment = promptsView.findViewById(R.id.recycle_sub_payment);
        final TextView status = promptsView.findViewById(R.id.status);
        final TextView payment_status = promptsView.findViewById(R.id.payment_status);
        final TextView date = promptsView.findViewById(R.id.date);
        final TextView customer_name = promptsView.findViewById(R.id.customer_name);
        final TextView phone = promptsView.findViewById(R.id.phone);
        final AppCompatImageView btn_close = promptsView.findViewById(R.id.btn_close);

        status.setText(transaction.getStatus());
        payment_status.setText(transaction.getPayment_status());
        date.setText(transaction.getTransaction_date());

        Contact contact = contactDbController.getCustomerById(transaction.getContact_id());

        String customerInfo=contact.getName() + ", " + (contact.getState()!=null ? contact.getState(): "" )+ " " + (contact.getCountry()!=null? contact.getCountry():"");
        customer_name.setText(customerInfo);
        phone.setText(contact.getMobile());


        recyclerView.setAdapter(subVenteAdapter);
        recyclerView.setLayoutManager(new LinearLayoutManager(_context));

        recycle_sub_payment.setAdapter(payementSellAdapter);
        recycle_sub_payment.setLayoutManager(new LinearLayoutManager(_context));

        subVenteAdapter.setData(sell_lines);
        subVenteAdapter.notifyDataSetChanged();

        ArrayList<Transaction> transactionArrayList= transactionPayementDbController.getAllTransactionById(transaction.getId());
        payementSellAdapter.setData(transactionArrayList);
        payementSellAdapter.notifyDataSetChanged();


        alertDialogBuilder.setView(promptsView);
        final androidx.appcompat.app.AlertDialog mAlertDialog = alertDialogBuilder.create();
        if (mAlertDialog.getWindow() != null)
            mAlertDialog.getWindow().getAttributes().windowAnimations = R.style.SlidingDialogAnimation;
        mAlertDialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));

        btn_close.setOnClickListener(v -> {
            mAlertDialog.dismiss();
        });
        mAlertDialog.show();
    }

    public void setItemView()
    {
        if(venteAdapter.getItemCount() > 0)
        {
            recycle_vente.setVisibility(View.VISIBLE);
            noItemFound.setVisibility(View.GONE);
        }
        else
        {
            recycle_vente.setVisibility(View.GONE);
            noItemFound.setVisibility(View.VISIBLE);
        }
    }

    public void replaceFragment(Fragment someFragment) {
        FragmentTransaction transaction = getFragmentManager().beginTransaction();
        transaction.replace(R.id.nav_host_fragment, someFragment);
        transaction.addToBackStack(null);
        transaction.commit();
    }
}