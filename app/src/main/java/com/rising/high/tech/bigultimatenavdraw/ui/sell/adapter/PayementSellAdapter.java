package com.rising.high.tech.bigultimatenavdraw.ui.sell.adapter;

import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.Spinner;
import android.widget.TextView;

import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.AppCompatImageView;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.RecyclerView;

import com.google.gson.Gson;
import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.db.ContactDbController;
import com.rising.high.tech.bigultimatenavdraw.db.TransactionPayementDbController;
import com.rising.high.tech.bigultimatenavdraw.model.Contact;
import com.rising.high.tech.bigultimatenavdraw.model.Transaction;

import java.util.ArrayList;

public class PayementSellAdapter extends RecyclerView.Adapter<PayementSellAdapter.TransactionViewHolder> {

    private static final String TAG = "PayementContactAdapter";

    private ArrayList<Transaction> dataList = new ArrayList<>();
    private TransactionPayementDbController transactionPayementDbController;
    private ContactDbController contactDbController;
    Context context;

    @Override
    public TransactionViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        context = parent.getContext();
        transactionPayementDbController = new TransactionPayementDbController(context);
        transactionPayementDbController.open();
        contactDbController = new ContactDbController(context);
        return new TransactionViewHolder(LayoutInflater.from(parent.getContext())
                .inflate(R.layout.payement_sell_item, parent, false));

    }

    @Override
    public void onBindViewHolder(TransactionViewHolder holder, int position) {

        holder.paidOn.setText(dataList.get(position).getPaid_on());
      //  holder.referenceNo.setText(dataList.get(position).getPayment_ref_no());
        holder.amount.setText(dataList.get(position).getAmount() + "");
        holder.payementMethod.setText(dataList.get(position).getMethod());

        Contact contact= contactDbController.getCustomerById(dataList.get(position).getPayment_for());
        holder.contactName.setText(contact.getName());

    }

    public void setData(ArrayList<Transaction> arrayList) {
        this.dataList = arrayList;
        notifyDataSetChanged();
    }


    @Override
    public int getItemCount() {
        return dataList.size();
    }

    public class TransactionViewHolder extends RecyclerView.ViewHolder {

        TextView paidOn, referenceNo, amount, payementMethod, contactName;
        Spinner spinnerAction;


        public TransactionViewHolder(View itemView) {
            super(itemView);

            paidOn = itemView.findViewById(R.id.paid_on);
            referenceNo = itemView.findViewById(R.id.reference_no);
            amount = itemView.findViewById(R.id.amount);
            payementMethod = itemView.findViewById(R.id.payement_method);
            contactName = itemView.findViewById(R.id.contact_id);

        }
    }

    private void deleteItem(int postition) {
        //Preparing views
        // get prompts.xml view
        LayoutInflater li = LayoutInflater.from(context);
        View promptsView = li.inflate(R.layout.delete_dialog_main, null);

        androidx.appcompat.app.AlertDialog.Builder alertDialogBuilder = new androidx.appcompat.app.AlertDialog.Builder(
                context);

        alertDialogBuilder.setView(promptsView);

        final Button ButtonSave = promptsView.findViewById(R.id.btn_save);
        final Button ButtonClose = promptsView.findViewById(R.id.btn_close);

        final androidx.appcompat.app.AlertDialog mAlertDialog = alertDialogBuilder.create();
        if (mAlertDialog.getWindow() != null)
            mAlertDialog.getWindow().getAttributes().windowAnimations = R.style.SlidingDialogAnimation;
        mAlertDialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));

        ButtonClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mAlertDialog.dismiss();
            }
        });

        ButtonSave.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

                transactionPayementDbController.deletePayment(dataList.get(postition).getId());
                dataList.remove(dataList.get(postition));
                mAlertDialog.dismiss();
                notifyDataSetChanged();
            }
        });


        mAlertDialog.show();
    }



    private void naviguateFragment(Fragment myFragment) {
        AppCompatActivity activity = (AppCompatActivity) context;
        activity.getSupportFragmentManager().beginTransaction().replace(R.id.nav_host_fragment, myFragment).addToBackStack(null).commit();
    }

}
