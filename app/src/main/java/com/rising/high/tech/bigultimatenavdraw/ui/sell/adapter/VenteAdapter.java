package com.rising.high.tech.bigultimatenavdraw.ui.sell.adapter;

import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.widget.AppCompatImageView;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;


import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.db.ContactDbController;
import com.rising.high.tech.bigultimatenavdraw.db.BusinessLocationDbController;
import com.rising.high.tech.bigultimatenavdraw.db.TransactionDbController;
import com.rising.high.tech.bigultimatenavdraw.db.TransactionPayementDbController;
import com.rising.high.tech.bigultimatenavdraw.db.TransactionSellLineDbController;
import com.rising.high.tech.bigultimatenavdraw.db.VariationLocationDetailDbController;
import com.rising.high.tech.bigultimatenavdraw.model.Contact;
import com.rising.high.tech.bigultimatenavdraw.model.Business_location;
import com.rising.high.tech.bigultimatenavdraw.model.Product;
import com.rising.high.tech.bigultimatenavdraw.model.Sell_lines;
import com.rising.high.tech.bigultimatenavdraw.model.Transaction;
import com.rising.high.tech.bigultimatenavdraw.ui.purchase.adapter.SubPaymentAdapter;
import com.rising.high.tech.bigultimatenavdraw.util.SessionManager;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;

import static com.rising.high.tech.bigultimatenavdraw.util.Constant.PAID;

public class VenteAdapter extends RecyclerView.Adapter<VenteAdapter.VenteViewHolder> {

    private final String TAG = this.getClass().getSimpleName();
    private ArrayList<Transaction> dataList = new ArrayList<>();
    private BusinessLocationDbController businessLocationDbController;
    private ContactDbController contactDbController;
    private TransactionDbController transactionDbController;
    private TransactionPayementDbController transactionPayementDbController;
    private TransactionSellLineDbController transactionSellLineDbController;
    private VariationLocationDetailDbController variationLocationDetailDbController;
    private SessionManager session;
    private HashMap<String, Object> user;
    Context context;
    private SubPaymentSellAdapter subPaymentAdapter;

    @Override
    public VenteViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        context = parent.getContext();

        session = new SessionManager(context);
        user = session.getUserDetails();

        businessLocationDbController = new BusinessLocationDbController(context);
        businessLocationDbController.open();

        contactDbController = new ContactDbController(context);
        contactDbController.open();

        transactionPayementDbController = new TransactionPayementDbController(context);
        transactionPayementDbController.open();

        transactionSellLineDbController = new TransactionSellLineDbController(context);
        transactionSellLineDbController.open();

        variationLocationDetailDbController=new VariationLocationDetailDbController(context);
        variationLocationDetailDbController.open();

        transactionDbController = new TransactionDbController(context);
        transactionDbController.open();
        subPaymentAdapter = new SubPaymentSellAdapter();

        return new VenteViewHolder(LayoutInflater.from(parent.getContext())
                .inflate(R.layout.vente_item_layout, parent, false));

    }

    @Override
    public void onBindViewHolder(VenteViewHolder holder, int position) {
        holder.dateTxt.setText(dataList.get(position).getTransaction_date());
        holder.factureNo.setText(dataList.get(position).getRef_no());
        String stationName = businessLocationDbController.getStationById(dataList.get(position).getLocation_id()).getName();
        holder.locationTxt.setText(stationName);

        holder.payementStatus.setText(dataList.get(position).getPayment_status());
        if (dataList.get(position).getPayment_status() !=null && dataList.get(position).getPayment_status().equals(PAID)) {
            holder.payementStatus.setBackground(context.getResources().getDrawable(R.drawable.rounded_green_bg));
            if (dataList.get(position).getIs_deleted()==1){
                holder.payementStatus.setText(context.getResources().getString(R.string.label_deleteddd));
                holder.payementStatus.setBackground(context.getResources().getDrawable(R.drawable.rounded_tranparent_bg));
            }
        }

        holder.montantTotal.setText(dataList.get(position).getFinal_total() + user.get(session.KEY_SYMBOL));
        holder.totalPaid.setText(dataList.get(position).getFinal_total() + user.get(session.KEY_SYMBOL));
        holder.customerName.setText(contactDbController.getCustomerById(dataList.get(position).getContact_id()).getName());
        holder.payementMethod.setText(transactionPayementDbController.getTransactionById(dataList.get(position).getId()).getMethod());

        if (dataList.get(position).getIs_deleted()==1){
        holder.linearLaout.setBackgroundColor(context.getResources().getColor(R.color.Red_white));}

        //holder.payementMethod.setText();

    }

    public void setData(ArrayList<Transaction> arrayList) {
        Collections.reverse(arrayList);
        this.dataList = arrayList;
        notifyDataSetChanged();
    }

    @Override
    public int getItemCount() {
        return dataList.size();
    }

    public class VenteViewHolder extends RecyclerView.ViewHolder {
        TextView dateTxt, factureNo, locationTxt, payementStatus, montantTotal, customerName, payementMethod, totalPaid;
        RecyclerView recycle_sub_vente;
        LinearLayout sub_item, container, linearLaout;

        Button btnDetail;
        Spinner spinnerAction;

        public VenteViewHolder(View itemView) {
            super(itemView);

            dateTxt = itemView.findViewById(R.id.id_date);
            factureNo = itemView.findViewById(R.id.facture_no);
            locationTxt = itemView.findViewById(R.id.location_txt);
            payementStatus = itemView.findViewById(R.id.payement_status);
            montantTotal = itemView.findViewById(R.id.montant_total);
            container = itemView.findViewById(R.id.container);
            btnDetail = itemView.findViewById(R.id.btn_detail);
            customerName = itemView.findViewById(R.id.customer_name);
            payementMethod = itemView.findViewById(R.id.payement_method);
            totalPaid = itemView.findViewById(R.id.montant_paid);

            spinnerAction = itemView.findViewById(R.id.spinner_action);
            linearLaout = itemView.findViewById(R.id.linear_layout);

            spinnerAction.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
                @Override
                public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                    spinnerAction.setSelection(0, true);

                    switch (position) {
                        case 1: {
                            // dataList.get(getAdapterPosition()).setExpanded(!dataList.get(getAdapterPosition()).isExpanded());
                            if (mOnDataChangeListener != null) {
                                mOnDataChangeListener.onDataChanged(dataList.get(getAdapterPosition()));
                            }
                            notifyItemChanged(getAdapterPosition());
                            break;
                        }
                        case 2: {
                            if (dataList.get(getAdapterPosition()).getIs_deleted()==1){
                                Toast.makeText(context, context.getResources().getString(R.string.label_deleted_transaction), Toast.LENGTH_SHORT).show();
                            }else {
                                deleteItem(getAdapterPosition());
                            }
                            break;
                        }
                        case 3: {
                            viewPayement(getAdapterPosition());
                            break;
                        }
                        case 4: {
                            if (dataList.get(getAdapterPosition()).getIs_deleted()==1){
                                Toast.makeText(context, context.getResources().getString(R.string.label_deleted_transaction), Toast.LENGTH_SHORT).show();
                            }else {
                                mOnDataChangeListener.onSellReturn(dataList.get(getAdapterPosition()));
                            }
                            break;
                        }
                    }
                }

                @Override
                public void onNothingSelected(AdapterView<?> parent) {

                }
            });
        }
    }

    private void viewPayement(int position) {
        //Preparing views
        // get prompts.xml view

        LayoutInflater li = LayoutInflater.from(context);
        View promptsView = li.inflate(R.layout.view_payement_selll_dialog_main, null);

        androidx.appcompat.app.AlertDialog.Builder alertDialogBuilder = new androidx.appcompat.app.AlertDialog.Builder(
                context);

        alertDialogBuilder.setView(promptsView);

        final Button ButtonSave = promptsView.findViewById(R.id.btn_save);
        final AppCompatImageView ButtonClose = promptsView.findViewById(R.id.btn_close);

        final TextView supplier_name = promptsView.findViewById(R.id.supplier_name);
        final TextView supplier_phone = promptsView.findViewById(R.id.supplier_phone);
        final TextView business_location = promptsView.findViewById(R.id.business_location);
        final RecyclerView recyclerView = promptsView.findViewById(R.id.recycle_sub_vente);
        final TextView label_reference_num = promptsView.findViewById(R.id.label_reference_num);
        final TextView purchase_date = promptsView.findViewById(R.id.purchase_date);
        final TextView purchase_status = promptsView.findViewById(R.id.purchase_status);
        final TextView payement_status = promptsView.findViewById(R.id.payement_status);

        recyclerView.setAdapter(subPaymentAdapter);
        recyclerView.setLayoutManager(new LinearLayoutManager(context));

        ArrayList<Transaction> purchaseLines = transactionPayementDbController.getAllTransactionById(dataList.get(position).getId());
        subPaymentAdapter.setData(purchaseLines);
        subPaymentAdapter.setOnDataChangeListener(new SubPaymentSellAdapter.OnDataChangeListener() {
            @Override
            public void onDataChanged() {
                dataList.get(position).setPayment_status(transactionDbController.getTransactionById(dataList.get(position).getId()).getPayment_status());
                notifyItemChanged(position);
            }
        });

        Contact contact = contactDbController.getCustomerById(dataList.get(position).getContact_id());

        supplier_name.setText(contact.getName());
        supplier_phone.setText(contact.getMobile());
        Business_location businesslocation = businessLocationDbController.getStationById(dataList.get(position).getLocation_id());

        business_location.setText(businesslocation.getName() + " " + (businesslocation.getCity()!=null?businesslocation.getCity():"" ) + " " + (businesslocation.getCountry()!=null? businesslocation.getCountry():""));
        label_reference_num.setText(dataList.get(position).getRef_no());
        purchase_date.setText(dataList.get(position).getTransaction_date());
        purchase_status.setText(dataList.get(position).getStatus());
        payement_status.setText(dataList.get(position).getPayment_status());

        //  final ImageView image_product = promptsView.findViewById(R.id.image_product);

        final androidx.appcompat.app.AlertDialog mAlertDialog = alertDialogBuilder.create();
        if (mAlertDialog.getWindow() != null)
            mAlertDialog.getWindow().getAttributes().windowAnimations = R.style.SlidingDialogAnimation;
        mAlertDialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));


        ButtonClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mAlertDialog.dismiss();
            }
        });


        mAlertDialog.show();
    }

    private void deleteItem(int position) {
        //Preparing views
        // get prompts.xml view
        LayoutInflater li = LayoutInflater.from(context);
        View promptsView = li.inflate(R.layout.delete_dialog_main, null);

        androidx.appcompat.app.AlertDialog.Builder alertDialogBuilder = new androidx.appcompat.app.AlertDialog.Builder(
                context);

        alertDialogBuilder.setView(promptsView);

        final Button ButtonSave = promptsView.findViewById(R.id.btn_save);
        final Button ButtonClose = promptsView.findViewById(R.id.btn_close);

        final androidx.appcompat.app.AlertDialog mAlertDialog = alertDialogBuilder.create();
        if (mAlertDialog.getWindow() != null)
            mAlertDialog.getWindow().getAttributes().windowAnimations = R.style.SlidingDialogAnimation;
        mAlertDialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));


        ButtonClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mAlertDialog.dismiss();
            }
        });

        ButtonSave.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {


                ArrayList<Sell_lines> sell_lines =transactionSellLineDbController.getSellLineByTransaction(dataList.get(position).getId());
                for (Sell_lines sell_lines1 : sell_lines) {

               //     int currentQty=variationLocationDetailDbController.getVariationLocationDetailsByStationIdProductId(dataList.get(position).getLocation_id(), sell_lines1.getProduct_id()).getQty_available();
                  //  int newQtyAvailable=currentQty + sell_lines1.getQuantity();
                    variationLocationDetailDbController.updatePurchaseQty(sell_lines1.getProduct_id(), dataList.get(position).getLocation_id(), sell_lines1.getQuantity());
                  //  transactionSellLineDbController.delete(sell_lines1.getId());
                }
              //  transactionDbController.deleteTransaction(dataList.get(position).getId());
                transactionDbController.markDeleteTransaction(dataList.get(position));
             //   transactionPayementDbController.deletePayment(dataList.get(position).getId());


          //      dataList.remove(position);

                mOnDataChangeListener.onSellDeleted();

                mAlertDialog.dismiss();

                notifyDataSetChanged();
            }
        });

        mAlertDialog.show();
    }


    public interface OnDataChangeListener {
        void onDataChanged(Transaction transaction);
        void onSellReturn(Transaction transaction);
        void onSellDeleted();
    }

    OnDataChangeListener mOnDataChangeListener;

    public void setOnDataChangeListener(OnDataChangeListener onDataChangeListener) {
        mOnDataChangeListener = onDataChangeListener;
    }

}
