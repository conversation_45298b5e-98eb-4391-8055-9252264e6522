package com.rising.high.tech.bigultimatenavdraw.ui.sellreturn;

import android.app.DatePickerDialog;
import android.content.Context;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.Toast;

import androidx.appcompat.widget.AppCompatButton;
import androidx.appcompat.widget.AppCompatEditText;
import androidx.appcompat.widget.AppCompatSpinner;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.db.ContactDbController;
import com.rising.high.tech.bigultimatenavdraw.db.PurchaseLineDbController;
import com.rising.high.tech.bigultimatenavdraw.db.BusinessLocationDbController;
import com.rising.high.tech.bigultimatenavdraw.db.TransactionDbController;
import com.rising.high.tech.bigultimatenavdraw.db.TransactionSellLineDbController;
import com.rising.high.tech.bigultimatenavdraw.db.VariationLocationDetailDbController;
import com.rising.high.tech.bigultimatenavdraw.model.Purchase_line;
import com.rising.high.tech.bigultimatenavdraw.model.Sell_lines;
import com.rising.high.tech.bigultimatenavdraw.model.Transaction;
import com.rising.high.tech.bigultimatenavdraw.model.Variation_location_details;
import com.rising.high.tech.bigultimatenavdraw.ui.sellreturn.adapter.AddSellReturnListAdapter;
import com.rising.high.tech.bigultimatenavdraw.util.Constant;
import com.rising.high.tech.bigultimatenavdraw.util.FileUtil;
import com.rising.high.tech.bigultimatenavdraw.util.ProductUtil;
import com.rising.high.tech.bigultimatenavdraw.util.SessionManager;
import com.rising.high.tech.bigultimatenavdraw.util.StringFormat;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Locale;
import java.util.Objects;


import static com.rising.high.tech.bigultimatenavdraw.util.Constant.DUE;
import static com.rising.high.tech.bigultimatenavdraw.util.StringFormat.populateSetDate;

public class AddSellReturnFragment extends Fragment implements View.OnClickListener, AdapterView.OnItemSelectedListener {
    private Context _context;

    AppCompatTextView invoiceNoTxt;

    AppCompatTextView dateTxt;
    AppCompatTextView customerNameTxt;
    AppCompatTextView location;
    AppCompatTextView totalReturnDiscount;
    AppCompatTextView totalReturnTax;
    AppCompatTextView returnTotal;
    AppCompatEditText invoiceNoEdt;
    AppCompatEditText dateEdt;
    AppCompatEditText discountAmountTxt;
    AppCompatSpinner spinnerDiscountType;
    RecyclerView productRecycler;
    AppCompatButton saveBtn;

    TransactionDbController transactionDbController;
    AddSellReturnListAdapter addSellReturnAdapter;
    private TransactionSellLineDbController transactionSellLineDbController;
    BusinessLocationDbController businessLocationDbController;
    ContactDbController contactDbController;
    VariationLocationDetailDbController variationLocationDetailDbController;
    PurchaseLineDbController purchaseLineDbController;
    final Calendar c = Calendar.getInstance();
    int parentSellId;
    HashMap<String, Object> user;
    SessionManager session;
    double returnSubTotalAmount = 0.0, totalReturnDiscountAmount = 0.0, returnTotalAmount = 0.0;
    ArrayList<Sell_lines> sell_lines;
    Transaction transactionData;
    String isEdit = "false", screenFrom = Constant.SELL_RETURN;

    public AddSellReturnFragment() {
        // Required empty public constructor
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        View root = inflater.inflate(R.layout.fragment_add_sell_return, container, false);
        _context = getContext();
        initDB();
        getBundleData();
        initView();

        return root;
    }

    private void getBundleData() {
        if (getArguments() != null) {
            isEdit = getArguments().getString(Constant.IS_EDIT);
            screenFrom = getArguments().getString(Constant.SCREEN_FROM);
            parentSellId = getArguments().getInt(Constant.SELL_RETURN_PARENT_ID);
            setData(parentSellId);

        }
    }

    private void initDB() {
        session = new SessionManager(_context);
        user = session.getUserDetails();
        transactionSellLineDbController = new TransactionSellLineDbController(_context);
        transactionSellLineDbController.open();
        businessLocationDbController = new BusinessLocationDbController(_context);
        businessLocationDbController.open();
        transactionDbController = new TransactionDbController(_context);
        transactionDbController.open();
        contactDbController = new ContactDbController(_context);
        contactDbController.open();
        variationLocationDetailDbController = new VariationLocationDetailDbController(_context);
        variationLocationDetailDbController.open();
        purchaseLineDbController = new PurchaseLineDbController(_context);
        purchaseLineDbController.open();
    }

    private void initView() {

        spinnerDiscountType.setOnItemSelectedListener(this);
        saveBtn.setOnClickListener(this);
        dateEdt.setOnClickListener(this);
        discountAmountTxt.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {
                if (charSequence.toString().length() != 0 && !(Objects.requireNonNull(discountAmountTxt.getText()).toString().isEmpty())) {
                    if (spinnerDiscountType.getSelectedItemPosition() == 0) {
                        discountAmountTxt.setError(getString(R.string.select_discount_type));
                        discountAmountTxt.setText("");
                    } else if (spinnerDiscountType.getSelectedItem().equals("fixed") && Double.parseDouble(Objects.requireNonNull(discountAmountTxt.getText()).toString()) > returnSubTotalAmount) {
                        discountAmountTxt.setError("Discount amount should be less than " + returnSubTotalAmount);
                        discountAmountTxt.setText("");
                    } else if (spinnerDiscountType.getSelectedItem().equals("percentage") && Double.parseDouble(Objects.requireNonNull(discountAmountTxt.getText()).toString()) > 99) {
                        discountAmountTxt.setError("Discount percentage should be less than 99 %");
                        discountAmountTxt.setText("");
                    } else if (spinnerDiscountType.getSelectedItem().equals("percentage")) {
                        totalReturnDiscountAmount = (Double.parseDouble(Objects.requireNonNull(discountAmountTxt.getText()).toString()) / 100.0) * returnSubTotalAmount;
                        totalReturnDiscount.setText(String.format(Locale.ENGLISH, "%.2f", (totalReturnDiscountAmount)));
                        returnTotalAmount = returnSubTotalAmount - totalReturnDiscountAmount;
                        returnTotal.setText(String.format(Locale.ENGLISH, "%.2f", (returnTotalAmount)));
                    } else if (spinnerDiscountType.getSelectedItem().equals("fixed")) {
                        totalReturnDiscountAmount = Double.parseDouble(Objects.requireNonNull(discountAmountTxt.getText()).toString());
                        totalReturnDiscount.setText(String.format(Locale.ENGLISH, "%.2f", totalReturnDiscountAmount));
                        returnTotalAmount = returnSubTotalAmount - totalReturnDiscountAmount;
                        returnTotal.setText(String.format(Locale.ENGLISH, "%.2f", (returnTotalAmount)));
                    }
                } else {
                    totalReturnDiscountAmount = 0.0;
                    totalReturnDiscount.setText("0.0");
                }
            }

            @Override
            public void afterTextChanged(Editable editable) {

            }
        });
    }

    @Override
    public void onClick(View view) {
        int id = view.getId();
        if (id == R.id.dateEdt) {
            pickDate();
        } else if (id == R.id.saveBtn) {
//            if (spinnerDiscountType.getSelectedItemPosition() == 0) {
//                Toast.makeText(_context, R.string.select_discount_type, Toast.LENGTH_SHORT).show();
//            } else {
                if (isEdit.equals("true")) {
                    editSellReturn();
                } else {
                    addSellReturn();
                }
         //   }


        }

    }

    private void setUpRecyclerView(int parentSellId) {
        sell_lines = transactionSellLineDbController.getSellLineByTransaction(parentSellId);
        addSellReturnAdapter = new AddSellReturnListAdapter(1);
        addSellReturnAdapter.setData(sell_lines);
        productRecycler.setAdapter(addSellReturnAdapter);
        productRecycler.setLayoutManager(new LinearLayoutManager(_context));
        addSellReturnAdapter.setOnDataChangeListener(sell_lines1 -> {
            sell_lines = sell_lines1;

            returnSubTotalAmount = Double.parseDouble(ProductUtil.getTotalAmountSellReturn(sell_lines1, _context));
        });

        double totalCPrice = 0.0;
        String total = "0.0";
        if (isEdit.equals("true")) {
            for (Sell_lines xProduct : sell_lines) {
                totalCPrice += Float.parseFloat(xProduct.getUnit_price()) * xProduct.getQty_returned();
            }
            total = String.format(Locale.ENGLISH, "%.2f", totalCPrice) + user.get(SessionManager.KEY_SYMBOL);
        }
//
        returnTotal.setText(total);
        returnSubTotalAmount = totalCPrice;
    }


    public void pickDate() {
        DatePickerDialog datePickerDialog = new DatePickerDialog(_context,
                (datePicker, year, month, day) -> {
                    String myFormat = populateSetDate(year, month, day);
                    dateEdt.setText(myFormat);
                }, c.get(Calendar.YEAR), c.get(Calendar.MONTH), c.get(Calendar.DAY_OF_MONTH));
        datePickerDialog.getDatePicker().setMaxDate(new Date().getTime());
        datePickerDialog.show();
    }

    @Override
    public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {

        if (parent.getId() == R.id.spinnerDiscountType) {
            if (spinnerDiscountType.getSelectedItem().equals("percentage") && !(Objects.requireNonNull(discountAmountTxt.getText()).toString().isEmpty())) {

                totalReturnDiscountAmount = (Double.parseDouble(Objects.requireNonNull(discountAmountTxt.getText()).toString()) / 100.0) * returnSubTotalAmount;
                totalReturnDiscount.setText(String.format(Locale.ENGLISH, "%.2f", totalReturnDiscountAmount));
                returnTotalAmount = returnSubTotalAmount - totalReturnDiscountAmount;
                returnTotal.setText(String.format(Locale.ENGLISH, "%.2f", returnTotalAmount));
            } else if (spinnerDiscountType.getSelectedItem().equals("fixed") && !(Objects.requireNonNull(discountAmountTxt.getText()).toString().isEmpty())) {
                totalReturnDiscountAmount = Double.parseDouble(Objects.requireNonNull(discountAmountTxt.getText()).toString());
                totalReturnDiscount.setText(String.format(Locale.ENGLISH, "%.2f", totalReturnDiscountAmount));
                returnTotalAmount = returnSubTotalAmount - totalReturnDiscountAmount;
                returnTotal.setText(String.format(Locale.ENGLISH, "%.2f", returnTotalAmount));
            }
        }
    }

    @Override
    public void onNothingSelected(AdapterView<?> parent) {

    }

    private void addSellReturn() {

        Transaction transaction = new Transaction();
        transaction.setBusiness_id(transactionData.getBusiness_id());
        transaction.setLocation_id(transactionData.getLocation_id());
        transaction.setType(Constant.SELL_RETURN);
        transaction.setStatus(transactionData.getStatus());
        transaction.setIs_sync("no");
        transaction.setPayment_status(DUE);
        transaction.setContact_id(transactionData.getContact_id());
        transaction.setInvoice_no(Objects.requireNonNull(invoiceNoEdt.getText()).toString());
        transaction.setTransaction_date(StringFormat.actualTime());
        transaction.setTotal_before_tax(transactionData.getTotal_before_tax());
        transaction.setDiscount_type(spinnerDiscountType.getSelectedItem().toString());
        transaction.setDiscount_amount(Objects.requireNonNull(discountAmountTxt.getText()).toString());
        transaction.setFinal_total((String.format(Locale.ENGLISH, "%.2f", returnTotalAmount)));
        transaction.setReturn_parent_id(parentSellId);

        int idInsert = transactionDbController.insertLocal(transaction);
        if (idInsert > 0) {
            for (Sell_lines sell_lines_ : sell_lines) {
                transactionSellLineDbController.updateReturnQty(sell_lines_.getQty_returned(), sell_lines_.getId());
                Variation_location_details variation_location_details = variationLocationDetailDbController.getVariationLocationDetailsByStationIdProductId(transaction.getLocation_id(), sell_lines_.getProduct_id());
                int qtyAvailable = variation_location_details.getQty_available() + sell_lines_.getQty_returned();
                variationLocationDetailDbController.updateAvailableQty(qtyAvailable, variation_location_details.getId());
            }
            FileUtil.showDialog(_context, "Successful", "Sell Return Added Successfully");
            replaceFragment(new ListSellReturnFragment());
        } else {
            Toast.makeText(_context, "Failed to Update Data ", Toast.LENGTH_LONG).show();
        }
    }

    private void editSellReturn() {

        Transaction transaction = new Transaction();

        transaction.setDiscount_type(spinnerDiscountType.getSelectedItem().toString());
        transaction.setDiscount_amount(Objects.requireNonNull(discountAmountTxt.getText()).toString());
        transaction.setFinal_total(String.valueOf(returnTotalAmount));
        transaction.setId(transactionData.getId());
        transaction.setTransaction_date(Objects.requireNonNull(dateEdt.getText()).toString());

        int idInsert = transactionDbController.updateSellReturnData(transaction);
        if (idInsert > 0) {
            for (Sell_lines sell_lines_ : sell_lines) {
                transactionSellLineDbController.updateReturnQty(sell_lines_.getQty_returned(), sell_lines_.getId());
                Variation_location_details variation_location_details = variationLocationDetailDbController.getVariationLocationDetailsByStationIdProductId(transactionData.getLocation_id(), sell_lines_.getProduct_id());
                Purchase_line purchaseLine = purchaseLineDbController.getPurchaseLineByVariationProduct(sell_lines_.getVariation_id(), sell_lines_.getProduct_id());
                int qtyAvailable = purchaseLine.getQuantity() - (sell_lines_.getQuantity() - sell_lines_.getQty_returned());
                variationLocationDetailDbController.updateAvailableQty(qtyAvailable, variation_location_details.getId());
            }
            FileUtil.showDialog(_context, "Successful", "Sell Return Updated Successfully");
            replaceFragment(new ListSellReturnFragment());
        } else {
            Toast.makeText(_context, "Failed to Update Data ", Toast.LENGTH_LONG).show();
        }
    }

    public void replaceFragment(Fragment someFragment) {
        FragmentTransaction transaction = getFragmentManager().beginTransaction();
        transaction.replace(R.id.nav_host_fragment, someFragment);
        transaction.addToBackStack(null);
        transaction.commit();
    }

    public void setData(int parentSellId) {
        int transactionCount = transactionDbController.getParentSaleCount(parentSellId);
        transactionData = transactionDbController.getTransactionById(parentSellId);
        if (transactionCount <= 0 && screenFrom.equals(Constant.SELL)) {
            isEdit = "false";
            System.out.println("parentSellId  " + parentSellId);
            setUpRecyclerView(parentSellId);
        } else if (transactionCount > 0 && screenFrom.equals(Constant.SELL)) {
            isEdit = "true";
            setUpRecyclerView(parentSellId);
        } else {
            isEdit = "true";
            setUpRecyclerView(transactionData.getReturn_parent_id());
        }

        invoiceNoTxt.setText(transactionData.getInvoice_no());
        dateTxt.setText(transactionData.getTransaction_date());
        dateEdt.setText(transactionData.getTransaction_date());
        customerNameTxt.setText(contactDbController.getCustomerById(transactionData.getContact_id()).getName());
        location.setText(businessLocationDbController.getStationById(transactionData.getLocation_id()).getName());
        if (transactionData.getDiscount_amount() == null || transactionData.getDiscount_amount().isEmpty()) {
            discountAmountTxt.setText(R.string._00_0);
        } else {
            discountAmountTxt.setText(transactionData.getDiscount_amount());
        }
        if (transactionData.getTax_amount() == null || transactionData.getTax_amount().isEmpty()) {
            totalReturnTax.setText(R.string._00_0);
        } else {
            totalReturnTax.setText(transactionData.getTax_amount());
        }

        ArrayAdapter<CharSequence> adapter = ArrayAdapter.createFromResource(_context, R.array.discount_type, android.R.layout.simple_spinner_item);
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        spinnerDiscountType.setAdapter(adapter);
        if (transactionData.getDiscount_type() != null) {
            spinnerDiscountType.setSelection(adapter.getPosition(transactionData.getDiscount_type()));
            if (spinnerDiscountType.getSelectedItem() != null) {

                if (spinnerDiscountType.getSelectedItem().equals("percentage") && !(transactionData.getDiscount_amount().isEmpty())) {

                    totalReturnDiscountAmount = (Double.parseDouble(transactionData.getDiscount_amount()) / 100.0) * returnSubTotalAmount;
                    totalReturnDiscount.setText(String.format(Locale.ENGLISH, "%.2f", (totalReturnDiscountAmount)));
                    returnTotalAmount = returnSubTotalAmount - totalReturnDiscountAmount;
                    returnTotal.setText(String.format(Locale.ENGLISH, "%.2f", returnTotalAmount));
                } else if (spinnerDiscountType.getSelectedItem().equals("fixed") && !(transactionData.getDiscount_amount().isEmpty())) {

                    totalReturnDiscountAmount = Double.parseDouble(transactionData.getDiscount_amount());
                    totalReturnDiscount.setText(String.format(Locale.ENGLISH, "%.2f", (totalReturnDiscountAmount)));
                    returnTotalAmount = returnSubTotalAmount - totalReturnDiscountAmount;
                    returnTotal.setText(String.format(Locale.ENGLISH, "%.2f", (returnTotalAmount)));
                }
            } else {
                totalReturnDiscount.setText(getString(R.string._00_0));
            }
        } else {
            totalReturnDiscount.setText(getString(R.string._00_0));
        }
        if (isEdit.equals("true")) {
            invoiceNoEdt.setText(transactionData.getInvoice_no());
            Transaction transactionParentData = transactionDbController.getTransactionById(transactionData.getReturn_parent_id());
            invoiceNoTxt.setText(transactionParentData.getInvoice_no());
        } else {
            String inv = "";
            if (transactionDbController.getLastReturnSalesInvoiceNo().isEmpty()) {
                inv = "CNC" + Calendar.getInstance().get(Calendar.YEAR) + "/0001";
            } else {
                String lastInv[] = transactionDbController.getLastReturnSalesInvoiceNo().split("/");
                String lastInvValue = lastInv[1];
                int lastInvValueInc = Integer.parseInt(lastInvValue) + 1;
                inv = "CNC" + Calendar.getInstance().get(Calendar.YEAR) + "/" + String.format(Locale.ENGLISH, "%04d", lastInvValueInc);
            }
            invoiceNoEdt.setText(inv);
        }
    }

}