package com.rising.high.tech.bigultimatenavdraw.ui.sellreturn;

import android.annotation.SuppressLint;
import android.app.DatePickerDialog;
import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;
import androidx.appcompat.widget.AppCompatButton;
import androidx.appcompat.widget.AppCompatEditText;
import androidx.appcompat.widget.AppCompatImageView;
import androidx.appcompat.widget.AppCompatSpinner;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.core.content.res.ResourcesCompat;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.gson.Gson;
import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.db.ContactDbController;
import com.rising.high.tech.bigultimatenavdraw.db.BusinessLocationDbController;
import com.rising.high.tech.bigultimatenavdraw.db.TransactionDbController;
import com.rising.high.tech.bigultimatenavdraw.db.TransactionPayementDbController;
import com.rising.high.tech.bigultimatenavdraw.db.TransactionSellLineDbController;
import com.rising.high.tech.bigultimatenavdraw.db.VariationLocationDetailDbController;
import com.rising.high.tech.bigultimatenavdraw.model.Contact;
import com.rising.high.tech.bigultimatenavdraw.model.Sell_lines;
import com.rising.high.tech.bigultimatenavdraw.model.Business_location;
import com.rising.high.tech.bigultimatenavdraw.model.Transaction;
import com.rising.high.tech.bigultimatenavdraw.model.Variation_location_details;
import com.rising.high.tech.bigultimatenavdraw.ui.sellreturn.adapter.AddSellReturnListAdapter;
import com.rising.high.tech.bigultimatenavdraw.ui.sellreturn.adapter.SellReturnAdapter;
import com.rising.high.tech.bigultimatenavdraw.ui.sellreturn.adapter.SubPaymentReturnSalesAdapter;
import com.rising.high.tech.bigultimatenavdraw.util.Constant;
import com.rising.high.tech.bigultimatenavdraw.util.FileUtil;
import com.rising.high.tech.bigultimatenavdraw.util.ProductUtil;
import com.rising.high.tech.bigultimatenavdraw.util.SessionManager;
import com.rising.high.tech.bigultimatenavdraw.util.StringFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Locale;
import java.util.Objects;


import static com.rising.high.tech.bigultimatenavdraw.util.Constant.PAID;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.PARTIAL;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.SELL_RETURN;
import static com.rising.high.tech.bigultimatenavdraw.util.StringFormat.populateSetDate;

public class ListSellReturnFragment extends Fragment  implements View.OnClickListener, AdapterView.OnItemSelectedListener {
    private static final String TAG = "ListSellReturnFragment";
    private Context _context;

    TextView noItemFound;
    Spinner spinnerStation;
    Spinner userSpinner;
    EditText startDate;
    EditText endDate;
    AppCompatButton btnSearch;
    RecyclerView recycleSellReturn;
    LinearLayout filterHeader;
    LinearLayout filterContainer;
    ImageView filterArrow;
    Spinner spinnerCustomer;

    TransactionDbController transactionDbController;
    SellReturnAdapter sellReturnAdapter;
    private TransactionSellLineDbController transactionSellLineDbController;
    BusinessLocationDbController businessLocationDbController;
    ContactDbController contactDbController;
    final Calendar c = Calendar.getInstance();
    private ArrayList<Contact> arrayListCustomers;
    private ArrayList<Business_location> arrayListBusinesslocation;
    String customerName;
    int customerId,stationId;
    VariationLocationDetailDbController variationLocationDetailDbController;
    private TransactionPayementDbController transactionPayementDbController;
    private HashMap<String, Object> user;
    private ArrayList<Transaction> transactionList = new ArrayList<>();
    ArrayList<Transaction> subPaymentTransaction;
    SessionManager session;
    public ListSellReturnFragment() {
        // Required empty public constructor
    }
    private SubPaymentReturnSalesAdapter subPaymentAdapter;
    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        View root = inflater.inflate(R.layout.fragment_sell_returnlist, container, false);
        _context = getContext();

        initDB();
        initSpinners();
        initListeners();
        return root;
    }

    private void initDB() {
        session = new SessionManager(_context);
        user = session.getUserDetails();
        transactionSellLineDbController = new TransactionSellLineDbController(_context);
        transactionSellLineDbController.open();
        businessLocationDbController = new BusinessLocationDbController(_context);
        businessLocationDbController.open();
        transactionDbController = new TransactionDbController(_context);
        transactionDbController.open();
        contactDbController = new ContactDbController(_context);
        contactDbController.open();
        variationLocationDetailDbController = new VariationLocationDetailDbController(_context);
        variationLocationDetailDbController.open();
        transactionPayementDbController = new TransactionPayementDbController(_context);
        transactionPayementDbController.open();
    }

    private void initSpinners() {
        arrayListCustomers = contactDbController.getAllCustomers();
        arrayListBusinesslocation = businessLocationDbController.getAllStationSpinner();


        ArrayList<String> stations = new ArrayList<>();
        for (int i = 0; i < arrayListBusinesslocation.size(); i++) {
            stations.add(arrayListBusinesslocation.get(i).getName());
        }
        ArrayAdapter<String> stationAdapter = new ArrayAdapter<>(_context, android.R.layout.simple_spinner_item, stations);
        stationAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        spinnerStation.setAdapter(stationAdapter);

        ArrayList<String> customers = new ArrayList<>();
        for (int i = 0; i < arrayListCustomers.size(); i++) {
            customers.add(arrayListCustomers.get(i).getName());
        }
        ArrayAdapter<String> dataAdapter = new ArrayAdapter<>(_context, android.R.layout.simple_spinner_item, customers);
        dataAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        spinnerCustomer.setAdapter(dataAdapter);


    }

    @Override
    public void onClick(View view)
    {
        int id = view.getId();
        if (id == R.id.new_start_date) {
            pickDate(true);
        }
        else if (id == R.id.new_end_date) {
            pickDate(false);
        }
        else if (id == R.id.btnSearch) {
            searchList();
        }
        else if (id == R.id.filter_header) {
            filterContainer.setVisibility(filterContainer.getVisibility() == View.VISIBLE ? View.GONE : View.VISIBLE);
            if(filterContainer.getVisibility() == View.VISIBLE)
            {
                filterArrow.setImageDrawable(ResourcesCompat.getDrawable(getResources(),R.drawable.ic_baseline_expand_less_24,null));
                filterContainer.setVisibility(View.GONE);
            }
            else
            {
                filterArrow.setImageDrawable(ResourcesCompat.getDrawable(getResources(),R.drawable.ic_baseline_expand_more_24,null));
                filterContainer.setVisibility(View.VISIBLE);
            }
        }
    }
    private void initListeners() {
        startDate.setOnClickListener(this);
        endDate.setOnClickListener(this);
        btnSearch.setOnClickListener(this);
        filterHeader.setOnClickListener(this);
        spinnerCustomer.setOnItemSelectedListener(this);

        setUpRecyclerView();
    }
    private void setUpRecyclerView()
    {
        sellReturnAdapter = new SellReturnAdapter();
        transactionList=transactionDbController.getTransactionType(SELL_RETURN);
        sellReturnAdapter.setData(transactionList);
        sellReturnAdapter.setonClickAction(new SellReturnAdapter.onClickAction() {

            @Override
            public void onClickView(Transaction transaction) {
                viewSellReturn(transaction);
            }

            @Override
            public void onClickEdit(Transaction transaction) {
                AddSellReturnFragment addSellReturnFragment= new AddSellReturnFragment();
                Bundle bundle = new Bundle();
                bundle.putInt(Constant.SELL_RETURN_PARENT_ID, transaction.getId());
                bundle.putString(Constant.SCREEN_FROM, SELL_RETURN);
                bundle.putString(Constant.IS_EDIT, "true");
                addSellReturnFragment.setArguments(bundle);
                replaceFragment(addSellReturnFragment);

            }

            @Override
            public void onClickDelete(int position) {
                deleteSellReturn(position);
            }

            @Override
            public void onClickAddPayment(Transaction transaction) {
                addPayment(transaction);
            }

            @Override
            public void onClickViewPayment(Transaction transaction) {
                viewPayment(transaction);
            }

            @Override
            public void onClickPrint(Transaction transaction) {

            }
        });
        recycleSellReturn.setAdapter(sellReturnAdapter);
        recycleSellReturn.setLayoutManager(new LinearLayoutManager(_context));
        sellReturnAdapter.notifyDataSetChanged();
        setItemView();
    }


    public void setItemView()
    {
        if(sellReturnAdapter.getItemCount() > 0)
        {
            recycleSellReturn.setVisibility(View.VISIBLE);
            noItemFound.setVisibility(View.GONE);
        }
        else
        {
            recycleSellReturn.setVisibility(View.GONE);
            noItemFound.setVisibility(View.VISIBLE);
        }
    }
    public void pickDate(Boolean isStartDate)
    {
        DatePickerDialog datePickerDialog = new DatePickerDialog(_context,
                (datePicker, year, month, day) -> {

                    String myFormat = populateSetDate(year, month, day);
                    if(isStartDate)
                    {
                        startDate.setText(myFormat);
                    }
                    else
                    {
                        endDate.setText(myFormat);
                    }

                }, c.get(Calendar.YEAR), c.get(Calendar.MONTH), c.get(Calendar.DAY_OF_MONTH));
        datePickerDialog.getDatePicker().setMaxDate(new Date().getTime());
        datePickerDialog.show();
    }

    @Override
    public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
        if (parent.getId() == R.id.spinnerCustomer) {
            customerId = arrayListCustomers.get(position).getId();
            customerName = arrayListCustomers.get(position).getFirst_name();
        }
        if (parent.getId() == R.id.spinnerStation) {
            stationId = arrayListBusinesslocation.get(position).getId();

        }
    }

    @Override
    public void onNothingSelected(AdapterView<?> parent) {

    }
    public void replaceFragment(Fragment someFragment) {
        FragmentTransaction transaction = getFragmentManager().beginTransaction();
        transaction.replace(R.id.nav_host_fragment, someFragment);
        transaction.addToBackStack(null);
        transaction.commit();
    }
    private void deleteSellReturn(int position) {

        LayoutInflater li = LayoutInflater.from(_context);
        View promptsView = li.inflate(R.layout.delete_dialog_main, null);

        androidx.appcompat.app.AlertDialog.Builder alertDialogBuilder = new androidx.appcompat.app.AlertDialog.Builder(_context);
        alertDialogBuilder.setView(promptsView);

        final Button ButtonSave = promptsView.findViewById(R.id.btn_save);
        final Button ButtonClose = promptsView.findViewById(R.id.btn_close);
        final androidx.appcompat.app.AlertDialog mAlertDialog = alertDialogBuilder.create();
        if (mAlertDialog.getWindow() != null)
            mAlertDialog.getWindow().getAttributes().windowAnimations = R.style.SlidingDialogAnimation;
        Objects.requireNonNull(mAlertDialog.getWindow()).setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));

        ButtonClose.setOnClickListener(v -> mAlertDialog.dismiss());

        ButtonSave.setOnClickListener(v -> {

            transactionDbController.deleteTransaction(transactionList.get(position).getId());
            ArrayList<Sell_lines> sell_lines = transactionSellLineDbController.getSellLineByTransaction(transactionList.get(position).getReturn_parent_id());
            for(Sell_lines sell_lines_: sell_lines)
            {
                transactionSellLineDbController.updateReturnQty(0,sell_lines_.getId());
                Variation_location_details variation_location_details = variationLocationDetailDbController.getVariationLocationDetailsByStationIdProductId(transactionList.get(position).getLocation_id(), sell_lines_.getProduct_id());
                int qtyAvailable = variation_location_details.getQty_available() - sell_lines_.getQty_returned();
                variationLocationDetailDbController.updateAvailableQty(qtyAvailable,variation_location_details.getId());
            }
            transactionList.remove(position);
            mAlertDialog.dismiss();
            Objects.requireNonNull(recycleSellReturn.getAdapter()).notifyDataSetChanged();
        });

        mAlertDialog.show();
    }
    private void searchList() {
        sellReturnAdapter.setData(transactionDbController.filterSellReturnList(customerId, stationId, startDate.getText().toString(), endDate.getText().toString()));
        sellReturnAdapter.notifyDataSetChanged();
        setItemView();
    }
    @SuppressLint("SetTextI18n")
    private void addPayment(Transaction transaction) {
        LayoutInflater li = LayoutInflater.from(_context);
        View promptsView = li.inflate(R.layout.dialog_add_payment_sell_return, null);
        androidx.appcompat.app.AlertDialog.Builder alertDialogBuilder = new androidx.appcompat.app.AlertDialog.Builder(_context);
        alertDialogBuilder.setView(promptsView);

        final AppCompatTextView customerName = promptsView.findViewById(R.id.customerName);
        final AppCompatTextView invoiceNo = promptsView.findViewById(R.id.invoiceNo);
        final AppCompatTextView businessLocation = promptsView.findViewById(R.id.businessLocation);
        final AppCompatTextView totalAmount = promptsView.findViewById(R.id.totalAmount);
        final AppCompatTextView paymentNote = promptsView.findViewById(R.id.paymentNote);
        final AppCompatEditText advanceBalanceEdt = promptsView.findViewById(R.id.advanceBalanceEdt);
        final AppCompatEditText paidOnEdt = promptsView.findViewById(R.id.paidOnEdt);
        final AppCompatEditText attachDocument = promptsView.findViewById(R.id.attachDocument);
        final AppCompatEditText paymentNoteEdt = promptsView.findViewById(R.id.paymentNoteEdt);
        final AppCompatSpinner paymentMethod = promptsView.findViewById(R.id.paymentMethod);
        final AppCompatButton  btnSave = promptsView.findViewById(R.id.btnSave);
        final AppCompatImageView btnClose = promptsView.findViewById(R.id.btnClose);

        Contact contact = contactDbController.getCustomerById(transaction.getContact_id());
        Transaction payment = transactionPayementDbController.getTransactionById(transaction.getId());
        Business_location businesslocation = businessLocationDbController.getStationById(transaction.getLocation_id());

        customerName.setText(contact.getName());
        businessLocation.setText(businesslocation.getName() + " " + businesslocation.getCity() + " " + businesslocation.getCountry());
        totalAmount.setText(transaction.getFinal_total() + " " + user.get(SessionManager.KEY_SYMBOL));
        paymentNote.setText(transaction.getNote());
        invoiceNo.setText(transaction.getInvoice_no());
        float final_total = Float.parseFloat(transaction.getFinal_total());

        float amount = 0.f;
        for (Transaction transaction_ : transactionPayementDbController.getAllTransactionById(transaction.getId())) {
            amount += transaction_.getAmount();
        }

        float due = (final_total - amount);
        advanceBalanceEdt.setText(due + "");
        paidOnEdt.setText(StringFormat.actualTime());
        paymentNoteEdt.setText(payment.getNote());

        if (transaction.getMethod() != null) {
            int indexP = Arrays.asList(_context.getResources().getStringArray(R.array.payment_method_array)).indexOf(transaction.getMethod());
            paymentMethod.setSelection(indexP);
        }

        paidOnEdt.setOnClickListener(v -> {
            DatePickerDialog datePickerDialog = new DatePickerDialog(_context,
                    (datePicker, year, month, day) -> {
                        String myFormat = StringFormat.populateSetFullDate(year, month, day, c.get(Calendar.HOUR), c.get(Calendar.MINUTE));
                        paidOnEdt.setText(myFormat);
                    }, c.get(Calendar.YEAR), c.get(Calendar.MONTH), c.get(Calendar.DAY_OF_MONTH));
            datePickerDialog.show();
        });

        final androidx.appcompat.app.AlertDialog mAlertDialog = alertDialogBuilder.create();
        if (mAlertDialog.getWindow() != null)
            mAlertDialog.getWindow().getAttributes().windowAnimations = R.style.SlidingDialogAnimation;
        Objects.requireNonNull(mAlertDialog.getWindow()).setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));


        btnClose.setOnClickListener(v -> mAlertDialog.dismiss());

        btnSave.setOnClickListener(v -> {

            if (Float.parseFloat(Objects.requireNonNull(advanceBalanceEdt.getText()).toString()) <= due) {

                transaction.setAmount(Float.parseFloat(advanceBalanceEdt.getText().toString()));
                transaction.setMethod(paymentMethod.getSelectedItem().toString());
                transaction.setNote(Objects.requireNonNull(paymentNoteEdt.getText()).toString());
                transaction.setPaid_on(Objects.requireNonNull(paidOnEdt.getText()).toString());
                transaction.setTransaction_id(transaction.getId());

                int index = transactionPayementDbController.insertLocal(transaction);
                if (index > 0) {
                    updatePaymentStatus(transaction);
                    FileUtil.showDialog(_context,"Successful",_context.getResources().getString(R.string.lbl_added_success));
                    sellReturnAdapter.notifyDataSetChanged();
                    mAlertDialog.dismiss();

                } else {
                    Toast.makeText(_context, "Error insert", Toast.LENGTH_LONG).show();
                }
            } else {
                Toast.makeText(_context, _context.getResources().getText(R.string.label_maximum_amount) + " " + due, Toast.LENGTH_LONG).show();
            }
        });

        mAlertDialog.show();
        mAlertDialog.getWindow().setLayout(1600, 800);
    }

    @SuppressLint("SetTextI18n")
    private void viewPayment(Transaction transaction) {

        LayoutInflater li = LayoutInflater.from(_context);
        View promptsView = li.inflate(R.layout.dialog_view_payment_sell_return, null);
        androidx.appcompat.app.AlertDialog.Builder alertDialogBuilder = new androidx.appcompat.app.AlertDialog.Builder(
                _context);
        alertDialogBuilder.setView(promptsView);
        final AppCompatTextView customerName = promptsView.findViewById(R.id.customerName);
        final AppCompatTextView businessLocation = promptsView.findViewById(R.id.businessLocation);
        final AppCompatTextView businessMobile = promptsView.findViewById(R.id.businessMobile);
        final AppCompatTextView businessEmail = promptsView.findViewById(R.id.businessEmail);
        final AppCompatTextView invoiceNo = promptsView.findViewById(R.id.invoiceNo);
        final AppCompatTextView paymentDate = promptsView.findViewById(R.id.paymentDate);
        final AppCompatTextView paymentStatus = promptsView.findViewById(R.id.paymentStatus);
        final RecyclerView recycleSubList = promptsView.findViewById(R.id.recycleSubList);
        final AppCompatImageView btnClose = promptsView.findViewById(R.id.btnClose);
        final AppCompatTextView viewPayment = promptsView.findViewById(R.id.viewPayment);
        subPaymentAdapter = new SubPaymentReturnSalesAdapter();
        recycleSubList.setAdapter(subPaymentAdapter);
        recycleSubList.setLayoutManager(new LinearLayoutManager(_context));
        subPaymentTransaction = transactionPayementDbController.getAllTransactionById(transaction.getId());
        subPaymentAdapter.setData(subPaymentTransaction);
        subPaymentAdapter.setonClickAction(new SubPaymentReturnSalesAdapter.onClickAction() {

            @Override
            public void onEditSubPayment(Transaction transaction) {
                editSubPayment(transaction);
            }

            @Override
            public void onViewSubPayment(Transaction transaction) {

            }

            @Override
            public void onDeleteSubPayment(int position,Transaction transaction) {
                deleteSubPayment(position,transaction);
            }
         
        });
        Contact contact = contactDbController.getCustomerById(transaction.getContact_id());
        customerName.setText(contact.getName());
        businessMobile.setText(contact.getMobile());
        businessEmail.setText(contact.getEmail());
        Business_location businesslocation = businessLocationDbController.getStationById(transaction.getLocation_id());
        businessLocation.setText(businesslocation.getName() + " " + businesslocation.getCity() + " " + businesslocation.getCountry());
        invoiceNo.setText(transaction.getInvoice_no());
        viewPayment.setText("View Payment ( Invoice No. : " +transaction.getInvoice_no()+ " )");
        paymentDate.setText(transaction.getTransaction_date());
        paymentStatus.setText(transaction.getPayment_status());
        final androidx.appcompat.app.AlertDialog mAlertDialog = alertDialogBuilder.create();
        if (mAlertDialog.getWindow() != null)
            mAlertDialog.getWindow().getAttributes().windowAnimations = R.style.SlidingDialogAnimation;
        Objects.requireNonNull(mAlertDialog.getWindow()).setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        btnClose.setOnClickListener(v -> mAlertDialog.dismiss());
        mAlertDialog.show();
        mAlertDialog.getWindow().setLayout(1600, 800);
    }
    private void editSubPayment(Transaction transaction_) {
        LayoutInflater li = LayoutInflater.from(_context);
        View promptsView = li.inflate(R.layout.dialog_add_payment_sell_return, null);
        androidx.appcompat.app.AlertDialog.Builder alertDialogBuilder = new androidx.appcompat.app.AlertDialog.Builder(_context);
        alertDialogBuilder.setView(promptsView);

        final AppCompatTextView customerName = promptsView.findViewById(R.id.customerName);
        final AppCompatTextView invoiceNo = promptsView.findViewById(R.id.invoiceNo);
        final AppCompatTextView businessLocation = promptsView.findViewById(R.id.businessLocation);
        final AppCompatTextView totalAmount = promptsView.findViewById(R.id.totalAmount);
        final AppCompatTextView paymentNote = promptsView.findViewById(R.id.paymentNote);
        final AppCompatEditText advanceBalanceEdt = promptsView.findViewById(R.id.advanceBalanceEdt);
        final AppCompatEditText paidOnEdt = promptsView.findViewById(R.id.paidOnEdt);
        final AppCompatEditText attachDocument = promptsView.findViewById(R.id.attachDocument);
        final AppCompatEditText paymentNoteEdt = promptsView.findViewById(R.id.paymentNoteEdt);
        final AppCompatSpinner paymentMethod = promptsView.findViewById(R.id.paymentMethod);
        final AppCompatButton  btnSave = promptsView.findViewById(R.id.btnSave);
        final AppCompatImageView btnClose = promptsView.findViewById(R.id.btnClose);
        final AppCompatTextView addPaymentTxt = promptsView.findViewById(R.id.addPaymentTxt);
        addPaymentTxt.setText(R.string.edit_payment_label);
        Transaction transaction = transactionDbController.getTransactionById(transaction_.getTransaction_id());

        Contact contact = contactDbController.getCustomerById(transaction.getContact_id());
        Transaction payment = transactionPayementDbController.getTransactionById(transaction.getId());
        Business_location businesslocation = businessLocationDbController.getStationById(transaction.getLocation_id());
        invoiceNo.setText(transaction.getInvoice_no());
        customerName.setText(contact.getName());
        businessLocation.setText(businesslocation.getName() + " " + businesslocation.getCity() + " " + businesslocation.getCountry());
        totalAmount.setText(transaction.getFinal_total() + " " + user.get(session.KEY_SYMBOL));
        paymentNote.setText(transaction.getNote());
        /**
         * get amount from all payments
         */

        advanceBalanceEdt.setText(transaction_.getAmount() + "");
        paidOnEdt.setText(transaction_.getPaid_on());
        paymentNoteEdt.setText(transaction_.getNote());
        //  if (transaction_.getMethod() != null) {
        int indexP = Arrays.asList(_context.getResources().getStringArray(R.array.payment_method_array)).indexOf(transaction_.getMethod());

        paymentMethod.setSelection(indexP);

        /**
         * init button click listner
         */
        paidOnEdt.setOnClickListener(v -> {
            DatePickerDialog datePickerDialog = new DatePickerDialog(_context,
                    (datePicker, year, month, day) -> {
                        String myFormat = StringFormat.populateSetFullDate(year, month, day, c.get(Calendar.HOUR), c.get(Calendar.MINUTE));
                        paidOnEdt.setText(myFormat);
                    }, c.get(Calendar.YEAR), c.get(Calendar.MONTH), c.get(Calendar.DAY_OF_MONTH));
            datePickerDialog.show();
        });

        final androidx.appcompat.app.AlertDialog mAlertDialog = alertDialogBuilder.create();
        if (mAlertDialog.getWindow() != null)
            mAlertDialog.getWindow().getAttributes().windowAnimations = R.style.SlidingDialogAnimation;
        Objects.requireNonNull(mAlertDialog.getWindow()).setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));

        btnClose.setOnClickListener(v -> mAlertDialog.dismiss());

        btnSave.setOnClickListener(v -> {

            transaction_.setAmount(Float.parseFloat(Objects.requireNonNull(advanceBalanceEdt.getText()).toString()));
            transaction_.setMethod(paymentMethod.getSelectedItem().toString());
            transaction_.setNote(paymentNoteEdt.getText().toString());
            transaction_.setPaid_on(paidOnEdt.getText().toString());
            transaction_.setTransaction_id(transaction.getId());

                int index = transactionPayementDbController.updatePayement(transaction_);
                sellReturnAdapter.notifyDataSetChanged();
                if (index > 0) {
                    updatePaymentStatus(transaction);
                    FileUtil.showDialog(_context, "Successful", _context.getResources().getString(R.string.edit_payment_success));
                    subPaymentAdapter.notifyDataSetChanged();
                    mAlertDialog.dismiss();

                } else {
                    Toast.makeText(_context, "Error insert", Toast.LENGTH_LONG).show();
                }


        });


        mAlertDialog.show();
        mAlertDialog.getWindow().setLayout(1600, 800);
    }
    private void updatePaymentStatus(Transaction transaction)
    {
        float amount = 0.f;
        for (Transaction transaction1 : transactionPayementDbController.getAllTransactionById(transaction.getId())) {
            amount += transaction1.getAmount();
        }
        float final_total = Float.parseFloat(transaction.getFinal_total());
        float due = (final_total - amount);
        if (due <= 0 ) {
            transaction.setPayment_status(PAID);
        } else {
            transaction.setPayment_status(PARTIAL);
        }
     transactionDbController.updatePaymentData(transaction);
        setUpRecyclerView();
    }
    private void deleteSubPayment(int position,Transaction transaction_) {

        LayoutInflater li = LayoutInflater.from(_context);
        View promptsView = li.inflate(R.layout.delete_dialog_main, null);
        androidx.appcompat.app.AlertDialog.Builder alertDialogBuilder = new androidx.appcompat.app.AlertDialog.Builder(_context);
        alertDialogBuilder.setView(promptsView);
        final Button ButtonSave = promptsView.findViewById(R.id.btn_save);
        final Button ButtonClose = promptsView.findViewById(R.id.btn_close);

        final androidx.appcompat.app.AlertDialog mAlertDialog = alertDialogBuilder.create();
        if (mAlertDialog.getWindow() != null)
            mAlertDialog.getWindow().getAttributes().windowAnimations = R.style.SlidingDialogAnimation;
        Objects.requireNonNull(mAlertDialog.getWindow()).setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));

        ButtonClose.setOnClickListener(v -> mAlertDialog.dismiss());
        ButtonSave.setOnClickListener(v -> {

            Transaction transaction = transactionDbController.getTransactionById(transaction_.getTransaction_id());
        transactionPayementDbController.deletePayment(transaction_.getId());
          updatePaymentStatus(transaction);
            Toast.makeText(_context, "Deleted ", Toast.LENGTH_LONG).show();
            
            subPaymentTransaction.remove(position);
            subPaymentAdapter.notifyDataSetChanged();
            setUpRecyclerView();
            mAlertDialog.dismiss();
        });

        mAlertDialog.show();
    }

    private void viewSellReturn(Transaction transaction) {
        LayoutInflater li = LayoutInflater.from(_context);
        View promptsView = li.inflate(R.layout.dialog_view_sell_return, null);
        androidx.appcompat.app.AlertDialog.Builder alertDialogBuilder = new androidx.appcompat.app.AlertDialog.Builder(_context);
        alertDialogBuilder.setView(promptsView);
      final androidx.appcompat.app.AlertDialog mAlertDialog = alertDialogBuilder.create();
        if (mAlertDialog.getWindow() != null)
            mAlertDialog.getWindow().getAttributes().windowAnimations = R.style.SlidingDialogAnimation;
        Objects.requireNonNull(mAlertDialog.getWindow()).setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));

        final RecyclerView productRecycler = promptsView.findViewById(R.id.productRecycler);
        final AppCompatTextView returnTotal = promptsView.findViewById(R.id.returnTotal);
        final AppCompatTextView returnDate = promptsView.findViewById(R.id.returnDate);
        final AppCompatTextView customerName = promptsView.findViewById(R.id.customerName);
        final AppCompatTextView location = promptsView.findViewById(R.id.location);
        final AppCompatTextView invoiceNoTxt = promptsView.findViewById(R.id.invoiceNoTxt);
        final AppCompatTextView date = promptsView.findViewById(R.id.date);
        final AppCompatTextView netTotalAmount = promptsView.findViewById(R.id.netTotalAmount);
        final AppCompatTextView totalReturnDiscount = promptsView.findViewById(R.id.totalReturnDiscount);
        final AppCompatTextView totalReturnTax = promptsView.findViewById(R.id.totalReturnTax);
        final AppCompatTextView sellReturnTxt = promptsView.findViewById(R.id.sellReturnTxt);
        final AppCompatImageView btnClose = promptsView.findViewById(R.id.btnClose);
        btnClose.setOnClickListener(v -> mAlertDialog.dismiss());

        Transaction transactionMainSale = transactionDbController.getTransactionById(transaction.getReturn_parent_id());
        invoiceNoTxt.setText(transactionMainSale.getInvoice_no());
        sellReturnTxt.setText("Sell Return (Invoice No. : "+transaction.getInvoice_no()+ " )");

        ArrayList<Sell_lines> sell_lines = new ArrayList<>(transactionSellLineDbController.getSellLineByTransaction(transaction.getReturn_parent_id()));
        AddSellReturnListAdapter addSellReturnAdapter = new AddSellReturnListAdapter(2);
        addSellReturnAdapter.setData(sell_lines);
        productRecycler.setAdapter(addSellReturnAdapter);
        productRecycler.setLayoutManager(new LinearLayoutManager(_context));
        addSellReturnAdapter.notifyDataSetChanged();

        double returnSubTotalAmount=Double.parseDouble(ProductUtil.getTotalAmountSellReturn(sell_lines, _context));
        netTotalAmount.setText(String.format(Locale.ENGLISH,"%.2f",returnSubTotalAmount));

        if (transaction.getDiscount_type().equals("percentage") && !(transaction.getDiscount_amount().isEmpty())) {

            double totalReturnDiscountAmount = (Double.parseDouble(transaction.getDiscount_amount()) / 100.0) * returnSubTotalAmount;
            totalReturnDiscount.setText(String.format(Locale.ENGLISH, "%.2f", (totalReturnDiscountAmount)));

        } else if (transaction.getDiscount_type().equals("fixed") && !(transaction.getDiscount_amount().isEmpty())) {

            double totalReturnDiscountAmount = Double.parseDouble(transaction.getDiscount_amount());
            totalReturnDiscount.setText(String.format(Locale.ENGLISH, "%.2f", (totalReturnDiscountAmount)));
        }
        totalReturnTax.setText(transaction.getTax_amount());
        System.out.println("transaction.getTax_amount()  :"+transaction.getTax_amount());
        if(transaction.getTax_amount() == null || transaction.getTax_amount().isEmpty())
        {
            totalReturnTax.setText("0.00");
        }

        returnTotal.setText(transaction.getFinal_total());
        returnDate.setText(transaction.getTransaction_date());
        date.setText(transactionMainSale.getTransaction_date());
        customerName.setText(contactDbController.getCustomerById(transaction.getContact_id()).getName());
        location.setText( businessLocationDbController.getStationById(transaction.getLocation_id()).getName());
        mAlertDialog.show();
        mAlertDialog.getWindow().setLayout(1600, 1000);
    }

}