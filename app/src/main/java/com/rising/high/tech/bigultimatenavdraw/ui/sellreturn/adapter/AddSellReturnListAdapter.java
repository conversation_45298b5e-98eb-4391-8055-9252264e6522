package com.rising.high.tech.bigultimatenavdraw.ui.sellreturn.adapter;

import android.content.Context;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.appcompat.widget.AppCompatEditText;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.recyclerview.widget.RecyclerView;
import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.model.Sell_lines;

import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Locale;
import java.util.Objects;


public class AddSellReturnListAdapter extends RecyclerView.Adapter<AddSellReturnListAdapter.SellReturnViewHolder> {

    private ArrayList<Sell_lines> dataList = new ArrayList<>();
    Context context;
    int from_ = 1; //To check view is from Edit or View screen

    @NotNull
    @Override
    public SellReturnViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        context = parent.getContext();

        return new SellReturnViewHolder(LayoutInflater.from(parent.getContext())
                .inflate(R.layout.list_add_sell_return_item, parent, false));

    }
    public AddSellReturnListAdapter(int from){
        this.from_ = from;
    }
    @Override
    public void onBindViewHolder(SellReturnViewHolder holder, int position) {
        holder.productName.setText(dataList.get(position).getProduct_name());
        holder.unitPrice.setText(dataList.get(position).getUnit_price());
        holder.sellQty.setText(String.valueOf(dataList.get(position).getQuantity()));
//        dataList.get(position).setQty_returned(dataList.get(position).getQuantity());
        holder.returnQty.setText(String.valueOf(dataList.get(position).getQty_returned()));
        holder.returnQtyView.setText(String.valueOf(dataList.get(position).getQty_returned()));
        if(from_ == 2)
        {
            holder.returnQtyView.setVisibility(View.VISIBLE);
            holder.returnQty.setVisibility(View.GONE);
        }
        else
        {
            holder.returnQtyView.setVisibility(View.GONE);
            holder.returnQty.setVisibility(View.VISIBLE);

        }
        if(dataList.get(position).getUnit_price() != null)
        {
            Double subTotal= dataList.get(position).getQty_returned() * Double.parseDouble(dataList.get(position).getUnit_price());
            holder.returnSubTotal.setText(String.valueOf(subTotal));
        }
        holder.returnQty.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {

                if (charSequence.toString().length() != 0 && !(Objects.requireNonNull(holder.returnQty.getText()).toString().isEmpty())) {
                    if(Integer.parseInt(Objects.requireNonNull(holder.returnQty.getText()).toString()) > dataList.get(position).getQuantity() )
                    {
                        holder.returnQty.setError("Only " + dataList.get(position).getQuantity() + " Quantity available");
                        holder.returnQty.setText("");
                    }
                    else
                    {
                        int qty = Integer.parseInt(holder.returnQty.getText().toString());
                        double unit_price = Double.parseDouble(holder.unitPrice.getText().toString());
                        double total=(qty * unit_price);
                        holder.returnSubTotal.setText(String.format(Locale.ENGLISH,"%.2f",(total)));
                        dataList.get(position).setQty_returned(Integer.parseInt(holder.returnQty.getText().toString()));

                        if (mOnDataChangeListener != null) {
                            mOnDataChangeListener.onDataChanged(dataList);
                        }
                    }

                }
                else
                {
                    System.out.println("33333333333333333333" + holder.returnQty.getText().toString());
                    holder.returnSubTotal.setText(String.format(Locale.ENGLISH,"%.2f",0.0));
                    dataList.get(position).setQty_returned(0);
                    if (mOnDataChangeListener != null) {
                        mOnDataChangeListener.onDataChanged(dataList);
                    }
                }
            }

            @Override
            public void afterTextChanged(Editable editable) {

            }
        });

    }

    public void setData(ArrayList<Sell_lines> arrayList) {
        Collections.reverse(arrayList);
        this.dataList = arrayList;
        notifyDataSetChanged();
    }

    @Override
    public int getItemCount() {
        return dataList.size();
    }

    public static class SellReturnViewHolder extends RecyclerView.ViewHolder {
        AppCompatTextView productName, unitPrice,sellQty,returnSubTotal,returnQtyView;
        AppCompatEditText returnQty;

        public SellReturnViewHolder(View itemView) {
            super(itemView);

            productName = itemView.findViewById(R.id.productName);
            unitPrice = itemView.findViewById(R.id.unitPrice);
            sellQty = itemView.findViewById(R.id.sellQty);
            returnQty = itemView.findViewById(R.id.returnQty);
            returnSubTotal = itemView.findViewById(R.id.returnSubTotal);
            returnQtyView = itemView.findViewById(R.id.returnQtyView);

        }
    }
    public interface OnDataChangeListener {
        void onDataChanged(ArrayList<Sell_lines> sellLines);
    }

    OnDataChangeListener mOnDataChangeListener;

    public void setOnDataChangeListener(OnDataChangeListener onDataChangeListener) {
        mOnDataChangeListener = onDataChangeListener;
    }
}
