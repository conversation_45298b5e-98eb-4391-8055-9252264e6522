package com.rising.high.tech.bigultimatenavdraw.ui.sellreturn.adapter;

import android.content.Context;
import android.graphics.Color;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.LinearLayout;
import android.widget.Spinner;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.core.content.ContextCompat;
import androidx.core.content.res.ResourcesCompat;
import androidx.recyclerview.widget.RecyclerView;
import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.db.ContactDbController;
import com.rising.high.tech.bigultimatenavdraw.db.BusinessLocationDbController;
import com.rising.high.tech.bigultimatenavdraw.db.TransactionDbController;
import com.rising.high.tech.bigultimatenavdraw.db.TransactionPayementDbController;
import com.rising.high.tech.bigultimatenavdraw.model.Transaction;
import com.rising.high.tech.bigultimatenavdraw.util.SessionManager;
import com.rising.high.tech.bigultimatenavdraw.util.StringFormat;
import org.jetbrains.annotations.NotNull;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.PAID;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.DUE;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.PARTIAL;

public class SellReturnAdapter extends RecyclerView.Adapter<SellReturnAdapter.SellReturnViewHolder> {

    private ArrayList<Transaction> dataList = new ArrayList<>();
    private BusinessLocationDbController businessLocationDbController;
    private ContactDbController contactDbController;
    private TransactionDbController transactionDbController;
    private HashMap<String, Object> user;
    Context context;
    private TransactionPayementDbController transactionPayementDbController;

    @NotNull
    @Override
    public SellReturnViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        context = parent.getContext();

        SessionManager session = new SessionManager(context);
        user = session.getUserDetails();
        businessLocationDbController = new BusinessLocationDbController(context);
        businessLocationDbController.open();

        contactDbController = new ContactDbController(context);
        contactDbController.open();

        transactionPayementDbController = new TransactionPayementDbController(context);
        transactionPayementDbController.open();

        transactionDbController = new TransactionDbController(context);
        transactionDbController.open();

        return new SellReturnViewHolder(LayoutInflater.from(parent.getContext())
                .inflate(R.layout.list_sell_return_item, parent, false));

    }

    @Override
    public void onBindViewHolder(SellReturnViewHolder holder, int position) {
        holder.txtDate.setText(dataList.get(position).getTransaction_date());
        holder.invoiceNo.setText(dataList.get(position).getInvoice_no());
        holder.customerName.setText(contactDbController.getCustomerById(dataList.get(position).getContact_id()).getName());
        holder.paymentStatus.setText(StringFormat.toTitleCase(dataList.get(position).getPayment_status()));
        String stationName = businessLocationDbController.getStationById(dataList.get(position).getLocation_id()).getName();
        holder.location.setText(stationName);
        if (dataList.get(position).getPayment_status() != null && dataList.get(position).getPayment_status().equals(PAID)) {
            holder.paymentStatus.setBackground(ResourcesCompat.getDrawable(context.getResources(),R.drawable.green_bg_no_padding,null));
            holder.paymentStatus.setTextColor(Color.WHITE);
        }
        else if (dataList.get(position).getPayment_status() != null && dataList.get(position).getPayment_status().equals(DUE)) {
            holder.paymentStatus.setBackground(ResourcesCompat.getDrawable(context.getResources(),R.drawable.rounded_orange_bg,null));
            holder.paymentStatus.setTextColor(Color.WHITE);
        }
        else if (dataList.get(position).getPayment_status() != null && dataList.get(position).getPayment_status().equals(PARTIAL)) {
            holder.paymentStatus.setBackground(ResourcesCompat.getDrawable(context.getResources(),R.drawable.rounded_blue_bg,null));
            holder.paymentStatus.setTextColor(Color.WHITE);
        }
        else
        {
            holder.paymentStatus.setTextColor(Color.BLACK);
        }
        String total=dataList.get(position).getFinal_total() + user.get(SessionManager.KEY_SYMBOL);
        holder.totalAmount.setText(total);
        if(dataList.get(position).getFinal_total() != null && dataList.get(position).getTotal_paid() != null) {
            double due = Double.parseDouble(dataList.get(position).getFinal_total()) - Double.parseDouble(dataList.get(position).getTotal_paid());
            holder.totalAmount.setText(String.valueOf(due));
        }
        if(dataList.get(position).getReturn_parent_id() != null) {
            holder.parentSale.setText(transactionDbController.getParentSaleInvoiceNumber(dataList.get(position).getReturn_parent_id().toString()));
        }
        if (position % 2 == 1) {
            holder.linearLayout.setBackgroundColor(ContextCompat.getColor(context, R.color.white));
        } else {
            holder.linearLayout.setBackgroundColor(ContextCompat.getColor(context,R.color.lightGrey));

        }
        int arrayList = R.array.array_action_return_sell;

        if(dataList.get(position).getPayment_status().equals(PAID)) {
            arrayList = R.array.array_action_return_sell_2;
        }
        float amount = 0.f;
        for (Transaction transaction1 : transactionPayementDbController.getAllTransactionById(dataList.get(position).getId())) {
            amount += transaction1.getAmount();
        }
        float final_total = Float.parseFloat(dataList.get(position).getFinal_total());
        float due = (final_total - amount);
        holder.paymentDue.setText(String.valueOf(due));


        ArrayAdapter<CharSequence> adapter = ArrayAdapter.createFromResource(context,arrayList, android.R.layout.simple_spinner_item);
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        holder.spinnerAction.setAdapter(adapter);

        holder.spinnerAction.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position_, long id) {
                switch (position_) {
                    case 1: {
                        if (onClickAction != null) {
                            onClickAction.onClickView(dataList.get(position));
                        }
                        notifyItemChanged(position);
                        holder.spinnerAction.setSelection(0, true);
                        break;
                    }
                    case 2: {
                        if (onClickAction != null) {
                            onClickAction.onClickEdit(dataList.get(position));
                        }
                        notifyItemChanged(position);
                        holder.spinnerAction.setSelection(0, true);
                        break;
                    }
                    case 3: {
                        if (onClickAction != null) {
                            onClickAction.onClickDelete(position);
                        }
                        notifyItemChanged(position);
                        holder.spinnerAction.setSelection(0, true);
                        break;
                    }

                    case 4: {
                        if (onClickAction != null) {
                            if(dataList.get(position).getPayment_status().equals(PAID)) {
                                onClickAction.onClickViewPayment(dataList.get(position));
                            }
                            else
                            {
                                onClickAction.onClickAddPayment(dataList.get(position));
                            }
                        }
                        notifyItemChanged(position);
                        holder.spinnerAction.setSelection(0, true);
                        break;
                    }

                }
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {

            }
        });

    }

    public void setData(ArrayList<Transaction> arrayList) {
        Collections.reverse(arrayList);
        this.dataList = arrayList;
        notifyDataSetChanged();
    }

    @Override
    public int getItemCount() {
        return dataList.size();
    }

    public static class SellReturnViewHolder extends RecyclerView.ViewHolder {
        AppCompatTextView txtDate, invoiceNo, parentSale, customerName, paymentStatus, location, totalAmount, paymentDue;
        Spinner spinnerAction;
        LinearLayout linearLayout;

        public SellReturnViewHolder(View itemView) {
            super(itemView);

            linearLayout = itemView.findViewById(R.id.linearLayout);
            txtDate = itemView.findViewById(R.id.txtDate);
            invoiceNo = itemView.findViewById(R.id.invoiceNo);
            parentSale = itemView.findViewById(R.id.parentSale);
            customerName = itemView.findViewById(R.id.customerName);
            paymentStatus = itemView.findViewById(R.id.paymentStatus);
            location = itemView.findViewById(R.id.location);
            totalAmount = itemView.findViewById(R.id.totalAmount);
            paymentDue = itemView.findViewById(R.id.paymentDue);
            spinnerAction = itemView.findViewById(R.id.spinnerAction);


        }
    }

    public interface onClickAction {
        void onClickView(Transaction transaction);
        void onClickEdit(Transaction transaction);
        void onClickDelete(int position);
        void onClickAddPayment(Transaction transaction);
        void onClickViewPayment(Transaction transaction);
        void onClickPrint(Transaction transaction);
    }

    onClickAction onClickAction;

    public void setonClickAction(onClickAction mOnClickAction) {
        onClickAction = mOnClickAction;
    }

}
