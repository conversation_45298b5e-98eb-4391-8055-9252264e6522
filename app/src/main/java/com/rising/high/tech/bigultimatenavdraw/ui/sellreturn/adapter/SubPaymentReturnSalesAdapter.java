package com.rising.high.tech.bigultimatenavdraw.ui.sellreturn.adapter;

import android.content.Context;
import android.content.res.Resources;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import androidx.appcompat.widget.AppCompatTextView;
import androidx.recyclerview.widget.RecyclerView;

import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.db.ContactDbController;
import com.rising.high.tech.bigultimatenavdraw.db.BusinessLocationDbController;
import com.rising.high.tech.bigultimatenavdraw.db.TransactionDbController;
import com.rising.high.tech.bigultimatenavdraw.db.TransactionPayementDbController;
import com.rising.high.tech.bigultimatenavdraw.model.Transaction;
import com.rising.high.tech.bigultimatenavdraw.util.SessionManager;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.HashMap;

public class SubPaymentReturnSalesAdapter extends RecyclerView.Adapter<SubPaymentReturnSalesAdapter.subPaymentViewHolder> {
    private static final String TAG = "SubPaymentAdapter";
    private ArrayList<Transaction> dataList = new ArrayList<>();
    Context context;
    private TransactionPayementDbController transactionPayementDbController;
    private TransactionDbController transactionDbController;
    private ContactDbController contactDbController;
    private BusinessLocationDbController businessLocationDbController;
    private Resources resources;
    private SessionManager session;
    private HashMap<String, Object> user;
    final Calendar c = Calendar.getInstance();

    @Override
    public subPaymentViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        context = parent.getContext();
        resources = context.getResources();

        session = new SessionManager(context);
        user = session.getUserDetails();

        transactionPayementDbController = new TransactionPayementDbController(context);
        transactionPayementDbController.open();

        transactionDbController = new TransactionDbController(context);
        transactionDbController.open();

        businessLocationDbController = new BusinessLocationDbController(context);
        businessLocationDbController.open();

        contactDbController = new ContactDbController(context);
        contactDbController.open();


        return new subPaymentViewHolder(LayoutInflater.from(parent.getContext())
                .inflate(R.layout.dialog_list_sub_payment_sell_return, parent, false));
    }

    @Override
    public void onBindViewHolder(subPaymentViewHolder holder, int position) {
        holder.date.setText(dataList.get(position).getPaid_on());
        holder.referenceNo.setText(dataList.get(position).getRef_no());
        holder.amount.setText(String.valueOf(dataList.get(position).getAmount()));
        holder.paymentMethod.setText(dataList.get(position).getMethod());
        holder.paymentNote.setText(dataList.get(position).getNote());
        holder.editPayment.setOnClickListener(v -> {
            if (onClickAction != null) {
                onClickAction.onEditSubPayment(dataList.get(position));
            }
            notifyDataSetChanged();
        });
        holder.deletePayment.setOnClickListener(v -> {
            if (onClickAction != null) {
                onClickAction.onDeleteSubPayment(position,dataList.get(position));
            }
            notifyDataSetChanged();
        });
        holder.viewPayment.setOnClickListener(v -> {
            if (onClickAction != null) {
                onClickAction.onViewSubPayment(dataList.get(position));
            }
            notifyDataSetChanged();
        });
    }

    public void setData(ArrayList<Transaction> arrayList) {
        this.dataList = arrayList;
        notifyDataSetChanged();
    }

    @Override
    public int getItemCount() {
        return dataList.size();
    }

    public static class subPaymentViewHolder extends RecyclerView.ViewHolder {
        AppCompatTextView date, referenceNo, amount, paymentMethod, paymentNote;
        ImageView deletePayment, editPayment, viewPayment;

        public subPaymentViewHolder(View itemView) {
            super(itemView);
            date = itemView.findViewById(R.id.date);
            referenceNo = itemView.findViewById(R.id.referenceNo);
            amount = itemView.findViewById(R.id.amount);
            paymentMethod = itemView.findViewById(R.id.paymentMethod);
            paymentNote = itemView.findViewById(R.id.paymentNote);
            editPayment = itemView.findViewById(R.id.editPayment);
            deletePayment = itemView.findViewById(R.id.deletePayment);
            viewPayment = itemView.findViewById(R.id.viewPayment);

        }
    }
    public interface onClickAction {
        void onEditSubPayment(Transaction transaction);
        void onViewSubPayment(Transaction transaction);
        void onDeleteSubPayment(int position,Transaction transaction);
    }

    onClickAction onClickAction;

    public void setonClickAction(onClickAction mOnClickAction) {
        onClickAction = mOnClickAction;
    }
}
