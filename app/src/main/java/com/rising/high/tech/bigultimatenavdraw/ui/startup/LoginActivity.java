package com.rising.high.tech.bigultimatenavdraw.ui.startup;

import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.ImageView;
import android.widget.LinearLayout;

import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.AppCompatEditText;

import com.google.android.material.snackbar.Snackbar;
import com.google.gson.Gson;
import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.db.ContactDbController;
import com.rising.high.tech.bigultimatenavdraw.db.PermissionDbController;
import com.rising.high.tech.bigultimatenavdraw.db.RoleDbController;
import com.rising.high.tech.bigultimatenavdraw.db.RoleHasPermissionDbController;
import com.rising.high.tech.bigultimatenavdraw.db.UserDbController;
import com.rising.high.tech.bigultimatenavdraw.db.UserHasRolesDbController;
import com.rising.high.tech.bigultimatenavdraw.model.Contact;
import com.rising.high.tech.bigultimatenavdraw.model.Permission;
import com.rising.high.tech.bigultimatenavdraw.model.Role;
import com.rising.high.tech.bigultimatenavdraw.model.User;
import com.rising.high.tech.bigultimatenavdraw.ui.NaviMainActivity;

import com.rising.high.tech.bigultimatenavdraw.util.SessionManager;
import com.rising.high.tech.bigultimatenavdraw.util.StringFormat;

import java.util.ArrayList;
import java.util.Objects;

import static com.rising.high.tech.bigultimatenavdraw.util.Constant.ACTIVE;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.BUSINESS_LOCATION_ADD;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.BUSINESS_LOCATION_DELETE;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.BUSINESS_LOCATION_EDIT;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.BUSINESS_LOCATION_VIEW;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.BUSINESS_SETTING_ACCESS;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.CUSTOMER;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.EXPENSE_ACCESS;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.LOCAL_MASTER;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.LOCAL_USE;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.MOBILE;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.REPORT_ACCESS;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.SELL_RETURN_ACCESS;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.SERVER_MASTER;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.STOCK_ADJUSTEMENT_ACCESS;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.STOCK_TRANSFER_ACCESS;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.VARIATION_ADD;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.VARIATION_DELETE;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.VARIATION_EDIT;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.VARIATION_VIEW;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.WARRANTY_ADD;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.WARRANTY_DELETE;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.WARRANTY_EDIT;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.WARRANTY_VIEW;

public class LoginActivity extends AppCompatActivity {
    private static final String TAG = "LoginActivity";
    SessionManager session;
    private ContactDbController contactDbController;

    CheckBox localmasterCheckbox, localCheckbox, servermasterCheckbox;
    private PermissionDbController permissionDbController;
    private RoleDbController roleDbController;
    private UserDbController userDbController;
    private UserHasRolesDbController userHasRolesDbController;
    private RoleHasPermissionDbController roleHasPermissionDbController;
    AppCompatEditText etUsername, etPassword;
    ImageView logoImage;
    Button loginBtn;
    LinearLayout useContainer;
    LinearLayout linearLayout;
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        this.getWindow().setFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN, WindowManager.LayoutParams.FLAG_FULLSCREEN);
        setContentView(R.layout.activity_login);
        session = new SessionManager(getApplicationContext());
        etUsername = findViewById(R.id.etUsername);
        etPassword = findViewById(R.id.etPassword);
        loginBtn = findViewById(R.id.loginBtn);
        useContainer = findViewById(R.id.use_container);
        logoImage = findViewById(R.id.logoImage);
        localCheckbox = findViewById(R.id.localCheckbox);
        servermasterCheckbox = findViewById(R.id.servermasterCheckbox);
        localmasterCheckbox = findViewById(R.id.localmasterCheckbox);
        linearLayout = findViewById(R.id.linearLayout);

        initDb();
        initListners();
        initDbDefaultUser();
    }

    private void initListners() {

        loginBtn.setOnClickListener(view -> {
            //      if(Objects.requireNonNull(etUsername.getText()).toString().equals("rht") && Objects.requireNonNull(etPassword.getText()).toString().equals("qwerty"))
            if (checkCridential()) {
                firstLogin();
            } else {
                session.saveParams(false);
                Snackbar snackbar = Snackbar.make(linearLayout, "Invalid Username or Password,", Snackbar.LENGTH_LONG);
                snackbar.show();

            }
        });
        logoImage.setOnClickListener(v -> {
            useContainer.setVisibility(View.VISIBLE);
        });

    }

    private void initDb() {
        contactDbController = new ContactDbController(this);
        contactDbController.open();
        permissionDbController = new PermissionDbController(this);
        permissionDbController.open();
        roleDbController = new RoleDbController(this);
        roleDbController.open();
        userDbController = new UserDbController(this);
        userDbController.open();
        userHasRolesDbController = new UserHasRolesDbController(this);
        userHasRolesDbController.open();
        roleHasPermissionDbController = new RoleHasPermissionDbController(this);
        roleHasPermissionDbController.open();
    }

    private boolean checkCridential() {
        //  if(Objects.requireNonNull(etUsername.getText()).toString().equals("rht") && Objects.requireNonNull(etPassword.getText()).toString().equals("qwerty"))

        return userDbController.getUserId(etUsername.getText().toString(), etPassword.getText().toString()) > 0 || (Objects.requireNonNull(etUsername.getText()).toString().equals("rht") && Objects.requireNonNull(etPassword.getText()).toString().equals("qwerty"));
    }

    private void firstLogin() {

        //session.saveBoolean(session.IS_LOGIN, true);

        session.saveParams(true);
        session.saveInt(SessionManager.ID_USER, userDbController.getUserId(etUsername.getText().toString(), etPassword.getText().toString()));
        session.saveString(SessionManager.KEY_NAME, etUsername.getText().toString());
        session.saveString(session.KEY_SYMBOL, " $");


        startActivity(new Intent(this, NaviMainActivity.class));
        overridePendingTransition(R.anim.in_from_right, R.anim.out_to_left);
        finish();

    }

    public void onCheckboxClicked(View view) {
        // Is the view now checked?
        boolean checked = ((CheckBox) view).isChecked();

        // Check which checkbox was clicked
        switch (view.getId()) {
            case R.id.localCheckbox:
                if (checked) {
                    servermasterCheckbox.setChecked(false);
                    localmasterCheckbox.setChecked(false);
                    session.saveBoolean(LOCAL_USE, true);
                    session.saveBoolean(SERVER_MASTER, false);
                    session.saveBoolean(LOCAL_MASTER, false);
                }

                break;
            case R.id.servermasterCheckbox:
                if (checked) {
                    localCheckbox.setChecked(false);
                    localmasterCheckbox.setChecked(false);
                    session.saveBoolean(LOCAL_USE, false);
                    session.saveBoolean(SERVER_MASTER, true);
                    session.saveBoolean(LOCAL_MASTER, false);
                }
                break;
            case R.id.localmasterCheckbox:
                if (checked) {
                    servermasterCheckbox.setChecked(false);
                    localCheckbox.setChecked(false);
                    session.saveBoolean(LOCAL_USE, false);
                    session.saveBoolean(SERVER_MASTER, false);
                    session.saveBoolean(LOCAL_MASTER, true);

                }
                break;
        }
    }

    private void initDbDefaultUser() {
        // create all permissions
        if (permissionDbController.dbSyncCount() == 0) {

            /**
             * Create default Walk-in customer customer
             */
            Contact contact = new Contact();
            /**
             * TODO : make Business id general
             */
            contact.setBusiness_id(1);
            contact.setType(CUSTOMER);
            contact.setName("Walk-In Customer");
            contact.setFirst_name("Walk-In");
            contact.setLast_name("Customer");
            contact.setContact_status(ACTIVE);
            contact.setCreated_by("1");
            contact.setBalance("00");
            contact.setCreated_at(StringFormat.actualTime());

            contactDbController.insertLocal(contact);


            // Create permissions
            ArrayList<Permission> arrayListPermission = new ArrayList<>();

            arrayListPermission.add(new Permission("product.view", MOBILE));
            arrayListPermission.add(new Permission("product.add", MOBILE));
            arrayListPermission.add(new Permission("product.edit", MOBILE));
            arrayListPermission.add(new Permission("product.delete", MOBILE));

            arrayListPermission.add(new Permission("category.view", MOBILE));
            arrayListPermission.add(new Permission("category.add", MOBILE));
            arrayListPermission.add(new Permission("category.edit", MOBILE));
            arrayListPermission.add(new Permission("category.delete", MOBILE));

            arrayListPermission.add(new Permission("brand.view", MOBILE));
            arrayListPermission.add(new Permission("brand.add", MOBILE));
            arrayListPermission.add(new Permission("brand.edit", MOBILE));
            arrayListPermission.add(new Permission("brand.delete", MOBILE));

            arrayListPermission.add(new Permission("roles.view", MOBILE));
            arrayListPermission.add(new Permission("roles.add", MOBILE));
            arrayListPermission.add(new Permission("roles.edit", MOBILE));
            arrayListPermission.add(new Permission("roles.delete", MOBILE));

            arrayListPermission.add(new Permission("user.view", MOBILE));
            arrayListPermission.add(new Permission("user.add", MOBILE));
            arrayListPermission.add(new Permission("user.edit", MOBILE));
            arrayListPermission.add(new Permission("user.delete", MOBILE));

            arrayListPermission.add(new Permission("contact.view", MOBILE));
            arrayListPermission.add(new Permission("contact.add", MOBILE));
            arrayListPermission.add(new Permission("contact.edit", MOBILE));
            arrayListPermission.add(new Permission("contact.delete", MOBILE));

//            arrayListPermission.add(new Permission("customer.view", MOBILE));
//            arrayListPermission.add(new Permission("customer.add", MOBILE));
//            arrayListPermission.add(new Permission("customer.edit", MOBILE));
//            arrayListPermission.add(new Permission("customer.delete", MOBILE));

            arrayListPermission.add(new Permission("purchase.view", MOBILE));
            arrayListPermission.add(new Permission("purchase.add", MOBILE));
            arrayListPermission.add(new Permission("purchase.edit", MOBILE));
            arrayListPermission.add(new Permission("purchase.delete", MOBILE));

            arrayListPermission.add(new Permission("sell.view", MOBILE));
            arrayListPermission.add(new Permission("sell.add", MOBILE));
            arrayListPermission.add(new Permission("sell.edit", MOBILE));
            arrayListPermission.add(new Permission("sell.delete", MOBILE));
            arrayListPermission.add(new Permission("list_drafts", MOBILE));
            arrayListPermission.add(new Permission("list_quotations", MOBILE));
            arrayListPermission.add(new Permission("discount.access", MOBILE));

            arrayListPermission.add(new Permission("tax_rate.view", MOBILE));
            arrayListPermission.add(new Permission("tax_rate.add", MOBILE));
            arrayListPermission.add(new Permission("tax_rate.edit", MOBILE));
            arrayListPermission.add(new Permission("tax_rate.delete", MOBILE));

            arrayListPermission.add(new Permission("unit.view", MOBILE));
            arrayListPermission.add(new Permission("unit.add", MOBILE));
            arrayListPermission.add(new Permission("unit.edit", MOBILE));
            arrayListPermission.add(new Permission("unit.delete", MOBILE));

            arrayListPermission.add(new Permission("view_cash_register", MOBILE));
            arrayListPermission.add(new Permission("close_cash_register", MOBILE));

            arrayListPermission.add(new Permission(BUSINESS_SETTING_ACCESS, MOBILE));
            arrayListPermission.add(new Permission(EXPENSE_ACCESS, MOBILE));
            arrayListPermission.add(new Permission(REPORT_ACCESS, MOBILE));

            arrayListPermission.add(new Permission(BUSINESS_LOCATION_VIEW, MOBILE));
            arrayListPermission.add(new Permission(BUSINESS_LOCATION_ADD, MOBILE));
            arrayListPermission.add(new Permission(BUSINESS_LOCATION_EDIT, MOBILE));
            arrayListPermission.add(new Permission(BUSINESS_LOCATION_DELETE, MOBILE));

            arrayListPermission.add(new Permission(VARIATION_VIEW, MOBILE));
            arrayListPermission.add(new Permission(VARIATION_ADD, MOBILE));
            arrayListPermission.add(new Permission(VARIATION_EDIT, MOBILE));
            arrayListPermission.add(new Permission(VARIATION_DELETE, MOBILE));

            arrayListPermission.add(new Permission("profit_loss_report.view", MOBILE));
            arrayListPermission.add(new Permission("stock_report.view", MOBILE));

            arrayListPermission.add(new Permission(SELL_RETURN_ACCESS, MOBILE));

            arrayListPermission.add(new Permission(STOCK_TRANSFER_ACCESS, MOBILE));
            arrayListPermission.add(new Permission(STOCK_ADJUSTEMENT_ACCESS, MOBILE));

            arrayListPermission.add(new Permission(WARRANTY_VIEW, MOBILE));
            arrayListPermission.add(new Permission(WARRANTY_ADD, MOBILE));
            arrayListPermission.add(new Permission(WARRANTY_EDIT, MOBILE));
            arrayListPermission.add(new Permission(WARRANTY_DELETE, MOBILE));



            permissionDbController.fill(arrayListPermission);

            //Create default role Admin
            Role role = new Role();
            role.setName("admin");
            role.setGuard_name(MOBILE);
            role.setBusiness_id(1);
            role.setIs_default(1);
            role.setIs_service_staff(0);
            int role_id = roleDbController.insertLocal(role);

            User user = new User();
            user.setUsername("rht");
            user.setFirst_name("rht");
            user.setLast_name("");
            user.setPassword("qwerty");
            user.setStatus("true");
            user.setEmail("<EMAIL>");
            int user_id = userDbController.insertLocal(user);

            role.setUser_id(user_id);
            role.setRole_id(role_id);
            userHasRolesDbController.insertLocal(role);
            //Business access
          //  role.setPermission_id(46);
        //    roleHasPermissionDbController.insertLocal(role);
            //create default user role with all permission
          //  for (Permission permission : permissionDbController.getAllPermissions()) {
            for (int i =1 ; i<66; i++) {
                role.setPermission_id(i);
                roleHasPermissionDbController.insertLocal(role);
            }


        }
    }

    @Override
    protected void onStart() {
        super.onStart();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
    }

    @Override
    protected void onStop() {
        super.onStop();
    }
}
