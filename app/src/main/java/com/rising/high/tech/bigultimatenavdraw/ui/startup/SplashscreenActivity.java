package com.rising.high.tech.bigultimatenavdraw.ui.startup;

import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.Window;
import android.view.WindowManager;
import androidx.appcompat.app.AppCompatActivity;
import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.ui.NaviMainActivity;
import com.rising.high.tech.bigultimatenavdraw.util.Constant;
import com.rising.high.tech.bigultimatenavdraw.util.SessionManager;

import java.util.Arrays;
import java.util.stream.Collectors;

public class SplashscreenActivity extends AppCompatActivity {
    private static final String TAG = "SplashscreenActivity";
    SessionManager session;
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        requestWindowFeature(Window.FEATURE_NO_TITLE);
        this.getWindow().setFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN, WindowManager.LayoutParams.FLAG_FULLSCREEN);
        setContentView(R.layout.activity_splashscreen);


        //remove this line
       // startActivity(new Intent(SplashscreenActivity.this, LoginActivity.class));
        goToNextScreen();
        session = new SessionManager(getApplicationContext());
       // throw new RuntimeException("Test Crash"); // Force a crash

    }
    @Override
    protected void onStart() {
        super.onStart();
    }
    private void goToNextScreen() {
        int SPLASH_DISPLAY_LENGTH = 1000;
        new Handler(Looper.getMainLooper()).postDelayed(new Runnable() {
            @Override
            public void run() {
                if(session.isLoggedIn())
                {
                    startActivity(new Intent(SplashscreenActivity.this, NaviMainActivity.class));
                }
                else
                {
                    startActivity(new Intent(SplashscreenActivity.this, LoginActivity.class));
                }
                overridePendingTransition(R.anim.in_from_right, R.anim.out_to_left);
                finish();
            }
        }, SPLASH_DISPLAY_LENGTH);
    }
    @Override
    protected void onDestroy() {
        super.onDestroy();
    }
    @Override
    protected void onStop() {
        super.onStop();
    }

}
