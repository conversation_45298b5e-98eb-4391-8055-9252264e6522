package com.rising.high.tech.bigultimatenavdraw.ui.stockadjustement;

import android.app.DatePickerDialog;
import android.content.Context;
import android.content.Intent;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.AutoCompleteTextView;
import android.widget.Button;
import android.widget.DatePicker;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.material.snackbar.Snackbar;
import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.db.ProductDbController;
import com.rising.high.tech.bigultimatenavdraw.db.BusinessLocationDbController;
import com.rising.high.tech.bigultimatenavdraw.db.StockAdjustementLineDbController;
import com.rising.high.tech.bigultimatenavdraw.db.TransactionDbController;
import com.rising.high.tech.bigultimatenavdraw.db.VariationLocationDetailDbController;
import com.rising.high.tech.bigultimatenavdraw.db.VariationsDbController;
import com.rising.high.tech.bigultimatenavdraw.model.Product;
import com.rising.high.tech.bigultimatenavdraw.model.Sell_lines;
import com.rising.high.tech.bigultimatenavdraw.model.Business_location;
import com.rising.high.tech.bigultimatenavdraw.model.Transaction;
import com.rising.high.tech.bigultimatenavdraw.model.Variation;
import com.rising.high.tech.bigultimatenavdraw.model.Variation_location_details;
import com.rising.high.tech.bigultimatenavdraw.ui.adapter.SpinStationAdapter;
import com.rising.high.tech.bigultimatenavdraw.ui.adapter.VariationLocationDetailsTransfertAdapter;
import com.rising.high.tech.bigultimatenavdraw.util.FileUtil;
import com.rising.high.tech.bigultimatenavdraw.util.ProductUtil;
import com.rising.high.tech.bigultimatenavdraw.util.SessionManager;
import com.rising.high.tech.bigultimatenavdraw.util.StringFormat;

import java.util.ArrayList;
import java.util.Calendar;


import static com.rising.high.tech.bigultimatenavdraw.util.Constant.STOCK_ADJUSTMENT;

public class AddStockAdjustementFragment extends Fragment {
    private static final String TAG = "AddStockAdjustementFragment";
    private Context _context;
    private SpinStationAdapter spinStationAdapter;
    private BusinessLocationDbController businessLocationDbController;
    private ProductDbController productDbController;
    private VariationLocationDetailDbController variationLocationDetailDbController;
    private VariationsDbController variationsDbController;
    private StockAdjustementLineDbController stockAdjustementLineDbController;
    private TransactionDbController transactionDbController;
    private VariationLocationDetailsTransfertAdapter variationLocationDetailsTransfertAdapter;
    final Calendar c = Calendar.getInstance();
    SessionManager session;
    private Integer userId;
    Button btnBack;
    AutoCompleteTextView searchEdit;
    Spinner spinnerLocation;
    Spinner spinnerAdjustmentType;
    RecyclerView recycle_product;
    Button addBtn;
    EditText referenceNo;
    EditText dateTransaction;
    EditText additionalNote;
    EditText totalAmountRecovered;
    TextView totalItemsTxt;
    TextView netTotalAmountTxt;
    LinearLayout container_sub_product;
    LinearLayout linearLayout;

    public AddStockAdjustementFragment() {
        // Required empty public constructor
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        // Inflate the layout for this fragment
        View root = inflater.inflate(R.layout.fragment_add_stock_adjustement_main, container, false);
        _context = getContext();
        session = new SessionManager(_context);

        variationLocationDetailsTransfertAdapter = new VariationLocationDetailsTransfertAdapter();
        recycle_product.setAdapter(variationLocationDetailsTransfertAdapter);
        recycle_product.setLayoutManager(new LinearLayoutManager(_context));

        initDB();
        initSpinners();
        initListners();
        setProductSearchDapter(productDbController.getAllProduct());

        return root;
    }

    private void setProductSearchDapter(ArrayList<Product> arrayListTemp) {
        ArrayAdapter<Product> adapter = new ArrayAdapter<Product>(_context, android.R.layout.simple_list_item_1, arrayListTemp);
        searchEdit.setAdapter(adapter);
        searchEdit.setDropDownBackgroundDrawable(new ColorDrawable(_context.getResources().getColor(R.color.colorPrimary)));
        searchEdit.setOnItemClickListener(new AdapterView.OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> parent, View arg1, int pos, long id) {
                Product selected = (Product) parent.getAdapter().getItem(pos);
                Business_location businesslocation = (Business_location) spinnerLocation.getSelectedItem();

                if (spinnerLocation.getSelectedItemPosition() == 0) {
                    Snackbar snackbar = Snackbar.make(linearLayout, _context.getResources().getString(R.string.lbl_please_select_station), Snackbar.LENGTH_LONG);
                    snackbar.show();
                }else if (!variationLocationDetailDbController.isProductHasVariationInStation(businesslocation.getId(), selected.getId())) {
                    Snackbar snackbar = Snackbar.make(linearLayout, _context.getResources().getString(R.string.label_empty_add_product_to) + businesslocation.getName(), Snackbar.LENGTH_LONG);
                    snackbar.show();
                } else{
                    Variation_location_details variation_location_details = variationLocationDetailDbController.getVariationLocationDetailsByStationIdProductId(businesslocation.getId(), selected.getId());
                    if(variation_location_details.getQty_available()==0) {
                        Snackbar snackbar = Snackbar.make(linearLayout, _context.getResources().getString(R.string.label_empty_product), Snackbar.LENGTH_LONG);
                        snackbar.show();
                    }else {
                        variation_location_details.setQuantity("0");
                        variationLocationDetailsTransfertAdapter.updateData(variation_location_details);
                        container_sub_product.setVisibility(View.VISIBLE);
                    }
                }
                searchEdit.setText("");

            }
        });
    }

    private void initDB() {
        businessLocationDbController = new BusinessLocationDbController(_context);

        productDbController = new ProductDbController(_context);

        variationLocationDetailDbController = new VariationLocationDetailDbController(_context);

        variationsDbController= new VariationsDbController(_context);

        stockAdjustementLineDbController = new StockAdjustementLineDbController(_context);

        transactionDbController = new TransactionDbController(_context);
    }

    private void initListners() {
        referenceNo.setText(StringFormat.generateInvoiceStockAdjferNo(_context));
        dateTransaction.setText(StringFormat.populateSetFullDate(c.get(Calendar.YEAR), c.get(Calendar.MONTH), c.get(Calendar.DAY_OF_MONTH), c.get(Calendar.HOUR), c.get(Calendar.MINUTE)));
        dateTransaction.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                DatePickerDialog datePickerDialog = new DatePickerDialog(_context,
                        new DatePickerDialog.OnDateSetListener() {
                            @Override
                            public void onDateSet(DatePicker datePicker, int year, int month, int day) {
                                String myFormat = StringFormat.populateSetFullDate(year, month, day, c.get(Calendar.HOUR), c.get(Calendar.MINUTE));
                                dateTransaction.setText(myFormat);
                            }
                        }, c.get(Calendar.YEAR), c.get(Calendar.MONTH), c.get(Calendar.DAY_OF_MONTH));
                datePickerDialog.show();
            }
        });

        btnBack.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                replaceFragment(new StockAdjustementFragment());
            }
        });

        addBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

                if (spinnerLocation.getSelectedItemPosition() != 0 && spinnerAdjustmentType.getSelectedItemPosition() != 0 && variationLocationDetailsTransfertAdapter.getData().size()>0) {
                    Transaction stock_adjustement = new Transaction();
                    /**
                     * TODO : add bussines id
                     */
                    stock_adjustement.setBusiness_id(1);
                    Business_location businesslocation = (Business_location) spinnerLocation.getSelectedItem();
                    stock_adjustement.setLocation_id(businesslocation.getId());
                    stock_adjustement.setType(STOCK_ADJUSTMENT);
                    stock_adjustement.setAdjustment_type(spinnerAdjustmentType.getSelectedItem().toString());
                    stock_adjustement.setRef_no(referenceNo.getText().toString());
                    stock_adjustement.setTransaction_date(dateTransaction.getText().toString());
                    stock_adjustement.setAdditional_notes(additionalNote.getText().toString());
                    stock_adjustement.setTotal_amount_recovered(totalAmountRecovered.getText().toString());
                    stock_adjustement.setFinal_total(netTotalAmountTxt.getText().toString());

                    stock_adjustement.setCreated_by(userId);
                //    stock_adjustement.setCreated_by(1);

                    int idInsert = transactionDbController.insertLocal(stock_adjustement);
                    if (idInsert > 0) {
                        ArrayList<Variation_location_details> stock_adjustment_lines = variationLocationDetailsTransfertAdapter.getData();
                        for (Variation_location_details variation_location_detail : stock_adjustment_lines) {
                            Sell_lines sell_lines = new Sell_lines();
                            sell_lines.setTransaction_id(idInsert);
                            sell_lines.setProduct_id(variation_location_detail.getProduct_id());
                            sell_lines.setVariation_id(variation_location_detail.getProduct_id());
                            sell_lines.setQuantity(variation_location_detail.getSell_qty());
                            Product product = productDbController.getProductById(variation_location_detail.getProduct_id());
                            Variation variation= variationsDbController.getVariationByProductId(product.getId());
                            sell_lines.setUnit_price(variation.getDefault_sell_price());
                            int indexId = stockAdjustementLineDbController.insertLocal(sell_lines);
                            if (indexId > 0) {
                                //decrease quantiy for sell transaction
                                variationLocationDetailDbController.updateSellQty(variation_location_detail.getProduct_id(), businesslocation.getId(), variation_location_detail.getSell_qty());

                                replaceFragment(new StockAdjustementFragment());
                                //increase quantiy for purchase transaction
                                //  variationLocationDetailDbController.updatePurchaseQty(variation_location_details.getProduct_id(), stationTo.getId(), variation_location_details.getQty_available());
                            }

                        }

                        if (idInsert > 0) {
                            FileUtil.showDialog(_context,"Successful",getResources().getString(R.string.stock_adj_success));
                            replaceFragment(new StockAdjustementFragment());
                        }

                    }

                }else{
                    Toast.makeText(_context, getResources().getString(R.string.label_all_field_required), Toast.LENGTH_LONG).show();
                }
            }
        });

        variationLocationDetailsTransfertAdapter.setOnDataChangeListener(new VariationLocationDetailsTransfertAdapter.OnDataChangeListener() {
            @Override
            public void onDataChanged(ArrayList<Variation_location_details> variation_location_details) {
                ArrayList<Product> arrayList = new ArrayList<>();
                for (Variation_location_details variation_location_details1 : variation_location_details) {
                    Product product = productDbController.getProductById(variation_location_details1.getProduct_id());
                    product.setSell_qte(variation_location_details1.getSell_qty());
                    arrayList.add(product);
                }
                netTotalAmountTxt.setText(ProductUtil.getTotalAmountDpp(arrayList, _context));
                totalItemsTxt.setText(variation_location_details.size() + "");
            }
        });

    }

    private void initSpinners() {
        userId = (int) session.getUserDetails().get(session.ID_USER);

        spinStationAdapter = new SpinStationAdapter(_context, android.R.layout.simple_spinner_item, businessLocationDbController.getAllStationSpinner());
        spinnerLocation.setAdapter(spinStationAdapter);
    }

    public void replaceFragment(Fragment someFragment) {
        FragmentTransaction transaction = getFragmentManager().beginTransaction();
        transaction.replace(R.id.nav_host_fragment, someFragment);
        transaction.addToBackStack(null);
        transaction.commit();
    }
}