package com.rising.high.tech.bigultimatenavdraw.ui.stockadjustement.adapter;

import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.Spinner;
import android.widget.TextView;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.gson.Gson;
import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.db.PurchaseLineDbController;
import com.rising.high.tech.bigultimatenavdraw.db.BusinessLocationDbController;
import com.rising.high.tech.bigultimatenavdraw.db.StockAdjustementLineDbController;
import com.rising.high.tech.bigultimatenavdraw.db.TransactionDbController;
import com.rising.high.tech.bigultimatenavdraw.db.TransactionSellLineDbController;
import com.rising.high.tech.bigultimatenavdraw.db.UserDbController;
import com.rising.high.tech.bigultimatenavdraw.db.VariationLocationDetailDbController;
import com.rising.high.tech.bigultimatenavdraw.model.Purchase_line;
import com.rising.high.tech.bigultimatenavdraw.model.Sell_lines;
import com.rising.high.tech.bigultimatenavdraw.model.Business_location;
import com.rising.high.tech.bigultimatenavdraw.model.Transaction;
import com.rising.high.tech.bigultimatenavdraw.model.User;
import com.rising.high.tech.bigultimatenavdraw.ui.purchase.adapter.SubPurchaseAdapter;
import com.rising.high.tech.bigultimatenavdraw.util.SessionManager;

import java.util.ArrayList;
import java.util.Collections;

import static com.rising.high.tech.bigultimatenavdraw.util.Constant.IN_TRANSIT;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.PENDING;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.RECEIVED;
import static com.rising.high.tech.bigultimatenavdraw.util.SessionManager.KEY_NAME;

public class StockAdjustementAdapter extends RecyclerView.Adapter<StockAdjustementAdapter.VenteViewHolder> {
    private final String TAG = this.getClass().getSimpleName();
    private ArrayList<Transaction> dataList = new ArrayList<>();
    private BusinessLocationDbController businessLocationDbController;
    private TransactionDbController transactionDbController;
    private PurchaseLineDbController purchaseLineDbController;
    private TransactionSellLineDbController transactionSellLineDbController;
    private StockAdjustementLineDbController stockAdjustementLineDbController;
    private VariationLocationDetailDbController variationLocationDetailDbController;
    private UserDbController userDbController;
    Context context;
    SessionManager session;

    @Override
    public VenteViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {

        context = parent.getContext();
        session = new SessionManager(context);

        businessLocationDbController = new BusinessLocationDbController(context);
        transactionDbController = new TransactionDbController(context);
        purchaseLineDbController = new PurchaseLineDbController(context);
        transactionSellLineDbController = new TransactionSellLineDbController(context);
        stockAdjustementLineDbController = new StockAdjustementLineDbController(context);
        variationLocationDetailDbController= new VariationLocationDetailDbController(context);
        userDbController= new UserDbController(context);

        return new VenteViewHolder(LayoutInflater.from(parent.getContext())
                .inflate(R.layout.stock_adjustment_item_layout, parent, false));
    }

    @Override
    public void onBindViewHolder(VenteViewHolder holder, int position) {

        holder.dateTxt.setText(dataList.get(position).getTransaction_date());
        holder.factureNo.setText(dataList.get(position).getRef_no());
        String locationTo = businessLocationDbController.getStationById(dataList.get(position).getLocation_id()).getName();
        holder.location.setText(locationTo);
        holder.montant_total.setText(dataList.get(position).getFinal_total());
        holder.note.setText(dataList.get(position).getAdditional_notes());
        holder.adjustment_type.setText(dataList.get(position).getAdjustment_type());
        holder.total_amount_recovred.setText(dataList.get(position).getTotal_amount_recovered());
        holder.note.setText(dataList.get(position).getAdditional_notes());

        User user=userDbController.getUsersById(dataList.get(position).getCreated_by());
        holder.added_by.setText(user.getUsername());

    }

    private void editStatus(int position) {
        LayoutInflater li = LayoutInflater.from(context);
        View promptsView = li.inflate(R.layout.edir_status_stocktransfert_dialog_main, null);
        androidx.appcompat.app.AlertDialog.Builder alertDialogBuilder = new androidx.appcompat.app.AlertDialog.Builder(context);

        alertDialogBuilder.setView(promptsView);

        final Button ButtonSave = promptsView.findViewById(R.id.btn_save);
        final Button ButtonClose = promptsView.findViewById(R.id.btn_close);

        final Spinner spin_status = promptsView.findViewById(R.id.spin_status);

        final androidx.appcompat.app.AlertDialog mAlertDialog = alertDialogBuilder.create();
        if (mAlertDialog.getWindow() != null)
            mAlertDialog.getWindow().getAttributes().windowAnimations = R.style.SlidingDialogAnimation;
        mAlertDialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));

        Transaction stock_transfert = transactionDbController.getTransactionById(dataList.get(position).getId());

        ButtonClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mAlertDialog.dismiss();
            }
        });

        ButtonSave.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (spin_status.getSelectedItemPosition() == 1)
                    stock_transfert.setStatus(PENDING);
                if (spin_status.getSelectedItemPosition() == 2)
                    stock_transfert.setStatus(IN_TRANSIT);
                if (spin_status.getSelectedItemPosition() == 3) {
                    stock_transfert.setStatus(RECEIVED);
                }
                dataList.get(position).setStatus(stock_transfert.getStatus());
                notifyDataSetChanged();
                transactionDbController.updateTransaction(stock_transfert);
                mAlertDialog.dismiss();
            }
        });

        mAlertDialog.show();

    }

    private void deleteItem(int position) {
        // Preparing views
        LayoutInflater li = LayoutInflater.from(context);
        View promptsView = li.inflate(R.layout.delete_dialog_main, null);

        androidx.appcompat.app.AlertDialog.Builder alertDialogBuilder = new androidx.appcompat.app.AlertDialog.Builder(
                context);

        alertDialogBuilder.setView(promptsView);

        final Button ButtonSave = promptsView.findViewById(R.id.btn_save);
        final Button ButtonClose = promptsView.findViewById(R.id.btn_close);

        final androidx.appcompat.app.AlertDialog mAlertDialog = alertDialogBuilder.create();
        if (mAlertDialog.getWindow() != null)
            mAlertDialog.getWindow().getAttributes().windowAnimations = R.style.SlidingDialogAnimation;
        mAlertDialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));

        ButtonClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mAlertDialog.dismiss();
            }
        });

        ButtonSave.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

                ArrayList<Sell_lines> sell_lines = stockAdjustementLineDbController.getStockAdjustmentByTransaction(dataList.get(position).getId());
                for (Sell_lines sell_lines1 : sell_lines) {
                    variationLocationDetailDbController.updatePurchaseQty(sell_lines1.getProduct_id(), dataList.get(position).getLocation_id(), sell_lines1.getQuantity());
                    stockAdjustementLineDbController.deleteRow(sell_lines1.getId());
                }

                transactionDbController.deleteTransaction(dataList.get(position).getId());
                dataList.remove(position);
                notifyDataSetChanged();
                mAlertDialog.dismiss();
            }
        });


        mAlertDialog.show();
    }

    public void setData(ArrayList<Transaction> arrayList) {
        Collections.reverse(arrayList);
        this.dataList = arrayList;
        notifyDataSetChanged();
    }

    @Override
    public int getItemCount() {
        return dataList.size();
    }

    public class VenteViewHolder extends RecyclerView.ViewHolder {
        TextView dateTxt, factureNo, location_from, location, adjustment_type, montant_total, note, total_amount_recovred, added_by;
        Spinner spinner_action;

        public VenteViewHolder(View itemView) {
            super(itemView);

            dateTxt = itemView.findViewById(R.id.id_date);
            factureNo = itemView.findViewById(R.id.facture_no);
            location_from = itemView.findViewById(R.id.location_from);
            location = itemView.findViewById(R.id.location);
            montant_total = itemView.findViewById(R.id.montant_total);
            note = itemView.findViewById(R.id.note);
            total_amount_recovred = itemView.findViewById(R.id.total_amount_recovred);
            note = itemView.findViewById(R.id.note);
            adjustment_type = itemView.findViewById(R.id.adjustment_type);
            added_by = itemView.findViewById(R.id.added_by);
            spinner_action = itemView.findViewById(R.id.spinner_action);

            spinner_action.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
                @Override
                public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                    switch (position) {
                        case 1: {
                            viewDetail(getAdapterPosition());
                            spinner_action.setSelection(0, true);
                            break;
                        }
                        case 2: {
                            deleteItem(getAdapterPosition());
                            spinner_action.setSelection(0, true);
                            break;
                        }
                    }
                }

                @Override
                public void onNothingSelected(AdapterView<?> parent) {

                }
            });
        }
    }

    private void viewDetail(int position) {
        //Preparing views
        // get prompts.xml view

        LayoutInflater li = LayoutInflater.from(context);
        View promptsView = li.inflate(R.layout.view_stock_adjustment_dialog_main, null);

        androidx.appcompat.app.AlertDialog.Builder alertDialogBuilder = new androidx.appcompat.app.AlertDialog.Builder(
                context);

        alertDialogBuilder.setView(promptsView);
        SubPurchaseAdapter subPurchaseAdapter = new SubPurchaseAdapter();

        final Button ButtonSave = promptsView.findViewById(R.id.btn_save);
        final ImageView ButtonClose = promptsView.findViewById(R.id.btn_close);

        final TextView business_location = promptsView.findViewById(R.id.business_location);
        final RecyclerView recyclerView = promptsView.findViewById(R.id.recycle_sub_vente);
        final TextView label_reference_num = promptsView.findViewById(R.id.label_reference_num);
        final TextView purchase_date = promptsView.findViewById(R.id.purchase_date);
        final TextView adjustement_type = promptsView.findViewById(R.id.adjustement_type);
        final TextView location_from = promptsView.findViewById(R.id.location_from);
        final TextView phone_from = promptsView.findViewById(R.id.phone_from);
        final TextView reason = promptsView.findViewById(R.id.reason);

        recyclerView.setAdapter(subPurchaseAdapter);
        recyclerView.setLayoutManager(new LinearLayoutManager(context));

        ArrayList<Sell_lines> sell_lines = stockAdjustementLineDbController.getStockAdjustmentByTransaction(dataList.get(position).getId());
        ArrayList<Purchase_line> purchase_lines = new ArrayList<>();
        for (Sell_lines sell_lines1 : sell_lines) {
            Purchase_line purchase_line = new Purchase_line();
            purchase_line.setQuantity(sell_lines1.getQuantity());
            purchase_line.setProduct_id(sell_lines1.getProduct_id());
            purchase_line.setPurchase_price(sell_lines1.getUnit_price());
            purchase_lines.add(purchase_line);
        }
        subPurchaseAdapter.setData(purchase_lines);

        Business_location businesslocation = businessLocationDbController.getStationById(dataList.get(position).getLocation_id());

        business_location.setText(businesslocation.getName() + ", " + businesslocation.getCity() + " " + businesslocation.getCountry());
        label_reference_num.setText(dataList.get(position).getRef_no());
        purchase_date.setText(dataList.get(position).getTransaction_date());
        adjustement_type.setText(dataList.get(position).getAdjustment_type());
        reason.setText(dataList.get(position).getNote());


        Transaction transaction = transactionDbController.getTransactionById(dataList.get(position).getTransfer_parent_id());
        Business_location locationFrom = businessLocationDbController.getStationById(transaction.getLocation_id());


        location_from.setText(locationFrom.getName() + ", " + locationFrom.getCountry() + " " + locationFrom.getCity());
        phone_from.setText("");
        //  final ImageView image_product = promptsView.findViewById(R.id.image_product);

        final androidx.appcompat.app.AlertDialog mAlertDialog = alertDialogBuilder.create();
        if (mAlertDialog.getWindow() != null)
            mAlertDialog.getWindow().getAttributes().windowAnimations = R.style.SlidingDialogAnimation;
        mAlertDialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));

        ButtonClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mAlertDialog.dismiss();
            }
        });


        mAlertDialog.show();
    }


    private void naviguateFragment(int position) {

    }

    public interface OnDataChangeListener {
        void onDataChanged(Transaction transaction);
    }

    OnDataChangeListener mOnDataChangeListener;

    public void setOnDataChangeListener(OnDataChangeListener onDataChangeListener) {
        mOnDataChangeListener = onDataChangeListener;
    }

}
