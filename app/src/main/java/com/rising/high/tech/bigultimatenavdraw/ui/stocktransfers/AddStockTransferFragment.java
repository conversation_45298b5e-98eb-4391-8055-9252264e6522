package com.rising.high.tech.bigultimatenavdraw.ui.stocktransfers;

import android.app.DatePickerDialog;
import android.content.Context;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.AutoCompleteTextView;
import android.widget.Button;
import android.widget.DatePicker;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.material.snackbar.Snackbar;
import com.google.gson.Gson;
import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.db.ProductDbController;
import com.rising.high.tech.bigultimatenavdraw.db.PurchaseLineDbController;
import com.rising.high.tech.bigultimatenavdraw.db.BusinessLocationDbController;
import com.rising.high.tech.bigultimatenavdraw.db.TransactionDbController;
import com.rising.high.tech.bigultimatenavdraw.db.TransactionSellLineDbController;
import com.rising.high.tech.bigultimatenavdraw.db.VariationLocationDetailDbController;
import com.rising.high.tech.bigultimatenavdraw.db.VariationsDbController;
import com.rising.high.tech.bigultimatenavdraw.model.Business_location;
import com.rising.high.tech.bigultimatenavdraw.model.Product;
import com.rising.high.tech.bigultimatenavdraw.model.Purchase_line;
import com.rising.high.tech.bigultimatenavdraw.model.Sell_lines;
import com.rising.high.tech.bigultimatenavdraw.model.Transaction;
import com.rising.high.tech.bigultimatenavdraw.model.Variation;
import com.rising.high.tech.bigultimatenavdraw.model.Variation_location_details;
import com.rising.high.tech.bigultimatenavdraw.ui.adapter.SpinStationAdapter;
import com.rising.high.tech.bigultimatenavdraw.ui.adapter.VariationLocationDetailsTransfertAdapter;
import com.rising.high.tech.bigultimatenavdraw.util.FileUtil;
import com.rising.high.tech.bigultimatenavdraw.util.ProductUtil;
import com.rising.high.tech.bigultimatenavdraw.util.SessionManager;
import com.rising.high.tech.bigultimatenavdraw.util.StringFormat;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;


import static com.rising.high.tech.bigultimatenavdraw.util.Constant.FINAL;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.ID;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.IN_TRANSIT;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.PAID;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.PENDING;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.PURCHASE_TRANSFER;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.RECEIVED;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.SELL_TRANSFER;

public class AddStockTransferFragment extends Fragment {
    private static final String TAG = "AddStockTransferFragment";
    private Context _context;
    final Calendar c = Calendar.getInstance();
    private SpinStationAdapter spinStationAdapter;
    private BusinessLocationDbController businessLocationDbController;
    private ProductDbController productDbController;
    private VariationLocationDetailDbController variationLocationDetailDbController;
    private TransactionDbController transactionDbController;
    private TransactionSellLineDbController transactionSellLineDbController;
    private PurchaseLineDbController purchaseLineDbController;
    private VariationsDbController variationsDbController;
    private VariationLocationDetailsTransfertAdapter variationLocationDetailsTransfertAdapter;

    EditText stockDate;
    EditText referenceNo;
    Spinner spinnerPurchaseStatus;
    Spinner spinnerLocationFrom;
    Spinner spinnerLocationTo;
    AutoCompleteTextView searchEdit;
    RecyclerView recycle_product;
    Button addBtn;
    Button btnBack;
    TextView totalItemsTxt;
    TextView netTotalAmountTxt;
    TextView additionalNote;
    LinearLayout container_sub_product;
    EditText shipping_detail_txt;
    LinearLayout linearLayout;
    private Boolean isEdit = false;
    private Integer indexId = 0;
    SessionManager session;
    private Integer userId;

    public AddStockTransferFragment(boolean isEdit) {
        // Required empty public constructor
        this.isEdit = isEdit;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        // Inflate the layout for this fragment
        View root = inflater.inflate(R.layout.fragment_add_stock_transfer_main, container, false);
        _context = getContext();
        session = new SessionManager(_context);

        if (isEdit) {
            Bundle args = getArguments();
            indexId = args.getInt(ID, 0);
        }

        variationLocationDetailsTransfertAdapter = new VariationLocationDetailsTransfertAdapter();
        recycle_product.setAdapter(variationLocationDetailsTransfertAdapter);
        recycle_product.setLayoutManager(new LinearLayoutManager(_context));

        initDB();
        initSpinners();
        initListners();
        setProductSearchDapter(productDbController.getAllProduct());
        setData();
        userId = (int) session.getUserDetails().get(session.ID_USER);
        return root;
    }

    private void setData() {

        if (isEdit) {
            Transaction transactionStockTransfer = transactionDbController.getTransactionById(indexId);
            addBtn.setText(getResources().getString(R.string.label_updatee));
            stockDate.setText(transactionStockTransfer.getTransaction_date());
            if (transactionStockTransfer.getStatus() != null) {
                String status = transactionStockTransfer.getStatus();
                if (transactionStockTransfer.getStatus().matches("in_transit"))
                    status = "in transit";
                int indexP = Arrays.asList(getResources().getStringArray(R.array.array_transfer_stock_status)).indexOf(status);
                spinnerPurchaseStatus.setSelection(indexP);
            }
            // getting station from
            Transaction transactionFrom = transactionDbController.getTransactionById(transactionStockTransfer.getTransfer_parent_id());
            Business_location business_locationFrom = businessLocationDbController.getStationById(transactionFrom.getLocation_id());
            int spinnerPositionFrom = spinStationAdapter.getPosition(business_locationFrom);
            spinnerLocationFrom.setSelection(spinnerPositionFrom);

            // getting station from
            Business_location business_locationTo = businessLocationDbController.getStationById(transactionStockTransfer.getLocation_id());
            int spinnerPositionTo = spinStationAdapter.getPosition(business_locationTo);
            spinnerLocationTo.setSelection(spinnerPositionTo);
            shipping_detail_txt.setText(transactionStockTransfer.getShipping_charges());
            additionalNote.setText(transactionStockTransfer.getAdditional_notes());
            // variationLocationDetailsTransfertAdapter.setd


            //getting sell line to get transfered products
            ArrayList<Sell_lines> sell_lines = transactionSellLineDbController.getSellLineByTransaction(transactionStockTransfer.getTransfer_parent_id());

            ArrayList<Variation_location_details> variationLocationDetailsArrayList = new ArrayList<>();
            for (Sell_lines sell_lines1 : sell_lines) {
                Variation_location_details variation_location_details1 = new Variation_location_details();
                variation_location_details1.setProduct_id(sell_lines1.getProduct_id());
                variation_location_details1.setVariation_id(sell_lines1.getVariation_id());
                variation_location_details1.setQuantity(sell_lines1.getQuantity() + "");
                variation_location_details1.setLocation_id(transactionFrom.getLocation_id());
                variationLocationDetailsArrayList.add(variation_location_details1);

                Variation_location_details variation_location_details = variationLocationDetailDbController.getVariationLocationDetailsByStationIdProductId(transactionFrom.getLocation_id(), sell_lines1.getProduct_id());
                variation_location_details1.setOld_qty_available(variation_location_details.getQty_available());

            }
            variationLocationDetailsTransfertAdapter.setData(variationLocationDetailsArrayList);

            ArrayList<Product> arrayList = new ArrayList<>();
            for (Variation_location_details variation_location_details1 : variationLocationDetailsArrayList) {
                Product product = productDbController.getProductById(variation_location_details1.getProduct_id());
                product.setSell_qte(Integer.parseInt(variation_location_details1.getQuantity()));
                arrayList.add(product);
            }

            netTotalAmountTxt.setText(ProductUtil.getTotalAmountDpp(arrayList, _context));
            totalItemsTxt.setText(arrayList.size() + "");
        }
    }

    private void initDB() {

        businessLocationDbController = new BusinessLocationDbController(_context);

        productDbController = new ProductDbController(_context);

        variationLocationDetailDbController = new VariationLocationDetailDbController(_context);

        transactionDbController = new TransactionDbController(_context);

        transactionSellLineDbController = new TransactionSellLineDbController(_context);

        purchaseLineDbController = new PurchaseLineDbController(_context);

        variationsDbController = new VariationsDbController(_context);
    }

    private void initSpinners() {
        spinStationAdapter = new SpinStationAdapter(_context, android.R.layout.simple_spinner_item, businessLocationDbController.getAllStationSpinner());
        spinnerLocationTo.setAdapter(spinStationAdapter);
        spinnerLocationFrom.setAdapter(spinStationAdapter);
    }


    private void initListners() {

        stockDate.setText(StringFormat.populateSetFullDate(c.get(Calendar.YEAR), c.get(Calendar.MONTH), c.get(Calendar.DAY_OF_MONTH), c.get(Calendar.HOUR), c.get(Calendar.MINUTE)));

        stockDate.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                DatePickerDialog datePickerDialog = new DatePickerDialog(_context,
                        new DatePickerDialog.OnDateSetListener() {
                            @Override
                            public void onDateSet(DatePicker datePicker, int year, int month, int day) {
                                String myFormat = StringFormat.populateSetFullDate(year, month, day, c.get(Calendar.HOUR), c.get(Calendar.MINUTE));
                                stockDate.setText(myFormat);
                            }
                        }, c.get(Calendar.YEAR), c.get(Calendar.MONTH), c.get(Calendar.DAY_OF_MONTH));
                datePickerDialog.show();
            }
        });

        addBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Business_location businesslocationFrom = (Business_location) spinnerLocationFrom.getSelectedItem();
                Business_location businesslocationTo = (Business_location) spinnerLocationTo.getSelectedItem();
                if (businesslocationFrom != businesslocationTo) {

                    if (!stockDate.getText().equals("")) {
                        if (spinnerPurchaseStatus.getSelectedItemPosition() == 0) {
                            Snackbar snackbar = Snackbar.make(linearLayout, _context.getResources().getString(R.string.label_select_statuus), Snackbar.LENGTH_LONG);
                            snackbar.show();
                        } else if (variationLocationDetailsTransfertAdapter.getData().size() == 0) {
                            Snackbar snackbar = Snackbar.make(linearLayout, _context.getResources().getString(R.string.label_add_product_dirst_required), Snackbar.LENGTH_LONG);
                            snackbar.show();
                        } else {
                            Transaction sell = new Transaction();

                            Transaction transactionStockTransfer = transactionDbController.getTransactionById(indexId);

                            int idSell, idPurchase;

                            sell.setBusiness_id(1);
                            sell.setPayment_status(PAID);
                            sell.setTransaction_date(stockDate.getText().toString());
                            sell.setTotal_before_tax(netTotalAmountTxt.getText().toString());
                            sell.setFinal_total(netTotalAmountTxt.getText().toString());
                            sell.setAdditional_notes(additionalNote.getText().toString());

                            /**
                             * TODO add user generic
                             */
                            sell.setCreated_by(userId);
                            //if completed we put final and received in purchase and sell
                            if (spinnerPurchaseStatus.getSelectedItemPosition() == 1)
                                sell.setStatus(PENDING);
                            if (spinnerPurchaseStatus.getSelectedItemPosition() == 2)
                                sell.setStatus(IN_TRANSIT);
                            if (spinnerPurchaseStatus.getSelectedItemPosition() == 3) {
                                sell.setStatus(FINAL);
                            }

                            sell.setLocation_id(businesslocationFrom.getId());
                            sell.setType(SELL_TRANSFER);
                            sell.setShipping_charges(shipping_detail_txt.getText().toString());

                            if (isEdit) {
                                sell.setId(transactionStockTransfer.getTransfer_parent_id());
                                idSell = transactionDbController.updateTransaction(sell);
                                //  idSell = 0;

                            } else {
                                sell.setRef_no(StringFormat.generateInvoiceSellTransferNo(_context));
                                idSell = transactionDbController.insertLocal(sell);
                            }


                            Transaction purchase = new Transaction();

                            /**
                             * TODO add business id generic
                             */
                            purchase.setBusiness_id(1);
                            purchase.setPayment_status(PAID);
                            purchase.setTransaction_date(stockDate.getText().toString());
                            purchase.setTotal_before_tax(netTotalAmountTxt.getText().toString());
                            purchase.setFinal_total(netTotalAmountTxt.getText().toString());
                            /**
                             * TODO add user generic
                             */
                            purchase.setCreated_by(userId);

                            //if completed we put final and received in purchase and sell
                            if (spinnerPurchaseStatus.getSelectedItemPosition() == 1)
                                purchase.setStatus(PENDING);
                            if (spinnerPurchaseStatus.getSelectedItemPosition() == 2)
                                purchase.setStatus(IN_TRANSIT);
                            if (spinnerPurchaseStatus.getSelectedItemPosition() == 3) {
                                purchase.setStatus(RECEIVED);
                            }

                            purchase.setLocation_id(businesslocationFrom.getId());
                            purchase.setType(SELL_TRANSFER);
                            purchase.setLocation_id(businesslocationTo.getId());
                            purchase.setType(PURCHASE_TRANSFER);
                            purchase.setAdditional_notes(additionalNote.getText().toString());
                            purchase.setShipping_charges(shipping_detail_txt.getText().toString());

                            if (isEdit) {
                                purchase.setTransfer_parent_id(indexId);
                                purchase.setId(indexId);
                                idPurchase = transactionDbController.updateTransaction(purchase);
                                ArrayList<Sell_lines> sell_lines = transactionSellLineDbController.getSellLineByTransaction(transactionStockTransfer.getTransfer_parent_id());
                                ArrayList<Purchase_line> purchase_lines = purchaseLineDbController.getPurchaseLineByTransaction(transactionStockTransfer.getId());

                                transactionSellLineDbController.deleteByTransaction(transactionStockTransfer.getTransfer_parent_id());
                                purchaseLineDbController.deletePurchaseByTransaction(transactionStockTransfer.getId());
                                for (int i = 0; i < variationLocationDetailsTransfertAdapter.getData().size(); i++) {
                                    //check sell_lines if already exist from first insert
                                    int currentQty = variationLocationDetailsTransfertAdapter.getData().get(i).getSell_qty();

                                    Sell_lines newSell_lines = new Sell_lines();
                                    newSell_lines.setTransaction_id(transactionStockTransfer.getTransfer_parent_id());
                                    newSell_lines.setProduct_id(variationLocationDetailsTransfertAdapter.getData().get(i).getProduct_id());
                                    newSell_lines.setVariation_id(variationLocationDetailsTransfertAdapter.getData().get(i).getProduct_id());
                                    newSell_lines.setQuantity(currentQty);
                                    Variation variation = variationsDbController.getVariationByProductId(variationLocationDetailsTransfertAdapter.getData().get(i).getProduct_id());
                                    newSell_lines.setUnit_price(variation.getDefault_sell_price());
                                    newSell_lines.setUnit_price_inc_tax(variation.getSell_price_inc_tax());
                                    newSell_lines.setUnit_price_before_discount(variation.getDefault_purchase_price());
                                    transactionSellLineDbController.insertLocal(newSell_lines);

                                    //insert into purchase line for the stations who receives the stock
                                    Purchase_line newPurchaseLine = new Purchase_line();
                                    newPurchaseLine.setTransaction_id(transactionStockTransfer.getId());
                                    newPurchaseLine.setProduct_id(variationLocationDetailsTransfertAdapter.getData().get(i).getProduct_id());
                                    newPurchaseLine.setVariation_id(variationLocationDetailsTransfertAdapter.getData().get(i).getProduct_id());
                                    newPurchaseLine.setQuantity(currentQty);
                                    newPurchaseLine.setPurchase_price(variation.getDefault_purchase_price());
                                    newPurchaseLine.setPurchase_price_inc_tax(variation.getDpp_inc_tax());
                                    purchaseLineDbController.insertLocal(newPurchaseLine);

                                    //  }

                                    if (spinnerPurchaseStatus.getSelectedItemPosition() == 3) {
                                        if (!variationLocationDetailsTransfertAdapter.getData().get(i).getQuantity().matches(sell_lines.get(i).getQuantity() + "")) {
                                            //decrease quantiy for sell transaction
                                            variationLocationDetailDbController.updateSellQty(sell_lines.get(i).getProduct_id(), businesslocationFrom.getId(), currentQty);
                                            //increase quantiy for purchase transaction
                                            variationLocationDetailDbController.updatePurchaseQty(sell_lines.get(i).getProduct_id(), businesslocationTo.getId(), currentQty);
                                        }
                                    }
                                }
                            } else {
                                purchase.setTransfer_parent_id(idSell);
                                purchase.setRef_no(StringFormat.generateInvoicePurchaseTransferNo(_context));
                                idPurchase = transactionDbController.insertLocal(purchase);
                                //insert into purchase_lines and transaction_sell_lines tables Update quantity in variation_location_detail
                                if (idSell > 0 && idPurchase > 0) {
                                    for (Variation_location_details variation_location_details : variationLocationDetailsTransfertAdapter.getData()) {
                                        //getting product unit price
                                        Product product = productDbController.getProductById(variation_location_details.getProduct_id());
                                        Variation variation = variationsDbController.getVariationByProductId(product.getId());
                                        //insert transaction sell line for selled stock
                                        Sell_lines sell_lines = new Sell_lines();
                                        sell_lines.setTransaction_id(idSell);
                                        sell_lines.setProduct_id(variation_location_details.getProduct_id());
                                        sell_lines.setVariation_id(variation_location_details.getProduct_id());
                                        sell_lines.setQuantity(variation_location_details.getSell_qty());

                                        sell_lines.setUnit_price(variation.getDefault_sell_price());
                                        sell_lines.setUnit_price_inc_tax(variation.getSell_price_inc_tax());
                                        sell_lines.setUnit_price_before_discount(variation.getDefault_purchase_price());

                                        transactionSellLineDbController.insertLocal(sell_lines);

                                        //insert into purchase line for the stations who receives the stock
                                        Purchase_line purchaseLine = new Purchase_line();
                                        purchaseLine.setTransaction_id(idPurchase);
                                        purchaseLine.setProduct_id(variation_location_details.getProduct_id());
                                        purchaseLine.setVariation_id(variation_location_details.getProduct_id());
                                        purchaseLine.setQuantity(variation_location_details.getSell_qty());
                                        purchaseLine.setPurchase_price(variation.getDefault_purchase_price());
                                        purchaseLine.setPurchase_price_inc_tax(variation.getDpp_inc_tax());
                                        purchaseLineDbController.insertLocal(purchaseLine);

                                        if (spinnerPurchaseStatus.getSelectedItemPosition() == 3) {
                                            //decrease quantiy for sell transaction
                                            variationLocationDetailDbController.updateSellQty(variation_location_details.getProduct_id(), businesslocationFrom.getId(), variation_location_details.getSell_qty());
                                            //increase quantiy for purchase transaction
                                            variationLocationDetailDbController.updatePurchaseQty(variation_location_details.getProduct_id(), businesslocationTo.getId(), variation_location_details.getSell_qty());
                                        }
                                    }
                                }
                            }
                            if (idPurchase > 0 && idSell > 0) {
                                FileUtil.showDialog(_context, "Successful", getResources().getString(R.string.stock_transfer_success));
                                replaceFragment(new StockTransferFragment());
                            }
                        }
                    } else {
                        Snackbar snackbar = Snackbar.make(linearLayout, _context.getResources().getString(R.string.label_all_field_required), Snackbar.LENGTH_LONG);
                        snackbar.show();
                    }
                } else {
                    Snackbar snackbar = Snackbar.make(linearLayout, _context.getResources().getString(R.string.label_select_different_station), Snackbar.LENGTH_LONG);
                    snackbar.show();
                }
            }
        });

        variationLocationDetailsTransfertAdapter.setOnDataChangeListener(new VariationLocationDetailsTransfertAdapter.OnDataChangeListener() {
            @Override
            public void onDataChanged(ArrayList<Variation_location_details> variation_location_details) {
                ArrayList<Product> arrayList = new ArrayList<>();
                for (Variation_location_details variation_location_details1 : variation_location_details) {
                    Product product = productDbController.getProductById(variation_location_details1.getProduct_id());
                    product.setSell_qte(variation_location_details1.getSell_qty());
                    arrayList.add(product);
                }
                netTotalAmountTxt.setText(ProductUtil.getTotalAmountDpp(arrayList, _context));
                totalItemsTxt.setText(variation_location_details.size() + "");
            }
        });

        btnBack.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                replaceFragment(new StockTransferFragment());
            }
        });
    }

    private void setProductSearchDapter(ArrayList<Product> arrayListTemp) {
        ArrayAdapter<Product> adapter = new ArrayAdapter<Product>(_context, android.R.layout.simple_list_item_1, arrayListTemp);
        searchEdit.setAdapter(adapter);
        searchEdit.setDropDownBackgroundDrawable(new ColorDrawable(_context.getResources().getColor(R.color.colorPrimary)));
        searchEdit.setOnItemClickListener(new AdapterView.OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> parent, View arg1, int pos, long id) {
                if (spinnerLocationFrom.getSelectedItemPosition() == 0) {
                    Snackbar snackbar = Snackbar.make(linearLayout, _context.getResources().getString(R.string.label_plz_select_locations_from), Snackbar.LENGTH_LONG);
                    snackbar.show();
                } else if (spinnerLocationTo.getSelectedItemPosition() == 0) {
                    Snackbar snackbar = Snackbar.make(linearLayout, _context.getResources().getString(R.string.label_plz_select_locations_to), Snackbar.LENGTH_LONG);
                    snackbar.show();
                } else {
                    Business_location businesslocationFrom = (Business_location) spinnerLocationFrom.getSelectedItem();
                    Business_location businesslocationTo = (Business_location) spinnerLocationTo.getSelectedItem();
                    if (businesslocationFrom == businesslocationTo) {
                        Snackbar snackbar = Snackbar.make(linearLayout, _context.getResources().getString(R.string.label_select_different_station), Snackbar.LENGTH_LONG);
                        snackbar.show();
                    } else {
                        Product selected = (Product) parent.getAdapter().getItem(pos);
                        Variation_location_details variation_location_details = variationLocationDetailDbController.getVariationLocationDetailsByStationIdProductId(businesslocationFrom.getId(), selected.getId());
                        if (variation_location_details.getQty_available() == 0) {
                            Snackbar snackbar = Snackbar.make(linearLayout, _context.getResources().getString(R.string.label_empty_product), Snackbar.LENGTH_LONG);
                            snackbar.show();
                        } else {
                            if (!variationLocationDetailDbController.isProductHasVariationInStation(businesslocationTo.getId(), selected.getId())) {
                                Snackbar snackbar = Snackbar.make(linearLayout, _context.getResources().getString(R.string.label_empty_add_product_to) +businesslocationTo.getName() , Snackbar.LENGTH_LONG);
                                snackbar.show();
                            } else {
                                variation_location_details.setQuantity("0");
                                variationLocationDetailsTransfertAdapter.updateData(variation_location_details);
                                container_sub_product.setVisibility(View.VISIBLE);
                            }
                        }
                    }
                }
                searchEdit.setText("");

            }
        });
    }

    public void replaceFragment(Fragment someFragment) {
        FragmentTransaction transaction = getFragmentManager().beginTransaction();
        transaction.replace(R.id.nav_host_fragment, someFragment);
        transaction.addToBackStack(null);
        transaction.commit();
    }
}