package com.rising.high.tech.bigultimatenavdraw.ui.stocktransfers;

import android.content.Context;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.TextView;

import androidx.appcompat.widget.SearchView;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.gson.Gson;
import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.db.TransactionDbController;
import com.rising.high.tech.bigultimatenavdraw.ui.stocktransfers.adapter.StockTransferAdapter;
import com.rising.high.tech.bigultimatenavdraw.util.SessionManager;

import butterknife.BindView;
import butterknife.ButterKnife;

import static com.rising.high.tech.bigultimatenavdraw.util.Constant.PURCHASE_TRANSFER;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.SERVER_MASTER;

public class StockTransferFragment extends Fragment {
    private static final String TAG = "StockTransferFragment";
    private Context _context;
    SessionManager session;
    private StockTransferAdapter stockTransferAdapter;
    private TransactionDbController transactionDbController;

    @BindView(R.id.btn_add)
    Button btnAdd;
    @BindView(R.id.recycler_list)
    RecyclerView recyclerList;
    @BindView(R.id.noItemFound)
    TextView noItemFound;
    @BindView(R.id.id_filter)
    Button btnFilter;
    @BindView(R.id.id_search_edit)
    EditText searchEdit;

    public StockTransferFragment() {
        // Required empty public constructor
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        // Inflate the layout for this fragment
        View root = inflater.inflate(R.layout.fragment_stock_transfer_main, container, false);
        ButterKnife.bind(this, root);
        _context = getContext();
        session = new SessionManager(_context);

        initDB();
        initListners();
        checkRoles();

        return root;
    }

    public void setItemView() {
        if (stockTransferAdapter.getItemCount() > 0) {
            recyclerList.setVisibility(View.VISIBLE);
            noItemFound.setVisibility(View.GONE);
        } else {
            recyclerList.setVisibility(View.GONE);
            noItemFound.setVisibility(View.VISIBLE);
        }
    }

    private void checkRoles()
    {
        if (session.getBoolean(SERVER_MASTER))
        {
            btnAdd.setVisibility(View.INVISIBLE);
        }
    }

    private void initDB(){
        transactionDbController = new TransactionDbController(_context);
        transactionDbController.open();
    }

    private void initListners() {
        stockTransferAdapter = new StockTransferAdapter();
        recyclerList.setAdapter(stockTransferAdapter);
        recyclerList.setLayoutManager(new LinearLayoutManager(_context));
        stockTransferAdapter.setData(transactionDbController.getTransactionType(PURCHASE_TRANSFER));
        setItemView();
        btnAdd.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                replaceFragment(new AddStockTransferFragment(false));
            }
        });

        btnFilter.setOnClickListener(v -> {
            if (searchEdit.toString().length() != 0){
                stockTransferAdapter.setData(transactionDbController.getTransactionLikeType(PURCHASE_TRANSFER, searchEdit.getText().toString()));
            }else {
                stockTransferAdapter.setData(transactionDbController.getTransactionType(PURCHASE_TRANSFER));
            }
            stockTransferAdapter.notifyDataSetChanged();
            setItemView();
        });
    }


    public void replaceFragment(Fragment someFragment) {
        FragmentTransaction transaction = getFragmentManager().beginTransaction();
        transaction.replace(R.id.nav_host_fragment, someFragment);
        transaction.addToBackStack(null);
        transaction.commit();
    }

}