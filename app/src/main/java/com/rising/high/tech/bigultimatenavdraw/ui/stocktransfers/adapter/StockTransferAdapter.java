package com.rising.high.tech.bigultimatenavdraw.ui.stocktransfers.adapter;

import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.AppCompatSpinner;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.gson.Gson;
import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.db.PurchaseLineDbController;
import com.rising.high.tech.bigultimatenavdraw.db.BusinessLocationDbController;
import com.rising.high.tech.bigultimatenavdraw.db.TransactionDbController;
import com.rising.high.tech.bigultimatenavdraw.db.TransactionSellLineDbController;
import com.rising.high.tech.bigultimatenavdraw.db.VariationLocationDetailDbController;
import com.rising.high.tech.bigultimatenavdraw.model.Purchase_line;
import com.rising.high.tech.bigultimatenavdraw.model.Sell_lines;
import com.rising.high.tech.bigultimatenavdraw.model.Business_location;
import com.rising.high.tech.bigultimatenavdraw.model.Transaction;
import com.rising.high.tech.bigultimatenavdraw.ui.purchase.adapter.SubPurchaseAdapter;
import com.rising.high.tech.bigultimatenavdraw.ui.stocktransfers.AddStockTransferFragment;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;

import static com.rising.high.tech.bigultimatenavdraw.util.Constant.COMPLETED;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.IN_TRANSIT;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.PENDING;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.RECEIVED;

public class StockTransferAdapter extends RecyclerView.Adapter<StockTransferAdapter.VenteViewHolder> {

    private final String TAG = this.getClass().getSimpleName();
    private ArrayList<Transaction> dataList = new ArrayList<>();
    private BusinessLocationDbController businessLocationDbController;
    private TransactionDbController transactionDbController;
    private PurchaseLineDbController purchaseLineDbController;
    private TransactionSellLineDbController transactionSellLineDbController;
    private VariationLocationDetailDbController variationLocationDetailDbController;
    Context context;

    @Override
    public VenteViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        context = parent.getContext();

        businessLocationDbController = new BusinessLocationDbController(context);
        businessLocationDbController.open();
        transactionDbController = new TransactionDbController(context);
        transactionDbController.open();
        purchaseLineDbController = new PurchaseLineDbController(context);
        purchaseLineDbController.open();
        transactionSellLineDbController = new TransactionSellLineDbController(context);
        transactionSellLineDbController.open();
        variationLocationDetailDbController = new VariationLocationDetailDbController(context);
        variationLocationDetailDbController.open();

        return new VenteViewHolder(LayoutInflater.from(parent.getContext())
                .inflate(R.layout.stock_transfert_item_layout, parent, false));
    }

    @Override
    public void onBindViewHolder(VenteViewHolder holder, int position) {
        holder.dateTxt.setText(dataList.get(position).getTransaction_date());
        holder.factureNo.setText(dataList.get(position).getRef_no());
        String locationTo = businessLocationDbController.getStationById(dataList.get(position).getLocation_id()).getName();
        Transaction transaction = transactionDbController.getTransactionById(dataList.get(position).getTransfer_parent_id());
        String locationFrom = businessLocationDbController.getStationById(transaction.getLocation_id()).getName();
        holder.location_to.setText(locationTo);
        holder.location_from.setText(locationFrom);
        if (dataList.get(position).getStatus().matches(RECEIVED)) {
            holder.payement_status.setText(COMPLETED);
            holder.payement_status.setBackground(context.getResources().getDrawable(R.drawable.rounded_green_bg));
        } else if (dataList.get(position).getStatus().matches(PENDING)) {
            holder.payement_status.setText(dataList.get(position).getStatus());
            holder.payement_status.setBackground(context.getResources().getDrawable(R.drawable.rounded_red_bg));
        } else {
            holder.payement_status.setText(dataList.get(position).getStatus());
            holder.payement_status.setBackground(context.getResources().getDrawable(R.drawable.rounded_orange_bg));
        }
        holder.montant_total.setText(dataList.get(position).getFinal_total());
        holder.note.setText(dataList.get(position).getAdditional_notes());
        holder.payement_status.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!dataList.get(position).getStatus().equals(RECEIVED)) {
                    editStatus(position);
                }
            }
        });

    }

    public void setData(ArrayList<Transaction> arrayList) {
        Collections.reverse(arrayList);
        this.dataList = arrayList;
        notifyDataSetChanged();
    }

    @Override
    public int getItemCount() {
        return dataList.size();
    }

    public class VenteViewHolder extends RecyclerView.ViewHolder {
        TextView dateTxt, factureNo, location_from, location_to, payement_status, montant_total, note;
        Spinner spinnerAction;

        public VenteViewHolder(View itemView) {
            super(itemView);

            dateTxt = itemView.findViewById(R.id.id_date);
            factureNo = itemView.findViewById(R.id.facture_no);
            location_from = itemView.findViewById(R.id.location_from);
            location_to = itemView.findViewById(R.id.location_to);
            payement_status = itemView.findViewById(R.id.payement_status);
            montant_total = itemView.findViewById(R.id.montant_total);
            note = itemView.findViewById(R.id.note);
            spinnerAction = itemView.findViewById(R.id.spinner_action);

            spinnerAction.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
                @Override
                public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                    spinnerAction.setSelection(0, true);
                    switch (position) {
                        case 1: {
                            viewDetail(getAdapterPosition());
                            break;
                        }
                        case 2: {
                            deleteItem(getAdapterPosition());
                            break;
                        }
                        case 3: {
                            editStockTransfert(getAdapterPosition());
                            break;
                        }
                    }
                }
                @Override
                public void onNothingSelected(AdapterView<?> parent) {

                }
            });
        }
    }
    private void editStockTransfert(int position)
    {
        if (dataList.get(position).getStatus().matches(RECEIVED)){
            Toast.makeText(context, context.getResources().getString(R.string.label_cannot_update_stock_transfer), Toast.LENGTH_LONG).show();
        }else {
            naviguateFragment(new AddStockTransferFragment(true), dataList.get(position).getId());
        }
    }
    private void viewDetail(int position) {
        //Preparing views
        // get prompts.xml view

        LayoutInflater li = LayoutInflater.from(context);
        View promptsView = li.inflate(R.layout.view_stock_transfer_dialog_main, null);

        androidx.appcompat.app.AlertDialog.Builder alertDialogBuilder = new androidx.appcompat.app.AlertDialog.Builder(
                context);

        alertDialogBuilder.setView(promptsView);
        SubPurchaseAdapter subPurchaseAdapter = new SubPurchaseAdapter();

        final Button ButtonSave = promptsView.findViewById(R.id.btn_save);
        final ImageView ButtonClose = promptsView.findViewById(R.id.btn_close);

        final TextView business_location = promptsView.findViewById(R.id.business_location);
        final RecyclerView recyclerView = promptsView.findViewById(R.id.recycle_sub_vente);
        final TextView label_reference_num = promptsView.findViewById(R.id.label_reference_num);
        final TextView purchase_date = promptsView.findViewById(R.id.purchase_date);
        final TextView purchase_status = promptsView.findViewById(R.id.purchase_status);
        final TextView location_from = promptsView.findViewById(R.id.location_from);
        final TextView phone_from = promptsView.findViewById(R.id.phone_from);

        recyclerView.setAdapter(subPurchaseAdapter);
        recyclerView.setLayoutManager(new LinearLayoutManager(context));

        ArrayList<Purchase_line> purchaseLines = purchaseLineDbController.getPurchaseLineByTransaction(dataList.get(position).getId());
        subPurchaseAdapter.setData(purchaseLines);

        // Contact contact = contactDbController.getCustomerById(dataList.get(position).getContact_id());
        // supplier_name.setText(contact.getName());
        // supplier_phone.setText(contact.getMobile());

        Business_location businesslocation = businessLocationDbController.getStationById(dataList.get(position).getLocation_id());

        business_location.setText(businesslocation.getName() + ", " + businesslocation.getCity() + " " + businesslocation.getCountry());
        label_reference_num.setText(dataList.get(position).getRef_no());
        purchase_date.setText(dataList.get(position).getTransaction_date());
        purchase_status.setText(dataList.get(position).getStatus());

        Transaction transaction = transactionDbController.getTransactionById(dataList.get(position).getTransfer_parent_id());
        Business_location locationFrom = businessLocationDbController.getStationById(transaction.getLocation_id());

        location_from.setText(locationFrom.getName() + ", " + locationFrom.getCountry() + " " + locationFrom.getCity());
        phone_from.setText("");
        //  final ImageView image_product = promptsView.findViewById(R.id.image_product);

        final androidx.appcompat.app.AlertDialog mAlertDialog = alertDialogBuilder.create();
        if (mAlertDialog.getWindow() != null)
            mAlertDialog.getWindow().getAttributes().windowAnimations = R.style.SlidingDialogAnimation;
        mAlertDialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));


        ButtonClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mAlertDialog.dismiss();
            }
        });


        mAlertDialog.show();
    }

    private void editStatus(int position) {
        LayoutInflater li = LayoutInflater.from(context);
        View promptsView = li.inflate(R.layout.edir_status_stocktransfert_dialog_main, null);
        androidx.appcompat.app.AlertDialog.Builder alertDialogBuilder = new androidx.appcompat.app.AlertDialog.Builder(context);

        alertDialogBuilder.setView(promptsView);

        final Button ButtonSave = promptsView.findViewById(R.id.btn_save);
        final Button ButtonClose = promptsView.findViewById(R.id.btn_close);

        final AppCompatSpinner spin_status = promptsView.findViewById(R.id.spin_status);

        final androidx.appcompat.app.AlertDialog mAlertDialog = alertDialogBuilder.create();
        if (mAlertDialog.getWindow() != null)
            mAlertDialog.getWindow().getAttributes().windowAnimations = R.style.SlidingDialogAnimation;
        mAlertDialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));

        Transaction stock_transfert = transactionDbController.getTransactionById(dataList.get(position).getId());
        if (dataList.get(position).getStatus() != null) {
            String status = dataList.get(position).getStatus();
            if (status.toString().matches("in_transit")) status="in transit";
            int indexP = Arrays.asList(context.getResources().getStringArray(R.array.array_transfer_stock_status)).indexOf(status);
            spin_status.setSelection(indexP);
        }

        ButtonClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mAlertDialog.dismiss();
            }
        });

        ButtonSave.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (spin_status.getSelectedItemPosition() == 1)
                    stock_transfert.setStatus(PENDING);
                if (spin_status.getSelectedItemPosition() == 2)
                    stock_transfert.setStatus(IN_TRANSIT);
                if (spin_status.getSelectedItemPosition() == 3) {
                    stock_transfert.setStatus(RECEIVED);
                }

                if (spin_status.getSelectedItemPosition() == 3) {
                    Transaction transactionPurchaseTransfer = transactionDbController.getTransactionById(dataList.get(position).getId());
                    //   Transaction transactionSellTransfer = transactionDbController.getTransactionById(transactionPurchaseTransfer.getTransfer_parent_id());
                    Transaction TransactionPurchase = transactionPurchaseTransfer;
                    Transaction TransactionSell =  transactionDbController.getTransactionById(transactionPurchaseTransfer.getTransfer_parent_id());
                    ArrayList<Sell_lines> sell_lines = transactionSellLineDbController.getSellLineByTransaction(transactionPurchaseTransfer.getTransfer_parent_id());
                    for (Sell_lines sell_lines1 : sell_lines) {
                        //decrease quantiy for sell transaction
                        variationLocationDetailDbController.updateSellQty(sell_lines1.getProduct_id(), TransactionSell.getLocation_id(), sell_lines1.getQuantity());
                        //increase quantiy for purchase transaction
                        variationLocationDetailDbController.updatePurchaseQty(sell_lines1.getProduct_id(), TransactionPurchase.getLocation_id(), sell_lines1.getQuantity());
                    }
                }

                dataList.get(position).setStatus(stock_transfert.getStatus());
                notifyDataSetChanged();
                transactionDbController.updateTransaction(dataList.get(position));
                mAlertDialog.dismiss();

            }
        });

        mAlertDialog.show();

    }

    private void deleteItem(int position) {
        // Preparing views
        // get prompts.xml view
        LayoutInflater li = LayoutInflater.from(context);
        View promptsView = li.inflate(R.layout.delete_dialog_main, null);

        androidx.appcompat.app.AlertDialog.Builder alertDialogBuilder = new androidx.appcompat.app.AlertDialog.Builder(
                context);

        alertDialogBuilder.setView(promptsView);

        final Button ButtonSave = promptsView.findViewById(R.id.btn_save);
        final Button ButtonClose = promptsView.findViewById(R.id.btn_close);

        final androidx.appcompat.app.AlertDialog mAlertDialog = alertDialogBuilder.create();
        if (mAlertDialog.getWindow() != null)
            mAlertDialog.getWindow().getAttributes().windowAnimations = R.style.SlidingDialogAnimation;
        mAlertDialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));

        ButtonClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mAlertDialog.dismiss();
            }
        });

        ButtonSave.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {


                ArrayList<Sell_lines> sellLines = transactionSellLineDbController.getSellLineByTransaction(dataList.get(position).getTransfer_parent_id());
                ArrayList<Purchase_line> purchaseLines = purchaseLineDbController.getPurchaseLineByTransaction(dataList.get(position).getId());

                for (Purchase_line purchaseLine : purchaseLines) {
                    purchaseLineDbController.deletePurchase(purchaseLine.getId());
                }

                for (Sell_lines sellLine : sellLines) {
                    transactionSellLineDbController.delete(sellLine.getId());
                }

                transactionDbController.deleteTransaction(dataList.get(position).getId());
                transactionDbController.deleteTransaction(dataList.get(position).getTransfer_parent_id());

                dataList.remove(position);
                notifyDataSetChanged();
                mAlertDialog.dismiss();
            }
        });

        mAlertDialog.show();
    }

    private void naviguateFragment(Fragment myFragment, Integer id) {
        AppCompatActivity activity = (AppCompatActivity) context;

        if (id != null) {
            Bundle bundle = new Bundle();
            bundle.putInt("id", id);
            myFragment.setArguments(bundle);
        }

        activity.getSupportFragmentManager().beginTransaction().replace(R.id.nav_host_fragment, myFragment).addToBackStack(null).commit();
    }

    public interface OnDataChangeListener {
        void onDataChanged(Transaction transaction);
    }

    OnDataChangeListener mOnDataChangeListener;

    public void setOnDataChangeListener(OnDataChangeListener onDataChangeListener) {
        mOnDataChangeListener = onDataChangeListener;
    }

}
