package com.rising.high.tech.bigultimatenavdraw.ui.sync;

import android.app.ProgressDialog;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.graphics.Bitmap;
import android.os.AsyncTask;
import android.os.Bundle;

import androidx.fragment.app.Fragment;

import android.util.Base64;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.CompoundButton;
import android.widget.LinearLayout;
import android.widget.Switch;
import android.widget.Toast;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.api.APIClient;
import com.rising.high.tech.bigultimatenavdraw.db.BrandDbController;
import com.rising.high.tech.bigultimatenavdraw.db.CategoryDbController;
import com.rising.high.tech.bigultimatenavdraw.db.CompanyDbController;
import com.rising.high.tech.bigultimatenavdraw.db.ContactDbController;
import com.rising.high.tech.bigultimatenavdraw.db.CustomerGroupsDbController;
import com.rising.high.tech.bigultimatenavdraw.db.DiscountDbController;
import com.rising.high.tech.bigultimatenavdraw.db.ProductDbController;
import com.rising.high.tech.bigultimatenavdraw.db.ProductLocationDbController;
import com.rising.high.tech.bigultimatenavdraw.db.ProfitLossDbController;
import com.rising.high.tech.bigultimatenavdraw.db.PurchaseLineDbController;
import com.rising.high.tech.bigultimatenavdraw.db.TaxRatesDbController;
import com.rising.high.tech.bigultimatenavdraw.db.TransactionSellLineDbController;
import com.rising.high.tech.bigultimatenavdraw.db.BusinessLocationDbController;
import com.rising.high.tech.bigultimatenavdraw.db.TransactionDbController;
import com.rising.high.tech.bigultimatenavdraw.db.TransactionPayementDbController;
import com.rising.high.tech.bigultimatenavdraw.db.UnitDbController;
import com.rising.high.tech.bigultimatenavdraw.db.UserDbController;
import com.rising.high.tech.bigultimatenavdraw.db.VariationLocationDetailDbController;
import com.rising.high.tech.bigultimatenavdraw.db.VariationsDbController;
import com.rising.high.tech.bigultimatenavdraw.db.WarrantyDbController;
import com.rising.high.tech.bigultimatenavdraw.model.Brand;
import com.rising.high.tech.bigultimatenavdraw.model.Business;
import com.rising.high.tech.bigultimatenavdraw.model.Business_location;
import com.rising.high.tech.bigultimatenavdraw.model.Category;
import com.rising.high.tech.bigultimatenavdraw.model.Contact;
import com.rising.high.tech.bigultimatenavdraw.model.Customer_groups;
import com.rising.high.tech.bigultimatenavdraw.model.Discount;
import com.rising.high.tech.bigultimatenavdraw.model.Product;
import com.rising.high.tech.bigultimatenavdraw.model.Purchase_line;
import com.rising.high.tech.bigultimatenavdraw.model.ResponseUser;
import com.rising.high.tech.bigultimatenavdraw.model.Tax_rates;
import com.rising.high.tech.bigultimatenavdraw.model.Transaction;
import com.rising.high.tech.bigultimatenavdraw.model.Unit;
import com.rising.high.tech.bigultimatenavdraw.model.User;
import com.rising.high.tech.bigultimatenavdraw.model.Variation;
import com.rising.high.tech.bigultimatenavdraw.model.Variation_location_details;
import com.rising.high.tech.bigultimatenavdraw.model.Warranty;
import com.rising.high.tech.bigultimatenavdraw.ui.NaviMainActivity;
import com.rising.high.tech.bigultimatenavdraw.util.FileUtil;
import com.rising.high.tech.bigultimatenavdraw.util.SessionManager;

import java.io.ByteArrayOutputStream;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;

import butterknife.BindView;
import butterknife.ButterKnife;
import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.core.Observable;
import io.reactivex.rxjava3.schedulers.Schedulers;

import static android.content.Context.MODE_PRIVATE;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.LOCAL_MASTER;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.NO;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.SERVER_MASTER;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.YES;

/**
 * A simple {@link Fragment} subclass.
 * Use the {@link SyncFragment#newInstance} factory method to
 * create an instance of this fragment.
 */

public class SyncFragment extends Fragment {

    private static final String TAG = "SyncFragment";
    private ProgressDialog mConnectProgressDialog;
    private Context _context;
    SessionManager session;

    Button syncBtn, btnAllData, btnFromServer;
    CategoryDbController categoryDbController;
    ProductDbController productDbController;
    BusinessLocationDbController businessLocationDbController;
    ContactDbController contactDbController;
    TransactionDbController transactionDbController;
    PurchaseLineDbController purchaseLineDbController;
    CustomerGroupsDbController customerGroupsDbController;
    BrandDbController brandDbController;
    WarrantyDbController warrantyDbController;
    TaxRatesDbController taxRatesDbController;
    TransactionSellLineDbController transactionSellLineDbController;
    TransactionPayementDbController transactionPayementDbController;
    VariationsDbController variationsDbController;
    ProfitLossDbController profitLossDbController;
    VariationLocationDetailDbController variationLocationDetailDbController;
    ProductLocationDbController productLocationDbController;
    UnitDbController unitDbController;
    DiscountDbController discountDbController;
    CompanyDbController companyDbController;
    UserDbController userDbController;

    Boolean cProductSwitch = false;
    Boolean cCategorySwitch = false;
    Boolean cCustomerSwitch = false;
    Boolean cTransactionSwitch = false;

    Switch productSwitch, categorySwitch, transactionSwitch, customerSwitch;

    ShowToastClient showToastClient;


    @BindView(R.id.layout_switch)
    LinearLayout layoutSwitch;

    public SyncFragment() {
        // Required empty public constructor
    }

    /**
     * Use this factory method to create a new instance of
     * this fragment using the provided parameters.
     *
     * @param param1 Parameter 1.
     * @param param2 Parameter 2.
     * @return A new instance of fragment SyncFragment.
     */
    // TODO: Rename and change types and number of parameters
    public static SyncFragment newInstance(String param1, String param2) {
        SyncFragment fragment = new SyncFragment();
        Bundle args = new Bundle();
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        // Inflate the layout for this fragment
        View root = inflater.inflate(R.layout.fragment_sync, container, false);
        ButterKnife.bind(this, root);

        productSwitch = root.findViewById(R.id.product_switch);
        categorySwitch = root.findViewById(R.id.category_switch);
        customerSwitch = root.findViewById(R.id.costumer_switch);
        transactionSwitch = root.findViewById(R.id.transaction_switch);
        btnAllData = root.findViewById(R.id.id_btn_all_data);
        btnFromServer = root.findViewById(R.id.id_btn_all_data_from_server);
        syncBtn = root.findViewById(R.id.sync_btn);

        _context = getContext();
        session = new SessionManager(_context);

        initDB();

        initListners();

        setSwitchState();

        checkRoles();

        return root;

    }

    private void checkRoles() {
        if (session.getBoolean(SERVER_MASTER)) {
            syncBtn.setVisibility(View.GONE);
            layoutSwitch.setVisibility(View.GONE);
        }


        if (session.getBoolean(LOCAL_MASTER)) {
            btnFromServer.setVisibility(View.GONE);
        }
    }

    private void sync() {
        if (productSwitch.isChecked()) {
            cProductSwitch = true;
            if (cProductSwitch) {

                //dicomment this line
                storeBusinessLocation();


                //this one works fine to impe,emt whene is deployed
                // storeBusiness();

                //      storeUsers();


            }
        } else {
            cProductSwitch = false;
        }

        if (customerSwitch.isChecked()) {
            cCustomerSwitch = true;

            postCustomerGroups();
            // postExpenses();
        } else {
            cCustomerSwitch = false;
        }
        if (transactionSwitch.isChecked()) {
            cTransactionSwitch = true;


            ArrayList<Business_location> localBusiness_location = businessLocationDbController.getSyncStation(NO);
            ArrayList<Product> localProduct = productDbController.getSyncProduct(NO);
            if (localProduct.size() == 0 && localBusiness_location.size() == 0) {

                if (contactDbController.getSyncCustomer(NO).size() == 0) {
                    ArrayList<Product> openingProductToSync = new ArrayList<>();
                    ArrayList<Transaction> openingTransactionArrayList = transactionDbController.getNonSyncTransactionsByOpeningStock();
                    if (openingTransactionArrayList.size() == 0) {
                        postTransactions();
                    } else {
                        for (Transaction transaction : openingTransactionArrayList) {
                            openingProductToSync.add(productDbController.getProductById(transaction.getOpening_stock_product_id()));
                        }
                        postOpeningStock(openingProductToSync);
                    }
                } else {
                    Toast.makeText(_context, _context.getResources().getString(R.string.label_contacts_must_sync), Toast.LENGTH_LONG).show();
                }


            } else {
                Toast.makeText(_context, _context.getResources().getString(R.string.label_product_must_sync), Toast.LENGTH_LONG).show();
            }

            //postOpeningStock();
        } else {
            cTransactionSwitch = false;
        }

    }

    private void getAllData() {

        showProgress();
        Observable<ResponseUser<ArrayList<Product>>> observablePostSells = APIClient.getINSTANCE(_context).getProduct()
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread());
        observablePostSells.subscribe(o ->
                {
                    mConnectProgressDialog.dismiss();

                    Log.d(TAG, "on  test subscribe ## " + new Gson().toJson(o));
                    Toast.makeText(_context, o.getMsg(), Toast.LENGTH_LONG).show();

                },
                e ->
                {
                    Log.d(TAG, "onError ## " + e.getMessage());
                    mConnectProgressDialog.dismiss();
                }
        );
    }


    private void getAllDataServerMaster() {

        showProgress();

        Observable<ResponseUser<ArrayList<Contact>>> observableGetContact = APIClient.getINSTANCE(_context).getCustomer();
        Observable<ResponseUser<ArrayList<Category>>> observableGetCategory = APIClient.getINSTANCE(_context).getCategory();
        Observable<ResponseUser<ArrayList<Business_location>>> observableGetBusinessLocation = APIClient.getINSTANCE(_context).getBusinessLocation();

        Observable<ResponseUser<ArrayList<Product>>> observableGetProduct = APIClient.getINSTANCE(_context).getProduct();
        Observable<ResponseUser<ArrayList<Brand>>> observableGetBrand = APIClient.getINSTANCE(_context).getBrand();
        Observable<ResponseUser<ArrayList<Warranty>>> observableGetWarranty = APIClient.getINSTANCE(_context).getWarranty();
        // Observable<ResponseUser<ArrayList<Transaction>>> observableGetSells = APIClient.getINSTANCE(_context).getSells();
        // Observable<ResponseUser<ArrayList<Transaction>>> observableGetExpense = APIClient.getINSTANCE(_context).getExpense();
        //  Observable<ResponseUser<ProfitLossReport>> observableGetProfitLost = APIClient.getINSTANCE(_context).getProfitLost();
        //   Observable<ResponseUser<ArrayList<ProductStock>>> observableGetStockProduct = APIClient.getINSTANCE(_context).getProductStock();
        Observable<ResponseUser<ArrayList<Unit>>> observableGetUnit = APIClient.getINSTANCE(_context).getUnit();
        Observable<ResponseUser<Business>> observablegetBusinessSetting = APIClient.getINSTANCE(_context).getBusinessSetting();

        Observable.zip(observableGetContact, observableGetCategory, observableGetBusinessLocation,
                observableGetProduct, observableGetUnit, observableGetBrand, observableGetWarranty, observablegetBusinessSetting, (a, b, c, d, e, f, g, h) -> {
                    /*
                     Insert customers data
                     */
                    Log.d(TAG, "customers ## " + new Gson().toJson(a));
                    if (a.getData() != null && a.getData().size() > 0) {
                        contactDbController.deleteAll();
                        for (Contact contact : a.getData()) {
                            contactDbController.insert(contact);
                        }
                    }

                    /*
                     Insert categories data
                     */
                    Log.d(TAG, "categories ## " + new Gson().toJson(b));
                    if (b.getData() != null && b.getData().size() > 0) {
                        categoryDbController.deleteAll();
                        for (Category category : b.getData()) {
                            categoryDbController.insert(category);
                        }
                    }

                    /*
                     Insert bussiness location data
                     */
                    Log.d(TAG, "bussines location ## " + new Gson().toJson(c));
                    if (c != null && c.getData().size() > 0) {
                        businessLocationDbController.clear();
                        for (Business_location xBusinesslocation : c.getData()) {
                            businessLocationDbController.insert(xBusinesslocation);
                        }
                    }

                     /*
                     Insert products data
                     */
                    Log.d(TAG, "products ## " + new Gson().toJson(d));
                    if (d.getData() != null && d.getData().size() > 0) {
                        productDbController.deleteAll();
                        variationsDbController.deleteAll();
                        variationLocationDetailDbController.deleteAll();
                        productLocationDbController.deleteAll();
                        for (Product product : d.getData()) {
                            productDbController.insert(product);
                        }
                    }

                      /*
                     Insert units data
                     */
                    Log.d(TAG, "i units ## " + new Gson().toJson(e));
                    if (e.getData() != null && e.getData().size() > 0) {
                        unitDbController.clear();
                        for (Unit unit : e.getData()) {
                            unitDbController.insert(unit);
                        }
                    }

                    /*
                     Insert brands data
                     */
                    Log.d(TAG, "f brand ## " + new Gson().toJson(f));
                    if (f.getData() != null && f.getData().size() > 0) {
                        brandDbController.clear();
                        for (Brand brand : f.getData()) {
                            brandDbController.insert(brand);
                        }
                    }

                    Log.d(TAG, "g warranty ## " + new Gson().toJson(g));
                    if (g.getData() != null && g.getData().size() > 0) {
//                        warrantyDbController.clear();
//                        for (Warranty warranty : g.getData()) {
//                            warrantyDbController.insert(warranty);
//                        }
                    }

                    Log.d(TAG, "h businness settings ## " + new Gson().toJson(h));
                    if (h.getData() != null && h.getSuccess() == 1) {
                        companyDbController.clear();
                        companyDbController.insert(h.getData());
                    }


                    return "Success";
                }).subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(o -> {
                    Log.d(TAG, " onNext ## " + o);
                    mConnectProgressDialog.dismiss();

               //     Toast.makeText(_context, " Success ...", Toast.LENGTH_LONG).show();
                    FileUtil.showDialog(_context, "Successful", getResources().getString(R.string.label_updated_succesfully));

                    _context.startActivity(new Intent(_context, NaviMainActivity.class));
                }, e -> {
                    Log.d(TAG, "error ## " + e.getMessage());
                    mConnectProgressDialog.dismiss();
                });


    }


    private void showProgress() {
        mConnectProgressDialog = ProgressDialog.show(_context,
                "Synchronisation...", "Sync data", true, false);
    }

    private void initDB() {

        contactDbController = new ContactDbController(_context);
        contactDbController.open();

        categoryDbController = new CategoryDbController(_context);
        categoryDbController.open();

        productDbController = new ProductDbController(_context);
        productDbController.open();

        businessLocationDbController = new BusinessLocationDbController(_context);
        businessLocationDbController.open();

        transactionDbController = new TransactionDbController(_context);
        transactionDbController.open();

        purchaseLineDbController = new PurchaseLineDbController(_context);
        purchaseLineDbController.open();

        transactionSellLineDbController = new TransactionSellLineDbController(_context);
        transactionSellLineDbController.open();

        transactionPayementDbController = new TransactionPayementDbController(_context);
        transactionPayementDbController.open();

        variationsDbController = new VariationsDbController(_context);
        variationsDbController.open();

        profitLossDbController = new ProfitLossDbController(_context);
        profitLossDbController.open();

        customerGroupsDbController = new CustomerGroupsDbController(_context);
        customerGroupsDbController.open();

        brandDbController = new BrandDbController(_context);
        brandDbController.open();
        warrantyDbController = new WarrantyDbController(_context);
        warrantyDbController.open();

        variationLocationDetailDbController = new VariationLocationDetailDbController(_context);
        variationLocationDetailDbController.open();

        unitDbController = new UnitDbController(_context);
        unitDbController.open();

        taxRatesDbController = new TaxRatesDbController(_context);
        taxRatesDbController.open();

        discountDbController = new DiscountDbController(_context);
        discountDbController.open();

        productLocationDbController = new ProductLocationDbController(_context);
        productLocationDbController.open();

        companyDbController = new CompanyDbController(_context);
        companyDbController.open();

        userDbController = new UserDbController(_context);
    }

    private void saveParams(String key, String value) {
        SharedPreferences sharedPreferences = getActivity().getSharedPreferences("shared preferences", MODE_PRIVATE);
        SharedPreferences.Editor editor = sharedPreferences.edit();
        Gson gson = new Gson();
        String json = gson.toJson(value);
        editor.putString(key, json);
        editor.apply();
    }

    private String getParams(String key) {
        SharedPreferences sharedPreferences = getActivity().getSharedPreferences("shared preferences", MODE_PRIVATE);
        Gson gson = new Gson();
        String json = sharedPreferences.getString(key, null);
        Type type = new TypeToken<String>() {
        }.getType();

        return gson.fromJson(json, type);
    }


    private void setSwitchState() {

        if (getParams("productSS") != null && getParams("productSS").matches("true")) {
            productSwitch.setChecked(true);
        } else {
            productSwitch.setChecked(false);
        }

        if (getParams("categorySS") != null && getParams("categorySS").matches("true")) {
            categorySwitch.setChecked(true);
        } else {
            categorySwitch.setChecked(false);
        }

        if (getParams("costumerSS") != null && getParams("costumerSS").matches("true")) {
            customerSwitch.setChecked(true);
        } else {
            customerSwitch.setChecked(false);
        }

        if (getParams("transactionSS") != null && getParams("transactionSS").matches("true")) {
            transactionSwitch.setChecked(true);
        } else {
            transactionSwitch.setChecked(false);
        }

    }


    private void initListners() {
        syncBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

                // Log.d(TAG, "no sync customers ### " + new Gson().toJson(customerDbController.getSyncCustomer("no")));
                // postContact(customerDbController.getSyncCustomer("no").get(0));

                //  getAllData();

                //  Log.d(TAG, "no sync customers ### " + new Gson().toJson(transactionDbController.getSyncTransaction("no")));
                //  Log.d(TAG, "P transactio ### " + new Gson().toJson(pTransactionDbController.getProductsTransaction(transactionDbController.getSyncTransaction("no").get(0).getTransaction_id())));

                // postData();
                sync();
            }
        });

        btnFromServer.setOnClickListener(v -> {
            getAllDataServerMaster();
        });

        productSwitch.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                if (isChecked) {
                    saveParams("productSS", "true");
                    customerSwitch.setChecked(false);
                    transactionSwitch.setChecked(false);
                    //do stuff when Switch is ON
                } else {
                    saveParams("productSS", "false");
                    //do stuff when Switch if OFF
                }
            }
        });

        categorySwitch.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                if (isChecked) {
                    saveParams("categorySS", "true");
                    //do stuff when Switch is ON
                } else {
                    saveParams("categorySS", "false");
                    //do stuff when Switch if OFF
                }
            }
        });

        customerSwitch.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                if (isChecked) {
                    transactionSwitch.setChecked(false);
                    productSwitch.setChecked(false);
                    saveParams("costumerSS", "true");
                    //do stuff when Switch is ON
                } else {
                    saveParams("costumerSS", "false");
                    //do stuff when Switch if OFF
                }
            }
        });

        transactionSwitch.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                if (isChecked) {
                    customerSwitch.setChecked(false);
                    productSwitch.setChecked(false);
                    saveParams("transactionSS", "true");
                    //do stuff when Switch is ON
                } else {
                    saveParams("transactionSS", "false");
                    //do stuff when Switch if OFF
                }
            }
        });


        btnAllData.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                getAllData();
            }
        });
    }

    private void storeBusiness() {


        showProgress();

        //  Log.d(TAG, "localBusiness_location  ## " + new Gson().toJson(localBusiness_location));
        Business business = companyDbController.getSyncCompanyData(NO);
        Log.d(TAG, "business ## " + new Gson().toJson(business));
        if (business.getId() == 0) {
            storeUsers();
        } else {
            Observable<ResponseUser<String>> observablePostBusines = APIClient.getINSTANCE(_context).storeBusiness(business)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread());
            observablePostBusines.subscribe(o ->
                    {

                        Log.d(TAG, "on subscribe storeBusiness ## " + new Gson().toJson(o));
                        if (o.getSuccess() == 1) {
                            business.setSync("yes");
                            companyDbController.setSyncCompany(business);
                            storeUsers();
                        } else {
                            mConnectProgressDialog.dismiss();
                            Toast.makeText(_context, "error server", Toast.LENGTH_LONG).show();
                        }
                    },
                    e ->
                    {
                        Log.d(TAG, "onError ## " + e.getMessage());
                        mConnectProgressDialog.dismiss();
                    }
            );
        }


    }

    private void storeUsers() {
        ArrayList<User> userArrayList = userDbController.getSyncUsers(NO);
        if (userArrayList.size() == 0) {
            storeBusinessLocation();
        } else {
            //    showProgress();

            HashMap<Object, Object> hashMap = new HashMap<>();
            hashMap.put("users", userArrayList);
            //  Log.d(TAG, "localBusiness_location  ## " + new Gson().toJson(localBusiness_location));
            Log.d(TAG, "users ## " + new Gson().toJson(hashMap));
            Observable<ResponseUser<ArrayList<User>>> observablePostUsers = APIClient.getINSTANCE(_context).storeUsers(hashMap)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread());
            observablePostUsers.subscribe(o ->
                    {
                        Log.d(TAG, "on subscribe PostUsers ## " + new Gson().toJson(o));

                        if (o.getSuccess() == 1) {
                            ArrayList<User> serverUser = o.getData();
                            for (int i = 0; i < userArrayList.size(); i++) {
                                userArrayList.get(i).setUser_server_id(serverUser.get(i).getId());
                                userArrayList.get(i).setSync(YES);
                                userDbController.setSyncUsers(userArrayList.get(i));
                            }
                            storeBusinessLocation();

                        } else {
                            Toast.makeText(_context, "error server", Toast.LENGTH_LONG).show();
                            mConnectProgressDialog.dismiss();
                        }
                    },
                    e ->
                    {
                        Toast.makeText(_context, "error server", Toast.LENGTH_LONG).show();
                        Log.d(TAG, "onError PostUsers ## " + e.getMessage());
                        mConnectProgressDialog.dismiss();
                    }
            );
        }

    }

    private void storeBusinessLocation() {
        showProgress();
        ArrayList<Business_location> localBusiness_location = businessLocationDbController.getSyncStation(NO);
        if (localBusiness_location.size() == 0) {
            postCategory();
        } else {
            Log.d(TAG, "localBusiness_location  ## " + new Gson().toJson(localBusiness_location));

            HashMap<Object, Object> hashMap = new HashMap<>();
            hashMap.put("business_locations", localBusiness_location);
            Observable<ResponseUser<ArrayList<Business_location>>> observablePostBusines_location = APIClient.getINSTANCE(_context).storeBusinessLocation(hashMap)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread());
            observablePostBusines_location.subscribe(o ->
                    {
                        if (o.getSuccess() == 1) {
                            ArrayList<Business_location> serverBusinessLocation = o.getData();

                            for (int i = 0; i < localBusiness_location.size(); i++) {
                                localBusiness_location.get(i).setLocation_server_id(serverBusinessLocation.get(i).getId());
                                localBusiness_location.get(i).setSync(YES);
                                businessLocationDbController.setSynBusinessLocation(localBusiness_location.get(i));
                            }

                            postCategory();
                            Log.d(TAG, "on subscribe storeBusinessLocation ## " + new Gson().toJson(o));
                        } else {
                            mConnectProgressDialog.dismiss();
                            Toast.makeText(_context, "error server", Toast.LENGTH_LONG).show();
                        }
                    },
                    e ->
                    {
                        Log.d(TAG, "localBusiness_location onError ## " + e.getMessage());
                        mConnectProgressDialog.dismiss();
                    }
            );
        }
    }

    private void postTransactions() {
        showProgress();
        ArrayList<Transaction> localTransaction = transactionDbController.getSyncSell(NO);
        if (localTransaction.size() == 0) {
            postExpenses();
        } else {
            //   showProgress();
            HashMap<Object, Object> hashMap = new HashMap<>();
            hashMap.put("sells", localTransaction);
            Log.d(TAG, "localTransaction ## " + new Gson().toJson(localTransaction));
            Observable<ResponseUser<String>> observablePostSells = APIClient.getINSTANCE(_context).postSell(hashMap)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread());
            observablePostSells.subscribe(o ->
                    {
                        //  mConnectProgressDialog.dismiss();
                        if (o.getSuccess() == 1) {
                            for (Transaction transaction : localTransaction) {
                                transactionDbController.setSyncTransaction(transaction);
                            }
                            Log.d(TAG, "on subscribe ## " + new Gson().toJson(o));

                            postExpenses();
                        } else {
                            Toast.makeText(_context, "error server", Toast.LENGTH_LONG).show();
                        }
                    },
                    e ->
                    {
                        Log.d(TAG, "onError ## " + e.getMessage());
                        mConnectProgressDialog.dismiss();
                    }
            );
        }
    }


    private void postExpenses() {
        ArrayList<Transaction> localTransaction = transactionDbController.getExpenseSell(NO);
        if (localTransaction.size() == 0) {
            Toast.makeText(_context, getResources().getString(R.string.string_data_up_to_date), Toast.LENGTH_LONG).show();
            mConnectProgressDialog.dismiss();
        } else {
            showProgress();
            HashMap<Object, Object> hashMap = new HashMap<>();
            hashMap.put("expenses", localTransaction);
            Log.d(TAG, "localExpenses  ## " + new Gson().toJson(localTransaction));
            Observable<ResponseUser<String>> observablePostSells = APIClient.getINSTANCE(_context).storeExpenses(hashMap)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread());
            observablePostSells.subscribe(o ->
                    {
                        Log.d(TAG, "on subscribe expenses  ## " + new Gson().toJson(o));

                        mConnectProgressDialog.dismiss();
                        if (o.getSuccess() == 1) {
                            for (Transaction transaction : localTransaction) {
                                transactionDbController.setSyncTransaction(transaction);
                            }
                            Log.d(TAG, "on subscribe ## " + new Gson().toJson(o));
                            Toast.makeText(_context, "Transaction Sync Successfully", Toast.LENGTH_LONG).show();
                        } else {
                            Toast.makeText(_context, "error server", Toast.LENGTH_LONG).show();
                        }
                    },
                    e ->
                    {
                        Log.d(TAG, "onError ## " + e.getMessage());
                        mConnectProgressDialog.dismiss();
                    }
            );
        }
    }

    private void postCustomerGroups() {

        ArrayList<Customer_groups> localCustomerGroups = customerGroupsDbController.getSyncCustomerGroups(NO);
        if (localCustomerGroups.size() == 0) {
            postCustomers();
        } else {
            showProgress();

            HashMap<Object, Object> hashMap = new HashMap<>();
            hashMap.put("customer_groups", localCustomerGroups);
            Observable<ResponseUser<ArrayList<Customer_groups>>> observableCustomerGroups = APIClient.getINSTANCE(_context).storeCustomerGroups(hashMap)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread());
            observableCustomerGroups.subscribe(o ->
                    {
                        mConnectProgressDialog.dismiss();
                        if (o.getSuccess() == 1) {
                            ArrayList<Customer_groups> serverCustomerGroups = o.getData();
                            for (int i = 0; i < localCustomerGroups.size(); i++) {
                                localCustomerGroups.get(i).setCg_server_id(serverCustomerGroups.get(i).getId());
                                localCustomerGroups.get(i).setSync(YES);
                                customerGroupsDbController.syncCustomerGroups(localCustomerGroups.get(i));
                            }

                            postCustomers();

                            Log.d(TAG, "on subscribe ## " + new Gson().toJson(o));
                            Toast.makeText(_context, o.getMsg(), Toast.LENGTH_LONG).show();
                        } else {
                            Toast.makeText(_context, "error server", Toast.LENGTH_LONG).show();
                        }
                    },
                    e ->
                    {
                        Log.d(TAG, "onError ## " + e.getMessage());
                        mConnectProgressDialog.dismiss();
                    }
            );
        }
    }

    private void postTaxesRates() {

        ArrayList<Tax_rates> localTax_rates = taxRatesDbController.getSyncTax_rates(NO);
        Log.d(TAG, " non sync tax rates gson " + new Gson().toJson(localTax_rates));
        if (localTax_rates.size() == 0) {
            postProduct();
        } else {
            // showProgress();

            HashMap<Object, Object> hashMap = new HashMap<>();
            hashMap.put("tax_rates", localTax_rates);
            Observable<ResponseUser<ArrayList<Tax_rates>>> observableCustomerGroups = APIClient.getINSTANCE(_context).storeTaxRates(hashMap)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread());
            observableCustomerGroups.subscribe(o ->
                    {
                        Log.d(TAG, "on subscribe ## " + new Gson().toJson(o));

                        //    mConnectProgressDialog.dismiss();
                        if (o.getSuccess() == 1) {
                            ArrayList<Tax_rates> serverTax_rates = o.getData();
                            for (int i = 0; i < localTax_rates.size(); i++) {
                                localTax_rates.get(i).setTr_server_id(serverTax_rates.get(i).getId());
                                localTax_rates.get(i).setSync(YES);
                                taxRatesDbController.setSynTax_rates(localTax_rates.get(i));
                            }

                            postProduct();

                            //  Toast.makeText(_context, o.getMsg(), Toast.LENGTH_LONG).show();
                        } else {
                            mConnectProgressDialog.dismiss();
                            Toast.makeText(_context, "error server", Toast.LENGTH_LONG).show();
                        }
                    },
                    e ->
                    {
                        Log.d(TAG, "onError ## " + e.getMessage());
                        mConnectProgressDialog.dismiss();
                    }
            );
        }
    }

    private void postOpeningStock(ArrayList<Product> local_product) {
        ArrayList<HashMap<Object, Object>> arrayListhashMap = new ArrayList<>();
        //   ArrayList<Variation_location_details> variation_location_details=variationLocationDetailDbController.getVariationLocationDetailsByProductId(product_id);
        for (int i = 0; i < local_product.size(); i++) {
            ArrayList<Transaction> transactionArrayList = transactionDbController.getTransactionsByOpeningStockProductId(local_product.get(i).getId());
            Product product__ = productDbController.getProductById(local_product.get(i).getId());
            Variation variation__ = variationsDbController.getVariationByProductId(local_product.get(i).getId());
            ArrayList<Variation_location_details> variation_location_details = new ArrayList<>();
            if (transactionArrayList.size() == 0) {
                Toast.makeText(_context, "There is no opening stock for " + product__.getName(), Toast.LENGTH_LONG).show();
            } else {
                for (Transaction transaction : transactionArrayList) {

                    Purchase_line purchase_line = purchaseLineDbController.getPurchaseLineTransaction(transaction.getId());
                    Variation_location_details variation_location_detail = variationLocationDetailDbController.getVariationLocationDetailsByStationIdProductId(transaction.getLocation_id(), local_product.get(i).getId());

                    variation_location_detail.setLocation_id(transaction.getLocation_id());

                    variation_location_detail.setProduct_id(local_product.get(i).getProduct_server_id());
                    variation_location_detail.setProduct_variation_id(variation__.getVariation_server_id());
                    variation_location_detail.setVariation_id(variation__.getVariation_server_id());
                    variation_location_detail.setPurchase_price(transaction.getFinal_total());
                    variation_location_detail.setQuantity(purchase_line.getQuantity() + "");
                    variation_location_detail.setTransaction_date(transaction.getTransaction_date());
                    variation_location_detail.setFinal_total(transaction.getFinal_total());
                    variation_location_details.add(variation_location_detail);
                }

                HashMap<Object, Object> hashMap = new HashMap<>();
                hashMap.put("product_id", product__.getProduct_server_id());
                hashMap.put("created_by", 1);
                hashMap.put("variation_location_details", variation_location_details);
                arrayListhashMap.add(hashMap);
            }
        }
        if (arrayListhashMap.size() > 0) {
            HashMap<Object, Object> hashMapAll = new HashMap<>();
            hashMapAll.put("opening_stocks", arrayListhashMap);
            Log.d(TAG, "opening stock " + new Gson().toJson(hashMapAll));
            //  showProgress();
            Observable<ResponseUser<String>> observableStoreOpeningStock = APIClient.getINSTANCE(_context).storeOpeningStock(hashMapAll)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread());
            observableStoreOpeningStock.subscribe(o ->
                    {
                        //  mConnectProgressDialog.dismiss();
                        if (o.getSuccess() == 1) {
                            for (int i = 0; i < local_product.size(); i++) {
                                ArrayList<Transaction> transactionArrayList = transactionDbController.getTransactionsByOpeningStockProductId(local_product.get(i).getId());
                                for (Transaction transaction : transactionArrayList) {
                                    transactionDbController.setSyncTransaction(transaction);
                                }
                            }
                            postTransactions();
                        } else {
                            Toast.makeText(_context, "error server", Toast.LENGTH_LONG).show();
                        }
                        Log.d(TAG, "on subscribe ## " + new Gson().toJson(o));
                    },
                    e ->
                    {
                        Log.d(TAG, "onError ## " + e.getMessage());
                        //  mConnectProgressDialog.dismiss();
                    }
            );
        }

    }

    private void postOpeningStockK(ArrayList<Product> local_product) {

        ArrayList<HashMap<Object, Object>> arrayListhashMap = new ArrayList<>();

        //   ArrayList<Variation_location_details> variation_location_details=variationLocationDetailDbController.getVariationLocationDetailsByProductId(product_id);
        for (int i = 0; i < local_product.size(); i++) {
            ArrayList<Transaction> transactionArrayList = transactionDbController.getTransactionsByOpeningStockProductId(local_product.get(i).getId());
            Product product__ = productDbController.getProductById(local_product.get(i).getId());
            Variation variation__ = variationsDbController.getVariationByProductId(local_product.get(i).getId());
            ArrayList<Variation_location_details> variation_location_details = new ArrayList<>();
            if (transactionArrayList.size() == 0) {
                Toast.makeText(_context, "There is no opening stock for " + product__.getName(), Toast.LENGTH_LONG).show();
            } else {
                for (Transaction transaction : transactionArrayList) {

                    Purchase_line purchase_line = purchaseLineDbController.getPurchaseLineTransaction(transaction.getId());
                    Variation_location_details variation_location_detail = variationLocationDetailDbController.getVariationLocationDetailsByStationIdProductId(transaction.getLocation_id(), local_product.get(i).getId());
                    variation_location_detail.setProduct_id(productDbController.getProductById(local_product.get(i).getId()).getProduct_server_id());
                    variation_location_detail.setProduct_variation_id(variation__.getVariation_server_id());
                    variation_location_detail.setVariation_id(variation__.getVariation_server_id());
                    variation_location_detail.setPurchase_price(transaction.getFinal_total());
                    variation_location_detail.setQuantity(variation_location_detail.getQty_available() + "");
                    variation_location_detail.setTransaction_date(transaction.getTransaction_date());
                    variation_location_detail.setFinal_total(transaction.getFinal_total());
                    variation_location_details.add(variation_location_detail);
                }

                HashMap<Object, Object> hashMap = new HashMap<>();
                hashMap.put("product_id", product__.getProduct_server_id());
//            hashMap.put("business_id", 1);
//            hashMap.put("purchase_price", 22);
//            hashMap.put("quantity", 109);
                hashMap.put("created_by", 1);
                //  hashMap.put("final_total", 267);
                hashMap.put("variation_location_details", variation_location_details);


                arrayListhashMap.add(hashMap);
            }
        }
        if (arrayListhashMap.size() > 0) {
            HashMap<Object, Object> hashMapAll = new HashMap<>();
            hashMapAll.put("opening_stocks", arrayListhashMap);

            Log.d(TAG, "variation location ids ## " + new Gson().toJson(hashMapAll));
            showProgress();
            Observable<ResponseUser<String>> observableStoreOpeningStock = APIClient.getINSTANCE(_context).storeOpeningStock(hashMapAll)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread());
            observableStoreOpeningStock.subscribe(o ->
                    {
                        mConnectProgressDialog.dismiss();
                        for (int i = 0; i < local_product.size(); i++) {
                            ArrayList<Transaction> transactionArrayList = transactionDbController.getTransactionsByOpeningStockProductId(local_product.get(i).getId());
                            for (Transaction transaction : transactionArrayList) {
                                transactionDbController.setSyncTransaction(transaction);
                            }
                        }
                        Log.d(TAG, "on subscribe ## " + new Gson().toJson(o));
                    },
                    e ->
                    {
                        Log.d(TAG, "onError ## " + e.getMessage());
                        mConnectProgressDialog.dismiss();
                    }
            );
        }

    }

    private void startPrintTicket() {
       /* showToastClient = new ShowToastClient();
        showToastClient.execute();*/
        //  postProduct(productDbController.getSyncProduct("no"));
        //  getProduct();

//        if (cProductSwitch) {
//            //  getListCategorieApi();
//            if (productDbController.getSyncProduct("no").size() > 0) {
//                Toast.makeText(_context, "## beging process ### ", Toast.LENGTH_LONG).show();
//                postProduct();
//            } else {
//                // getListProductApi();
//                Toast.makeText(_context, "## No product to push ### ", Toast.LENGTH_LONG).show();
//                getProduct();
//            }
//        }
    }

    private void postProduct() {
        ArrayList<Product> localProduct = productDbController.getSyncProduct("no");

        if (localProduct.size() == 0) {
            mConnectProgressDialog.dismiss();
            Toast.makeText(_context, getResources().getString(R.string.string_data_up_to_date), Toast.LENGTH_LONG).show();
        } else {
            //  showProgress();
            /*
            Convert Image to base64 before send
             */
            for (Product product1 : localProduct) {
                String base64String = "";
                try {
                    ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
                    product1.getImage_product().compress(Bitmap.CompressFormat.PNG, 100, byteArrayOutputStream);
                    byte[] byteArray = byteArrayOutputStream.toByteArray();
                    base64String = Base64.encodeToString(byteArray, Base64.DEFAULT);
                } catch (Exception e) {
                    //  OSSUtils.logError(e.getMessage());
                }
                product1.setImage(base64String);
            }

            HashMap<Object, Object> hashMap = new HashMap<>();
            hashMap.put("products", localProduct);
            Log.d(TAG, "localProduct ## " + new Gson().toJson(hashMap));

            Observable<ResponseUser<ArrayList<Product>>> observablePostProducts = APIClient.getINSTANCE(_context).storeProduct(hashMap)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread());
            observablePostProducts.subscribe(o ->
                    {
                        mConnectProgressDialog.dismiss();
                        Log.d(TAG, "on subscribe ## " + new Gson().toJson(o));
                        if (o.getSuccess() == 1) {
                            ArrayList<Product> serverProducts = o.getData();
                            for (int i = 0; i < localProduct.size(); i++) {
                                localProduct.get(i).setProduct_server_id(serverProducts.get(i).getId());
                                localProduct.get(i).setIs_sync("yes");
                                if (serverProducts.get(i).getImage() != null) {
                                    localProduct.get(i).setImage_url(serverProducts.get(i).getImage_url());
                                }
                                productDbController.updateProductServer(localProduct.get(i));

                                Variation variation = localProduct.get(i).getVariations();
                                variation.setVariation_server_id(serverProducts.get(i).getVariation_id());
                                variationsDbController.editServerId(variation);
                            }

                            //     postOpeningStock(localProduct);

                            Toast.makeText(_context, "Products Synchronized Successfuly", Toast.LENGTH_LONG).show();
                        } else {
                            mConnectProgressDialog.dismiss();
                            Toast.makeText(_context, "error server", Toast.LENGTH_LONG).show();
                        }
                    },
                    e ->
                    {
                        Log.d(TAG, "onError ## " + e.getMessage());
                        mConnectProgressDialog.dismiss();
                    }
            );
        }
    }

    private void postCategory() {
        //  showProgress();

        ArrayList<Category> localCategory = categoryDbController.getSyncCategory(NO);
        if (localCategory.size() == 0) {
            postSubCategory();
        } else {

            HashMap<Object, Object> hashMap = new HashMap<>();
            Log.d(TAG, " categories " + new Gson().toJson(localCategory));

            hashMap.put("categories", localCategory);
            Observable<ResponseUser<ArrayList<Category>>> observablePostCategories = APIClient.getINSTANCE(_context).storeCategories(hashMap)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread());
            observablePostCategories.subscribe(o ->
                    {
                        Log.d(TAG, " categorie gson " + new Gson().toJson(o));
                        if (o.getSuccess() == 1) {
                            ArrayList<Category> serverCatgories = o.getData();
                            for (int i = 0; i < localCategory.size(); i++) {
                                localCategory.get(i).setCategory_server_id(serverCatgories.get(i).getId());
                                localCategory.get(i).setIs_sync("yes");
                                categoryDbController.editServerCategory(localCategory.get(i));
                            }
                            //    Toast.makeText(_context, "Post Categories Successfuly", Toast.LENGTH_LONG).show();
                            // mConnectProgressDialog.dismiss();
                            postSubCategory();
                        } else {
                            mConnectProgressDialog.dismiss();
                            Toast.makeText(_context, "error server", Toast.LENGTH_LONG).show();
                        }
                    },
                    e ->
                    {
                        Log.d(TAG, "onError ## " + e.getMessage());
                        mConnectProgressDialog.dismiss();
                    }
            );
        }


    }


    private void postUnits() {
        ArrayList<Unit> localUnits = unitDbController.getSyncUnits(NO);
        if (localUnits.size() == 0) {
            storeDiscounts();
        } else {
            // showProgress();

            HashMap<Object, Object> hashMap = new HashMap<>();

            hashMap.put("units", localUnits);
            Observable<ResponseUser<ArrayList<Unit>>> observablePostCategories = APIClient.getINSTANCE(_context).storeUnits(hashMap)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread());
            observablePostCategories.subscribe(o ->
                    {
                        Log.d(TAG, " units response gson " + new Gson().toJson(o));
                        if (o.getSuccess() == 1) {
                            ArrayList<Unit> serverUnits = o.getData();
                            for (int i = 0; i < localUnits.size(); i++) {
                                localUnits.get(i).setUnit_server_id(serverUnits.get(i).getId());
                                localUnits.get(i).setSync(YES);
                                unitDbController.editServerCategory(localUnits.get(i));
                            }
                            //   Toast.makeText(_context, "Post Units Successfuly", Toast.LENGTH_LONG).show();
                            //  mConnectProgressDialog.dismiss();
                            storeDiscounts();
                        } else {
                            mConnectProgressDialog.dismiss();
                            Toast.makeText(_context, "error server", Toast.LENGTH_LONG).show();
                        }
                    },
                    e ->
                    {
                        Log.d(TAG, "onError ## " + e.getMessage());
                        mConnectProgressDialog.dismiss();
                    }
            );
        }


    }

    private void storeDiscounts() {
        ArrayList<Discount> localDiscounts = discountDbController.getSyncDiscount(NO);
        //   Log.d(TAG, " localDiscounts response gson " + new Gson().toJson(localDiscounts));

        if (localDiscounts.size() == 0) {
            postTaxesRates();
        } else {
            //showProgress();

            HashMap<Object, Object> hashMap = new HashMap<>();

            hashMap.put("discounts", localDiscounts);
            Observable<ResponseUser<ArrayList<Discount>>> observablePostCategories = APIClient.getINSTANCE(_context).storeDiscounts(hashMap)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread());


            observablePostCategories.subscribe(o ->
                    {
                        Log.d(TAG, " discounts response gson " + new Gson().toJson(o));
                        if (o.getSuccess() == 1) {
                            ArrayList<Discount> serverDiscounts = o.getData();
                            for (int i = 0; i < localDiscounts.size(); i++) {
                                localDiscounts.get(i).setDiscount_server_id(serverDiscounts.get(i).getId());
                                localDiscounts.get(i).setSync(YES);
                                discountDbController.editServerDiscounts(localDiscounts.get(i));
                            }
                            //  Toast.makeText(_context, "Post discounnts Successfuly", Toast.LENGTH_LONG).show();
                            // mConnectProgressDialog.dismiss();
                            postTaxesRates();
                        } else {
                            mConnectProgressDialog.dismiss();
                            Toast.makeText(_context, "error server", Toast.LENGTH_LONG).show();
                        }
                    },
                    e ->
                    {
                        Log.d(TAG, "onError ## " + e.getMessage());
                        mConnectProgressDialog.dismiss();
                    }
            );
        }


    }

    private void postSubCategory() {

        ArrayList<Category> localSubCategory = categoryDbController.getSyncSubCategory("no");
        if (localSubCategory.size() == 0) {
            //  postUnits();

            postUnits();
        } else {
            //showProgress();

            HashMap<Object, Object> hashMap = new HashMap<>();
            Log.d(TAG, " subcategories " + new Gson().toJson(localSubCategory));

            hashMap.put("categories", localSubCategory);
            Observable<ResponseUser<ArrayList<Category>>> observablePostCategories = APIClient.getINSTANCE(_context).storeCategories(hashMap)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread());
            observablePostCategories.subscribe(o ->
                    {
                        Log.d(TAG, " subcategorie gson " + new Gson().toJson(o));
                        if (o.getSuccess() == 1) {
                            ArrayList<Category> serverCatgories = o.getData();
                            for (int i = 0; i < localSubCategory.size(); i++) {
                                localSubCategory.get(i).setCategory_server_id(serverCatgories.get(i).getId());
                                localSubCategory.get(i).setIs_sync("yes");
                                categoryDbController.editServerCategory(localSubCategory.get(i));
                            }
                            //   Toast.makeText(_context, "Post Categories Successfuly", Toast.LENGTH_LONG).show();
                            //     mConnectProgressDialog.dismiss();
                            //    postUnits();
                            postUnits();
                        } else {
                            mConnectProgressDialog.dismiss();
                            Toast.makeText(_context, "error server", Toast.LENGTH_LONG).show();
                        }
                    },
                    e ->
                    {
                        Log.d(TAG, "onError ## " + e.getMessage());
                        mConnectProgressDialog.dismiss();
                    }
            );
        }

    }

    private void postCustomers() {

        ArrayList<Contact> contacts = contactDbController.getSyncCustomer(NO);
        if (contacts.size() == 0) {
            Toast.makeText(_context, getResources().getString(R.string.string_data_up_to_date), Toast.LENGTH_LONG).show();
        } else {
            showProgress();
            HashMap<Object, Object> tmpHashMap = new HashMap<>();
            tmpHashMap.put("contacts", contacts);
            Log.d(TAG, " contacts #### " + new Gson().toJson(tmpHashMap));
            Observable<ResponseUser<ArrayList<Contact>>> observablePostCustomers = APIClient.getINSTANCE(_context).storeContact(tmpHashMap)
                    .subscribeOn(Schedulers.io())
                    .observeOn(AndroidSchedulers.mainThread());
            observablePostCustomers.subscribe(o -> {
                        mConnectProgressDialog.dismiss();
                        if (o.getSuccess() == 1) {

                            Log.d(TAG, "onNext postCustomers ## " + new Gson().toJson(o));
                            for (int i = 0; i < o.getData().size(); i++) {
                                contacts.get(i).setContact_server_id(o.getData().get(i).getId());
                                contactDbController.updateContactServer(contacts.get(i));
                            }
                            Toast.makeText(_context, "Post contacts successfuly", Toast.LENGTH_LONG).show();
                            // getCustomers();
                        }
                    }, e -> {
                        mConnectProgressDialog.dismiss();

                        Log.d(TAG, "onError ## " + e.getMessage());
                    }
            );
        }
    }

    private void getProduct() {
//        Toast.makeText(_context, "Start loading products ...", Toast.LENGTH_LONG).show();
//        Log.d(TAG, "### non sync products ### " + new Gson().toJson(productDbController.getSyncProduct("no")));
//        Observable<ResponseUser<ArrayList<Product>>> observableGetProduct = APIClient.getINSTANCE(_context).getProduct()
//                .subscribeOn(Schedulers.io())
//                .observeOn(AndroidSchedulers.mainThread());
//        observableGetProduct.subscribe(o -> {
//                    Log.d(TAG, "onNext products ## " + new Gson().toJson(o.getData()));
//                    if (o.getData() != null && o.getData().size() > 0) {
//                        productDbController.deleteAll();
//                        for (Product product : o.getData()) {
//                            productDbController.insert(product);
//                        }
//                    }
//                }, e -> {
//                    Log.d(TAG, "onError ## " + e.getMessage());
//                }
//        );
    }


    private void getCustomers() {
        Toast.makeText(_context, "Start loading customers ...", Toast.LENGTH_LONG).show();
        Log.d(TAG, "### non sync customers ### " + new Gson().toJson(contactDbController.getSyncCustomer("no")));

        Observable<ResponseUser<ArrayList<Contact>>> observableGetCustomer = APIClient.getINSTANCE(_context).getCustomer()
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread());
        observableGetCustomer.subscribe(o -> {
                    Log.d(TAG, "onNexts customer ## " + new Gson().toJson(o.getData()));
                    if (o.getData() != null && o.getData().size() > 0) {
                        contactDbController.deleteAll();
                        for (Contact contact : o.getData()) {
                            contactDbController.insert(contact);
                        }
                    }
                }, e -> {
                    Log.d(TAG, "onError ## " + e.getMessage());
                }
        );
    }


    public class ShowToastClient extends AsyncTask<String, Void, Boolean> {

        private String TAG = "PrintTicketClient";

        @Override
        protected Boolean doInBackground(String... params) {
/*
            try {
                // sync products
                Log.d(TAG, "======count=====  " + productDbController.dbSyncCount());
                if (cProductSwitch) {
                    //  getListCategorieApi();
                    if (productDbController.getSyncProduct("no") != null) {
                        syncProduct(productDbController.getSyncProduct("no"));
                    } else {
                        getListProductApi();
                    }
                }

                if (cCategorySwitch) {
                    // getListCategorieApi();
                    //  showSyncProgress();
                    getListCategorieApi();
                }

                if (cCustomerSwitch) {
                    //showSyncProgress();
                    if (customerDbController.getSyncCustomer("no") != null) {
                        syncCustomer(customerDbController.getSyncCustomer("no"));
                    } else {
                        getCustomer();
                    }

                }

                if (cTransactionSwitch) {
                    if (transactionDbController.isTransactionSync("no")) {
                        postTran(transactionDbController.getSyncTransaction("no"));
                        //  Log.d("TAG", "he suis ici");
                    } else {
                        //Toast.makeText(this, "All Transactions are up-to-date", Toast.LENGTH_LONG).show();
                    }
                }

                //  resultat = true;

            } catch (Exception e) {
                e.printStackTrace();
            }
            return resultat;*/

            return true;
        }

        @Override
        protected void onPreExecute() {
            super.onPreExecute();
            //  showCategoryProgress();

        }

        @Override
        protected void onPostExecute(Boolean aBoolean) {
            //super.onPostExecute(aBoolean);
//            Toast.makeText(getApplicationContext(), "All Data are up-to-date\n ", Toast.LENGTH_LONG).show();
//            categoryProgressDialog.dismiss();
//            resultat = false;

        }

        @Override
        protected void onCancelled() {
            super.onCancelled();
        }

    }


}