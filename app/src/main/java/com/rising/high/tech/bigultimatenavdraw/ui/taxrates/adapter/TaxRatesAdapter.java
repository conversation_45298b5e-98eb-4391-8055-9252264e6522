package com.rising.high.tech.bigultimatenavdraw.ui.taxrates.adapter;

import android.content.Context;
import android.content.res.Resources;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.widget.AppCompatImageView;
import androidx.recyclerview.widget.RecyclerView;

import com.google.gson.Gson;
import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.db.TaxRatesDbController;
import com.rising.high.tech.bigultimatenavdraw.model.Tax_rates;

import java.util.ArrayList;


public class TaxRatesAdapter extends RecyclerView.Adapter<TaxRatesAdapter.ListTax_ratesViewHolder> {
    private static final String TAG = "ListTax_ratesAdapter";
    private ArrayList<Tax_rates> dataList = new ArrayList<>();
    private Resources resources;
    Context context;

    private TaxRatesDbController taxRatesDbController;

    @Override
    public ListTax_ratesViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        context = parent.getContext();
        resources = context.getResources();
        taxRatesDbController = new TaxRatesDbController(context);
        taxRatesDbController.open();

        return new ListTax_ratesViewHolder(LayoutInflater.from(parent.getContext())
                .inflate(R.layout.tax_rates_item, parent, false));
    }

    @Override
    public void onBindViewHolder(ListTax_ratesViewHolder holder, int position) {

        holder.name.setText(dataList.get(position).getName());
        holder.tax_rate.setText(dataList.get(position).getAmount());

    }

    public void setData(ArrayList<Tax_rates> arrayList) {
        this.dataList = arrayList;
        notifyDataSetChanged();
    }


    @Override
    public int getItemCount() {
        return dataList.size();
    }

    public class ListTax_ratesViewHolder extends RecyclerView.ViewHolder {

        TextView name, tax_rate;
        AppCompatImageView btnDelete, btnEdit;

        public ListTax_ratesViewHolder(View itemView) {
            super(itemView);

            name = itemView.findViewById(R.id.id_name);
            tax_rate = itemView.findViewById(R.id.tax_rate);
            btnDelete = itemView.findViewById(R.id.btn_delete);
            btnEdit = itemView.findViewById(R.id.btn_edit);

            btnDelete.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    deleteItem(getAdapterPosition());
                }
            });

            btnEdit.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    editTax_rates(getAdapterPosition());
                }
            });


        }
    }


    private void editTax_rates(int position) {
        //Preparing views
        // get prompts.xml view
        LayoutInflater li = LayoutInflater.from(context);
        View promptsView = li.inflate(R.layout.tax_rates_add_main, null);


        androidx.appcompat.app.AlertDialog.Builder alertDialogBuilder = new androidx.appcompat.app.AlertDialog.Builder(
                context);

        alertDialogBuilder.setView(promptsView);

        final EditText txtName = promptsView.findViewById(R.id.tax_name);
        final TextView title_id = promptsView.findViewById(R.id.title_id);
        final EditText tax_amount = promptsView.findViewById(R.id.tax_amount);
        final Button ButtonSave = promptsView.findViewById(R.id.btn_save);
        final AppCompatImageView ButtonClose = promptsView.findViewById(R.id.btn_close);

        txtName.setText(dataList.get(position).getName());
        tax_amount.setText(dataList.get(position).getAmount());

        ButtonSave.setText(context.getResources().getString(R.string.label_updatee));
        title_id.setText(context.getResources().getString(R.string.string_edit_tax_rate));

        final androidx.appcompat.app.AlertDialog mAlertDialog = alertDialogBuilder.create();
        if (mAlertDialog.getWindow() != null)
            mAlertDialog.getWindow().getAttributes().windowAnimations = R.style.SlidingDialogAnimation;
        mAlertDialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));

        ButtonClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mAlertDialog.dismiss();
            }
        });


        ButtonSave.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!txtName.getText().toString().equals("") && !tax_amount.getText().toString().equals("")) {

                    Tax_rates tax_rates = dataList.get(position);
                    tax_rates.setName(txtName.getText().toString());
                    tax_rates.setAmount(tax_amount.getText().toString());

                    int inserted = taxRatesDbController.editTax_rates(tax_rates);
                    if (inserted > 0) {
                        Toast.makeText(context, context.getResources().getString(R.string.lbl_added_success), Toast.LENGTH_LONG).show();
                        notifyItemChanged(position);
                        mAlertDialog.dismiss();
                    } else {
                        Toast.makeText(context, "Error while adding", Toast.LENGTH_LONG).show();
                    }
                } else {
                    Toast.makeText(context, context.getResources().getString(R.string.label_all_field_required), Toast.LENGTH_LONG).show();
                }

            }
        });


        mAlertDialog.show();
    }

    private void deleteItem(int postition) {
        //Preparing views
        // get prompts.xml view
        LayoutInflater li = LayoutInflater.from(context);
        View promptsView = li.inflate(R.layout.delete_dialog_main, null);

        androidx.appcompat.app.AlertDialog.Builder alertDialogBuilder = new androidx.appcompat.app.AlertDialog.Builder(
                context);

        alertDialogBuilder.setView(promptsView);

        final Button ButtonSave = promptsView.findViewById(R.id.btn_save);
        final Button ButtonClose = promptsView.findViewById(R.id.btn_close);

        final androidx.appcompat.app.AlertDialog mAlertDialog = alertDialogBuilder.create();
        if (mAlertDialog.getWindow() != null)
            mAlertDialog.getWindow().getAttributes().windowAnimations = R.style.SlidingDialogAnimation;
        mAlertDialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));

        ButtonClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mAlertDialog.dismiss();
            }
        });

        ButtonSave.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

                taxRatesDbController.deleteItem(dataList.get(postition).getId());
                dataList.remove(postition);
                mAlertDialog.dismiss();
                notifyDataSetChanged();

            }
        });


        mAlertDialog.show();
    }


}
