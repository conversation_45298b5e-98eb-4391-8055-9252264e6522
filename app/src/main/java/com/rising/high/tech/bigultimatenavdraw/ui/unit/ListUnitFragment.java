package com.rising.high.tech.bigultimatenavdraw.ui.unit;

import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.Spinner;
import android.widget.TextView;

import androidx.appcompat.widget.AppCompatImageView;
import androidx.appcompat.widget.SearchView;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.db.ProductDbController;
import com.rising.high.tech.bigultimatenavdraw.db.UnitDbController;
import com.rising.high.tech.bigultimatenavdraw.model.Unit;
import com.rising.high.tech.bigultimatenavdraw.ui.product.adapter.ListItemProductAdapter;
import com.rising.high.tech.bigultimatenavdraw.ui.unit.adapter.ListUnitAdapter;
import com.rising.high.tech.bigultimatenavdraw.util.FileUtil;
import com.rising.high.tech.bigultimatenavdraw.util.SessionManager;
import com.rising.high.tech.bigultimatenavdraw.util.StringFormat;
import com.rising.high.tech.bigultimatenavdraw.util.serverInteraction;

import java.util.ArrayList;
import java.util.Objects;

//import butterknife.BindView;
//import butterknife.ButterKnife;

import static com.rising.high.tech.bigultimatenavdraw.util.Constant.NO;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.PRODUCT_ADD;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.SERVER_MASTER;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.UNIT_ADD;

public class ListUnitFragment extends Fragment {

    private static final String TAG = "ListUnitFragment";
    private Context _context;

    Button addBtn;
    RecyclerView recycleUnit;
    Spinner spinnerCategory, spinnerStation, spinnerUnit;
    ListItemProductAdapter listItemProductAdapter;
    ProductDbController productDbController;
    ListUnitAdapter listUnitAdapter;
    UnitDbController unitDbController;
    @BindView(R.id.noItemFound)
    TextView noItemFound;
    @BindView(R.id.id_filter)
    Button btnFilter;
    @BindView(R.id.id_search_edit)
    SearchView searchEdit;
    private ArrayList<Unit> dataList = new ArrayList<>();

    public ListUnitFragment() {
        // Required empty public constructor
    }

    SessionManager session;

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        // Inflate the layout for this fragment
        View root = inflater.inflate(R.layout.fragment_list_unit, container, false);
        ButterKnife.bind(this, root);
        _context = getContext();
        session = new SessionManager(_context);
        recycleUnit = root.findViewById(R.id.recycle_unit);
        addBtn = root.findViewById(R.id.id_add);

        checkRoles();

        unitDbController = new UnitDbController(_context);
        unitDbController.open();

        setUpRecyclerView();

        initListners();

        return root;
    }

    private void initListners() {
        addBtn.setOnClickListener(v -> addUnit());

        searchEdit.setQueryHint("Search Here");
        searchEdit.setOnQueryTextListener(new SearchView.OnQueryTextListener() {

            @Override
            public boolean onQueryTextSubmit(String query) {
                listUnitAdapter.setData(unitDbController.getUnitsLike(query));
                setItemView();
                return false;
            }

            @Override
            public boolean onQueryTextChange(String newText) {
                listUnitAdapter.setData(unitDbController.getUnitsLike(newText));
                setItemView();
                return false;
            }
        });
    }

    private void checkRoles() {
        if (session.getBoolean(SERVER_MASTER) || !session.checkPermissionSubModule(UNIT_ADD))
        {
            addBtn.setVisibility(View.INVISIBLE);
        }
    }

    private void setUpRecyclerView() {
        listUnitAdapter = new ListUnitAdapter();
        dataList = unitDbController.getAllUnits();
        listUnitAdapter.setData(dataList);
        listUnitAdapter.setonClickAction(new ListUnitAdapter.onClickAction() {
            @Override
            public void onClickDelete(int position) {
                deleteItem(position);
            }
        });
        recycleUnit.setAdapter(listUnitAdapter);
        recycleUnit.setLayoutManager(new LinearLayoutManager(_context));
        listUnitAdapter.notifyDataSetChanged();
        setItemView();
    }

    public void replaceFragment(Fragment someFragment) {
        FragmentTransaction transaction = getFragmentManager().beginTransaction();
        transaction.replace(R.id.nav_host_fragment, someFragment);
        transaction.addToBackStack(null);
        transaction.commit();
    }


    private void addUnit() {
        //Preparing views
        // get prompts.xml view
        LayoutInflater li = LayoutInflater.from(_context);
        View promptsView = li.inflate(R.layout.unit_add_main, null);


        androidx.appcompat.app.AlertDialog.Builder alertDialogBuilder = new androidx.appcompat.app.AlertDialog.Builder(
                _context);

        alertDialogBuilder.setView(promptsView);

        final EditText txtName = promptsView.findViewById(R.id.unit_name);
        final EditText shortName = promptsView.findViewById(R.id.id_short_name);
        final Spinner decimalSpinner = promptsView.findViewById(R.id.id_spinner_decimal);
        final Button ButtonSave = promptsView.findViewById(R.id.btn_save);
        final AppCompatImageView ButtonClose = promptsView.findViewById(R.id.btn_close);


        final androidx.appcompat.app.AlertDialog mAlertDialog = alertDialogBuilder.create();
        if (mAlertDialog.getWindow() != null)
            mAlertDialog.getWindow().getAttributes().windowAnimations = R.style.SlidingDialogAnimation;
        Objects.requireNonNull(mAlertDialog.getWindow()).setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));

        ButtonClose.setOnClickListener(v -> mAlertDialog.dismiss());


        ButtonSave.setOnClickListener(v -> {
            if (txtName.getText().toString().isEmpty()) {
                txtName.requestFocus();
                txtName.setError(getString(R.string.select_unit_name));
            } else if (shortName.getText().toString().isEmpty()) {
                shortName.requestFocus();
                shortName.setError(getString(R.string.select_short_name));
            } else if (decimalSpinner.getSelectedItemPosition() == 0) {
                StringFormat.showSnackBar(promptsView, R.string.select_allow_decimal, true);
            } else {

                Unit unit = new Unit();
                unit.setActual_name(txtName.getText().toString());
                unit.setShort_name(shortName.getText().toString());
                unit.setBusiness_id(session.getBusinessModel().getId());
                if (decimalSpinner.getSelectedItemPosition() == 1) {
                    unit.setAllow_decimal(1);
                }
                if (decimalSpinner.getSelectedItemPosition() == 2) {
                    unit.setAllow_decimal(0);
                }
                unit.setSync(NO);
                int inserted = unitDbController.insertLocal(unit);
                if (inserted > 0) {
                    FileUtil.showDialog(_context,
                            getString(R.string.success), getResources().getString(R.string.units_added_success));
                    listUnitAdapter.setData(unitDbController.getAllUnits());
                    listUnitAdapter.notifyDataSetChanged();
                    mAlertDialog.dismiss();
                } else {
                    StringFormat.showSnackBar(promptsView, R.string.update_db_error, true);
                }
            }
            setItemView();
        });
        mAlertDialog.show();
    }

    public void setItemView() {
        if (listUnitAdapter.getItemCount() > 0) {
            recycleUnit.setVisibility(View.VISIBLE);
            noItemFound.setVisibility(View.GONE);
        } else {
            recycleUnit.setVisibility(View.GONE);
            noItemFound.setVisibility(View.VISIBLE);
        }
    }

    private void deleteItem(int position) {

        LayoutInflater li = LayoutInflater.from(_context);
        View promptsView = li.inflate(R.layout.delete_dialog_main, null);

        androidx.appcompat.app.AlertDialog.Builder alertDialogBuilder = new androidx.appcompat.app.AlertDialog.Builder(_context);

        alertDialogBuilder.setView(promptsView);

        final Button ButtonDelete = promptsView.findViewById(R.id.btn_save);
        final Button ButtonClose = promptsView.findViewById(R.id.btn_close);

        final androidx.appcompat.app.AlertDialog mAlertDialog = alertDialogBuilder.create();
        if (mAlertDialog.getWindow() != null)
            mAlertDialog.getWindow().getAttributes().windowAnimations = R.style.SlidingDialogAnimation;
        Objects.requireNonNull(mAlertDialog.getWindow()).setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));

        ButtonClose.setOnClickListener(v -> mAlertDialog.dismiss());

        ButtonDelete.setOnClickListener(v -> {
            unitDbController.deleteItemById(dataList.get(position).getId());
            dataList.remove(position);
            mAlertDialog.dismiss();
            listUnitAdapter.notifyDataSetChanged();
            setItemView();
        });
        mAlertDialog.show();
    }
}