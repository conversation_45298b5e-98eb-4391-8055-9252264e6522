package com.rising.high.tech.bigultimatenavdraw.ui.unit.adapter;

import android.content.Context;
import android.content.res.Resources;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;

import androidx.recyclerview.widget.RecyclerView;

import com.google.gson.Gson;
import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.db.UnitDbController;
import com.rising.high.tech.bigultimatenavdraw.model.Unit;
import com.rising.high.tech.bigultimatenavdraw.model.Variation_template;
import com.rising.high.tech.bigultimatenavdraw.ui.variation.adapter.ListVariationAdapter;
import com.rising.high.tech.bigultimatenavdraw.util.FileUtil;
import com.rising.high.tech.bigultimatenavdraw.util.SessionManager;
import com.rising.high.tech.bigultimatenavdraw.util.StringFormat;
import com.squareup.picasso.Callback;
import com.squareup.picasso.Picasso;

import java.util.ArrayList;
import java.util.Arrays;

import static com.rising.high.tech.bigultimatenavdraw.util.Constant.ROLES_ADD;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.SERVER_MASTER;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.UNIT_DELETE;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.UNIT_EDIT;


public class ListUnitAdapter extends RecyclerView.Adapter<ListUnitAdapter.ListUnitViewHolder> {
    private static final String TAG = "ListUnitAdapter";
    private ArrayList<Unit> dataList = new ArrayList<>();
    private Resources resources;
    Context context;
    SessionManager session;

    private UnitDbController unitDbController;

    @Override
    public ListUnitViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        context = parent.getContext();
        resources = context.getResources();
        session = new SessionManager(context);
        unitDbController = new UnitDbController(context);
        unitDbController.open();

        return new ListUnitViewHolder(LayoutInflater.from(parent.getContext())
                .inflate(R.layout.unit_item, parent, false));
    }

    @Override
    public void onBindViewHolder(ListUnitViewHolder holder, int position) {

        if (session.getBoolean(SERVER_MASTER) || !session.checkPermissionSubModule(UNIT_EDIT)) {
            holder.btnEdit.setVisibility(View.GONE);
        }
        if (session.getBoolean(SERVER_MASTER) || !session.checkPermissionSubModule(UNIT_DELETE)) {
            holder.btnDelete.setVisibility(View.GONE);
        }
        holder.name.setText(dataList.get(position).getActual_name());
        holder.short_name.setText(dataList.get(position).getShort_name());
        holder.decimale.setText(dataList.get(position).getAllow_decimal() == 0 ? context.getResources().getString(R.string.label_no) : context.getResources().getString(R.string.label_oui));
        if (position % 2 == 1) {
            holder.linearLayout.setBackgroundColor(context.getResources().getColor(R.color.white));
        } else {
            holder.linearLayout.setBackgroundColor(context.getResources().getColor(R.color.lightGrey));
        }
    }

    public void setData(ArrayList<Unit> arrayList) {
        this.dataList = arrayList;
        notifyDataSetChanged();
    }


    @Override
    public int getItemCount() {
        return dataList.size();
    }

    public class ListUnitViewHolder extends RecyclerView.ViewHolder {

        TextView name, short_name, decimale;
        ImageView btnDelete, btnEdit;
        LinearLayout linearLayout;

        public ListUnitViewHolder(View itemView) {
            super(itemView);

            name = itemView.findViewById(R.id.id_name);
            linearLayout = itemView.findViewById(R.id.linearLayout);
            short_name = itemView.findViewById(R.id.short_name);
            decimale = itemView.findViewById(R.id.id_decimale);
            btnDelete = itemView.findViewById(R.id.btn_delete);
            btnEdit = itemView.findViewById(R.id.btn_edit);

            btnDelete.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (onClickAction != null) {
                        onClickAction.onClickDelete(getAdapterPosition());
                    }
                    notifyItemChanged(getAdapterPosition());
                }
            });

            btnEdit.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    editUnit(getAdapterPosition());
                }
            });


        }
    }


    private void editUnit(int position) {
        //Preparing views
        // get prompts.xml view
        LayoutInflater li = LayoutInflater.from(context);
        View promptsView = li.inflate(R.layout.unit_add_main, null);


        androidx.appcompat.app.AlertDialog.Builder alertDialogBuilder = new androidx.appcompat.app.AlertDialog.Builder(
                context);

        alertDialogBuilder.setView(promptsView);

        final EditText txtName = promptsView.findViewById(R.id.unit_name);
        final EditText shortName = promptsView.findViewById(R.id.id_short_name);
        final Spinner decimalSpinner = promptsView.findViewById(R.id.id_spinner_decimal);
        final Button ButtonSave = promptsView.findViewById(R.id.btn_save);
        final ImageView ButtonClose = promptsView.findViewById(R.id.btn_close);

        txtName.setText(dataList.get(position).getActual_name());
        shortName.setText(dataList.get(position).getShort_name());
        decimalSpinner.setSelection(dataList.get(position).getAllow_decimal()!=0? 1:2);
        ButtonSave.setText(context.getResources().getString(R.string.label_updatee));

        final androidx.appcompat.app.AlertDialog mAlertDialog = alertDialogBuilder.create();
        if (mAlertDialog.getWindow() != null)
            mAlertDialog.getWindow().getAttributes().windowAnimations = R.style.SlidingDialogAnimation;
        mAlertDialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));

        ButtonClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mAlertDialog.dismiss();
            }
        });

        ButtonSave.setOnClickListener(v -> {
            if (txtName.getText().toString().isEmpty())
            {
                txtName.requestFocus();
                txtName.setError(context.getString(R.string.select_unit_name));
            }
            else if(shortName.getText().toString().isEmpty()) {
                shortName.requestFocus();
                shortName.setError(context.getString(R.string.select_short_name));
            }
            else if ( decimalSpinner.getSelectedItemPosition() == 0) {
                StringFormat.showSnackBar(promptsView,R.string.select_allow_decimal,true);
            }
            else
            {
                Unit unit = dataList.get(position);
                unit.setActual_name(txtName.getText().toString());
                unit.setShort_name(shortName.getText().toString());

                if(decimalSpinner.getSelectedItemPosition() == 1){unit.setAllow_decimal(1);}
                if(decimalSpinner.getSelectedItemPosition() == 2){unit.setAllow_decimal(0);}

                int inserted = unitDbController.editUnit(unit);
                if (inserted > 0) {
                    FileUtil.showDialog(context,context.getString(R.string.success),context.getResources().getString(R.string.units_updated_success ));

                    notifyItemChanged(position);
                    mAlertDialog.dismiss();
                } else {
                    StringFormat.showSnackBar(promptsView, R.string.update_db_error, true);
                }
            }

        });


        mAlertDialog.show();
    }


    public interface onClickAction {
        void onClickDelete(int position);
    }

    onClickAction onClickAction;

    public void setonClickAction(onClickAction mOnClickAction) {
        onClickAction = mOnClickAction;
    }

}
