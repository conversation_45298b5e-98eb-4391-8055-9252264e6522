package com.rising.high.tech.bigultimatenavdraw.ui.usermanagement;

import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.TextView;

import androidx.appcompat.widget.AppCompatImageView;
import androidx.appcompat.widget.SearchView;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.db.UserDbController;
import com.rising.high.tech.bigultimatenavdraw.ui.usermanagement.adapter.UsersAdapter;
import com.rising.high.tech.bigultimatenavdraw.util.SessionManager;


import static com.rising.high.tech.bigultimatenavdraw.util.Constant.PURCHASE_ADD;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.SERVER_MASTER;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.USER_ADD;

public class UsersFragment extends Fragment {

    private static final String TAG = "UsersFragment";
    private Context _context;
    SessionManager session;

    TextView noItemFound;
    Button addBtn;
    RecyclerView recycle_taxe_rates;
    UsersAdapter usersAdapter;
    UserDbController userDbController;

    Button btnFilter;
    SearchView search_edit;

    public UsersFragment() {
        // Required empty public constructor
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        // Inflate the layout for this fragment
        View root = inflater.inflate(R.layout.fragment_users, container, false);
        _context = getContext();
        session = new SessionManager(_context);

        recycle_taxe_rates = root.findViewById(R.id.recycle_taxe_rates);
        addBtn = root.findViewById(R.id.id_add);

        userDbController = new UserDbController(_context);
        userDbController.open();

        usersAdapter = new UsersAdapter();
        recycle_taxe_rates.setAdapter(usersAdapter);
        recycle_taxe_rates.setLayoutManager(new LinearLayoutManager(_context));

        usersAdapter.setData(userDbController.getAllUsers());

        initListners();
        setItemView();
        checkRoles();

        return root;
    }

    private void checkRoles() {
        if (session.getBoolean(SERVER_MASTER) || !session.checkPermissionSubModule(USER_ADD)) {
            addBtn.setVisibility(View.INVISIBLE);
        }
    }

    private void initListners() {
        addBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                addUser();
            }
        });

        search_edit.setQueryHint("Search Here");
        search_edit.setOnQueryTextListener(new SearchView.OnQueryTextListener() {

            @Override
            public boolean onQueryTextSubmit(String query) {
                usersAdapter.setData(userDbController.getAllUsersLike(query));
                setItemView();
                return false;
            }

            @Override
            public boolean onQueryTextChange(String newText) {
                usersAdapter.setData(userDbController.getAllUsersLike(newText));
                setItemView();
                return false;
            }
        });

    }

    public void setItemView() {
        if (usersAdapter.getItemCount() > 0) {
            recycle_taxe_rates.setVisibility(View.VISIBLE);
            noItemFound.setVisibility(View.GONE);
        } else {
            recycle_taxe_rates.setVisibility(View.GONE);
            noItemFound.setVisibility(View.VISIBLE);
        }
    }

    public void replaceFragment(Fragment someFragment) {
        FragmentTransaction transaction = getFragmentManager().beginTransaction();
        transaction.replace(R.id.nav_host_fragment, someFragment);
        transaction.addToBackStack(null);
        transaction.commit();
    }

    private void addUser() {
        //Preparing views
        // get prompts.xml view
        replaceFragment(new AddUserFragment());
    }


}