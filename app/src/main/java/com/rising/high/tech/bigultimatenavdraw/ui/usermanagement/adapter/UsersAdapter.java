package com.rising.high.tech.bigultimatenavdraw.ui.usermanagement.adapter;

import android.content.Context;
import android.content.res.Resources;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.TextView;

import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.AppCompatImageView;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.RecyclerView;

import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.db.RoleDbController;
import com.rising.high.tech.bigultimatenavdraw.db.UserDbController;
import com.rising.high.tech.bigultimatenavdraw.db.UserHasRolesDbController;
import com.rising.high.tech.bigultimatenavdraw.model.User;
import com.rising.high.tech.bigultimatenavdraw.ui.usermanagement.EditUserFragment;
import com.rising.high.tech.bigultimatenavdraw.util.SessionManager;
import java.util.ArrayList;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.SERVER_MASTER;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.USER_DELETE;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.USER_EDIT;

public class UsersAdapter extends RecyclerView.Adapter<UsersAdapter.ListUserViewHolder> {
    private static final String TAG = "ListUserAdapter";
    private ArrayList<User> dataList = new ArrayList<>();
    private Resources resources;
    Context context;
    SessionManager session;

    private UserDbController userDbController;
    private UserHasRolesDbController userHasRolesDbController;
    private RoleDbController roleDbController;
    @Override
    public ListUserViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        context = parent.getContext();
        resources = context.getResources();
        session = new SessionManager(context);

        userDbController = new UserDbController(context);
        userDbController.open();

        userHasRolesDbController=new UserHasRolesDbController(context);
        userHasRolesDbController.open();
        roleDbController=new RoleDbController(context);
        roleDbController.open();
        return new ListUserViewHolder(LayoutInflater.from(parent.getContext())
                .inflate(R.layout.user_item, parent, false));
    }

    @Override
    public void onBindViewHolder(ListUserViewHolder holder, int position) {

        if (session.getBoolean(SERVER_MASTER) || !session.checkPermissionSubModule(USER_EDIT)) {
            holder.btnEdit.setVisibility(View.GONE);
        }
        if (session.getBoolean(SERVER_MASTER) ||  !session.checkPermissionSubModule(USER_DELETE)) {
            holder.btnDelete.setVisibility(View.GONE);
        }
        holder.id_username.setText(dataList.get(position).getUsername());
        if (position==0)
        {
            holder.btnDelete.setEnabled(false);
         //   holder.btnEdit.setEnabled(false);
        }
        int role_id= userHasRolesDbController.getRolesByUserId(dataList.get(position).getId()).getRole_id();
        String role_name=roleDbController.getRoleById(role_id).getName();
        holder.role.setText(role_name);
        holder.email.setText(dataList.get(position).getEmail());
        holder.name.setText(dataList.get(position).getFirst_name() + " " + dataList.get(position).getLast_name());

    }

    public void setData(ArrayList<User> arrayList) {
        this.dataList = arrayList;
        notifyDataSetChanged();
    }


    @Override
    public int getItemCount() {
        return dataList.size();
    }

    public class ListUserViewHolder extends RecyclerView.ViewHolder {

        TextView id_username, name, role, email;
        AppCompatImageView btnDelete, btnEdit;

        public ListUserViewHolder(View itemView) {
            super(itemView);

            id_username = itemView.findViewById(R.id.id_username);
            name = itemView.findViewById(R.id.name);
            role = itemView.findViewById(R.id.role);
            email = itemView.findViewById(R.id.email);

            btnDelete = itemView.findViewById(R.id.btn_delete);
            btnEdit = itemView.findViewById(R.id.btn_edit);

            btnDelete.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    deleteItem(getAdapterPosition());
                }
            });

            btnEdit.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {

                    naviguateFragment(dataList.get(getAdapterPosition()).getId());

                }
            });


        }
    }


//    private void editUser(int position) {
//        //Preparing views
//        // get prompts.xml view
//        LayoutInflater li = LayoutInflater.from(context);
//        View promptsView = li.inflate(R.layout.tax_rates_add_main, null);
//
//
//        androidx.appcompat.app.AlertDialog.Builder alertDialogBuilder = new androidx.appcompat.app.AlertDialog.Builder(
//                context);
//
//        alertDialogBuilder.setView(promptsView);
//
//        final EditText txtName = promptsView.findViewById(R.id.tax_name);
//        final EditText tax_amount = promptsView.findViewById(R.id.tax_amount);
//        final Button ButtonSave = promptsView.findViewById(R.id.btn_save);
//        final Button ButtonClose = promptsView.findViewById(R.id.btn_close);
//
//        txtName.setText(dataList.get(position).getName());
//        tax_amount.setText(dataList.get(position).getAmount());
//
//        ButtonSave.setText(context.getResources().getString(R.string.label_updatee));
//
//        final androidx.appcompat.app.AlertDialog mAlertDialog = alertDialogBuilder.create();
//        if (mAlertDialog.getWindow() != null)
//            mAlertDialog.getWindow().getAttributes().windowAnimations = R.style.SlidingDialogAnimation;
//        mAlertDialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
//
//        ButtonClose.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                mAlertDialog.dismiss();
//            }
//        });
//
//
//        ButtonSave.setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                if (!txtName.getText().toString().equals("") && !tax_amount.getText().toString().equals("")) {
//
//                    User tax_rates = dataList.get(position);
//                    tax_rates.setName(txtName.getText().toString());
//                    tax_rates.setAmount(tax_amount.getText().toString());
//
//                    int inserted = taxRatesDbController.editUser(tax_rates);
//                    if (inserted > 0) {
//                        Toast.makeText(context, context.getResources().getString(R.string.lbl_added_success), Toast.LENGTH_LONG).show();
//                        notifyItemChanged(position);
//                        mAlertDialog.dismiss();
//                    } else {
//                        Toast.makeText(context, "Error while adding", Toast.LENGTH_LONG).show();
//                    }
//                } else {
//                    Toast.makeText(context, context.getResources().getString(R.string.label_all_field_required), Toast.LENGTH_LONG).show();
//                }
//
//            }
//        });
//
//
//        mAlertDialog.show();
//    }

    private void deleteItem(int postition) {
        //Preparing views
        // get prompts.xml view
        LayoutInflater li = LayoutInflater.from(context);
        View promptsView = li.inflate(R.layout.delete_dialog_main, null);

        androidx.appcompat.app.AlertDialog.Builder alertDialogBuilder = new androidx.appcompat.app.AlertDialog.Builder(
                context);

        alertDialogBuilder.setView(promptsView);

        final Button ButtonSave = promptsView.findViewById(R.id.btn_save);
        final Button ButtonClose = promptsView.findViewById(R.id.btn_close);

        final androidx.appcompat.app.AlertDialog mAlertDialog = alertDialogBuilder.create();
        if (mAlertDialog.getWindow() != null)
            mAlertDialog.getWindow().getAttributes().windowAnimations = R.style.SlidingDialogAnimation;
        mAlertDialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));

        ButtonClose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mAlertDialog.dismiss();
            }
        });

        ButtonSave.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

                userDbController.deleteItem(dataList.get(postition).getId());
                dataList.remove(postition);
                mAlertDialog.dismiss();
                notifyDataSetChanged();

            }
        });


        mAlertDialog.show();
    }

    private void naviguateFragment(int id) {
        AppCompatActivity activity = (AppCompatActivity) context;
        Bundle bundle = new Bundle();
        bundle.putInt("id", id);
        Fragment myFragment = new EditUserFragment();
        myFragment.setArguments(bundle);
        activity.getSupportFragmentManager().beginTransaction().replace(R.id.nav_host_fragment, myFragment).addToBackStack(null).commit();
    }



}
