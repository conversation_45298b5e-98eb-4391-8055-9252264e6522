package com.rising.high.tech.bigultimatenavdraw.ui.variation.adapter;

import android.content.Context;
import android.content.res.Resources;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.ImageView;

import androidx.recyclerview.widget.RecyclerView;

import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.model.VariationTemplateValues;

import java.util.ArrayList;


public class ListAddVariationAdapter extends RecyclerView.Adapter<ListAddVariationAdapter.ListXVariationViewHolder> {

    private ArrayList<VariationTemplateValues> dataList = new ArrayList<>();
    private Resources resources;
    Context context;

    @Override
    public ListXVariationViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        context = parent.getContext();
        resources = context.getResources();
        return new ListXVariationViewHolder(LayoutInflater.from(parent.getContext())
                .inflate(R.layout.variation_add_item, parent, false));
    }

    @Override
    public void onBindViewHolder(ListXVariationViewHolder holder, int position) {
        holder.txtName.setText(dataList.get(position).getName());

        holder.txtName.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                if(s.length() != 0){
                    //   dataList.add(s.toString());
                 //  dataList.set(position,new XVariation(s.toString()));
                    dataList.get(position).setName(s.toString());

                }
            }

            @Override
            public void afterTextChanged(Editable s) {
            }
        });
    }

    public void setData(ArrayList<VariationTemplateValues> arrayList) {
        this.dataList = arrayList;
        notifyDataSetChanged();
    }

    public ArrayList<VariationTemplateValues> getData() {
        notifyDataSetChanged();
       return this.dataList ;
    }


    @Override
    public int getItemCount() {
        return dataList.size();
    }

    public class ListXVariationViewHolder extends RecyclerView.ViewHolder {

        ImageView btn_mins ;
        EditText txtName;
        public ListXVariationViewHolder(View itemView) {
            super(itemView);

            btn_mins = itemView.findViewById(R.id.btn_mins);
            txtName = itemView.findViewById(R.id.id_name);

            btn_mins.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    dataList.remove(getAdapterPosition());
                    notifyDataSetChanged();
                }
            });



        }
    }
}
