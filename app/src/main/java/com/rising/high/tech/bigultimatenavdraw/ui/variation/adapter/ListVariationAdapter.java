package com.rising.high.tech.bigultimatenavdraw.ui.variation.adapter;

import android.content.Context;
import android.content.res.Resources;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.appcompat.widget.AppCompatEditText;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.gson.Gson;
import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.db.VariationTemplateDbController;
import com.rising.high.tech.bigultimatenavdraw.db.VariationTemplateValuesDbController;
import com.rising.high.tech.bigultimatenavdraw.model.Transaction;
import com.rising.high.tech.bigultimatenavdraw.model.VariationTemplateValues;
import com.rising.high.tech.bigultimatenavdraw.model.Variation_template;
import com.rising.high.tech.bigultimatenavdraw.ui.sellreturn.adapter.SellReturnAdapter;
import com.rising.high.tech.bigultimatenavdraw.util.SessionManager;

import java.util.ArrayList;
import java.util.Objects;

import static com.rising.high.tech.bigultimatenavdraw.util.Constant.BUSINESS_LOCATION_DELETE;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.BUSINESS_LOCATION_EDIT;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.SERVER_MASTER;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.VARIATION_DELETE;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.VARIATION_EDIT;


public class ListVariationAdapter extends RecyclerView.Adapter<ListVariationAdapter.ListXVariationViewHolder> {
    private static final String TAG = "ListVariationAdapter";
    private ArrayList<Variation_template> dataList = new ArrayList<>();
    private Resources resources;
    private VariationTemplateDbController variationTemplateDbController;
    Context context;
    SessionManager session;

    VariationTemplateValuesDbController variationTemplateValuesDbController;
    @Override
    public ListXVariationViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        context = parent.getContext();
        session = new SessionManager(context);

        resources = context.getResources();
        variationTemplateDbController = new VariationTemplateDbController(context);
        variationTemplateDbController.open();
        variationTemplateValuesDbController = new VariationTemplateValuesDbController(context);
        variationTemplateValuesDbController.open();
        return new ListXVariationViewHolder(LayoutInflater.from(parent.getContext())
                .inflate(R.layout.variation_item, parent, false));
    }
    @Override
    public void onBindViewHolder(ListXVariationViewHolder holder, int position) {


        if (session.getBoolean(SERVER_MASTER) || !session.checkPermissionSubModule(VARIATION_EDIT)) {
            holder.btnEdit.setVisibility(View.GONE);
        }
        if (session.getBoolean(SERVER_MASTER) || !session.checkPermissionSubModule(VARIATION_DELETE)) {
            holder.btnDelete.setVisibility(View.GONE);
        }


        holder.name.setText(dataList.get(position).getName());
        ArrayList<VariationTemplateValues> tmpVariationTemplate = new ArrayList<>();
        tmpVariationTemplate = variationTemplateValuesDbController.getAllVariationValues(dataList.get(position).getId());

        StringBuilder valuesData= new StringBuilder();
        int index=0;
        for (VariationTemplateValues variationTemplate : tmpVariationTemplate) {
            if(valuesData.toString().isEmpty())
            {
                valuesData = new StringBuilder(variationTemplate.getName());
            }
            else if(index == tmpVariationTemplate.size())
            {
                valuesData.append(variationTemplate.getName());
            }
            else
            {
                valuesData.append(", ").append(variationTemplate.getName());
            }
            holder.values.setText(valuesData);
        }
        if (position % 2 == 1) {
            holder.linearLayout.setBackgroundColor(context.getResources().getColor(R.color.white));
        } else {
            holder.linearLayout.setBackgroundColor(context.getResources().getColor(R.color.lightGrey));

        }
    }

    public void setData(ArrayList<Variation_template> arrayList) {
        this.dataList = arrayList;
        notifyDataSetChanged();
    }


    @Override
    public int getItemCount() {
        return dataList.size();
    }

    public class ListXVariationViewHolder extends RecyclerView.ViewHolder {

        ImageView btnDelete, btnEdit;
        TextView name, values;
        LinearLayout linearLayout;

        public ListXVariationViewHolder(View itemView) {
            super(itemView);

            name = itemView.findViewById(R.id.id_name);
            values = itemView.findViewById(R.id.values);
            btnDelete = itemView.findViewById(R.id.btn_delete);
            btnEdit = itemView.findViewById(R.id.btn_edit);
            linearLayout = itemView.findViewById(R.id.linearLayout);

            btnDelete.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (onClickAction != null) {
                        onClickAction.onClickDelete(getAdapterPosition());
                    }
                    notifyItemChanged(getAdapterPosition());
                }
            });

            btnEdit.setOnClickListener(v -> {
                if (onClickAction != null) {
                    onClickAction.onClickEdit(dataList.get(getAdapterPosition()));
                }
                notifyItemChanged(getAdapterPosition());

            });

        }
    }

    public interface onClickAction {
        void onClickEdit(Variation_template variation_template);
        void onClickDelete(int position);
    }

    onClickAction onClickAction;

    public void setonClickAction(onClickAction mOnClickAction) {
        onClickAction = mOnClickAction;
    }
}
