package com.rising.high.tech.bigultimatenavdraw.ui.warranty;

import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.Spinner;
import android.widget.TextView;

import androidx.appcompat.widget.AppCompatButton;
import androidx.appcompat.widget.SearchView;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.gson.Gson;
import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.db.WarrantyDbController;
import com.rising.high.tech.bigultimatenavdraw.model.Warranty;
import com.rising.high.tech.bigultimatenavdraw.ui.warranty.adapter.ListWarrantyAdapter;
import com.rising.high.tech.bigultimatenavdraw.util.FileUtil;
import com.rising.high.tech.bigultimatenavdraw.util.SessionManager;
import com.rising.high.tech.bigultimatenavdraw.util.StringFormat;
import com.rising.high.tech.bigultimatenavdraw.util.serverInteraction;

import java.util.ArrayList;

import butterknife.BindView;
import butterknife.ButterKnife;

import static com.rising.high.tech.bigultimatenavdraw.util.Constant.SERVER_MASTER;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.USER_ADD;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.WARRANTY_ADD;

public class ListWarrantyFragment extends Fragment {

    private static final String TAG = "ListWarrantyFragment";
    private Context _context;

    RecyclerView recycle_warranty;
    ListWarrantyAdapter listWarrantyAdapter;
    WarrantyDbController warrantyDbController;

    AppCompatButton btnAdd;

    @BindView(R.id.id_search_edit)
    SearchView searchEdit;
    @BindView(R.id.noItemFound)
    TextView noItemFound;
    private ArrayList<Warranty> dataList = new ArrayList<>();
    SessionManager session;

    public ListWarrantyFragment() {
        // Required empty public constructor
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        // Inflate the layout for this fragment
        View root = inflater.inflate(R.layout.fragment_list_warranty, container, false);
        _context = getContext();
        ButterKnife.bind(this, root);
        session = new SessionManager(_context);
        recycle_warranty = root.findViewById(R.id.recycle_warranty);
        btnAdd = root.findViewById(R.id.id_add);

        checkRoles();


        warrantyDbController = new WarrantyDbController(_context);
        warrantyDbController.open();
        setUpRecyclerView();

        initListners();
        return root;
    }

    private void checkRoles()
    {
        if (session.getBoolean(SERVER_MASTER) || !session.checkPermissionSubModule(WARRANTY_ADD)) {
            btnAdd.setVisibility(View.INVISIBLE);
        }
    }

    private void initListners(){
        btnAdd.setOnClickListener(v -> showDetail());

        searchEdit.setQueryHint("Search Here");
        searchEdit.setOnQueryTextListener(new SearchView.OnQueryTextListener() {

            @Override
            public boolean onQueryTextSubmit(String query) {
                listWarrantyAdapter.setData(warrantyDbController.getWarrantiesLike(query));
                setItemView();
                return false;
            }

            @Override
            public boolean onQueryTextChange(String newText) {
                listWarrantyAdapter.setData(warrantyDbController.getWarrantiesLike(newText));
                setItemView();
                return false;
            }
        });
    }

    private void setUpRecyclerView()
    {
        recycle_warranty.setLayoutManager(new LinearLayoutManager(_context));
        listWarrantyAdapter = new ListWarrantyAdapter();
        dataList= warrantyDbController.getAllWarranty();
        listWarrantyAdapter.setData(dataList);
        recycle_warranty.setAdapter(listWarrantyAdapter);
        recycle_warranty.setLayoutManager(new LinearLayoutManager(_context));
        listWarrantyAdapter.notifyDataSetChanged();
        setItemView();
    }
    public void setItemView()
    {
        if(listWarrantyAdapter.getItemCount() > 0)
        {
            recycle_warranty.setVisibility(View.VISIBLE);
            noItemFound.setVisibility(View.GONE);
        }
        else
        {
            recycle_warranty.setVisibility(View.GONE);
            noItemFound.setVisibility(View.VISIBLE);
        }
    }


    public void replaceFragment(Fragment someFragment) {
        FragmentTransaction transaction = getFragmentManager().beginTransaction();
        transaction.replace(R.id.nav_host_fragment, someFragment);
        transaction.addToBackStack(null);
        transaction.commit();
    }

    private void showDetail() {
        LayoutInflater li = LayoutInflater.from(_context);
        View promptsView = li.inflate(R.layout.warranty_add_main, null);


        androidx.appcompat.app.AlertDialog.Builder alertDialogBuilder = new androidx.appcompat.app.AlertDialog.Builder(
                _context);

        alertDialogBuilder.setView(promptsView);
        final Button ButtonSave = promptsView.findViewById(R.id.btn_save);
        final ImageView ButtonClose = promptsView.findViewById(R.id.btn_close);
        final TextView name = promptsView.findViewById(R.id.name);
        final TextView description = promptsView.findViewById(R.id.id_description);
        final Spinner spinduration = promptsView.findViewById(R.id.id_spinner_duration);
        final EditText numbercount = promptsView.findViewById(R.id.number_count);

        final androidx.appcompat.app.AlertDialog mAlertDialog = alertDialogBuilder.create();
        if (mAlertDialog.getWindow() != null)
            mAlertDialog.getWindow().getAttributes().windowAnimations = R.style.SlidingDialogAnimation;
        mAlertDialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));

        ButtonClose.setOnClickListener(v -> mAlertDialog.dismiss());


        ButtonSave.setOnClickListener(v -> {
            if (name.getText().toString().isEmpty())
            {
                name.requestFocus();
                name.setError(getString(R.string.enter_warranty_name));
            }
            else if (numbercount.getText().toString().equals("")) {
                numbercount.requestFocus();
                numbercount.setError(getString(R.string.enter_duration));
            }
            else if (spinduration.getSelectedItemPosition() == 0) {
                StringFormat.showSnackBar(promptsView,R.string.select_duration,true);
            }
            else
            {
                Warranty warranty = new Warranty();
                warranty.setName(name.getText().toString());
                warranty.setDescription(description.getText().toString());
                warranty.setDuration(numbercount.getText().toString());
                warranty.setDuration_type(spinduration.getSelectedItem().toString());
                warranty.setBusiness_id(session.getBusinessModel().getId());

                int i = warrantyDbController.insertLocal(warranty);
                if (i>0){
                    FileUtil.showDialog(_context,getString(R.string.success),getResources().getString(R.string.warranties_added_success ));
                    listWarrantyAdapter.setData(warrantyDbController.getAllWarranty());
                    listWarrantyAdapter.notifyDataSetChanged();
                    mAlertDialog.dismiss();
                } else  StringFormat.showSnackBar(promptsView, R.string.failed_to_update_data,true);
            }
            setItemView();
        });


        mAlertDialog.show();
    }


}