package com.rising.high.tech.bigultimatenavdraw.ui.warranty.adapter;

import android.content.Context;
import android.content.res.Resources;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;

import androidx.recyclerview.widget.RecyclerView;

import com.google.gson.Gson;
import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.db.WarrantyDbController;
import com.rising.high.tech.bigultimatenavdraw.model.Warranty;
import com.rising.high.tech.bigultimatenavdraw.util.FileUtil;
import com.rising.high.tech.bigultimatenavdraw.util.SessionManager;
import com.rising.high.tech.bigultimatenavdraw.util.StringFormat;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Objects;

import static com.rising.high.tech.bigultimatenavdraw.util.Constant.CATEGORY_EDIT;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.SERVER_MASTER;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.WARRANTY_DELETE;
import static com.rising.high.tech.bigultimatenavdraw.util.Constant.WARRANTY_EDIT;


public class ListWarrantyAdapter extends RecyclerView.Adapter<ListWarrantyAdapter.ListWarrantyViewHolder> {

    private static final String TAG = "ListWarrantyAdapter";
    private ArrayList<Warranty> dataList = new ArrayList<>();
    private Resources resources;
    SessionManager session;

    WarrantyDbController warrantyDbController;
    Context context;

    @Override
    public ListWarrantyViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        context = parent.getContext();
        resources = context.getResources();
        session = new SessionManager(context);

        warrantyDbController = new WarrantyDbController(context);
        warrantyDbController.open();
        return new ListWarrantyViewHolder(LayoutInflater.from(parent.getContext())
                .inflate(R.layout.warranty_item, parent, false));
    }

    @Override
    public void onBindViewHolder(ListWarrantyViewHolder holder, int position) {
        if (session.getBoolean(SERVER_MASTER) || !session.checkPermissionSubModule(WARRANTY_EDIT)) {
            holder.btnEdit.setVisibility(View.GONE);
        }

        if (session.getBoolean(SERVER_MASTER) || !session.checkPermissionSubModule(WARRANTY_DELETE)) {
            holder.btnEdit.setVisibility(View.GONE);
        }

        holder.name.setText(dataList.get(position).getName());
        holder.desc.setText(dataList.get(position).getDescription());
        String duration = dataList.get(position).getDuration() + " " + dataList.get(position).getDuration_type();
        holder.duration.setText(duration);

        if (position % 2 == 1) {
            holder.linearLayout.setBackgroundColor(context.getResources().getColor(R.color.white));
        } else {
            holder.linearLayout.setBackgroundColor(context.getResources().getColor(R.color.lightGrey));

        }
    }

    public void setData(ArrayList<Warranty> arrayList) {
        this.dataList = arrayList;
        notifyDataSetChanged();
    }


    @Override
    public int getItemCount() {
        return dataList.size();
    }

    public class ListWarrantyViewHolder extends RecyclerView.ViewHolder {

        ImageView btnEdit;
        TextView name, values, desc, duration;
        LinearLayout linearLayout;

        public ListWarrantyViewHolder(View itemView) {
            super(itemView);
            linearLayout = itemView.findViewById(R.id.linearLayout);

            name = itemView.findViewById(R.id.id_name);
            desc = itemView.findViewById(R.id.id_desc);
            btnEdit = itemView.findViewById(R.id.btn_edit);
            duration = itemView.findViewById(R.id.id_duration);

            btnEdit.setOnClickListener(v -> editWarranty(getAdapterPosition()));
        }
    }


    private void editWarranty(int position) {
        LayoutInflater li = LayoutInflater.from(context);
        View promptsView = li.inflate(R.layout.warranty_add_main, null);


        androidx.appcompat.app.AlertDialog.Builder alertDialogBuilder = new androidx.appcompat.app.AlertDialog.Builder(
                context);

        alertDialogBuilder.setView(promptsView);
        final Button ButtonSave = promptsView.findViewById(R.id.btn_save);
        final ImageView ButtonClose = promptsView.findViewById(R.id.btn_close);
        final TextView name = promptsView.findViewById(R.id.name);
        final TextView description = promptsView.findViewById(R.id.id_description);
        final Spinner spinduration = promptsView.findViewById(R.id.id_spinner_duration);
        final EditText numbercount = promptsView.findViewById(R.id.number_count);

        name.setText(dataList.get(position).getName());
        description.setText(dataList.get(position).getDescription() != null ? dataList.get(position).getDescription() : "");
        numbercount.setText(dataList.get(position).getDuration());
        int indexT = Arrays.asList(resources.getStringArray(R.array.array_duration)).indexOf(dataList.get(position).getDuration_type());
        spinduration.setSelection(indexT);


        final androidx.appcompat.app.AlertDialog mAlertDialog = alertDialogBuilder.create();
        if (mAlertDialog.getWindow() != null)
            mAlertDialog.getWindow().getAttributes().windowAnimations = R.style.SlidingDialogAnimation;
        Objects.requireNonNull(mAlertDialog.getWindow()).setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));

        ButtonClose.setOnClickListener(v -> mAlertDialog.dismiss());


        ButtonSave.setOnClickListener(v -> {
            if (name.getText().toString().isEmpty())
            {
                name.requestFocus();
                name.setError(context.getString(R.string.enter_warranty_name));
            }
            else if (numbercount.getText().toString().equals("")) {
                numbercount.requestFocus();
                numbercount.setError(context.getString(R.string.enter_duration));
            }
            else if (spinduration.getSelectedItemPosition() == 0) {
                StringFormat.showSnackBar(promptsView,R.string.enter_duration,true);
            }
            else
            {

                Warranty warranty = dataList.get(position);
                warranty.setName(name.getText().toString());
                warranty.setDescription(description.getText().toString());
                warranty.setDuration(numbercount.getText().toString());
                warranty.setDuration_type(spinduration.getSelectedItem().toString());

                int i = warrantyDbController.editWaranty(warranty);
                if (i > 0) {
                    FileUtil.showDialog(context,context.getString(R.string.success),context.getResources().getString(R.string.warranties_updated_success ));
                    notifyItemChanged(position);
                    mAlertDialog.dismiss();
                } else StringFormat.showSnackBar(promptsView, R.string.failed_to_update_data,true);
            }

        });


        mAlertDialog.show();
    }

}
