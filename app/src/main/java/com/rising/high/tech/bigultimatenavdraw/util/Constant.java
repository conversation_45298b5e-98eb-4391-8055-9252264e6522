package com.rising.high.tech.bigultimatenavdraw.util;

public class Constant {
    public static String CASH = "cash";
    public static String FIXED = "fixed";
    public static String PERCENTAGE = "percentage";
    public static String PURCHASE = "purchase";
    public static String PURCHASE_VIEW = "purchase.view";
    public static String PURCHASE_ADD = "purchase.add";
    public static String PURCHASE_EDIT = "purchase.edit";
    public static String PURCHASE_DELETE = "purchase.delete";
    public static String PAID = "paid";
    public static String ID = "id";
    public static String DUE = "due";
    public static String PARTIAL = "partial";
    public static String PURCHASE_RETURN = "purchase_return";
    public static String SINGLE = "single";
    public final static String ORDERED = "ordered";
    public final static String RECEIVED = "received";
    public static String EXPENSE = "expense";
    public static String EXPENSE_REFUND = "expense_refund";
    public static String FINAL = "final";
    public final static String PENDING = "pending";
    public static String IN_TRANSIT = "in_transit";
    public static String COMPLETED = "completed";
    public static String SELL_TRANSFER = "sell_transfer";
    public static String PURCHASE_TRANSFER = "purchase_transfer";
    public static String STOCK_ADJUSTMENT = "stock_adjustment";
    public static String ACTIVE = "active";
    public final static String HOME = "home";
    public final static String SELL_RETURN_ACCESS = "sell_return.access";
    public final static String STOCK_TRANSFERT = "stock transfers";
    public final static String STOCK_ADJUSTEMENT = "stock adjustement";
    public final static String SYNCHRONISATION = "synchronization";
    public final static String LIST_PRODUCTS = "list products";
    public final static String PROFIT_LOSS = "profit / loss report";
    public final static String STOCK_REPORT = "stock report";
    public final static String VARIATIONS = "variations";
    public final static String CATEGORIES = "categories";
    public final static String CATEGORY = "category";
    public final static String CATEGORY_ADD = "category.add";
    public final static String CATEGORY_VIEW = "category.view";
    public final static String CATEGORY_DELETE = "category.delete";
    public final static String CATEGORY_EDIT = "category.edit";
    public final static String STOCK_REPORT_VIEW= "stock_report.view";
    public final static String PROFIT_LOSS_REPORT_VIEW= "profit_loss_report.view";
    public final static String UNITS = "units";
    public final static String UNIT = "unit";
    public final static String UNIT_VIEW = "unit.view";
    public final static String UNIT_ADD = "unit.add";
    public final static String UNIT_EDIT = "unit.edit";
    public final static String UNIT_DELETE = "unit.delete";
    public final static String WARRANTIES = "warranties";
    public final static String BRANDS = "brands";
    public final static String BRAND = "brand";
    public final static String BRAND_VIEW = "brand.view";
    public final static String BRAND_DELETE = "brand.delete";
    public final static String BRAND_ADD = "brand.add";
    public final static String BRAND_EDIT = "brand.edit";
    public final static String CONTACTS = "contacts";
    public final static String CUSTOMER_GROUPS = "customer groups";
    public final static String LIST_PURCHASES = "list purchases";
    public final static String PURCHASES_RETURN = "purchase return";
    public final static String QUOTATION = "quotation";
    public final static String LIST_QUOTATION = "list_quotations";
    public final static String DISCOUNT_ACCESS = "discount.access";
    public final static String EXPENSE_ACCESS = "expense.access";
    public final static String REPORT_ACCESS = "report.access";
    public final static String MOBILE = "mobile";
    public final static String ALL_SALLES = "all sales";

    public final static String ALL = "all";
    public final static String DISCOUNTS = "discounts";
    public final static String LIST_EXPENSE = "list expense";
    public final static String LIST_SELL_RETURN = "list sell return";
    public final static String EXPENSE_CATEGEORIES = "expense categories";
    public final static String BUSINESS_SETTINGS = "business settings";
    public final static String BUSINESS_SETTING = "business setting";
    public final static String BUSINESS_SETTING_ACCESS = "business_settings.access";
    public final static String STOCK_TRANSFER_ACCESS = "stock_transfer.access";
    public final static String STOCK_ADJUSTEMENT_ACCESS = "stock_adjustment.access";

    public final static String BUSINESS_LOCATION_VIEW = "business_location.view";
    public final static String BUSINESS_LOCATION_ADD = "business_location.add";
    public final static String BUSINESS_LOCATION_EDIT = "business_location.edit";
    public final static String BUSINESS_LOCATION_DELETE = "business_location.delete";

    public final static String WARRANTY_VIEW = "warranty.view";
    public final static String WARRANTY_ADD = "warranty.add";
    public final static String WARRANTY_EDIT = "warranty.edit";
    public final static String WARRANTY_DELETE = "warranty.delete";


    public final static String VARIATION_VIEW = "variation.view";
    public final static String VARIATION_ADD = "variation.add";
    public final static String VARIATION_EDIT = "variation.edit";
    public final static String VARIATION_DELETE= "variation.delete";
    public final static String BUSINESS_LOCATION = "business location";
    public final static String TAX_RATES = "tax rates";
    public final static String TAX_RATE = "tax rate";
    public final static String TAX_RATE_VIEW = "tax_rate.view";
    public final static String TAX_RATE_ADD = "tax_rate.add";
    public final static String TAX_RATE_DELETE = "tax_rate.delete";
    public final static String TAX_RATE_EDIT = "tax_rate.edit";
    public final static String USERS = "users";
    public final static String USER = "user";
    public final static String USER_VIEW = "user.view";
    public final static String USER_ADD = "user.add";
    public final static String USER_EDIT = "user.edit";
    public final static String USER_DELETE = "user.delete";
    public final static String ROLES = "roles";
    public final static String ROLES_VIEW = "roles.view";
    public final static String ROLES_ADD = "roles.add";
    public final static String ROLES_EDIT = "roles.edit";
    public final static String ROLES_DELETE = "roles.delete";
    public final static String VIEW_CASH_REGISTER = "view_cash_register";
    public static String INACTIVE = "inactive";
    public static String NO = "no";
    public static String YES = "yes";
    public static String CUSTOMER = "customer";
    public static String CONTACT = "contact";
    public static String BOTH = "both";
    public static String CONTACT_VIEW = "contact.view";
    public static String CONTACT_ADD = "contact.add";
    public static String CONTACT_DELETE = "contact.delete";
    public static String CONTACT_EDIT = "contact.edit";
    public static String SUPPLIER = "supplier";
    public static String DAYS = "days";
    public static String MONTHS = "months";
    public static String YEARS = "years";
    public final static String DRAFT = "draft";
    public final static String LIST_DRAFT = "list_drafts";
    public static String SELL = "sell";
    public static String SELL_VIEW = "sell.view";
    public static String SELL_EDIT = "sell.edit";
    public static String SELL_DELETE = "sell.delete";
    public static String SELL_ADD = "sell.add";
    public static String SELL_RETURN = "sell_return";
    public static String FOLDER_NAME = "sell";
    public static Boolean ISLOGIN = false;
    public static String  SELL_RETURN_PARENT_ID = "sell_return_parent_id";
    public static String IS_EDIT = "is_edit";
    public static String OPENING_STOCK = "opening_stock";
    public static String OPENING_BALANCE = "opening_balance";
    public static String SCREEN_FROM = "screen_from";
    public static String CURRENCY_LIST = "currencies.json";
    public static String CATEGORY_TYPE = "product";
    public static String PRODUCT = "product";
    public static String PRODUCT_ADD = "product.add";
    public static String PRODUCT_VIEW = "product.view";
    public static String PRODUCT_EDIT = "product.edit";
    public static String PRODUCT_DELETE = "product.delete";
    public static String KEY_BUSINESS_MODEL = "business_model";

    public static String LOCAL_USE = "local_use";
    public static String SERVER_MASTER = "server_master";
    public static String LOCAL_MASTER = "local_master";
    public final static String INCLUSIVE = "inclusive";
    public final static String EXCLUSIVE = "exclusive";
}
