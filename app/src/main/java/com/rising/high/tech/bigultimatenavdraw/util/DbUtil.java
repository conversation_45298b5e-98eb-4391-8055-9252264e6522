package com.rising.high.tech.bigultimatenavdraw.util;

import android.content.Context;
import android.content.Intent;
import android.util.Log;

import com.google.gson.Gson;
import com.rising.high.tech.bigultimatenavdraw.db.StockAdjustementLineDbController;
import com.rising.high.tech.bigultimatenavdraw.db.TransactionDbController;
import com.rising.high.tech.bigultimatenavdraw.db.TransactionSellLineDbController;
import com.rising.high.tech.bigultimatenavdraw.ui.startup.SplashscreenActivity;

public class DbUtil {

    private static final String TAG = "DbUtil";
    private static TransactionSellLineDbController transactionSellLineDbController;
    private static TransactionDbController transactionDbController;
    private static StockAdjustementLineDbController stockAdjustementLineDbController;

    public static String getTotalSold(Context context, Integer id_product, Integer id_location){
        transactionSellLineDbController=new TransactionSellLineDbController(context);
        transactionSellLineDbController.open();

        transactionDbController=new TransactionDbController(context);
        transactionDbController.open();

        return transactionSellLineDbController.getTotalSold(id_product, id_location);

    }

    public static String getTotalTranfered(Context context, Integer id_product, Integer id_location){
        transactionSellLineDbController=new TransactionSellLineDbController(context);
        transactionSellLineDbController.open();

        transactionDbController=new TransactionDbController(context);
        transactionDbController.open();

        return transactionSellLineDbController.getTotalTranfered(id_product, id_location);

    }
    public static String getTotalAdjusted(Context context, Integer id_product, Integer id_location){

        transactionDbController=new TransactionDbController(context);
        transactionDbController.open();

        stockAdjustementLineDbController=new StockAdjustementLineDbController(context);
        stockAdjustementLineDbController.open();

        return stockAdjustementLineDbController.getTotalAdjusted(id_product, id_location);

    }


}
