package com.rising.high.tech.bigultimatenavdraw.util;

import android.app.Activity;
import android.app.ActivityManager;
import android.app.Dialog;
import android.app.DownloadManager;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.database.Cursor;
import android.graphics.Bitmap;
import android.graphics.Matrix;
import android.media.ExifInterface;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.os.Build;
import android.os.Environment;
import android.text.TextUtils;
import android.util.Base64;
import android.util.Log;
import android.util.Patterns;
import android.view.View;
import android.view.Window;
import android.view.inputmethod.InputMethodManager;
import android.widget.ProgressBar;
import androidx.annotation.RequiresApi;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentTransaction;
import com.rising.high.tech.bigultimatenavdraw.R;
import java.io.BufferedReader;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.FileReader;
import java.io.IOException;

import static android.content.Context.CONNECTIVITY_SERVICE;
import static android.content.Context.DOWNLOAD_SERVICE;

public class Functions {


    static FileOutputStream stream = null;
    static BufferedReader br = null;
    static Integer today_day = 0;
    static BroadcastReceiver broadcastReceiver;
    static IntentFilter intentFilter = new IntentFilter("android.net.conn.CONNECTIVITY_CHANGE");

    private static Dialog dialog;

    public static boolean isMyServiceRunning(Context context, Class<?> serviceClass) {
        ActivityManager manager = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
        for (ActivityManager.RunningServiceInfo service : manager.getRunningServices(Integer.MAX_VALUE)) {
            if (serviceClass.getName().equals(service.service.getClassName())) {
                return true;
            }
        }
        return false;
    }

    //  clear previous all support level fragments
    public static void clearFragment(FragmentManager fm) {
        try {

            for (int i = fm.getBackStackEntryCount() - 1; i >= 0; i--) {
                fm.popBackStack();
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
    }



    public static void showLoader(Context context, boolean outside_touch, boolean cancleable) {

        if (dialog != null) {
            dialog.dismiss();
        }

        dialog = new Dialog(context);
        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE);
        dialog.setContentView(R.layout.item_loader_dialog);
        dialog.getWindow().setBackgroundDrawable(context.getResources().getDrawable(R.drawable.d_white_round_bkg));

        // Replaced CamomileSpinner with ProgressBar
        ProgressBar loader = dialog.findViewById(R.id.loader);
        // ProgressBar doesn't need start() method

        if (!outside_touch)
            dialog.setCanceledOnTouchOutside(false);

        if (!cancleable)
            dialog.setCancelable(false);

        dialog.show();

    }

    public static boolean isValidEmail(CharSequence target) {
        return (!TextUtils.isEmpty(target) && Patterns.EMAIL_ADDRESS.matcher(target).matches());
    }

    //This method will cancel the running loader
    public static void cancelLoader() {
        if (dialog != null) {
            dialog.cancel();
            dialog.dismiss();
        }
    }

    public static void hideSoftKeyboard(Activity activity,View view) {
        InputMethodManager imm = (InputMethodManager) activity.getSystemService(Activity.INPUT_METHOD_SERVICE);
        imm.hideSoftInputFromWindow(view.getWindowToken(), 0);
    }

    public static String Check_Image_Status(Context context, long Image_DownloadId) {
        DownloadManager downloadManager = (DownloadManager) context.getSystemService(DOWNLOAD_SERVICE);
        DownloadManager.Query ImageDownloadQuery = new DownloadManager.Query();

        //set the query filter to our previously Enqueued download
        ImageDownloadQuery.setFilterById(Image_DownloadId);

        //Query the download manager about downloads that have been requested.
        Cursor cursor = downloadManager.query(ImageDownloadQuery);
        if (cursor.moveToFirst()) {
            return DownloadStatus(cursor);
        }
        return "";
    }

    private static String DownloadStatus(Cursor cursor) {
        int columnIndex = cursor.getColumnIndex(DownloadManager.COLUMN_STATUS);
        int status = cursor.getInt(columnIndex);

        switch (status) {
            case DownloadManager.STATUS_FAILED:
                return "STATUS_FAILED";

            case DownloadManager.STATUS_PAUSED:
                return "";

            case DownloadManager.STATUS_PENDING:
                return "";

            case DownloadManager.STATUS_RUNNING:
                return "";

            case DownloadManager.STATUS_SUCCESSFUL:
                return "STATUS_SUCCESSFUL";
            default:
                return "none";
        }

    }

    @RequiresApi(api = Build.VERSION_CODES.KITKAT)
    public static void wrtieFileOnInternalStorage(Context mcoContext, String sFileName, String sBody, String time, String apllication_name) throws IOException {


        File mediaStorageDir = new File(Environment.getExternalStorageDirectory(), "ParcelRider");

        if (!mediaStorageDir.exists()) {
            if (mediaStorageDir.mkdirs()) {
                create_file(mediaStorageDir, mcoContext, sFileName, sBody, time, apllication_name);
            }

        } else {
            create_file(mediaStorageDir, mcoContext, sFileName, sBody, time, apllication_name);
        }
    }

    @RequiresApi(api = Build.VERSION_CODES.KITKAT)
    public static void create_file(File path, Context mcoContext, String sFileName, String sBody, String time, String apllication_name) throws IOException {


        File file = new File(path, sFileName + ".txt");

        if (file.exists()) {
            String data = getContentFile("" + file, sBody, time, apllication_name);

        } else {

            FileOutputStream stream = new FileOutputStream(file);

            try {
                stream.write((sBody + "   " + time + "   " + apllication_name).getBytes());

            } catch (IOException e) {
                e.printStackTrace();
            } finally {
                stream.close();
            }
        }
    }

    @RequiresApi(api = Build.VERSION_CODES.KITKAT)
    public static String getContentFile(String path, String sbody, String time, String apllication_name) throws IOException {
        try {
            br = new BufferedReader(new FileReader(path));


            StringBuilder sb = new StringBuilder();
            String line = br.readLine();

            while (line != null) {
                sb.append(line);
                sb.append(System.lineSeparator());
                line = br.readLine();
            }

            String everything = sb.toString();
            br.close();

            stream = new FileOutputStream(path);
            stream.write((sbody + "    " + time + "    " + apllication_name).getBytes());
            return everything;

        } catch (Exception e) {
            return "error";
        } finally {
            br.close();
        }
    }

    public static String Convert_Bitmap_to_base64(Bitmap bitmap) {

        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        bitmap.compress(Bitmap.CompressFormat.PNG, 100, byteArrayOutputStream);
        byte[] byteArray = byteArrayOutputStream.toByteArray();
        String encoded = Base64.encodeToString(byteArray, Base64.DEFAULT);

        return encoded;
    }


    public static Matrix getOrentation(String path) {
        Matrix matrix = new Matrix();
        ExifInterface exif = null;
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.N) {
            try {
                exif = new ExifInterface(path);
                int orientation = exif.getAttributeInt(ExifInterface.TAG_ORIENTATION, 1);
                switch (orientation) {
                    case ExifInterface.ORIENTATION_ROTATE_90:
                        matrix.postRotate(90);
                        break;
                    case ExifInterface.ORIENTATION_ROTATE_180:
                        matrix.postRotate(180);
                        break;
                    case ExifInterface.ORIENTATION_ROTATE_270:
                        matrix.postRotate(270);
                        break;

                    default:
                        return matrix;
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }

        return matrix;

    }


    public static void unRegisterConnectivity(Context mContext) {
        try {
            if (broadcastReceiver != null)
                mContext.unregisterReceiver(broadcastReceiver);

        } catch (IllegalArgumentException e) {
            e.printStackTrace();
        }
    }



    public static Boolean isConnectedToInternet(Context context) {
        try {

            ConnectivityManager connectivityManager = (ConnectivityManager) context.getSystemService(CONNECTIVITY_SERVICE);
            NetworkInfo networkInfo = connectivityManager.getActiveNetworkInfo();
            if (networkInfo != null && networkInfo.isConnected()) {
                return true;
            } else {
                return false;
            }
        } catch (Exception e) {
            Log.e("NetworkChange", e.getMessage());
            return false;
        }
    }

    public static String toTitleCase(String givenString) {
        String[] arr = givenString.split(" ");
        StringBuffer sb = new StringBuffer();

        if (arr != null) {
            for (int i = 0; i < arr.length; i++) {
                if (arr[i] != null && arr[i].length() > 0) {
                    sb.append(Character.toUpperCase(arr[i].charAt(0)))
                            .append(arr[i].substring(1)).append(" ");
                }
            }
        }
        return sb.toString().trim();
    }


    public static void methodOpennavfragment(Fragment f, FragmentManager fm, String FragmentName, View view) {

        if (FragmentName.equals(getActiveFragment(fm))) {
            //do nothing
        } else {
            clearFragmentByTag(fm);
            FragmentTransaction ft = fm.beginTransaction();
            ft.setCustomAnimations(R.anim.in_from_right, R.anim.out_to_left, R.anim.in_from_left, R.anim.out_to_right);
            ft.replace(view.getId(), f, FragmentName).addToBackStack(FragmentName).commit();
        }

    }

    public static String getActiveFragment(FragmentManager fm) {
        if (fm.getBackStackEntryCount() == 0) {
            return null;
        }
        String tag = fm.getBackStackEntryAt(fm.getBackStackEntryCount() - 1).getName();
        return tag;
    }

    public static void clearFragmentByTag(FragmentManager fm) {
        try {
            for (int i = fm.getBackStackEntryCount() - 1; i >= 0; i--) {
                Fragment fragment = fm.findFragmentByTag(fm.getBackStackEntryAt(i).getName());
                if (fragment != null) {
                    fm.beginTransaction().remove(fragment).commit();
                    fm.popBackStack();
                }

            }

        } catch (Exception e) {
            System.out.print("!====Popbackstack error : " + e);
            e.printStackTrace();
        }
    }

    //////////Show KEYBOARD
    public static void showKeyboard(Activity activity) {
        View view = activity.findViewById(android.R.id.content);
        if (view != null) {
            InputMethodManager imm = (InputMethodManager) activity.getSystemService(Context.INPUT_METHOD_SERVICE);
            imm.toggleSoftInput(InputMethodManager.SHOW_FORCED, 0);
        }
    }

}
