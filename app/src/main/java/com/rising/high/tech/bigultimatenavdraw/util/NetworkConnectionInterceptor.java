package com.rising.high.tech.bigultimatenavdraw.util;

import android.app.Activity;
import android.content.Context;
import android.net.ConnectivityManager;
import android.widget.Toast;

import java.io.IOException;

import okhttp3.Interceptor;
import okhttp3.Request;
import okhttp3.Response;

public class NetworkConnectionInterceptor implements Interceptor {

    private Context mContext;

    public Context getmContext() {
        return mContext;
    }

    public NetworkConnectionInterceptor(Context context) {
        mContext = context;
    }

    @Override
    public Response intercept(Chain chain) throws IOException {
        if (!isConnected()) {
            //showAlertDialog();
            showNoConnection();
            //showAlertDialog();
            // Toast.makeText(mContext,"no internet connextion",Toast.LENGTH_LONG).show();
            throw new NoConnectivityException();
            // Throwing our custom exception 'NoConnectivityException'
        }

        Request newRequest  = chain.request().newBuilder()
                .addHeader("Authorization", "Bearer " + serverInteraction.token)
                .build();

        return chain.proceed(newRequest);
    }

    public boolean isConnected() {
        ConnectivityManager connectivityManager = (ConnectivityManager) mContext.getSystemService(Context.CONNECTIVITY_SERVICE);
/*        NetworkInfo netInfo = ConnectivityManager.getActiveNetworkInfo();

        return (netInfo != null && netInfo.isConnected());*/
        return true;
    }

  /*  private void showAlertDialog() {
        Log.d("tagg", mContext + "");
        ((Activity) mContext).runOnUiThread(new Runnable() {
            public void run() {
                AlertDialog.Builder builder = new AlertDialog.Builder(mContext);
                builder.setTitle("Error");
                builder.setMessage("No Network Connection").setCancelable(false)
                        .setIcon(R.drawable.edit_small)
                        .setPositiveButton("OK", new DialogInterface.OnClickListener() {
                            public void onClick(DialogInterface dialog, int id) {

                                //error = "";
                            }
                        });
                AlertDialog alert = builder.create();
                alert.show();
            }
        });

    }*/

    public void showNoConnection() {
        new Thread() {
            public void run() {
                ((Activity) mContext).runOnUiThread(new Runnable() {
                    public void run() {
                        Toast.makeText(mContext, "No Internet Connection", Toast.LENGTH_LONG).show();
                        //Do your UI operations like dialog opening or Toast here
                    }
                });
            }
        }.start();
       /* DialogCaller.showDialog(mContext,"title","message",new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {

            }
        });*/

        //AlertUtils.getInstance().showAlert(mContext, "Error", "Task failed");

        /*AlertDialogManager alertDialogManager = new AlertDialogManager();
        alertDialogManager.showAlertDialog(mContext,"Error","No Internet Connection",true);*/
       /* AlertDialog.Builder builder =new AlertDialog.Builder(mContext);
        builder.setTitle("No internet Connection");
        builder.setMessage("Please turn on internet connection to continue");
        builder.setNegativeButton("close", new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                dialog.dismiss();
            }
        });
        AlertDialog alertDialog = builder.create();
        alertDialog.show();*/
    }


}
