package com.rising.high.tech.bigultimatenavdraw.util;

import android.content.Context;
import android.icu.lang.UProperty;
import android.util.Log;

import com.google.gson.Gson;
import com.rising.high.tech.bigultimatenavdraw.db.DiscountDbController;
import com.rising.high.tech.bigultimatenavdraw.db.TransactionDbController;
import com.rising.high.tech.bigultimatenavdraw.db.TransactionSellLineDbController;
import com.rising.high.tech.bigultimatenavdraw.db.VariationsDbController;
import com.rising.high.tech.bigultimatenavdraw.model.Discount;
import com.rising.high.tech.bigultimatenavdraw.model.Product;
import com.rising.high.tech.bigultimatenavdraw.model.Sell_lines;
import com.rising.high.tech.bigultimatenavdraw.model.Variation;

import java.util.ArrayList;

public class ProductUtil {

    private static final String TAG = "ProductUtil";

    public static VariationsDbController variationsDbController;
    public static TransactionSellLineDbController transactionSellLineDbController;

    public static ArrayList<Discount> getHigherPriority(Context context, ArrayList<Discount> discounts) {

        DiscountDbController discountDbController = new DiscountDbController(context);
        discountDbController.open();
        int priority = 0;
        for (Discount discount : discounts) {
            if (discount.getPriority() > priority) priority = discount.getPriority();
        }
        ArrayList<Discount> discounts1 = discountDbController.getDiscountByPriority(priority, "2021-05-23" );

        return discounts1;
    }

    public static String getTotalAmountDefaultSellPrice(ArrayList<Product> arrayListTemp, Context context) {

        variationsDbController=new VariationsDbController(context);
        variationsDbController.open();
        float initTotal = 0;
        for (Product product : arrayListTemp) {
            Variation variation=variationsDbController.getVariationByProductId(product.getId());
            initTotal = initTotal + (Float.parseFloat(variation.getDefault_sell_price()) * product.getSell_qte());
        }
        return initTotal + "";
    }
    public static String getTotalAmountDpp(ArrayList<Product> arrayListTemp, Context context) {

        variationsDbController=new VariationsDbController(context);
        variationsDbController.open();
        float initTotal = 0;
        for (Product product : arrayListTemp) {
            Variation variation=variationsDbController.getVariationByProductId(product.getId());
            initTotal = initTotal + (Float.parseFloat(variation.getDpp_inc_tax()) * product.getSell_qte());
        }
        return initTotal + "";
    }
    public static  String getTotalAmountSellReturn(ArrayList<Sell_lines> arrayListTemp, Context context) {

        float initTotal = 0;
        for (Sell_lines sell_lines : arrayListTemp) {
            initTotal = initTotal + (Float.parseFloat(sell_lines.getUnit_price()) * sell_lines.getQty_returned());
        }
        return initTotal + "";
    }

}
