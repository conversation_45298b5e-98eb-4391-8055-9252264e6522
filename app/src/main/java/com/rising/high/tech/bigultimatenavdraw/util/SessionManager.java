package com.rising.high.tech.bigultimatenavdraw.util;

import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.content.SharedPreferences.Editor;
import android.util.Log;

import com.google.gson.Gson;
import com.rising.high.tech.bigultimatenavdraw.db.ContactDbController;
import com.rising.high.tech.bigultimatenavdraw.db.PermissionDbController;
import com.rising.high.tech.bigultimatenavdraw.db.RoleHasPermissionDbController;
import com.rising.high.tech.bigultimatenavdraw.db.UserHasRolesDbController;
import com.rising.high.tech.bigultimatenavdraw.model.Business;
import com.rising.high.tech.bigultimatenavdraw.model.Contact;
import com.rising.high.tech.bigultimatenavdraw.model.Permission;
import com.rising.high.tech.bigultimatenavdraw.model.Role;
import com.rising.high.tech.bigultimatenavdraw.model.User;
import com.rising.high.tech.bigultimatenavdraw.ui.startup.LoginActivity;
import com.rising.high.tech.bigultimatenavdraw.ui.startup.SplashscreenActivity;

import java.util.ArrayList;
import java.util.HashMap;

import static com.rising.high.tech.bigultimatenavdraw.util.Constant.PRODUCT;

public class SessionManager {
    private static final String TAG = "SessionManager";
    // Shared Preferences
    SharedPreferences pref;
    // Editor for Shared preferences
    Editor editor;
    // Context
    Context _context;
    // Shared pref mode
    int PRIVATE_MODE = 0;

    // Sharedpref file name
    private static final String PREF_NAME = "AndroiPOS";

    // All Shared Preferences Keys
    public static final String IS_LOGIN = "IsLoggedIn";

    // User name (make variable public to access from outside)
    public static final String KEY_NAME = "username";
    public static final String KEY_MATRICULE = "matricule";
    public static final String KEY_SOLDE = "solde";

    // Language
    public static final String LANGUAGE = "language";
    public static final String MEMBER_SHIP = "member_ship_type";
    public static final String ID_QR = "id_qr";
    public static final String ID_CORPORATE = "id_corporate";
    public static final String ID_DRIVER = "id_driver";
    public static final String ID_USER = "id_user";
    public static final String IS_APPROVE = "is_approve";
    public static final String PASSE_CODE = "passe_code";
    public static final String TEL = "tel";
    public static final String KEY_SYMBOL = "symbol";
    public static final String KEY_ID_BUSINESS = "id_business";
    public static final String LOCATION_ID = "location_id";
    public static final String SELECTED_CONTACT = "selected_contact";
    public static final String KEY_STATION_NAME = "station_name";
    public static final String KEY_LOGO = "logo";
    public static final String KEY_TYPE_SEARCH = "type_search";
    PermissionDbController permissionDbController;
    UserHasRolesDbController userHasRolesDbController;
    RoleHasPermissionDbController roleHasPermissionDbController;

    // Constructor
    public SessionManager(Context context) {
        this._context = context;
        pref = _context.getSharedPreferences(PREF_NAME, PRIVATE_MODE);
        editor = pref.edit();

        permissionDbController = new PermissionDbController(_context);
        permissionDbController.open();
        userHasRolesDbController = new UserHasRolesDbController(_context);
        userHasRolesDbController.open();
        roleHasPermissionDbController = new RoleHasPermissionDbController(this._context);
        roleHasPermissionDbController.open();

    }

    /**
     * Create login session
     */
    public void saveString(String key, String value) {
        // Storing login value as TRUE

        // Storing name in pref
        editor.putString(key, value);

        // Storing email in pref
        // commit changes
        editor.commit();
    }

    /**
     * Create login session
     */
    public void saveBoolean(String key, boolean b) {
        // Storing login value as TRUE

        // Storing name in pref
        editor.putBoolean(key, b);

        // Storing email in pref

        // commit changes
        editor.commit();
    }

    /**
     * Create login session
     */
    public void saveInt(String key, int value) {
        // Storing login value as TRUE

        // Storing name in pref
        editor.putInt(key, value);

        // commit changes
        editor.commit();
    }

    public void saveBusinessModel(Business business) {
        Editor edit = pref.edit();
        edit.putString(Constant.KEY_BUSINESS_MODEL, new Gson().toJson(business));
        edit.apply();
    }

    public Business getBusinessModel() {
        String business = pref.getString(Constant.KEY_BUSINESS_MODEL, null);
        return new Gson().fromJson(business, Business.class);
    }

    /**
     * Create login session
     */
    public void createLoginSession(String name, String email, String imei) {
        // Storing login value as TRUE
        editor.putBoolean(IS_LOGIN, true);

        // Storing name in pref
        editor.putString(KEY_NAME, name);

        // Storing email in pref

        // commit changes
        editor.commit();
    }

    /**
     * Create login session
     */
    public void saveParams(Boolean status) {
        editor.putBoolean(IS_LOGIN, status);
        editor.commit();
    }


    /**
     * Check login method wil check user login status
     * If false it will redirect user to login page
     * Else won't do anything
     */
    public void checkLogin() {
        // Check login status
 /*       if (!this.isLoggedIn()) {
            // user is not logged in redirect him to Login Activity
            Intent i = new Intent(_context, LoginActivity.class);
            // Closing all the Activities
            i.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);

            // Add new Flag to start new Activity
            i.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);

            // Staring Login Activity
            _context.startActivity(i);
        }*/

    }


    /**
     * Get stored session data
     */
    public HashMap<String, Object> getUserDetails() {
        HashMap<String, Object> user = new HashMap<>();
        // user name
        user.put(KEY_NAME, pref.getString(KEY_NAME, null));
        user.put(LANGUAGE, pref.getString(LANGUAGE, null));
        user.put(MEMBER_SHIP, pref.getString(MEMBER_SHIP, null));
        user.put(ID_CORPORATE, pref.getString(ID_CORPORATE, null));
        user.put(ID_QR, pref.getString(ID_QR, null));
        user.put(ID_DRIVER, pref.getString(ID_DRIVER, null));
        user.put(ID_USER, pref.getInt(ID_USER, 0));
        user.put(KEY_SYMBOL, pref.getString(KEY_SYMBOL, null));
        user.put(KEY_ID_BUSINESS, pref.getString(KEY_ID_BUSINESS, null));
        user.put(LOCATION_ID, pref.getInt(LOCATION_ID, 0));
        user.put(SELECTED_CONTACT, pref.getInt(SELECTED_CONTACT, 0));
        user.put(KEY_STATION_NAME, pref.getString(KEY_STATION_NAME, null));
        user.put(KEY_TYPE_SEARCH, pref.getString(KEY_TYPE_SEARCH, null));
        user.put(IS_LOGIN, pref.getBoolean(IS_LOGIN, false));

        user.put(KEY_MATRICULE, pref.getString(KEY_MATRICULE, null));
        user.put(KEY_SOLDE, pref.getString(KEY_SOLDE, null));
        user.put(IS_APPROVE, pref.getBoolean(IS_APPROVE, false));
        user.put(PASSE_CODE, pref.getBoolean(IS_APPROVE, false));
        user.put(TEL, pref.getBoolean(IS_APPROVE, false));


        // user email id
       /* user.put(KEY_PASS, pref.getString(KEY_PASS, null));

        // user parc id
        user.put(KEY_PARC, pref.getString(KEY_PARC, null));

        // user parc id
        user.put(KEY_IMEI, pref.getString(KEY_IMEI, null));

        // user user id
        user.put(USER_ID, pref.getString(USER_ID, null));

        // user user id
        user.put(USER_PRIV_NAME, pref.getString(USER_PRIV_NAME, null));

        user.put(USER_PHOTO, pref.getString(USER_PHOTO, null));

        // Storing parc in pref


        user.put(KEY_COMPANY_ID, pref.getString(KEY_COMPANY_ID, null));
        user.put(KEY_STATION_ID, pref.getString(KEY_STATION_ID, null));
        user.put(KEY_STATION_NAME, pref.getString(KEY_STATION_NAME, null));
        user.put(KEY_COMPANY_NAME, pref.getString(KEY_COMPANY_NAME, null));

        // get xlogin object
        String json = pref.getString(KEY_LOGIN_KEY, "");

        user.put(KEY_LOGIN_KEY, json);*/

        // return user
        return user;
    }

    /**
     * Clear session details
     */
    public void logoutUser() {
        // Clearing all data from Shared Preferences
        editor.clear();
        editor.commit();

        // After logout redirect user to Loing Activity
        Intent i = new Intent(_context, SplashscreenActivity.class);
        // Closing all the Activities
        i.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
        // Add new Flag to start new Activity
        i.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        // Staring Login Activity
        _context.startActivity(i);
    }

    /**
     * Quick check for login
     **/
    // Get Login UState
    public boolean isLoggedIn() {
        return pref.getBoolean(IS_LOGIN, false);
    }

    public boolean getBoolean(String key) {
        return pref.getBoolean(key, false);
    }

    public boolean checkPermissionModule(String permissionModule) {
        int role_id = userHasRolesDbController.getRolesByUserId(Integer.parseInt(getUserDetails().get(ID_USER).toString())).getRole_id();
        ArrayList<Integer> roleIdsList = permissionDbController.getPermissionIds(permissionModule);
        ArrayList<Integer> guarantedPermissionUser = roleHasPermissionDbController.getRolesPermissionById(role_id);

        boolean exist = true;
        if (guarantedPermissionUser.size() > 0) {
            for (Integer role : roleIdsList) {
                if (!guarantedPermissionUser.contains(role)) {
                    exist = false;
                };
            }
        } else {
            exist = false;
        }

        return exist;
    }

    public boolean checkPermissionSubModule(String permissionModule) {
        int role_user_id = userHasRolesDbController.getRolesByUserId(Integer.parseInt(getUserDetails().get(ID_USER).toString())).getRole_id();
        Integer role_id = permissionDbController.getPermissionId(permissionModule);

        ArrayList<Integer> guarantedPermissionUser = roleHasPermissionDbController.getRolesPermissionById(role_user_id);
        boolean isExist = false;

        if (guarantedPermissionUser.size() > 0) {
            if (guarantedPermissionUser.contains(role_id)) {
                isExist = true;
            }
            ;
        }

        return isExist;
    }

    public boolean checkFirstModule() {
        int role_user_id = userHasRolesDbController.getRolesByUserId(Integer.parseInt(getUserDetails().get(ID_USER).toString())).getRole_id();
        ArrayList<Integer> guarantedPermissionUser = roleHasPermissionDbController.getRolesPermissionById(role_user_id);
        boolean boolModule = guarantedPermissionUser.contains(44);
        return boolModule;
    }

    public boolean checkHasAllPermission() {
        int role_user_id = userHasRolesDbController.getRolesByUserId(Integer.parseInt(getUserDetails().get(ID_USER).toString())).getRole_id();
        ArrayList<Integer> guarantedPermissionUser = roleHasPermissionDbController.getRolesPermissionById(role_user_id);
        boolean boolModule = (guarantedPermissionUser.size() == 65);
        return boolModule;
    }

}