package com.rising.high.tech.bigultimatenavdraw.util;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.SharedPreferences;
import android.graphics.Color;
import android.util.Log;
import android.view.Gravity;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.TextView;

import androidx.coordinatorlayout.widget.CoordinatorLayout;

import com.google.android.material.snackbar.Snackbar;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.db.TransactionDbController;
import com.rising.high.tech.bigultimatenavdraw.db.TransactionPayementDbController;

import java.lang.reflect.Type;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Locale;
import java.util.TimeZone;

import static android.content.Context.MODE_PRIVATE;

public class StringFormat {

    private static final String TAG = "StringFormat";

    public static String getDateFormInput(String mytime) {
        SimpleDateFormat dateFormat = new SimpleDateFormat(
                "EEE MMM dd HH:mm:ss zzz yyyy", Locale.ENGLISH);

        Date myDate = null;
        try {
            myDate = dateFormat.parse(mytime);

        } catch (ParseException e) {
            e.printStackTrace();
        }

        SimpleDateFormat timeFormat = new SimpleDateFormat("yyyy-MM-dd");
        String finalDate = timeFormat.format(myDate);

        System.out.println(finalDate);

        return finalDate;
    }

    public static String actualTime( ) {
        Calendar c = Calendar.getInstance();
        String dateNow=StringFormat.populateSetFullDate(c.get(Calendar.YEAR), c.get(Calendar.MONTH), c.get(Calendar.DAY_OF_MONTH), c.get(Calendar.HOUR), c.get(Calendar.MINUTE));
        return dateNow;
    }


    public static String getCalculatedDate(String dateFormat, int days) {
        Calendar cal = Calendar.getInstance();
        SimpleDateFormat s = new SimpleDateFormat(dateFormat);
        cal.add(Calendar.DAY_OF_YEAR, days);
        return s.format(new Date(cal.getTimeInMillis()));
    }


    public static String ConvertDate(Date date) {

        DateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        String s = df.format(date);
        String result = s;
        try {
            date = df.parse(result);
        } catch (ParseException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
        return date.toString();
    }

    public static String populateSetDate(int year, int month, int day) {
        month += 1;
        String mt, dy;   //local variable
        if (month < 10)
            mt = "0" + month; //if month less than 10 then ad 0 before month
        else mt = String.valueOf(month);

        if (day < 10)
            dy = "0" + day;
        else dy = String.valueOf(day);
        String myFormat = year + "-" + mt + "-" + dy;
        return myFormat;
    }

    public static String populateSetFullDate(int year, int month, int day, int hour, int minute) {
        month += 1;
        String mt, dy, dh, mn;   //local variable
        if (month < 10)
            mt = "0" + month; //if month less than 10 then ad 0 before month
        else mt = String.valueOf(month);

        if (day < 10)
            dy = "0" + day;
        else dy = String.valueOf(day);

        if (hour < 10) {
            dh = "0" + hour;
        } else {
            dh = String.valueOf(hour);
        }

        if (minute < 10) {
            mn = "0" + minute;
        } else {
            mn = String.valueOf(minute);
        }
        String myFormat = year + "-" + mt + "-" + dy + " " + dh + ":" + minute + ":" + "00";
        return myFormat;
    }

    public static void saveParams(Context context, String key, String value) {
        SharedPreferences sharedPreferences = context.getSharedPreferences("shared preferences", MODE_PRIVATE);
        SharedPreferences.Editor editor = sharedPreferences.edit();
        Gson gson = new Gson();
        String json = gson.toJson(value);
        editor.putString(key, json);
        editor.apply();
    }


    public String getParams(Context context, String key) {
        SharedPreferences sharedPreferences = context.getSharedPreferences("shared preferences", MODE_PRIVATE);
        Gson gson = new Gson();
        String json = sharedPreferences.getString(key, null);
        Type type = new TypeToken<String>() {
        }.getType();

        return gson.fromJson(json, type);
    }
    public static  String getCurrentDateTime()
    {
        Date date = new Date();
        @SuppressLint("SimpleDateFormat") DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        return df.format(date);
    }
    public static String toTitleCase(String string) {

        // Check if String is null
        if (string == null) {

            return null;
        }

        boolean whiteSpace = true;

        StringBuilder builder = new StringBuilder(string); // String builder to store string
        final int builderLength = builder.length();

        // Loop through builder
        for (int i = 0; i < builderLength; ++i) {

            char c = builder.charAt(i); // Get character at builders position

            if (whiteSpace) {

                // Check if character is not white space
                if (!Character.isWhitespace(c)) {

                    // Convert to title case and leave whitespace mode.
                    builder.setCharAt(i, Character.toTitleCase(c));
                    whiteSpace = false;
                }
            } else if (Character.isWhitespace(c)) {

                whiteSpace = true; // Set character is white space

            } else {

                builder.setCharAt(i, Character.toLowerCase(c)); // Set character to lowercase
            }
        }

        return builder.toString(); // Return builders text
    }
    public static String generateInvoiceTransactionNo(Context context){
        TransactionDbController transactionDbController=new TransactionDbController(context);
        transactionDbController.open();
        String inv = "";
        if (transactionDbController.getLastSalesInvoiceNo().isEmpty()) {
            inv = "S" + Calendar.getInstance().get(Calendar.YEAR) + "/0001";
        } else {
            String lastInv[] = transactionDbController.getLastSalesInvoiceNo().split("/");
            String lastInvValue = lastInv[1];
            int lastInvValueInc = Integer.parseInt(lastInvValue) + 1;
            inv = "S" + Calendar.getInstance().get(Calendar.YEAR) + "/" + String.format(Locale.ENGLISH, "%04d", lastInvValueInc);
        }
        return inv;
    }
    public static String generateInvoicePurchaseTransferNo(Context context){
        TransactionDbController transactionDbController=new TransactionDbController(context);
        transactionDbController.open();
        String inv = "";
        if (transactionDbController.getLastPurchaseTransferInvoiceNo().isEmpty()) {
            inv = "PT" + Calendar.getInstance().get(Calendar.YEAR) + "/0001";
        } else {
            String lastInv[] = transactionDbController.getLastPurchaseTransferInvoiceNo().split("/");
            String lastInvValue = lastInv[1];
            int lastInvValueInc = Integer.parseInt(lastInvValue) + 1;
            inv = "PT" + Calendar.getInstance().get(Calendar.YEAR) + "/" + String.format(Locale.ENGLISH, "%04d", lastInvValueInc);
        }
        return inv;
    }
    public static String generateInvoiceSellTransferNo(Context context){
        TransactionDbController transactionDbController=new TransactionDbController(context);
        transactionDbController.open();
        String inv = "";
        if (transactionDbController.getLastSellTransferInvoiceNo().isEmpty()) {
            inv = "ST" + Calendar.getInstance().get(Calendar.YEAR) + "/0001";
        } else {
            String lastInv[] = transactionDbController.getLastSellTransferInvoiceNo().split("/");
            String lastInvValue = lastInv[1];
            int lastInvValueInc = Integer.parseInt(lastInvValue) + 1;
            inv = "ST" + Calendar.getInstance().get(Calendar.YEAR) + "/" + String.format(Locale.ENGLISH, "%04d", lastInvValueInc);
        }
        return inv;
    }
    public static String generateInvoiceStockAdjferNo(Context context){
        TransactionDbController transactionDbController=new TransactionDbController(context);
        transactionDbController.open();
        String inv = "";
        if (transactionDbController.getLastStockAdjInvoiceNo().isEmpty()) {
            inv = "SA" + Calendar.getInstance().get(Calendar.YEAR) + "/0001";
        } else {
            String lastInv[] = transactionDbController.getLastStockAdjInvoiceNo().split("/");
            String lastInvValue = lastInv[1];
            int lastInvValueInc = Integer.parseInt(lastInvValue) + 1;
            inv = "SA" + Calendar.getInstance().get(Calendar.YEAR) + "/" + String.format(Locale.ENGLISH, "%04d", lastInvValueInc);
        }
        return inv;
    }
    public static String generateInvoicePurchaseNo(Context context){
        TransactionDbController transactionDbController=new TransactionDbController(context);
        transactionDbController.open();
        String inv = "";
        if (transactionDbController.getLastPurchaseInvoiceNo().isEmpty()) {
            inv = "P" + Calendar.getInstance().get(Calendar.YEAR) + "/0001";
        } else {
            String lastInv[] = transactionDbController.getLastPurchaseInvoiceNo().split("/");
            String lastInvValue = lastInv[1];
            int lastInvValueInc = Integer.parseInt(lastInvValue) + 1;
            inv = "P" + Calendar.getInstance().get(Calendar.YEAR) + "/" + String.format(Locale.ENGLISH, "%04d", lastInvValueInc);
        }
        return inv;
    }
    public static String generateRefPaymentNo(Context context){
        TransactionPayementDbController transactionPayementDbController=new TransactionPayementDbController(context);
        transactionPayementDbController.open();
        String inv = "";
        if (transactionPayementDbController.getLastSellTransferPayInvoiceNo().isEmpty()) {
            inv = "TP" + Calendar.getInstance().get(Calendar.YEAR) + "/0001";
        } else {
            String lastInv[] = transactionPayementDbController.getLastSellTransferPayInvoiceNo().split("/");
            String lastInvValue = lastInv[1];
            int lastInvValueInc = Integer.parseInt(lastInvValue) + 1;
            inv = "TP" + Calendar.getInstance().get(Calendar.YEAR) + "/" + String.format(Locale.ENGLISH, "%04d", lastInvValueInc);
        }
        return inv;
    }
    public static String generateRefExpenseNo(Context context){
        TransactionDbController transactionDbController=new TransactionDbController(context);
        transactionDbController.open();
        String inv = "";
        if (transactionDbController.getLastExpenseInvoiceNo().isEmpty()) {
            inv = "EP" + Calendar.getInstance().get(Calendar.YEAR) + "/0001";
        } else {
            String lastInv[] = transactionDbController.getLastExpenseInvoiceNo().split("/");
            String lastInvValue = lastInv[1];
            int lastInvValueInc = Integer.parseInt(lastInvValue) + 1;
            inv = "EP" + Calendar.getInstance().get(Calendar.YEAR) + "/" + String.format(Locale.ENGLISH, "%04d", lastInvValueInc);
        }
        return inv;
    }
    public static void showSnackBar(View view,int title,Boolean isError)
    {
        Snackbar snackbar;
        snackbar = Snackbar.make(view, title, Snackbar.LENGTH_SHORT);
        View snackBarView = snackbar.getView();
        TextView textView = (TextView) snackBarView.findViewById(R.id.snackbar_text);
        if(isError) {
            snackBarView.setBackgroundColor(Color.RED);
        }
        else
        {
            snackBarView.setBackgroundColor(Color.BLACK);
        }
        textView.setTextColor(Color.WHITE);
        FrameLayout.LayoutParams params =(FrameLayout.LayoutParams)snackBarView.getLayoutParams();
        params.gravity = Gravity.TOP;
        params.gravity = Gravity.CENTER_HORIZONTAL;
        snackBarView.setLayoutParams(params);
        snackbar.show();


    }
    public static void showSnackBarF(View view,int title,Boolean isError)
    {
        Snackbar snackbar;
        snackbar = Snackbar.make(view, title, Snackbar.LENGTH_SHORT);
        View snackBarView = snackbar.getView();
        TextView textView = (TextView) snackBarView.findViewById(R.id.snackbar_text);
        if(isError) {
            snackBarView.setBackgroundColor(Color.RED);
        }
        else
        {
            snackBarView.setBackgroundColor(Color.BLACK);
        }
        textView.setTextColor(Color.WHITE);

        CoordinatorLayout.LayoutParams params = (CoordinatorLayout.LayoutParams)snackBarView.getLayoutParams();
        params.gravity = Gravity.TOP;
        params.gravity = Gravity.CENTER_HORIZONTAL;
        snackBarView.setLayoutParams(params);
        snackbar.show();
    }
}
