package com.rising.high.tech.bigultimatenavdraw.util;

import android.content.Context;

import androidx.appcompat.app.AppCompatActivity;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.fragment.app.FragmentTransaction;

import com.rising.high.tech.bigultimatenavdraw.R;
import com.rising.high.tech.bigultimatenavdraw.ui.contact.ListContactFragment;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;

public class helpFunctions {

    public static void replaceFragment(Context context) {
        Fragment newFragment = new ListContactFragment();
        FragmentTransaction transaction = ((AppCompatActivity)context).getSupportFragmentManager().beginTransaction();
        // Replace whatever is in the fragment_container view with this fragment,
        // and add the transaction to the back stack if needed
        transaction.replace(R.id.nav_host_fragment, newFragment);
        // Commit the transaction
        transaction.commit();
    }


    public static void pushFragment(Fragment newFragment, Context context){

        FragmentTransaction transaction = ((FragmentActivity)context).getSupportFragmentManager().beginTransaction();
        transaction.replace(R.id.nav_host_fragment, newFragment);
        transaction.setTransition(FragmentTransaction.TRANSIT_FRAGMENT_OPEN);
        transaction.addToBackStack(null);
        transaction.commit();

    }

   /* public static float getTotalTax(ArrayList<XProduct> products) {
        float total_tax = 00.0f;

        for (XProduct product : products) {
            Log.d("TAG", "tax item : " + product.getTax() + "");
            if (product.getTax() != null) {
                total_tax = total_tax + (product.getUnit_price_inc_tax() / Float.parseFloat(product.getTax().split("%")[0]));
            }
        }
        return total_tax;
    }


    public static float getTotalPriceFromData(ArrayList<XProduct> products) {
        float total_price = 00.0f;
        for (XProduct i : products) {
            total_price = total_price + (i.getSell_qte() * i.getUnit_price_inc_tax());
        }
        return total_price;
    }

    public static String getTotalPricePrint(ArrayList<XProduct> products , String currency) {
        float total_price = 00.0f;
        for (XProduct i : products) {
            total_price = total_price + (i.getSell_qte() * i.getUnit_price_inc_tax());
        }

        Float price = Float.parseFloat(new DecimalFormat("##.##").format(total_price));

        String m = "------------------";
        String s = "";
        String p = price+ " "+currency;

        s = "Total Amount :" +m.substring(0,p.length()) + p;

        s = s.replaceAll("-", " ");
        return s;

    }

    public static float getTotalItemFromData(ArrayList<XProduct> products) {
        int total_item = 0;
        for (XProduct i : products) {
            total_item = total_item + i.getSell_qte();
        }

        return total_item;
    }

    public static byte intToByteArray(int value) {
        byte[] b = ByteBuffer.allocate(4).putInt(value).array();

        for (int k = 0; k < b.length; k++) {
            System.out.println("Selva  [" + k + "] = " + "0x"
                    + UnicodeFormatter.byteToHex(b[k]));
        }

        return b[3];
    }

    public static String getOrderDetail(ArrayList<XProduct> arrayList) {
        // String m = "********************************\n";
        String m = "--------------------------------\n";
        // String m = "                                  ";
        String s = "";
        for (XProduct product : arrayList) {
            String pr = "";
            if (product.getProduct().length() >= 12) {
                pr = product.getProduct().substring(0, 12);
            } else {
                pr = product.getProduct();
            }
            s = s + pr + m.substring(pr.length(), 15) + product.getSell_qte() + m.substring(16, 23) + product.getUnit_price_inc_tax() + "\n";
        }
        //    s = s.replaceAll("-", " ");
        return s;
    }


    public static String getOrderDetailLast(ArrayList<XProduct> arrayList) {
        // String m = "********************************\n";

        //  final String data = new String(encodedBytes, "US-ASCII");
        String m = "--------------------------------\n";
        String s = "";
        for (XProduct product : arrayList) {
            String pr = "";
            if (product.getProduct().length() >= 12) {
                pr = product.getProduct().substring(0, 12);
            } else {
                pr = product.getProduct();

            }
            s = s + pr + m.substring(pr.length(), 15) + product.getSell_qte() + m.substring(13, 23) + product.getUnit_price_inc_tax() + "\n";
        }
        s = s.replaceAll("-", " ");
        return s;
    }


    public static String getOrderDetailProductTicket(int index, XProduct product, String curr) {
        // String m = "********************************\n";
        String m = "--------------------------------";
        Log.d("PRINTERTICKET", product.getProduct().length()+""  +" >>> " + product.getProduct() +" >>> " + product.getUnit_price_inc_tax());


        String s = "";
        String pr = "";
        String price = "";
        String qte = "";
        String separate = "";
           *//* if (product.getProduct().length() >= 12) {
                pr = product.getProduct().substring(0, 12);
            } else {
                pr = product.getProduct();
            }*//*
        pr = product.getProduct();
        qte = product.getSell_qte()+"";
        price = new DecimalFormat("##.##").format(product.getUnit_price_inc_tax())  ;
        price =price +" "+ curr;
        separate =  m.substring(pr.length() +qte.length() + 1 , (m.length()-price.length()));



        s =  index+"."+ pr + separate + price ;

        s = s.substring(0,20)+qte+s.substring(22);
        s = s.replaceAll("-", " ");
        return s;
    }


    public static String getOrderSDetail(ArrayList<XProduct> arrayList, String cur) {
        // String m = "********************************\n";
        String m = "--------------------------------\n";
        // String m = "                                  ";
        String s = "";
        for (XProduct product : arrayList) {
            String pr = "";
            if (product.getProduct().length() >= 12) {
                pr = product.getProduct().substring(0, 12);
            } else {
                pr = product.getProduct();
            }
            s = s + product.getSell_qte() + " . " + "  " + pr + "  ----  " + (product.getUnit_price_inc_tax() * product.getSell_qte()) + cur + "\n";
        }
        //    s = s.replaceAll("-", " ");
        return s;
    }


    public static String padRight(String s, int n) {
        return String.format("%-" + n + "s", s);
    }

    public static String padLeft(String s, int n) {
        return String.format("%" + n + "s", s);
    }

    public static String getOrderStringDetail(ArrayList<XProduct> arrayList, String cur) {
        String text = "";

        for (XProduct product : arrayList) {
            String name = product.getProduct();
            String phone = product.getUnit_price_inc_tax()+"";
            int nsp = 35 - name.length();
            text += "\n" + name + StringUtils.repeat(" ", nsp) + phone;
        }


        return text;
    }


    public static String formatCustDtl(String _str1, String _str2) {
        String str = "--------------------------------";
        String tmp = str;

        tmp = _str1 + (str.substring(_str1.length() + 1, str.length() - _str2.length())) + _str2 + "\n";
          tmp = tmp.replaceAll("-", " ");
        return tmp;
    }


    public static String populateSetDate(int year, int month, int day) {
        month += 1;
        String mt, dy;   //local variable
        if (month < 10)
            mt = "0" + month; //if month less than 10 then ad 0 before month
        else mt = String.valueOf(month);

        if (day < 10)
            dy = "0" + day;
        else dy = String.valueOf(day);
        String myFormat = year + "/" + mt + "/" + dy;
        return myFormat;
    }

    public static String getCurrentTime() {
        int year = Calendar.YEAR;
        int month = Calendar.MONTH;
        int day = Calendar.DAY_OF_MONTH;
        month += 1;
        String mt, dy;   //local variable
        if (month < 10)
            mt = "0" + month; //if month less than 10 then ad 0 before month
        else mt = String.valueOf(month);

        if (day < 10)
            dy = "0" + day;
        else dy = String.valueOf(day);
        String myFormat = year + "-" + mt + "-" + dy;
        return myFormat;
    }

    public static String getCustomDate() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss", Locale.getDefault());
        String currentDateandTime = sdf.format(new Date());
        return currentDateandTime;
    }
    */

}
