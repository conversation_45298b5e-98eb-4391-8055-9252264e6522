<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="1734.98dp"
    android:height="686.48dp"
    android:viewportWidth="1734.98"
    android:viewportHeight="686.48">
  <group>
    <clip-path
        android:pathData="M889,64.97h750v500h-750z"/>
    <path
        android:pathData="M1641.64,642.97 L1360.89,615.49c-0.68,0.26 0.91,-2.63 0.23,-2.37 -6.71,-13.32 -17.61,-24.18 -24.34,-37.49a70,70 0,0 1,-0.21 -61.81c10.27,-20.65 29.92,-34.64 47.89,-49.07 21.89,-17.58 42.48,-37.2 58,-60.55s25.94,-50.75 26.06,-78.82c0.07,-17.26 -3.69,-34.36 -3.64,-51.62 0.09,-34.22 17,-69 46.39,-86.48 24.76,-14.67 55.89,-16 83.6,-8.23s52.42,23.86 74.57,42.24a48,48 0,0 0,2.24 25.8l-35.58,363C1641.06,619.65 1637,633.37 1641.64,642.97Z"
        android:strokeAlpha="0.58"
        android:fillColor="#606060"
        android:fillAlpha="0.58"/>
    <path
        android:pathData="M1632.07,621.9 L1444.85,585.29c-0.47,0.13 0.78,-1.72 0.31,-1.59 -3.66,-9.4 -10.3,-17.4 -14,-26.8A47.4,47.4 0,0 1,1435 515.27c8.24,-13.23 22.37,-21.38 35.4,-29.94 15.87,-10.42 31,-22.3 43,-37S1534.13,415.81 1536,396.97c1.16,-11.61 -0.27,-23.36 0.88,-35 2.26,-23 15.86,-45.38 36.8,-55.22 17.61,-8.28 38.65,-7.17 56.8,-0.16S1664.26,325.97 1678,339.8a32.44,32.44 0,0 0,-0.15 17.51L1630.48,599.4C1633.18,606.13 1629.54,615.11 1632.07,621.9Z"
        android:fillColor="#eff4f4"/>
    <path
        android:pathData="M1180.78,592.23c-10.62,-8.26 -11.89,-21.37 -6.78,-32.15s15.55,-19.71 26.34,-27.9c18.92,-14.36 39.64,-27.43 57.6,-42.45 20.27,-17 37.5,-37.33 40.47,-60.33 2.08,-16.13 -3.65,-33.14 -18.14,-45.25C1239.34,349.97 1156.88,358.39 1113.53,384.97c-16.14,9.91 -29,22.41 -44,33.21S1036,438.4 1015.1,439.61s-43.84,-8.27 -47.61,-23.5c-4.37,-17.63 6.69,-34.76 -0.57,-52.36 -4.38,-10.65 -14,-20.47 -27.7,-25.06 -25.07,-8.41 -53.5,2.29 -79,10 -17.86,5.41 -37,9.47 -56.22,8.34s-38.56,-8.23 -47.85,-20.7c0,2 0.86,4.15 0.87,6.14l1.27,239.69 388.44,-3.09C1155.51,584.62 1170.56,588.26 1180.78,592.23Z"
        android:strokeAlpha="0.58"
        android:fillColor="#606060"
        android:fillAlpha="0.58"/>
    <path
        android:pathData="M1385.52,459.83c3.73,-16.27 1.52,-33.76 -9.24,-42.5 -8.19,-6.65 -20.28,-7.32 -31.64,-3.67C1294,429.97 1275.92,499.71 1232.13,529.1c-52.66,35.33 -114.23,7.52 -162.32,56.45 -14.28,14.52 -27.19,33.19 -28.4,52.36 -3.13,49.68 54,49.83 88.7,41.73 19.79,-4.63 39.94,-11 59.55,-11.47 21.78,-0.56 37.48,10.74 57.21,15.84C1292,695.66 1345,665.16 1366.92,612.69c11.92,-28.5 0.07,-45.24 -9.61,-67.58 -10.66,-24.59 22,-59.19 28.1,-84.77C1385.45,460.17 1385.49,459.97 1385.52,459.83Z"
        android:strokeAlpha="0.68"
        android:fillAlpha="0.68">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startY="647.8334"
            android:startX="1039.9117"
            android:endY="511.68466"
            android:endX="1395.2267"
            android:type="linear">
          <item android:offset="0" android:color="#FFAAFF2A"/>
          <item android:offset="0.51" android:color="#FF066D9A"/>
          <item android:offset="1" android:color="#FF55FFB5"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M1423.37,599.48C1413.23,580.35 1402.76,559.67 1397,538.77c-5.49,-19.8 8.94,-28.72 27,-28.63 20,0.11 40.82,4.6 59.25,-3 21.66,-9 34.25,-35.52 27.5,-58 -7.41,-24.62 -31.77,-42.05 -46.25,-62.51 -16.27,-23 -31.62,-49.56 -36.55,-77.64 -3.87,-22.07 0.31,-46.7 16,-62.67 17.84,-18.14 43.63,-23.24 65.61,-34.41 47.21,-24 45,-75.19 28.76,-118.55 -5,-13.27 -10.79,-28.4 -4.19,-40.94"
        android:strokeWidth="0.5"
        android:fillColor="#00000000"
        android:strokeColor="#fff"/>
    <path
        android:pathData="M1416.19,597.97c-10.14,-19.13 -20.61,-39.81 -26.42,-60.72 -5.49,-19.79 8.94,-28.72 27,-28.62 19.94,0.1 40.82,4.6 59.25,-3 21.66,-9 34.25,-35.52 27.5,-58 -7.41,-24.62 -31.77,-42.05 -46.25,-62.51 -16.27,-23 -31.62,-49.56 -36.55,-77.64 -3.87,-22.06 0.31,-46.7 16,-62.67 17.84,-18.13 43.63,-23.24 65.61,-34.41 47.21,-24 45,-75.18 28.76,-118.54 -5,-13.27 -10.79,-28.41 -4.19,-40.95"
        android:strokeWidth="0.5"
        android:fillColor="#00000000"
        android:strokeColor="#fff"/>
    <path
        android:pathData="M1409,596.53c-10.14,-19.13 -20.61,-39.81 -26.42,-60.71 -5.49,-19.8 8.94,-28.72 27,-28.63 19.94,0.1 40.82,4.6 59.25,-3 21.66,-9 34.25,-35.53 27.5,-58 -7.41,-24.62 -31.77,-42.05 -46.25,-62.51 -16.27,-23 -31.62,-49.56 -36.55,-77.65 -3.87,-22.06 0.31,-46.69 16,-62.66 17.84,-18.14 43.63,-23.24 65.61,-34.41 47.21,-24 45.05,-75.19 28.76,-118.55 -5,-13.27 -10.79,-28.4 -4.19,-40.94"
        android:strokeWidth="0.5"
        android:fillColor="#00000000"
        android:strokeColor="#fff"/>
    <path
        android:pathData="M1401.83,595.06c-10.14,-19.13 -20.61,-39.81 -26.42,-60.72 -5.49,-19.79 8.94,-28.72 27,-28.62 19.94,0.1 40.82,4.6 59.25,-3 21.66,-9 34.25,-35.52 27.5,-58 -7.41,-24.62 -31.77,-42.05 -46.25,-62.51 -16.27,-23 -31.62,-49.56 -36.55,-77.64 -3.87,-22.06 0.31,-46.7 16,-62.67C1440.2,223.75 1466,218.65 1488,207.48c47.2,-24 45,-75.18 28.76,-118.54 -5,-13.27 -10.79,-28.41 -4.19,-40.95"
        android:strokeWidth="0.5"
        android:fillColor="#00000000"
        android:strokeColor="#fff"/>
    <path
        android:pathData="M1394.65,593.58c-10.14,-19.13 -20.62,-39.81 -26.42,-60.72 -5.49,-19.79 8.94,-28.71 27,-28.62 19.94,0.1 40.82,4.6 59.25,-3 21.66,-9 34.25,-35.53 27.5,-58 -7.41,-24.62 -31.77,-42 -46.25,-62.51 -16.27,-23 -31.62,-49.56 -36.55,-77.65 -3.87,-22.06 0.31,-46.69 16,-62.66C1433,222.28 1458.81,217.18 1480.79,205.97c47.2,-24 45.05,-75.19 28.76,-118.55 -5,-13.27 -10.79,-28.41 -4.19,-40.94"
        android:strokeWidth="0.5"
        android:fillColor="#00000000"
        android:strokeColor="#fff"/>
    <path
        android:pathData="M1387.47,592.11c-10.14,-19.14 -20.62,-39.81 -26.42,-60.72 -5.49,-19.79 8.94,-28.72 27,-28.63 20,0.11 40.83,4.6 59.26,-3 21.66,-9 34.25,-35.52 27.49,-58 -7.4,-24.62 -31.76,-42.05 -46.24,-62.51 -16.27,-23 -31.62,-49.56 -36.55,-77.64 -3.87,-22.07 0.31,-46.7 16,-62.67 17.84,-18.14 43.63,-23.24 65.6,-34.41 47.21,-24 45.06,-75.18 28.77,-118.55 -5,-13.26 -10.79,-28.4 -4.19,-40.94"
        android:strokeWidth="0.5"
        android:fillColor="#00000000"
        android:strokeColor="#fff"/>
    <path
        android:pathData="M1380.29,590.63c-10.14,-19.13 -20.62,-39.81 -26.42,-60.72 -5.5,-19.79 8.93,-28.71 27,-28.62 20,0.1 40.83,4.6 59.26,-3 21.65,-9 34.25,-35.52 27.49,-58 -7.4,-24.62 -31.76,-42.05 -46.24,-62.51 -16.27,-23 -31.62,-49.56 -36.55,-77.65 -3.87,-22.06 0.31,-46.69 16,-62.67 17.83,-18.13 43.63,-23.24 65.6,-34.4 47.21,-24 45.06,-75.19 28.77,-118.55C1490.2,71.24 1484.4,56.1 1491,43.56"
        android:strokeWidth="0.5"
        android:fillColor="#00000000"
        android:strokeColor="#fff"/>
    <path
        android:pathData="M1373.11,589.16c-10.14,-19.14 -20.62,-39.81 -26.42,-60.72 -5.5,-19.79 8.93,-28.72 27,-28.63 19.95,0.11 40.83,4.6 59.26,-3 21.65,-9 34.25,-35.52 27.49,-58 -7.4,-24.62 -31.76,-42 -46.24,-62.51 -16.27,-23 -31.62,-49.56 -36.55,-77.64 -3.87,-22.07 0.31,-46.7 16,-62.67 17.83,-18.14 43.63,-23.24 65.6,-34.41 47.21,-24 45.06,-75.19 28.77,-118.55 -5,-13.26 -10.79,-28.4 -4.19,-40.94"
        android:strokeWidth="0.5"
        android:fillColor="#00000000"
        android:strokeColor="#fff"/>
    <path
        android:pathData="M1365.93,587.68c-10.14,-19.13 -20.62,-39.81 -26.42,-60.72 -5.5,-19.79 8.93,-28.72 27,-28.62 19.95,0.1 40.83,4.6 59.26,-3 21.65,-9 34.25,-35.52 27.49,-58 -7.4,-24.62 -31.76,-42 -46.24,-62.51 -16.27,-23 -31.63,-49.56 -36.55,-77.64 -3.87,-22.06 0.31,-46.69 16,-62.67 17.83,-18.13 43.63,-23.24 65.6,-34.4 47.21,-24 45.06,-75.19 28.77,-118.55 -5,-13.27 -10.79,-28.41 -4.19,-40.95"
        android:strokeWidth="0.5"
        android:fillColor="#00000000"
        android:strokeColor="#fff"/>
    <path
        android:pathData="M1358.75,586.21c-10.14,-19.14 -20.62,-39.81 -26.42,-60.72 -5.5,-19.79 8.93,-28.72 27,-28.63 19.95,0.11 40.83,4.6 59.26,-3 21.65,-9 34.25,-35.52 27.49,-58 -7.4,-24.62 -31.76,-42.05 -46.24,-62.51 -16.27,-23 -31.63,-49.56 -36.55,-77.64 -3.87,-22.07 0.31,-46.7 16,-62.67 17.83,-18.14 43.63,-23.24 65.6,-34.41 47.21,-24 45.06,-75.19 28.77,-118.55 -5,-13.26 -10.79,-28.4 -4.19,-40.94"
        android:strokeWidth="0.5"
        android:fillColor="#00000000"
        android:strokeColor="#fff"/>
    <path
        android:pathData="M1351.57,584.73C1341.43,565.6 1331,544.97 1325.15,523.97c-5.5,-19.79 8.93,-28.72 27,-28.62 19.95,0.1 40.83,4.6 59.26,-3 21.65,-9 34.25,-35.52 27.49,-58 -7.41,-24.62 -31.76,-42 -46.24,-62.51 -16.27,-23 -31.63,-49.56 -36.55,-77.64 -3.88,-22.06 0.31,-46.7 16,-62.67 17.83,-18.13 43.63,-23.24 65.6,-34.41 47.21,-24 45.06,-75.18 28.77,-118.54 -5,-13.27 -10.79,-28.41 -4.19,-40.95"
        android:strokeWidth="0.5"
        android:fillColor="#00000000"
        android:strokeColor="#fff"/>
    <path
        android:pathData="M1141.41,38.07C1151.55,57.21 1162,77.88 1167.83,98.79c5.49,19.79 -8.94,28.72 -27,28.63 -19.94,-0.11 -40.82,-4.6 -59.25,3 -21.66,9 -34.25,35.52 -27.5,58 7.41,24.62 31.77,42.05 46.25,62.51 16.27,23 31.62,49.56 36.55,77.64 3.87,22.07 -0.31,46.7 -16,62.67 -17.84,18.14 -43.63,23.24 -65.61,34.41 -47.21,24 -45.05,75.18 -28.76,118.55 5,13.26 10.79,28.4 4.19,40.94"
        android:strokeWidth="0.5"
        android:fillColor="#00000000"
        android:strokeColor="#fff"/>
    <path
        android:pathData="M1148.59,39.55C1158.73,58.68 1169.2,79.36 1175,100.27c5.49,19.79 -8.94,28.72 -27,28.62 -19.94,-0.1 -40.82,-4.6 -59.25,3 -21.66,9 -34.25,35.52 -27.5,58 7.41,24.62 31.77,42.05 46.25,62.51 16.27,23 31.62,49.56 36.55,77.65 3.87,22.06 -0.31,46.69 -16,62.67 -17.84,18.13 -43.63,23.24 -65.61,34.4 -47.2,24 -45,75.19 -28.76,118.55 5,13.27 10.79,28.41 4.19,40.95"
        android:strokeWidth="0.5"
        android:fillColor="#00000000"
        android:strokeColor="#fff"/>
    <path
        android:pathData="M1155.77,41.02c10.14,19.14 20.62,39.81 26.42,60.72 5.49,19.79 -8.94,28.72 -27,28.63 -19.94,-0.11 -40.82,-4.6 -59.25,3 -21.66,9 -34.25,35.52 -27.5,58 7.41,24.62 31.77,42 46.25,62.51 16.27,23 31.62,49.56 36.55,77.64 3.87,22.07 -0.31,46.7 -16,62.67 -17.84,18.14 -43.63,23.24 -65.61,34.41 -47.2,24 -45,75.19 -28.76,118.55 5,13.26 10.79,28.4 4.19,40.94"
        android:strokeWidth="0.5"
        android:fillColor="#00000000"
        android:strokeColor="#fff"/>
    <path
        android:pathData="M1163,42.5c10.14,19.13 20.62,39.81 26.42,60.72 5.49,19.79 -8.94,28.72 -27,28.62 -19.95,-0.1 -40.83,-4.6 -59.26,3 -21.66,9 -34.25,35.52 -27.49,58 7.4,24.62 31.76,42 46.24,62.51 16.27,23 31.62,49.56 36.55,77.64 3.87,22.06 -0.31,46.69 -16,62.67 -17.84,18.13 -43.63,23.24 -65.6,34.4 -47.21,24 -45.06,75.19 -28.77,118.55 5,13.27 10.79,28.41 4.19,41"
        android:strokeWidth="0.5"
        android:fillColor="#00000000"
        android:strokeColor="#fff"/>
    <path
        android:pathData="M1170.13,43.97c10.14,19.13 20.62,39.8 26.42,60.71 5.5,19.79 -8.93,28.72 -27,28.63 -19.95,-0.11 -40.83,-4.6 -59.26,3 -21.65,9 -34.25,35.52 -27.49,58 7.4,24.62 31.76,42.05 46.24,62.51 16.27,23 31.62,49.56 36.55,77.64 3.87,22.07 -0.31,46.7 -16,62.67C1131.77,415.28 1106,420.38 1084,431.55c-47.21,24 -45.06,75.19 -28.77,118.55 5,13.26 10.79,28.4 4.19,40.94"
        android:strokeWidth="0.5"
        android:fillColor="#00000000"
        android:strokeColor="#fff"/>
    <path
        android:pathData="M1177.31,45.45C1187.45,64.58 1197.93,85.26 1203.73,106.17c5.5,19.79 -8.93,28.72 -27,28.62 -19.95,-0.1 -40.83,-4.6 -59.26,3 -21.65,9 -34.25,35.52 -27.49,58 7.4,24.62 31.76,42 46.24,62.51 16.27,23 31.62,49.56 36.55,77.64 3.87,22.06 -0.31,46.7 -16,62.67 -17.83,18.13 -43.63,23.24 -65.6,34.41 -47.21,24 -45.06,75.18 -28.77,118.54 5,13.27 10.79,28.41 4.19,40.95"
        android:strokeWidth="0.5"
        android:fillColor="#00000000"
        android:strokeColor="#fff"/>
    <path
        android:pathData="M1184.49,46.97c10.14,19.13 20.62,39.81 26.42,60.71 5.5,19.8 -8.93,28.72 -27,28.63 -19.95,-0.11 -40.83,-4.6 -59.26,3 -21.65,9 -34.25,35.52 -27.49,58 7.4,24.62 31.76,42 46.24,62.51 16.27,23 31.63,49.56 36.55,77.64 3.87,22.07 -0.31,46.7 -16,62.67 -17.83,18.14 -43.63,23.24 -65.6,34.41 -47.21,24 -45.06,75.19 -28.77,118.55 5,13.26 10.79,28.4 4.19,40.94"
        android:strokeWidth="0.5"
        android:fillColor="#00000000"
        android:strokeColor="#fff"/>
    <path
        android:pathData="M1191.67,48.4c10.14,19.13 20.62,39.81 26.42,60.72 5.5,19.79 -8.93,28.72 -27,28.62 -19.95,-0.1 -40.83,-4.6 -59.26,3 -21.65,9 -34.25,35.52 -27.49,58 7.4,24.62 31.76,42 46.24,62.51 16.27,23 31.63,49.56 36.55,77.64 3.88,22.06 -0.31,46.7 -16,62.67 -17.83,18.13 -43.63,23.24 -65.6,34.41 -47.21,24 -45.06,75.18 -28.77,118.54 5,13.27 10.79,28.41 4.19,40.95"
        android:strokeWidth="0.5"
        android:fillColor="#00000000"
        android:strokeColor="#fff"/>
    <path
        android:pathData="M1198.85,49.88C1209,68.97 1219.47,89.69 1225.27,110.59c5.5,19.8 -8.93,28.72 -27,28.63 -19.95,-0.1 -40.83,-4.6 -59.26,3 -21.65,9 -34.25,35.52 -27.49,58 7.41,24.62 31.76,42.05 46.24,62.51 16.27,23 31.63,49.56 36.55,77.64 3.88,22.07 -0.31,46.7 -16,62.67 -17.83,18.14 -43.63,23.24 -65.6,34.41 -47.21,24 -45.06,75.19 -28.77,118.55 5,13.27 10.79,28.4 4.19,40.94"
        android:strokeWidth="0.5"
        android:fillColor="#00000000"
        android:strokeColor="#fff"/>
    <path
        android:pathData="M1206,51.35c10.14,19.13 20.62,39.81 26.42,60.72 5.5,19.79 -8.93,28.72 -27,28.62 -20,-0.1 -40.83,-4.6 -59.26,3 -21.65,9 -34.25,35.52 -27.49,58 7.41,24.62 31.76,42.05 46.24,62.51 16.28,23 31.63,49.56 36.55,77.64 3.88,22.06 -0.31,46.7 -16,62.67 -17.83,18.13 -43.63,23.24 -65.6,34.41 -47.21,24 -45.06,75.18 -28.77,118.54 5,13.27 10.79,28.41 4.19,41"
        android:strokeWidth="0.5"
        android:fillColor="#00000000"
        android:strokeColor="#fff"/>
    <path
        android:pathData="M1213.21,52.83C1223.35,71.97 1233.83,92.64 1239.63,113.55c5.5,19.79 -8.93,28.71 -27,28.62 -20,-0.1 -40.83,-4.6 -59.25,3 -21.66,9 -34.25,35.53 -27.5,58 7.41,24.62 31.77,42.05 46.24,62.51 16.28,23 31.63,49.56 36.56,77.65 3.87,22.06 -0.32,46.69 -16,62.66 -17.84,18.14 -43.63,23.24 -65.61,34.41 -47.21,24 -45.05,75.19 -28.76,118.55 5,13.27 10.79,28.4 4.18,40.94"
        android:strokeWidth="0.5"
        android:fillColor="#00000000"
        android:strokeColor="#fff"/>
    <path
        android:pathData="M1084,107.97q-1.11,3.72 -2,7.45c-4.25,18.71 -3,44.15 14.87,55.34 35.77,22.31 71.47,-3.87 108,-7.44 59.55,-5.81 110.14,63.08 169.48,38.11C1404.81,188.55 1426.15,153.19 1413,120.97c-20.58,-50.59 -73.76,-69 -122.55,-80.18 -39,-8.9 -79.45,-11.09 -119,-4.76 -16.63,2.65 -33.31,6.78 -47.84,15.3C1103,63.36 1090.68,85.6 1084,107.97Z"
        android:strokeAlpha="0.58"
        android:fillColor="#606060"
        android:fillAlpha="0.58"/>
    <path
        android:pathData="M1112.76,583.97c-7.82,-6.08 -8.76,-15.74 -5,-23.68s11.45,-14.51 19.4,-20.55c13.93,-10.58 29.19,-20.2 42.42,-31.26 14.93,-12.5 27.62,-27.49 29.8,-44.43A36.84,36.84 0,0 0,1186 430.72c-30.15,-25.2 -90.88,-19 -122.81,0.62 -11.89,7.3 -21.33,16.51 -32.42,24.46s-24.69,14.88 -40.07,15.77 -32.29,-6.1 -35.07,-17.31c-3.21,-13 4.93,-25.6 -0.41,-38.57a33.3,33.3 0,0 0,-20.4 -18.45c-18.47,-6.19 -39.41,1.69 -58.21,7.38 -13.16,4 -27.25,7 -41.4,6.15S806.84,404.71 800,395.52c0,1.46 0.63,3.06 0.64,4.52l0.93,176.54 286.08,-2.28C1094.15,578.36 1105.23,581.05 1112.76,583.97Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startY="366.04947"
            android:startX="1044.8624"
            android:endY="621.71704"
            android:endX="878.6305"
            android:type="linear">
          <item android:offset="0" android:color="#FFFF00FF"/>
          <item android:offset="0.08" android:color="#CCFF00FF"/>
          <item android:offset="0.17" android:color="#9BFF00FF"/>
          <item android:offset="0.26" android:color="#72FF00FF"/>
          <item android:offset="0.36" android:color="#4FFF00FF"/>
          <item android:offset="0.46" android:color="#33FF00FF"/>
          <item android:offset="0.57" android:color="#1CFF00FF"/>
          <item android:offset="0.68" android:color="#0CFF00FF"/>
          <item android:offset="0.81" android:color="#02FF00FF"/>
          <item android:offset="1" android:color="#00FF00FF"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M1719.57,235.85c11.83,-30.42 19.78,-63.74 12.81,-95.61a106.8,106.8 0,0 0,-136.5 -79C1587.12,63.97 1578.6,67.97 1571.7,74.02c-12.26,10.76 -18.19,26.77 -25.2,41.5s-16.76,29.83 -32.24,35c-20.71,6.86 -42.71,-6.94 -57.09,-23.34S1431.78,90.76 1413.6,78.72A66.86,66.86 0,0 0,1381.33 67.81c-16.19,-1.08 -33.59,4.66 -43.07,17.83 -10,13.95 -8.76,34.9 2.91,47.52 6.89,7.46 17.18,12.74 19.85,22.54 3.06,11.23 -5.75,22.33 -15.49,28.71S1324.29,194.97 1316.77,203.88c-11.8,13.93 -9.55,36.41 2.33,50.26s31.16,19.74 49.4,18.9 35.66,-7.64 52.08,-15.61 32.28,-17.22 49.4,-23.54c9.18,-3.4 19,-5.95 28.68,-4.78 17,2 31.57,16.58 33.7,33.51 1,8.22 -0.59,16.54 -0.25,24.82 1,25.37 22.95,48.2 48.25,50.24 10.95,0.88 21.84,-1.67 32.78,-2.63a113.17,113.17 0,0 1,49.58 6.83,121.21 121.21,0 0,0 16.7,-34.38C1687.79,281.24 1709.58,261.54 1719.57,235.85Z"
        android:strokeAlpha="0.56"
        android:fillAlpha="0.56">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startY="272.09674"
            android:startX="1322.369"
            android:endY="129.8433"
            android:endX="1729.4921"
            android:type="linear">
          <item android:offset="0" android:color="#FF85FFFF"/>
          <item android:offset="1" android:color="#FF5A15FF"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M912.88,0.65c0.78,0.07 -0.31,80.15 -0.29,87.12 0,5.88 0.1,12 2.17,17.35 2.75,7.16 8.84,10.32 15.45,10.91C948.07,117.63 959.72,102.44 974,93.53c9.11,-5.68 19.85,-8.47 30.09,-6.39a33.63,33.63 0,0 1,24.1 20c7.45,18.28 -2.91,35 -2.31,53.33 0.77,23.21 21.91,39.85 41.11,41.65 16.09,1.5 31.21,-6 45.48,-13.45 13.15,-6.85 30.9,-19.4 45.71,-14.56 10.4,3.41 16.56,14.3 23.86,22.42A63.85,63.85 0,0 0,1214.42 215.97c18,4.06 38.67,-3.49 48.55,-21.65 11.29,-20.75 3.48,-48 -8.11,-66.19 -12,-18.85 -28.7,-33.27 -41.8,-51.17 -7.44,-10.17 -14,-22.88 -11.84,-35.85a40.55,40.55 0,0 1,6.52 -15.5c8.62,-13.11 21.7,-22.42 35.91,-25.56C1133.27,9.02 1023.19,10.04 912.88,0.65Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startY="6.768396"
            android:startX="1088.5592"
            android:endY="223.3428"
            android:endX="1086.6255"
            android:type="linear">
          <item android:offset="0.14" android:color="#0000FFFF"/>
          <item android:offset="0.26" android:color="#0200FFFE"/>
          <item android:offset="0.36" android:color="#0C00FFFA"/>
          <item android:offset="0.44" android:color="#1E00FFF3"/>
          <item android:offset="0.53" android:color="#3500FFE9"/>
          <item android:offset="0.61" android:color="#5400FFDC"/>
          <item android:offset="0.69" android:color="#7A00FFCD"/>
          <item android:offset="0.76" android:color="#A800FFBB"/>
          <item android:offset="0.84" android:color="#DB00FFA6"/>
          <item android:offset="0.88" android:color="#FF00FF97"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M900.19,54.07c0.85,0.08 -0.35,87.28 -0.32,94.86 0,6.41 0.11,13 2.36,18.9 3,7.8 9.63,11.24 16.82,11.88 19.46,1.74 32.14,-14.8 47.71,-24.5 9.92,-6.18 21.62,-9.23 32.76,-7a36.62,36.62 0,0 1,26.25 21.83c8.11,19.91 -3.18,38.1 -2.52,58.07 0.84,25.28 23.85,43.4 44.76,45.35 17.53,1.64 34,-6.55 49.53,-14.65 14.31,-7.45 33.64,-21.12 49.77,-15.84 11.33,3.71 18,15.56 26,24.41 9.36,10.41 22.33,18.14 35.23,21.05 19.61,4.43 42.11,-3.8 52.87,-23.57 12.3,-22.59 3.79,-52.23 -8.83,-72.07 -13.05,-20.53 -31.25,-36.23 -45.51,-55.72 -8.11,-11.07 -15.22,-24.91 -12.9,-39a44.24,44.24 0,0 1,7.1 -16.88C1230.64,66.97 1244.88,56.79 1260.36,53.37 1140.16,63.19 1020.3,64.3 900.19,54.07Z"
        android:strokeAlpha="0.24"
        android:fillColor="#eff4f4"
        android:fillAlpha="0.24"/>
  </group>
</vector>
