<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/content"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/transparent"
    android:orientation="vertical"

    android:padding="20dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/rounded_white_bg"
        android:orientation="vertical"
        android:padding="@dimen/activity_vertical_margin">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="vertical"
            android:paddingVertical="@dimen/activity_left_margin">

            <TextView
                android:id="@+id/text_desactivate"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/string_desactivate"
                android:textColor="@color/black"
                android:textStyle="bold" />

            <TextView
                android:visibility="gone"
                android:id="@+id/text_activate"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/string_activate"
                android:textColor="@color/black"
                android:textStyle="bold" />

        </LinearLayout>


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="horizontal">

            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/btn_save"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp40"
                android:background="@drawable/bg_gradient"
                android:layout_marginHorizontal="@dimen/dp10"
                android:layout_marginTop="@dimen/dp5"
                android:text="@string/label_yes" />
            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/btn_close"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp40"
                android:background="@drawable/bg_gradient"
                android:layout_marginHorizontal="@dimen/dp10"
                android:layout_marginTop="@dimen/dp5"
                android:text="@string/label_noo" />


        </LinearLayout>

    </LinearLayout>
</LinearLayout>