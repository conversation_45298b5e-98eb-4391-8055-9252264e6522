<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

<!--    <LinearLayout-->
<!--        android:layout_width="match_parent"-->
<!--        android:layout_height="match_parent"-->
<!--        android:orientation="vertical"-->
<!--        android:padding="@dimen/activity_horizontal_margin">-->

<!--        <LinearLayout-->
<!--            android:layout_width="match_parent"-->
<!--            android:layout_height="match_parent"-->
<!--            android:orientation="vertical">-->
<!--            <LinearLayout-->
<!--                android:layout_width="match_parent"-->
<!--                android:layout_height="wrap_content"-->
<!--                android:paddingVertical="@dimen/nav_header_vertical_spacing"-->
<!--                android:orientation="horizontal">-->
<!--                <TextView-->
<!--                    android:layout_width="wrap_content"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:textStyle="bold"-->
<!--                    android:text="@string/label_category"-->
<!--                    android:textColor="@color/black"-->
<!--                    android:textSize="18dp"/>-->

<!--                <TextView-->
<!--                    android:layout_width="wrap_content"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:text="@string/label_add_new_category"                     android:textColor="@color/black"-->
<!--                    android:textSize="16dp"/>-->
<!--            </LinearLayout>-->


<!--            <LinearLayout-->
<!--                android:layout_width="match_parent"-->
<!--                android:layout_height="match_parent"-->
<!--                android:padding="@dimen/activity_vertical_margin">-->

<!--                <LinearLayout-->
<!--                    android:layout_width="match_parent"-->
<!--                    android:layout_height="match_parent"-->
<!--                    android:layout_weight="1"-->
<!--                    android:orientation="vertical"-->

<!--                    android:layout_marginHorizontal="@dimen/activity_left_margin">-->

<!--                    <TextView-->
<!--                        android:id="@+id/company_name_hint"-->
<!--                        android:layout_width="wrap_content"-->
<!--                        android:layout_height="wrap_content"-->
<!--                        android:text="Nom du produit (Désignation):*"-->
<!--                        android:textColor="@color/black" />-->

<!--                    <LinearLayout-->
<!--                        android:layout_width="match_parent"-->
<!--                        android:layout_height="wrap_content"-->
<!--                        android:gravity="center"-->
<!--                        android:orientation="horizontal"-->
<!--                        android:paddingTop="@dimen/margin_vertival">-->

<!--                        <ImageView-->
<!--                            android:layout_width="30px"-->
<!--                            android:layout_height="30px"-->
<!--                            android:src="@drawable/icons8_compost_heap" />-->

<!--                        <EditText-->
<!--                            android:id="@+id/product_name"-->
<!--                            android:layout_width="match_parent"-->
<!--                            android:layout_height="wrap_content"-->
<!--                            android:layout_weight="1" />-->
<!--                    </LinearLayout>-->

<!--                </LinearLayout>-->

<!--                <LinearLayout-->
<!--                    android:layout_width="match_parent"-->
<!--                    android:layout_height="match_parent"-->
<!--                    android:layout_weight="1"-->
<!--                    android:orientation="vertical"-->
<!--                    android:layout_marginHorizontal="@dimen/activity_left_margin">-->

<!--                    <TextView-->
<!--                        android:layout_width="wrap_content"-->
<!--                        android:layout_height="wrap_content"-->
<!--                        android:text="Catégorie:"-->
<!--                        android:textColor="@color/black" />-->

<!--                    <LinearLayout-->
<!--                        android:layout_width="match_parent"-->
<!--                        android:layout_height="match_parent"-->
<!--                        android:gravity="center"-->
<!--                        android:orientation="horizontal"-->
<!--                        android:paddingTop="@dimen/margin_vertival">-->

<!--                        <ImageView-->
<!--                            android:layout_width="30px"-->
<!--                            android:layout_height="30px"-->
<!--                            android:layout_weight="1"-->
<!--                            android:src="@drawable/icons_contacts" />-->

<!--                        <Spinner-->
<!--                            android:id="@+id/spinner_category"-->
<!--                            android:layout_width="match_parent"-->
<!--                            android:layout_height="wrap_content"-->
<!--                            android:layout_weight="1"/>-->
<!--                    </LinearLayout>-->

<!--                </LinearLayout>-->

<!--                <LinearLayout-->
<!--                    android:visibility="gone"-->
<!--                    android:layout_width="match_parent"-->
<!--                    android:layout_height="match_parent"-->
<!--                    android:layout_weight="1"-->
<!--                    android:orientation="vertical"-->
<!--                    android:layout_marginHorizontal="@dimen/activity_left_margin">-->

<!--                    <TextView-->
<!--                        android:layout_width="wrap_content"-->
<!--                        android:layout_height="wrap_content"-->
<!--                        android:text="SKU:"-->
<!--                        android:textColor="@color/black" />-->

<!--                    <LinearLayout-->
<!--                        android:layout_width="match_parent"-->
<!--                        android:layout_height="wrap_content"-->
<!--                        android:gravity="center"-->
<!--                        android:orientation="horizontal"-->
<!--                        android:paddingTop="@dimen/margin_vertival">-->

<!--                        <ImageView-->
<!--                            android:layout_width="30px"-->
<!--                            android:layout_height="30px"-->
<!--                            android:src="@drawable/icons8_user" />-->

<!--                        <EditText-->
<!--                            android:id="@+id/first_name_txt"-->
<!--                            android:layout_width="match_parent"-->
<!--                            android:layout_height="wrap_content"-->
<!--                            android:layout_weight="1" />-->
<!--                    </LinearLayout>-->

<!--                </LinearLayout>-->
<!--                <LinearLayout-->
<!--                    android:layout_width="match_parent"-->
<!--                    android:layout_height="match_parent"-->
<!--                    android:layout_weight="1"-->
<!--                    android:orientation="vertical"-->
<!--                    android:layout_marginHorizontal="@dimen/activity_left_margin">-->

<!--                    <TextView-->
<!--                        android:layout_width="wrap_content"-->
<!--                        android:layout_height="wrap_content"-->
<!--                        android:text="Type de code-barres:*"-->
<!--                        android:textColor="@color/black" />-->

<!--                    <LinearLayout-->
<!--                        android:layout_width="match_parent"-->
<!--                        android:layout_height="match_parent"-->
<!--                        android:gravity="center"-->
<!--                        android:orientation="horizontal"-->
<!--                        android:paddingTop="@dimen/margin_vertival">-->

<!--                        <ImageView-->
<!--                            android:layout_width="30px"-->
<!--                            android:layout_height="30px"-->
<!--                            android:layout_weight="1"-->
<!--                            android:src="@drawable/icons_contacts" />-->

<!--                        <Spinner-->
<!--                            android:id="@+id/spinner_type"-->
<!--                            android:layout_width="match_parent"-->
<!--                            android:layout_height="wrap_content"-->
<!--                            android:layout_weight="1"-->
<!--                            android:entries="@array/array_barcode_type" />-->
<!--                    </LinearLayout>-->

<!--                </LinearLayout>-->
<!--                <LinearLayout-->
<!--                    android:layout_width="match_parent"-->
<!--                    android:layout_height="match_parent"-->
<!--                    android:layout_weight="1"-->
<!--                    android:orientation="vertical"-->
<!--                    android:layout_marginHorizontal="@dimen/activity_left_margin">-->

<!--                    <TextView-->
<!--                        android:layout_width="wrap_content"-->
<!--                        android:layout_height="wrap_content"-->
<!--                        android:text="Unité:*"-->
<!--                        android:textColor="@color/black" />-->

<!--                    <LinearLayout-->
<!--                        android:layout_width="match_parent"-->
<!--                        android:layout_height="match_parent"-->
<!--                        android:gravity="center"-->
<!--                        android:orientation="horizontal"-->
<!--                        android:paddingTop="@dimen/margin_vertival">-->

<!--                        <ImageView-->
<!--                            android:layout_width="30px"-->
<!--                            android:layout_height="30px"-->
<!--                            android:layout_weight="1"-->
<!--                            android:src="@drawable/icons_contacts" />-->

<!--                        <Spinner-->
<!--                            android:id="@+id/spinner_unit"-->
<!--                            android:layout_width="match_parent"-->
<!--                            android:layout_height="wrap_content"-->
<!--                            android:layout_weight="1"-->
<!--                            android:entries="@array/type_select_unite" />-->
<!--                    </LinearLayout>-->

<!--                </LinearLayout>-->

<!--            </LinearLayout>-->
<!--            <LinearLayout-->
<!--                android:layout_width="match_parent"-->
<!--                android:layout_height="match_parent"-->
<!--                android:padding="@dimen/activity_vertical_margin">-->

<!--                <LinearLayout-->
<!--                    android:layout_width="match_parent"-->
<!--                    android:layout_height="match_parent"-->
<!--                    android:layout_weight="1"-->
<!--                    android:orientation="vertical"-->
<!--                    android:layout_marginHorizontal="@dimen/activity_left_margin">-->

<!--                    <TextView-->
<!--                        android:layout_width="wrap_content"-->
<!--                        android:layout_height="wrap_content"-->
<!--                        android:text="Marque:"-->
<!--                        android:textColor="@color/black" />-->

<!--                    <LinearLayout-->
<!--                        android:layout_width="match_parent"-->
<!--                        android:layout_height="match_parent"-->
<!--                        android:gravity="center"-->
<!--                        android:orientation="horizontal"-->
<!--                        android:paddingTop="@dimen/margin_vertival">-->

<!--                        <ImageView-->
<!--                            android:layout_width="30px"-->
<!--                            android:layout_height="30px"-->
<!--                            android:layout_weight="1"-->
<!--                            android:src="@drawable/icons_contacts" />-->

<!--                        <Spinner-->
<!--                            android:layout_width="match_parent"-->
<!--                            android:layout_height="wrap_content"-->
<!--                            android:layout_weight="1"-->
<!--                            android:entries="@array/type_product_marque" />-->
<!--                    </LinearLayout>-->

<!--                </LinearLayout>-->

<!--                <LinearLayout-->
<!--                    android:layout_width="match_parent"-->
<!--                    android:layout_height="match_parent"-->
<!--                    android:layout_weight="1"-->
<!--                    android:orientation="vertical"-->
<!--                    android:layout_marginHorizontal="@dimen/activity_left_margin">-->

<!--                    <TextView-->
<!--                        android:layout_width="wrap_content"-->
<!--                        android:layout_height="wrap_content"-->
<!--                        android:text="@string/lbl_prod_desc"-->
<!--                        android:textColor="@color/black" />-->

<!--                    <LinearLayout-->
<!--                        android:layout_width="match_parent"-->
<!--                        android:layout_height="wrap_content"-->
<!--                        android:gravity="center"-->
<!--                        android:orientation="horizontal"-->
<!--                        android:paddingTop="@dimen/margin_vertival">-->

<!--                        <ImageView-->
<!--                            android:layout_width="30px"-->
<!--                            android:layout_height="30px"-->
<!--                            android:src="@drawable/icons8_compost_heap" />-->

<!--                        <EditText-->
<!--                            android:id="@+id/bussiness_name_txt"-->
<!--                            android:layout_width="match_parent"-->
<!--                            android:layout_height="wrap_content"-->

<!--                            android:layout_weight="1" />-->
<!--                    </LinearLayout>-->

<!--                </LinearLayout>-->
<!--                <LinearLayout-->

<!--                    android:layout_width="match_parent"-->
<!--                    android:layout_height="match_parent"-->
<!--                    android:layout_weight="1"-->
<!--                    android:visibility="gone"-->
<!--                    android:orientation="vertical"-->
<!--                    android:layout_marginHorizontal="@dimen/activity_left_margin">-->

<!--                    <TextView-->
<!--                        android:layout_width="wrap_content"-->
<!--                        android:layout_height="wrap_content"-->
<!--                        android:text="Type de produit:* "-->
<!--                        android:textColor="@color/black" />-->

<!--                    <LinearLayout-->
<!--                        android:layout_width="match_parent"-->
<!--                        android:layout_height="match_parent"-->
<!--                        android:gravity="center"-->
<!--                        android:orientation="horizontal"-->
<!--                        android:paddingTop="@dimen/margin_vertival">-->

<!--                        <ImageView-->
<!--                            android:layout_width="30px"-->
<!--                            android:layout_height="30px"-->
<!--                            android:layout_weight="1"-->
<!--                            android:src="@drawable/icons_contacts" />-->

<!--                        <Spinner-->
<!--                            android:layout_width="match_parent"-->
<!--                            android:layout_height="wrap_content"-->
<!--                            android:layout_weight="1"-->
<!--                            android:entries="@array/type_product_type" />-->
<!--                    </LinearLayout>-->

<!--                </LinearLayout>-->

<!--                <LinearLayout-->

<!--                    android:layout_width="match_parent"-->
<!--                    android:layout_height="match_parent"-->
<!--                    android:layout_weight="1"-->
<!--                    android:visibility="invisible"-->
<!--                    android:orientation="vertical"-->
<!--                    android:layout_marginHorizontal="@dimen/activity_left_margin">-->

<!--                    <TextView-->
<!--                        android:layout_width="wrap_content"-->
<!--                        android:layout_height="wrap_content"-->
<!--                        android:text="@string/lbl_seling_pric_tax"-->
<!--                        android:textColor="@color/black" />-->

<!--                    <LinearLayout-->
<!--                        android:layout_width="wrap_content"-->
<!--                        android:layout_height="match_parent"-->
<!--                        android:gravity="center"-->
<!--                        android:orientation="horizontal"-->
<!--                        android:paddingTop="@dimen/margin_vertival">-->

<!--                        <ImageView-->
<!--                            android:layout_width="30px"-->
<!--                            android:layout_height="30px"-->
<!--                            android:layout_weight="1"-->
<!--                            android:src="@drawable/icons_contacts" />-->

<!--                        <Spinner-->
<!--                            android:layout_width="match_parent"-->
<!--                            android:layout_height="wrap_content"-->
<!--                            android:layout_weight="1"-->
<!--                            android:entries="@array/array_apply_tax_price" />-->
<!--                    </LinearLayout>-->

<!--                </LinearLayout>-->

<!--                <LinearLayout-->
<!--                    android:layout_width="match_parent"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:layout_weight="1"-->
<!--                    android:gravity="center"-->
<!--                    android:orientation="horizontal"-->
<!--                    android:paddingHorizontal="10dp"-->
<!--                    android:paddingVertical="10dp">-->

<!--                    <TextView-->
<!--                        android:layout_width="match_parent"-->
<!--                        android:layout_height="wrap_content"-->
<!--                        android:layout_weight="1"-->
<!--                        android:layout_below="@+id/discription_panne"-->
<!--                        android:text="@string/image_attache_libelle"-->
<!--                        android:textColor="@color/black" />-->

<!--                    <LinearLayout-->
<!--                        android:layout_width="match_parent"-->
<!--                        android:layout_weight="1"-->
<!--                        android:layout_height="wrap_content"-->
<!--                        android:layout_below="@+id/v2"-->
<!--                        android:gravity="right"-->
<!--                        android:orientation="horizontal">-->

<!--                        <ImageButton-->
<!--                            android:layout_width="30dp"-->
<!--                            android:layout_height="30dp"-->
<!--                            android:layout_gravity="right"-->
<!--                            android:layout_marginRight="0dp"-->
<!--                            android:background="@drawable/add"-->
<!--                            android:foregroundGravity="right" />-->
<!--                    </LinearLayout>-->
<!--                    <LinearLayout-->
<!--                        android:id="@+id/layoutGallery"-->
<!--                        android:layout_width="match_parent"-->
<!--                        android:layout_height="wrap_content"-->
<!--                        android:layout_weight="1"-->
<!--                        android:layout_marginRight="@dimen/activity_vertical_margin"-->
<!--                        android:background="@drawable/rounded_edittext"-->
<!--                        android:orientation="horizontal"-->
<!--                        android:paddingHorizontal="10dp">-->


<!--                        <Gallery-->
<!--                            android:id="@+id/gallery"-->
<!--                            android:layout_width="fill_parent"-->
<!--                            android:layout_height="wrap_content"-->
<!--                            android:layout_gravity="center"-->
<!--                            android:layout_weight="1" />-->

<!--                    </LinearLayout>-->
<!--                </LinearLayout>-->

<!--                <LinearLayout-->
<!--                    android:layout_width="match_parent"-->
<!--                    android:layout_height="match_parent"-->
<!--                    android:layout_weight="1"-->
<!--                    android:orientation="vertical"-->
<!--                    android:visibility="gone"-->
<!--                    android:layout_marginHorizontal="@dimen/activity_left_margin">-->

<!--                    <TextView-->
<!--                        android:layout_width="wrap_content"-->
<!--                        android:layout_height="wrap_content"-->
<!--                        android:text="@string/lbl_app_tax"-->
<!--                        android:textColor="@color/black" />-->

<!--                    <LinearLayout-->
<!--                        android:layout_width="match_parent"-->
<!--                        android:layout_height="wrap_content"-->
<!--                        android:gravity="center"-->
<!--                        android:orientation="horizontal"-->
<!--                        android:paddingTop="@dimen/margin_vertival">-->

<!--                        <ImageView-->
<!--                            android:layout_width="30px"-->
<!--                            android:layout_height="30px"-->
<!--                            android:src="@drawable/icons8_email" />-->


<!--                        <Spinner-->
<!--                            android:layout_width="match_parent"-->
<!--                            android:layout_height="wrap_content"-->
<!--                            android:layout_weight="1"-->
<!--                            android:entries="@array/array_apply_tax" />-->
<!--                    </LinearLayout>-->

<!--                </LinearLayout>-->



<!--            </LinearLayout>-->

<!--            <View-->
<!--                android:layout_width="match_parent"-->
<!--                android:layout_height="1px"-->
<!--                android:layout_marginVertical="@dimen/activity_vertical_margin"-->
<!--                android:layout_marginHorizontal="50dp"-->
<!--                android:background="@color/gray"/>-->

<!--            <LinearLayout-->
<!--                android:layout_width="match_parent"-->
<!--                android:layout_height="match_parent"-->
<!--                android:padding="@dimen/activity_vertical_margin">-->

<!--                <LinearLayout-->
<!--                    android:layout_width="match_parent"-->
<!--                    android:layout_height="match_parent"-->
<!--                    android:layout_weight="1"-->
<!--                    android:visibility="visible"-->
<!--                    android:orientation="vertical"-->
<!--                    android:layout_marginHorizontal="@dimen/activity_left_margin">-->

<!--                    <TextView-->
<!--                        android:layout_width="wrap_content"-->
<!--                        android:layout_height="wrap_content"-->
<!--                        android:text="@string/lbl_seling_pric_tax"-->
<!--                        android:textColor="@color/black" />-->

<!--                    <LinearLayout-->
<!--                        android:layout_width="match_parent"-->
<!--                        android:layout_height="match_parent"-->
<!--                        android:gravity="center"-->
<!--                        android:orientation="horizontal"-->
<!--                        android:paddingTop="@dimen/margin_vertival">-->

<!--                        <ImageView-->
<!--                            android:layout_width="30px"-->
<!--                            android:layout_height="30px"-->
<!--                            android:layout_weight="1"-->
<!--                            android:src="@drawable/icons_contacts" />-->

<!--                        <Spinner-->
<!--                            android:id="@+id/id_seling_tax_price_spinner"-->
<!--                            android:layout_width="match_parent"-->
<!--                            android:layout_height="wrap_content"-->
<!--                            android:layout_weight="1"-->
<!--                            android:entries="@array/array_apply_tax_price" />-->
<!--                    </LinearLayout>-->

<!--                </LinearLayout>-->

<!--                <LinearLayout-->
<!--                    android:layout_width="match_parent"-->
<!--                    android:layout_height="match_parent"-->
<!--                    android:layout_weight="1"-->
<!--                    android:orientation="vertical"-->
<!--                    android:layout_marginHorizontal="@dimen/activity_left_margin">-->

<!--                    <TextView-->
<!--                        android:layout_width="wrap_content"-->
<!--                        android:layout_height="wrap_content"-->
<!--                        android:text="@string/lbl_app_tax"-->
<!--                        android:textColor="@color/black" />-->

<!--                    <LinearLayout-->
<!--                        android:layout_width="match_parent"-->
<!--                        android:layout_height="match_parent"-->
<!--                        android:gravity="center"-->
<!--                        android:orientation="horizontal"-->
<!--                        android:paddingTop="@dimen/margin_vertival">-->

<!--                        <ImageView-->
<!--                            android:layout_width="30px"-->
<!--                            android:layout_height="30px"-->
<!--                            android:layout_weight="1"-->
<!--                            android:src="@drawable/icons_contacts" />-->

<!--                        <Spinner-->
<!--                            android:layout_width="match_parent"-->
<!--                            android:layout_height="wrap_content"-->
<!--                            android:entries="@array/array_apply_tax"-->
<!--                            android:layout_weight="1"/>-->

<!--                    </LinearLayout>-->

<!--                </LinearLayout>-->

<!--                <LinearLayout-->
<!--                    android:layout_width="match_parent"-->
<!--                    android:layout_height="match_parent"-->
<!--                    android:layout_weight="1"-->
<!--                    android:orientation="vertical"-->
<!--                    android:layout_marginHorizontal="@dimen/activity_left_margin">-->

<!--                    <TextView-->
<!--                        android:layout_width="wrap_content"-->
<!--                        android:layout_height="wrap_content"-->
<!--                        android:text="Type de produit:* "-->
<!--                        android:textColor="@color/black" />-->

<!--                    <LinearLayout-->
<!--                        android:layout_width="match_parent"-->
<!--                        android:layout_height="match_parent"-->
<!--                        android:gravity="center"-->
<!--                        android:orientation="horizontal"-->
<!--                        android:paddingTop="@dimen/margin_vertival">-->

<!--                        <ImageView-->
<!--                            android:layout_width="30px"-->
<!--                            android:layout_height="30px"-->
<!--                            android:layout_weight="1"-->
<!--                            android:src="@drawable/icons_contacts" />-->

<!--                        <Spinner-->
<!--                            android:layout_width="match_parent"-->
<!--                            android:layout_height="wrap_content"-->
<!--                            android:layout_weight="1"-->
<!--                            android:entries="@array/type_product_type" />-->
<!--                    </LinearLayout>-->

<!--                </LinearLayout>-->

<!--                <LinearLayout-->
<!--                    android:layout_width="match_parent"-->
<!--                    android:layout_height="match_parent"-->
<!--                    android:layout_weight="1"-->
<!--                    android:orientation="vertical"-->
<!--                    android:layout_marginHorizontal="@dimen/activity_left_margin">-->

<!--                    <TextView-->
<!--                        android:layout_width="wrap_content"-->
<!--                        android:layout_height="wrap_content"-->
<!--                        android:text="@string/string_default_price_vente"-->
<!--                        android:textColor="@color/black" />-->

<!--                    <LinearLayout-->
<!--                        android:layout_width="match_parent"-->
<!--                        android:layout_height="wrap_content"-->
<!--                        android:gravity="center"-->
<!--                        android:orientation="horizontal"-->
<!--                        android:paddingTop="@dimen/margin_vertival">-->

<!--                        <ImageView-->
<!--                            android:layout_width="30px"-->
<!--                            android:layout_height="30px"-->
<!--                            android:src="@drawable/icons8_location" />-->

<!--                        <EditText-->
<!--                            android:id="@+id/id_unit_price"-->
<!--                            android:layout_width="match_parent"-->
<!--                            android:layout_height="wrap_content"-->
<!--                            android:inputType="numberDecimal"-->
<!--                            android:layout_weight="1" />-->
<!--                    </LinearLayout>-->

<!--                </LinearLayout>-->

<!--            </LinearLayout>-->
<!--            <LinearLayout-->
<!--                android:visibility="gone"-->
<!--                android:layout_width="match_parent"-->
<!--                android:layout_height="match_parent"-->
<!--                android:padding="@dimen/activity_vertical_margin">-->

<!--                <LinearLayout-->
<!--                    android:layout_width="match_parent"-->
<!--                    android:layout_height="match_parent"-->
<!--                    android:layout_weight="1"-->
<!--                    android:orientation="vertical"-->
<!--                    android:layout_marginHorizontal="@dimen/activity_left_margin">-->

<!--                    <TextView-->
<!--                        android:layout_width="wrap_content"-->
<!--                        android:layout_height="wrap_content"-->
<!--                        android:text="@string/lbl_alert_exc_tax"-->
<!--                        android:textColor="@color/black" />-->

<!--                    <LinearLayout-->
<!--                        android:layout_width="match_parent"-->
<!--                        android:layout_height="wrap_content"-->
<!--                        android:gravity="center"-->
<!--                        android:orientation="horizontal"-->
<!--                        android:paddingTop="@dimen/margin_vertival">-->

<!--                        <ImageView-->
<!--                            android:layout_width="30px"-->
<!--                            android:layout_height="30px"-->
<!--                            android:src="@drawable/icons8_location" />-->

<!--                        <EditText-->
<!--                            android:id="@+id/id_exc_tax"-->
<!--                            android:layout_width="match_parent"-->
<!--                            android:layout_height="wrap_content"-->
<!--                            android:layout_weight="1" />-->
<!--                    </LinearLayout>-->

<!--                </LinearLayout>-->

<!--                <LinearLayout-->
<!--                    android:layout_width="match_parent"-->
<!--                    android:layout_height="match_parent"-->
<!--                    android:layout_weight="1"-->
<!--                    android:orientation="vertical"-->
<!--                    android:layout_marginHorizontal="@dimen/activity_left_margin">-->

<!--                    <TextView-->
<!--                        android:layout_width="wrap_content"-->
<!--                        android:layout_height="wrap_content"-->
<!--                        android:text="@string/lbl_alert_inc_tax"-->
<!--                        android:textColor="@color/black" />-->

<!--                    <LinearLayout-->
<!--                        android:layout_width="match_parent"-->
<!--                        android:layout_height="wrap_content"-->
<!--                        android:gravity="center"-->
<!--                        android:orientation="horizontal"-->
<!--                        android:paddingTop="@dimen/margin_vertival">-->

<!--                        <ImageView-->
<!--                            android:layout_width="30px"-->
<!--                            android:layout_height="30px"-->
<!--                            android:src="@drawable/icons8_location" />-->

<!--                        <EditText-->
<!--                            android:id="@+id/id_inc_tax"-->
<!--                            android:layout_width="match_parent"-->
<!--                            android:layout_height="wrap_content"-->
<!--                            android:layout_weight="1" />-->
<!--                    </LinearLayout>-->

<!--                </LinearLayout>-->

<!--                <LinearLayout-->
<!--                    android:layout_width="match_parent"-->
<!--                    android:layout_height="match_parent"-->
<!--                    android:layout_weight="1"-->
<!--                    android:orientation="vertical"-->
<!--                    android:layout_marginHorizontal="@dimen/activity_left_margin">-->

<!--                    <TextView-->
<!--                        android:layout_width="wrap_content"-->
<!--                        android:layout_height="wrap_content"-->
<!--                        android:text="@string/lbl_margin"-->
<!--                        android:textColor="@color/black" />-->

<!--                    <LinearLayout-->
<!--                        android:layout_width="match_parent"-->
<!--                        android:layout_height="wrap_content"-->
<!--                        android:gravity="center"-->
<!--                        android:orientation="horizontal"-->
<!--                        android:paddingTop="@dimen/margin_vertival">-->

<!--                        <ImageView-->
<!--                            android:layout_width="30px"-->
<!--                            android:layout_height="30px"-->
<!--                            android:src="@drawable/icons8_location" />-->

<!--                        <EditText-->
<!--                            android:id="@+id/id_margin"-->
<!--                            android:layout_width="match_parent"-->
<!--                            android:layout_height="wrap_content"-->
<!--                            android:layout_weight="1" />-->
<!--                    </LinearLayout>-->

<!--                </LinearLayout>-->

<!--                <LinearLayout-->
<!--                    android:layout_width="match_parent"-->
<!--                    android:layout_height="match_parent"-->
<!--                    android:layout_weight="1"-->
<!--                    android:orientation="vertical"-->
<!--                    android:layout_marginHorizontal="@dimen/activity_left_margin">-->

<!--                    <TextView-->
<!--                        android:layout_width="wrap_content"-->
<!--                        android:layout_height="wrap_content"-->
<!--                        android:text="@string/string_default_price_vente"-->
<!--                        android:textColor="@color/black" />-->

<!--                    <LinearLayout-->
<!--                        android:layout_width="match_parent"-->
<!--                        android:layout_height="wrap_content"-->
<!--                        android:gravity="center"-->
<!--                        android:orientation="horizontal"-->
<!--                        android:paddingTop="@dimen/margin_vertival">-->

<!--                        <ImageView-->
<!--                            android:layout_width="30px"-->
<!--                            android:layout_height="30px"-->
<!--                            android:src="@drawable/icons8_location" />-->

<!--                        <EditText-->
<!--                            android:id="@+id/default_sell_inc_tax"-->
<!--                            android:layout_width="match_parent"-->
<!--                            android:layout_height="wrap_content"-->
<!--                            android:layout_weight="1" />-->
<!--                    </LinearLayout>-->

<!--                </LinearLayout>-->

<!--            </LinearLayout>-->
<!--            <LinearLayout-->
<!--                android:layout_width="match_parent"-->
<!--                android:layout_height="wrap_content"-->
<!--                android:gravity="center"-->
<!--                android:orientation="horizontal"-->
<!--                android:padding="@dimen/padding_top">-->

<!--                <Button-->
<!--                    android:id="@+id/add_btn"-->
<!--                        style="@style/my_btn_style"-->
<!--                        android:layout_width="wrap_content"-->
<!--                        android:layout_height="70px"-->

<!--                    android:text="@string/label_add" />-->

<!--                <Button-->
<!--                    android:id="@+id/id_back"-->
<!--                    style="@style/my_btn_style"-->
<!--                    android:background="@drawable/rounded_btn_blue"-->
<!--                    android:layout_width="wrap_content"-->
<!--                    android:layout_height="wrap_content"-->
<!--                    android:text="@string/label_close" />-->

<!--            </LinearLayout>-->
<!--        </LinearLayout>-->

<!--    </LinearLayout>-->

</ScrollView>