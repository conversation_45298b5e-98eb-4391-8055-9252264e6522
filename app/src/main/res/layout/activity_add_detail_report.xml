<?xml version="1.0" encoding="utf-8"?>

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <include layout="@layout/toolbar" />

    <ProgressBar
        android:id="@+id/progress_report"
        android:layout_marginTop="-7dp"
        android:layout_marginBottom="-7dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:max="6"
        style="@style/Base.Widget.AppCompat.ProgressBar.Horizontal" />

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:context=".ui.dailyReports.DetailReport.DDetailReportMain">

        <com.google.android.material.appbar.AppBarLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/colorPrimary">

            <com.google.android.material.appbar.CollapsingToolbarLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <Button
                        android:id="@+id/id_validate_report"
                        android:layout_width="match_parent"
                        android:layout_height="35dp"
                        android:textStyle="bold"
                        android:visibility="visible"
                        android:layout_marginHorizontal="20dp"
                        style="@style/my_btn_style"
                        android:text="label_validate_report" />

                    <com.google.android.material.tabs.TabLayout
                        android:id="@+id/MyTabs"
                        app:tabTextColor="@color/black"
                        app:tabTextAppearance="@style/MyCustomTextAppearance"
                        android:layout_width="match_parent"
                        android:layout_height="?attr/actionBarSize"
                        app:layout_collapseMode="parallax"
                        app:tabGravity="center"
                        app:tabIndicatorColor="@android:color/black"
                        app:tabMode="scrollable"
                        app:tabSelectedTextColor="@android:color/black" />
                </LinearLayout>

            </com.google.android.material.appbar.CollapsingToolbarLayout>
        </com.google.android.material.appbar.AppBarLayout>

        <LinearLayout
            android:id="@+id/linearLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_behavior="@string/appbar_scrolling_view_behavior">

            <include
                layout="@layout/content_main_detail_report"
                app:layout_behavior="@string/appbar_scrolling_view_behavior" />
        </LinearLayout>


    </androidx.coordinatorlayout.widget.CoordinatorLayout>
</LinearLayout>