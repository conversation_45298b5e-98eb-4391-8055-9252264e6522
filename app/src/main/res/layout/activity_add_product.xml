<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/relativeView"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/light_white_"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="7dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/label_products"
                android:textColor="@color/black"
                android:textSize="20sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/id_sub_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="5dp"
                android:text="@string/label_add_new_product"
                android:textColor="@color/gray"
                android:textSize="14sp" />
        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="2dp"
            android:background="@color/blue_dark" />
    </LinearLayout>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp40">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="@dimen/dp10">

            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:divider="@android:color/transparent"
                android:dividerHeight="0.0px"
                android:elevation="8dp"
                android:visibility="visible"
                app:cardElevation="2dp"
                app:cardPreventCornerOverlap="false"
                app:cardUseCompatPadding="true">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:padding="@dimen/dp10">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:baselineAligned="false"
                        android:orientation="horizontal"
                        android:weightSum="8">

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="2">

                            <androidx.appcompat.widget.AppCompatEditText
                                android:id="@+id/product_name"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:drawableStart="@drawable/ic_new_product"
                                android:drawablePadding="@dimen/dp10"
                                android:gravity="start|center"
                                android:hint="@string/product_name"
                                android:inputType="textCapWords"
                                android:textColor="@color/black" />
                        </com.google.android.material.textfield.TextInputLayout>

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp10"
                            android:layout_weight="2">

                            <androidx.appcompat.widget.AppCompatEditText
                                android:id="@+id/product_sku"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:drawableStart="@drawable/ic_sku"
                                android:drawablePadding="@dimen/dp10"
                                android:gravity="start|center"
                                android:hint="@string/string_skuut"
                                android:inputType="textCapWords"
                                android:textColor="@color/black" />
                        </com.google.android.material.textfield.TextInputLayout>

                        <FrameLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp10"
                            android:layout_weight="2">

                            <androidx.appcompat.widget.AppCompatSpinner
                                android:id="@+id/spinnerBarcodeType"
                                android:layout_width="match_parent"
                                android:layout_height="@dimen/default_spinner_height"
                                android:layout_marginTop="5dp"
                                android:entries="@array/array_barcode_type"
                                android:textSize="12sp"
                                android:textStyle="normal" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/spinner_type_tint"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/dp_10"
                                android:background="@color/white"
                                android:paddingLeft="4dp"
                                android:paddingRight="4dp"
                                android:text="@string/string_baroce_type"
                                android:textSize="12sp"
                                android:visibility="visible" />
                        </FrameLayout>

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp10"
                            android:layout_weight="2"
                            android:background="@drawable/border_gray_spinner"

                            android:orientation="horizontal">

                            <FrameLayout
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="2">

                                <androidx.appcompat.widget.AppCompatSpinner
                                    android:id="@+id/spinner_unit"
                                    android:layout_width="match_parent"
                                    android:layout_height="@dimen/default_spinner_height"
                                    android:layout_marginTop="5dp"
                                    android:entries="@array/type_select_unite"
                                    android:textSize="12sp"
                                    style="@style/Widget.App.Spinner"
                                    android:textStyle="normal" />

                                <androidx.appcompat.widget.AppCompatTextView
                                    android:id="@+id/spinner_unit_tint"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginStart="@dimen/dp_10"
                                    android:background="@color/white"
                                    android:paddingLeft="4dp"
                                    android:paddingRight="4dp"
                                    android:text="@string/string_uniit"
                                    android:textSize="12sp"
                                    android:visibility="visible" />
                            </FrameLayout>
                            <ImageView
                                android:id="@+id/id_add_unit"
                                android:layout_width="@dimen/dp30"
                                android:layout_height="@dimen/dp30"
                                android:src="@drawable/ic_baseline_add_box_24"
                                android:layout_marginHorizontal="@dimen/dp3"
                                android:layout_gravity="center"/>
                        </LinearLayout>

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp20"
                        android:baselineAligned="false"
                        android:orientation="horizontal"
                        android:weightSum="8">

                        <FrameLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="2">

                            <androidx.appcompat.widget.AppCompatSpinner
                                android:id="@id/spinner_brand"
                                android:layout_width="match_parent"
                                android:layout_height="@dimen/default_spinner_height"
                                android:layout_marginTop="5dp"
                                android:entries="@array/type_product_marque"
                                android:textSize="12sp"
                                android:textStyle="normal" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/spinner_brand_tint"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/dp_10"
                                android:background="@color/white"
                                android:paddingLeft="4dp"
                                android:paddingRight="4dp"
                                android:text="@string/string_ubranndt"
                                android:textSize="12sp"
                                android:visibility="visible" />
                        </FrameLayout>

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp10"
                            android:layout_weight="2"
                            android:background="@drawable/border_gray_spinner"

                            android:orientation="horizontal">

                            <FrameLayout
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="2">

                                <androidx.appcompat.widget.AppCompatSpinner
                                    android:id="@+id/spinner_category"
                                    android:layout_width="match_parent"
                                    android:layout_height="@dimen/default_spinner_height"
                                    android:layout_marginTop="5dp"
                                    android:entries="@array/type_select_unite"
                                    android:textSize="12sp"
                                    style="@style/Widget.App.Spinner"
                                    android:textStyle="normal" />

                                <androidx.appcompat.widget.AppCompatTextView
                                    android:id="@+id/spinner_category_tint"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginStart="@dimen/dp_10"
                                    android:background="@color/white"
                                    android:paddingLeft="4dp"
                                    android:paddingRight="4dp"
                                    android:text="@string/string_pcategory_req"
                                    android:textSize="12sp"
                                    android:visibility="visible" />
                            </FrameLayout>
                            <ImageView
                                android:id="@+id/id_add_category"
                                android:layout_width="@dimen/dp30"
                                android:layout_height="@dimen/dp30"
                                android:src="@drawable/ic_baseline_add_box_24"
                                android:layout_marginHorizontal="@dimen/dp3"
                                android:layout_gravity="center"/>
                        </LinearLayout>

                        <FrameLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp10"
                            android:layout_weight="2">

                            <androidx.appcompat.widget.AppCompatSpinner
                                android:id="@+id/subCategorySpinner"
                                android:layout_width="match_parent"
                                android:layout_height="@dimen/default_spinner_height"
                                android:layout_marginTop="5dp"
                                android:entries="@array/array_sub_category"
                                android:textSize="12sp"
                                android:textStyle="normal" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/dp_10"
                                android:background="@color/white"
                                android:paddingLeft="4dp"
                                android:paddingRight="4dp"
                                android:text="@string/string_sub_pcategory"
                                android:textSize="12sp"
                                android:visibility="visible" />
                        </FrameLayout>

                        <FrameLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp10"
                            android:layout_weight="2">

                            <com.rising.high.tech.bigultimatenavdraw.util.MultiSelectSpinner
                                android:id="@+id/spinner_station"
                                android:layout_width="match_parent"
                                android:layout_height="@dimen/default_spinner_height"
                                android:layout_marginTop="5dp" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/spinner_station_tint"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/dp_10"
                                android:background="@color/white"
                                android:paddingLeft="4dp"
                                android:paddingRight="4dp"
                                android:text="@string/label_locationnn"
                                android:textSize="12sp"
                                android:visibility="visible" />
                        </FrameLayout>
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp20"
                        android:baselineAligned="false"
                        android:orientation="horizontal"
                        android:weightSum="8">

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_marginTop="5dp"
                            android:layout_weight="2"
                            android:background="@drawable/border_gray"
                            android:orientation="horizontal">

                            <androidx.appcompat.widget.AppCompatCheckBox
                                android:id="@+id/id_manage_stock"
                                android:layout_width="@dimen/dp30"
                                android:layout_height="@dimen/default_spinner_height"
                                android:checked="true" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/string_anage_stock"
                                android:textColor="@color/blue_txt"
                                android:textSize="14sp" />
                        </LinearLayout>


                        <com.google.android.material.textfield.TextInputLayout
                            android:id="@+id/manage_stock_container"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp10"
                            android:layout_weight="2">

                            <androidx.appcompat.widget.AppCompatEditText
                                android:id="@+id/edit_alert_quantity"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:gravity="start|center"
                                android:hint="@string/string_salert_quantity"
                                android:inputType="textCapWords"
                                android:textColor="@color/black" />
                        </com.google.android.material.textfield.TextInputLayout>

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp10"
                            android:layout_weight="2">

                            <androidx.appcompat.widget.AppCompatEditText
                                android:id="@+id/produxt_desc"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:gravity="start|center"
                                android:hint="@string/lbl_prod_desc"
                                android:inputType="textCapWords"
                                android:textColor="@color/black" />
                        </com.google.android.material.textfield.TextInputLayout>

                        <LinearLayout
                            android:layout_width="@dimen/dp0"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp10"
                            android:layout_weight="2"
                            android:orientation="horizontal">

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/id_image_add_attach"
                                android:layout_width="0dp"
                                android:layout_height="match_parent"
                                android:layout_marginTop="@dimen/dp5"
                                android:layout_weight="1.3"
                                android:background="@drawable/border_gray"
                                android:drawableEnd="@drawable/ic_baseline_add_24"
                                android:gravity="center"
                                android:text="@string/product_image"
                                android:textSize="16sp">

                            </androidx.appcompat.widget.AppCompatTextView>

                            <Gallery
                                android:id="@+id/gallery"
                                android:layout_width="0dp"
                                android:layout_height="60dp"
                                android:layout_marginStart="@dimen/dp10"
                                android:layout_marginTop="@dimen/dp5"
                                android:layout_weight="0.7"
                                android:gravity="start" />

                        </LinearLayout>

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:divider="@android:color/transparent"
                android:dividerHeight="0.0px"
                android:elevation="8dp"
                android:visibility="visible"
                app:cardElevation="2dp"
                app:cardPreventCornerOverlap="false"
                app:cardUseCompatPadding="true">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:padding="@dimen/dp10">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:baselineAligned="false"
                        android:orientation="horizontal"
                        android:weightSum="8">

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="2">

                            <androidx.appcompat.widget.AppCompatEditText
                                android:id="@+id/product_weight"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:drawableStart="@drawable/ic_weighing_scale"
                                android:drawablePadding="@dimen/dp10"
                                android:gravity="start|center"
                                android:hint="@string/lbl_prod_weghit"
                                android:inputType="textCapWords"
                                android:textColor="@color/black" />
                        </com.google.android.material.textfield.TextInputLayout>

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_marginStart="@dimen/dp10"
                            android:layout_weight="2"
                            android:background="@drawable/border_gray"
                            android:orientation="horizontal">

                            <androidx.appcompat.widget.AppCompatCheckBox
                                android:id="@+id/id_not_for_selling"
                                android:layout_width="@dimen/dp30"
                                android:layout_height="@dimen/default_spinner_height" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/string_not_selling"
                                android:textColor="@color/blue_txt"
                                android:textSize="14sp" />
                        </LinearLayout>

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:divider="@android:color/transparent"
                android:dividerHeight="0.0px"
                android:elevation="8dp"
                android:visibility="visible"
                app:cardElevation="2dp"
                app:cardPreventCornerOverlap="false"
                app:cardUseCompatPadding="true">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:padding="@dimen/dp10">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp20"
                        android:baselineAligned="false"
                        android:orientation="horizontal"
                        android:weightSum="8">

                        <FrameLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="2">

                            <androidx.appcompat.widget.AppCompatSpinner
                                android:id="@id/tax_rate"
                                android:layout_width="match_parent"
                                android:layout_height="@dimen/default_spinner_height"
                                android:layout_marginTop="5dp"
                                android:entries="@array/array_apply_tax"
                                android:textSize="12sp"
                                android:textStyle="normal" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/id_tax_rates_tint"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/dp_10"
                                android:background="@color/white"
                                android:paddingLeft="4dp"
                                android:paddingRight="4dp"
                                android:text="@string/lbl_app_tax"
                                android:textSize="12sp"
                                android:visibility="visible" />
                        </FrameLayout>

                        <FrameLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp10"
                            android:layout_weight="2">

                            <androidx.appcompat.widget.AppCompatSpinner
                                android:id="@+id/id_seling_tax_price_spinner"
                                android:layout_width="match_parent"
                                android:layout_height="@dimen/default_spinner_height"
                                android:layout_marginTop="5dp"
                                android:entries="@array/array_apply_tax_price"
                                android:textSize="12sp"
                                android:textStyle="normal" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/id_seling_tax_price_spinner_tint"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/dp_10"
                                android:background="@color/white"
                                android:paddingLeft="4dp"
                                android:paddingRight="4dp"
                                android:text="@string/lbl_seling_pric_tax"
                                android:textSize="12sp"
                                android:visibility="visible" />
                        </FrameLayout>

                        <FrameLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp10"
                            android:layout_weight="2">

                            <androidx.appcompat.widget.AppCompatSpinner
                                android:id="@+id/product_type_spin"
                                android:layout_width="match_parent"
                                android:layout_height="@dimen/default_spinner_height"
                                android:layout_marginTop="5dp"
                                android:entries="@array/type_product_type"
                                android:textSize="12sp"
                                android:textStyle="normal" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/product_type_spin_tint"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/dp_10"
                                android:background="@color/white"
                                android:paddingLeft="4dp"
                                android:paddingRight="4dp"
                                android:text="@string/label_product_type"
                                android:textSize="12sp"
                                android:visibility="visible" />
                        </FrameLayout>
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp20"
                        android:baselineAligned="false"
                        android:orientation="horizontal"
                        android:weightSum="10">

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="2">

                            <androidx.appcompat.widget.AppCompatEditText
                                android:id="@+id/id_exc_tax"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:gravity="start|center"
                                android:hint="@string/lbl_alert_exc_tax"
                                android:inputType="numberDecimal"
                                android:textColor="@color/black" />
                        </com.google.android.material.textfield.TextInputLayout>

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp10"
                            android:layout_weight="2">

                            <androidx.appcompat.widget.AppCompatEditText
                                android:id="@+id/id_inc_tax"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:gravity="start|center"
                                android:hint="@string/lbl_alert_inc_tax"
                                android:inputType="numberDecimal"
                                android:textColor="@color/black" />
                        </com.google.android.material.textfield.TextInputLayout>

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp10"
                            android:layout_weight="2">

                            <androidx.appcompat.widget.AppCompatEditText
                                android:id="@+id/id_margin"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:gravity="start|center"
                                android:hint="@string/label_product_margin"
                                android:inputType="textCapWords"
                                android:textColor="@color/black" />
                        </com.google.android.material.textfield.TextInputLayout>

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp10"
                            android:layout_weight="2">

                            <androidx.appcompat.widget.AppCompatEditText
                                android:id="@+id/id_default_sel_price_exc_tax"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:enabled="false"
                                android:gravity="start|center"
                                android:hint="@string/string_default_price_vente_exc"
                                android:inputType="numberDecimal"
                                android:textColor="@color/black" />
                        </com.google.android.material.textfield.TextInputLayout>

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp10"
                            android:layout_weight="2">

                            <androidx.appcompat.widget.AppCompatEditText
                                android:id="@+id/default_sell_inc_tax"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:enabled="false"
                                android:gravity="start|center"
                                android:hint="@string/string_default_price_vente_inc"
                                android:inputType="numberDecimal"
                                android:textColor="@color/black" />
                        </com.google.android.material.textfield.TextInputLayout>


                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/light_white_"
                android:gravity="center"
                android:orientation="horizontal"
                android:padding="@dimen/padding_top">

                <androidx.appcompat.widget.AppCompatButton
                    android:id="@+id/id_back"
                    android:layout_width="150dp"
                    android:layout_height="@dimen/dp50"
                    android:text="@string/label_close" />

                <androidx.appcompat.widget.AppCompatButton
                    android:id="@+id/add_btn"
                    android:layout_width="150dp"
                    android:layout_height="@dimen/dp50"
                    android:layout_marginStart="@dimen/dp20"
                    android:text="@string/label_add" />

            </LinearLayout>
        </LinearLayout>


    </ScrollView>

</RelativeLayout>