<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:padding="@dimen/activity_horizontal_margin">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="7dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/label_products"
                    android:textStyle="bold"
                    android:textColor="@color/black"
                    android:textSize="20sp" />

                <TextView
                    android:id="@+id/id_sub_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="5dp"
                    android:text="@string/label_add_new_product"
                    android:textColor="@color/gray"
                    android:textSize="14sp" />
            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="2dp"
                android:background="@color/blue_dark" />
        </LinearLayout>

        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/cell_shape_border"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:padding="@dimen/activity_vertical_margin">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginHorizontal="@dimen/activity_left_margin"
                            android:layout_weight="1"

                            android:orientation="vertical">

                            <TextView
                                android:id="@+id/company_name_hint"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/string_product_name"
                                android:textColor="@color/blue_txt" />

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:gravity="center"
                                android:orientation="horizontal"
                                android:paddingTop="@dimen/margin_vertical">

                                <ImageView
                                    android:layout_width="30px"
                                    android:layout_height="30px"
                                    android:src="@drawable/icons8_compost_heap" />

                                <EditText
                                    android:id="@+id/product_name"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:hint="@string/string_product_name" />
                            </LinearLayout>

                        </LinearLayout>


                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginHorizontal="@dimen/activity_left_margin"
                            android:layout_weight="1"

                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/string_skuut"
                                android:textColor="@color/blue_txt" />

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:gravity="center"
                                android:orientation="horizontal"
                                android:paddingTop="@dimen/margin_vertical">

                                <ImageView
                                    android:layout_width="30px"
                                    android:layout_height="30px"
                                    android:src="@drawable/icons8_compost_heap" />

                                <androidx.appcompat.widget.AppCompatEditText
                                    android:id="@+id/product_sku"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:hint="@string/string_skuut" />
                            </LinearLayout>

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginHorizontal="@dimen/activity_left_margin"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/string_baroce_type"
                                android:textColor="@color/blue_txt" />

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:gravity="center"
                                android:orientation="horizontal"
                                android:paddingTop="@dimen/margin_vertical">

                                <Spinner
                                    android:id="@+id/spinner_type"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    style="@style/my_spinner_style"

                                    android:entries="@array/array_barcode_type" />
                            </LinearLayout>

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginHorizontal="@dimen/activity_left_margin"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/string_uniit"
                                android:textColor="@color/blue_txt" />

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:gravity="center"
                                android:orientation="horizontal"
                                android:paddingTop="@dimen/margin_vertical">


                                <Spinner
                                    android:id="@+id/spinner_unit"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    style="@style/my_spinner_style"

                                    android:entries="@array/type_select_unite" />
                            </LinearLayout>

                        </LinearLayout>


                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginHorizontal="@dimen/activity_left_margin"
                            android:layout_weight="1"
                            android:orientation="vertical"
                            android:visibility="gone">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="SKU:"
                                android:textColor="@color/black" />

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:gravity="center"
                                android:orientation="horizontal"
                                android:paddingTop="@dimen/margin_vertical">

                                <ImageView
                                    android:layout_width="30px"
                                    android:layout_height="30px"
                                    android:src="@drawable/icons8_user" />

                                <EditText
                                    android:id="@+id/first_name_txt"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1" />
                            </LinearLayout>

                        </LinearLayout>


                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:padding="@dimen/activity_vertical_margin">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginHorizontal="@dimen/activity_left_margin"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/string_ubranndt"
                                android:textColor="@color/blue_txt" />

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:gravity="center"
                                android:orientation="horizontal"
                                android:paddingTop="@dimen/margin_vertical">


                                <Spinner
                                    android:id="@id/spinner_brand"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    style="@style/my_spinner_style"

                                    android:entries="@array/type_product_marque" />
                            </LinearLayout>

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginHorizontal="@dimen/activity_left_margin"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/string_pcategory_req"
                                android:textColor="@color/blue_txt" />

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:gravity="center"
                                android:orientation="horizontal"
                                android:paddingTop="@dimen/margin_vertical">

                                <Spinner
                                    android:id="@+id/spinner_category"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    style="@style/my_spinner_style"
                                    android:layout_weight="1" />
                            </LinearLayout>

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginHorizontal="@dimen/activity_left_margin"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/string_sub_pcategory"
                                android:textColor="@color/blue_txt" />

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:gravity="center"
                                android:orientation="horizontal"
                                android:paddingTop="@dimen/margin_vertical">


                                <Spinner
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    style="@style/my_spinner_style"

                                    android:entries="@array/array_sub_category" />
                            </LinearLayout>

                        </LinearLayout>

                        <LinearLayout
                            android:visibility="gone"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginHorizontal="@dimen/activity_left_margin"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/label_locationnn"
                                android:textColor="@color/blue_txt" />

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:gravity="center"
                                android:orientation="horizontal"
                                android:paddingTop="@dimen/margin_vertical">


                                <Spinner
                                    android:id="@+id/spinner_location"
                                    style="@style/my_spinner_style"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1" />

                            </LinearLayout>

                        </LinearLayout>
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginHorizontal="@dimen/activity_left_margin"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/label_locationnn"
                                android:textColor="@color/blue_txt" />

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:gravity="center"
                                android:orientation="horizontal"
                                android:paddingTop="@dimen/margin_vertical">

                                <com.rising.high.tech.bigultimatenavdraw.util.MultiSelectSpinner
                                    android:id="@+id/spinner_station"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1" />

                            </LinearLayout>

                        </LinearLayout>

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:padding="@dimen/activity_vertical_margin">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginHorizontal="@dimen/activity_left_margin"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:gravity="center"
                                android:orientation="horizontal"
                                android:paddingTop="@dimen/margin_vertical">

                                <CheckBox
                                    android:id="@+id/id_manage_stock"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:checked="true" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:text="@string/string_anage_stock"
                                    android:textColor="@color/blue_txt" />

                            </LinearLayout>

                            <LinearLayout
                                android:id="@+id/manage_stock_container"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:gravity="center"
                                android:orientation="horizontal"
                                android:paddingTop="@dimen/margin_vertical">

                                <ImageView
                                    android:layout_width="30px"
                                    android:layout_height="30px"
                                    android:src="@drawable/icons8_compost_heap" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:src="@drawable/icons8_compost_heap"
                                    android:text="@string/string_salert_quantity" />

                                <EditText
                                    android:id="@+id/edit_alert_quantity"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1" />
                            </LinearLayout>

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginHorizontal="@dimen/activity_left_margin"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/lbl_prod_desc"
                                android:textColor="@color/blue_txt" />

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:gravity="center"
                                android:orientation="horizontal"
                                android:paddingTop="@dimen/margin_vertical">

                                <ImageView
                                    android:layout_width="30px"
                                    android:layout_height="30px"
                                    android:src="@drawable/ic_baseline_edit_20" />

                                <EditText
                                    android:id="@+id/produxt_desc"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:hint="@string/lbl_prod_desc" />
                            </LinearLayout>

                        </LinearLayout>

                        <LinearLayout
                            android:id="@+id/id_single_layout"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:gravity="center"
                            android:orientation="vertical">


                            <LinearLayout
                                android:layout_width="fill_parent"
                                android:layout_height="wrap_content"
                                android:layout_below="@+id/v2"
                                android:layout_marginTop="@dimen/activity_vertical_margin"
                                android:gravity="center"
                                android:orientation="horizontal"
                                android:paddingHorizontal="16dp"
                                android:paddingVertical="16dp"
                                android:visibility="visible">

                                <TextView
                                    android:id="@+id/image_libelle_attache"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_below="@+id/discription_panne"
                                    android:text="@string/image_attache_libelle"
                                    android:textColor="@color/black" />

                                <LinearLayout
                                    android:layout_width="fill_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_below="@+id/v2"
                                    android:gravity="right"
                                    android:orientation="horizontal">

                                    <ImageButton
                                        android:id="@+id/id_image_add_attach"
                                        android:layout_width="30dp"
                                        android:layout_height="30dp"
                                        android:layout_gravity="right"
                                        android:layout_marginRight="0dp"
                                        android:background="@drawable/add"
                                        android:foregroundGravity="right" />
                                </LinearLayout>
                            </LinearLayout>

                            <LinearLayout
                                android:id="@+id/layoutGallery"
                                android:layout_width="fill_parent"
                                android:layout_height="wrap_content"
                                android:background="@drawable/rounded_edittext"
                                android:paddingHorizontal="10dp">

                                <Gallery
                                    android:id="@+id/gallery"
                                    android:layout_width="fill_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="center" />
                            </LinearLayout>


                        </LinearLayout>

                        <LinearLayout
                            android:visibility="invisible"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginHorizontal="@dimen/activity_left_margin"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/text_dic_amount"
                                android:textColor="@color/blue_txt" />

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:gravity="center"
                                android:orientation="horizontal"
                                android:paddingTop="@dimen/margin_vertical">

                                <ImageView
                                    android:layout_width="30px"
                                    android:layout_height="30px"
                                    android:layout_weight="1"
                                    android:src="@drawable/icons_contacts" />

                                <EditText
                                    android:id="@+id/discount_amnt"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:inputType="numberDecimal" />
                            </LinearLayout>

                        </LinearLayout>

                    </LinearLayout>


                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1px"
                    android:layout_marginHorizontal="50dp"
                    android:layout_marginVertical="@dimen/activity_vertical_margin"
                    android:background="@color/gray" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/cell_shape_border"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:padding="@dimen/activity_vertical_margin">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginHorizontal="@dimen/activity_left_margin"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/lbl_prod_weghit"
                                android:textColor="@color/blue_txt" />

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:gravity="center"
                                android:layout_weight="1"
                                android:orientation="horizontal"
                                android:paddingTop="@dimen/margin_vertical">

                                <ImageView
                                    android:layout_width="30px"
                                    android:layout_height="30px"
                                    android:src="@drawable/icons8_compost_heap" />

                                <EditText
                                    android:id="@+id/product_weight"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:hint="@string/lbl_prod_weghit" />
                            </LinearLayout>

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginHorizontal="@dimen/activity_left_margin"
                            android:layout_weight="1"

                            android:orientation="vertical">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:gravity="center"
                                android:orientation="horizontal"
                                android:paddingTop="@dimen/margin_vertical">

                                <CheckBox
                                    android:id="@+id/id_not_for_selling"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:text="@string/string_not_selling"
                                    android:textColor="@color/blue_txt" />

                            </LinearLayout>

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:gravity="center"
                                android:orientation="horizontal"
                                android:paddingTop="@dimen/margin_vertical"
                                android:layout_weight="1"
                                android:visibility="invisible">

                                <ImageView
                                    android:layout_width="30px"
                                    android:layout_height="30px"
                                    android:src="@drawable/icons8_compost_heap" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:src="@drawable/icons8_compost_heap"
                                    android:text="@string/string_salert_quantity" />

                                <EditText
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1" />
                            </LinearLayout>

                        </LinearLayout>


                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginHorizontal="@dimen/activity_left_margin"
                            android:layout_weight="1"
                            android:orientation="vertical"
                            android:visibility="invisible">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/label_product_type"
                                android:textColor="@color/black" />

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:gravity="center"
                                android:orientation="horizontal"
                                android:paddingTop="@dimen/margin_vertical">

                                <ImageView
                                    android:layout_width="30px"
                                    android:layout_height="30px"
                                    android:layout_weight="1"
                                    android:src="@drawable/icons_contacts" />

                                <Spinner
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:entries="@array/type_product_type" />
                            </LinearLayout>

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginHorizontal="@dimen/activity_left_margin"
                            android:layout_weight="1"
                            android:orientation="vertical"
                            android:visibility="invisible">


                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:gravity="center"
                                android:orientation="horizontal"
                                android:paddingTop="@dimen/margin_vertical">

                                <ImageView
                                    android:layout_width="30px"
                                    android:layout_height="30px"
                                    android:src="@drawable/icons8_location" />

                                <EditText
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:inputType="numberDecimal" />
                            </LinearLayout>

                        </LinearLayout>

                    </LinearLayout>


                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1px"
                    android:layout_marginHorizontal="50dp"
                    android:layout_marginVertical="@dimen/activity_vertical_margin"
                    android:background="@color/gray" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/cell_shape_border"

                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"

                        android:padding="@dimen/activity_vertical_margin">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginHorizontal="@dimen/activity_left_margin"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/lbl_app_tax"
                                android:textColor="@color/blue_txt" />

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:gravity="center"
                                android:orientation="horizontal"
                                android:paddingTop="@dimen/margin_vertical">

                                <Spinner
                                    android:id="@+id/id_tax_rates"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    style="@style/my_spinner_style"
                                    android:entries="@array/array_apply_tax" />

                            </LinearLayout>

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginHorizontal="@dimen/activity_left_margin"
                            android:layout_weight="1"
                            android:orientation="vertical"
                            android:visibility="visible">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/lbl_seling_pric_tax"
                                android:textColor="@color/blue_txt" />

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:gravity="center"
                                android:orientation="horizontal"
                                android:paddingTop="@dimen/margin_vertical">


                                <Spinner
                                    android:id="@+id/id_seling_tax_price_spinner"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    style="@style/my_spinner_style"

                                    android:entries="@array/array_apply_tax_price" />
                            </LinearLayout>

                        </LinearLayout>


                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginHorizontal="@dimen/activity_left_margin"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/label_product_type"
                                android:textColor="@color/blue_txt" />

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:gravity="center"
                                android:orientation="horizontal"
                                android:paddingTop="@dimen/margin_vertical">

                                <Spinner
                                    android:id="@+id/product_type_spin"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    style="@style/my_spinner_style"

                                    android:entries="@array/type_product_type" />
                            </LinearLayout>

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginHorizontal="@dimen/activity_left_margin"
                            android:layout_weight="1"
                            android:orientation="vertical"
                            android:visibility="invisible">


                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:gravity="center"
                                android:orientation="horizontal"
                                android:paddingTop="@dimen/margin_vertical">

                                <ImageView
                                    android:layout_width="30px"
                                    android:layout_height="30px"
                                    android:src="@drawable/icons8_location" />

                                <EditText
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:inputType="numberDecimal" />
                            </LinearLayout>

                        </LinearLayout>

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:padding="@dimen/activity_vertical_margin">


                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginHorizontal="@dimen/activity_left_margin"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/lbl_alert_exc_tax"
                                android:textColor="@color/blue_txt" />

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:gravity="center"
                                android:orientation="horizontal"
                                android:paddingTop="@dimen/margin_vertical">

                                <ImageView
                                    android:layout_width="30px"
                                    android:layout_height="30px"
                                    android:layout_weight="1"
                                    android:src="@drawable/icons_contacts" />

                                <EditText
                                    android:id="@+id/id_exc_tax"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:hint="@string/lbl_alert_exc_tax"
                                    android:inputType="numberDecimal" />

                            </LinearLayout>

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginHorizontal="@dimen/activity_left_margin"
                            android:layout_weight="1"
                            android:orientation="vertical"
                            android:visibility="visible">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/lbl_alert_inc_tax"
                                android:textColor="@color/blue_txt" />

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:gravity="center"
                                android:orientation="horizontal"
                                android:paddingTop="@dimen/margin_vertical">

                                <ImageView
                                    android:layout_width="30px"
                                    android:layout_height="30px"
                                    android:layout_weight="1"
                                    android:src="@drawable/icons_contacts" />

                                <EditText
                                    android:id="@+id/id_inc_tax"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:hint="@string/lbl_alert_inc_tax"
                                    android:inputType="numberDecimal" />
                            </LinearLayout>

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginHorizontal="@dimen/activity_left_margin"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/label_product_margin"
                                android:textColor="@color/blue_txt" />

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:gravity="center"
                                android:orientation="horizontal"
                                android:paddingTop="@dimen/margin_vertical">

                                <ImageView
                                    android:layout_width="30px"
                                    android:layout_height="30px"
                                    android:layout_weight="1"
                                    android:src="@drawable/icons_contacts" />

                                <EditText
                                    android:id="@+id/id_margin"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:inputType="numberDecimal"
                                    android:text="25.00" />
                            </LinearLayout>

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginHorizontal="@dimen/activity_left_margin"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/string_default_price_vente_exc"
                                android:textColor="@color/blue_txt" />

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:gravity="center"
                                android:orientation="horizontal"
                                android:paddingTop="@dimen/margin_vertical">

                                <ImageView
                                    android:layout_width="30px"
                                    android:layout_height="30px"
                                    android:src="@drawable/icons8_location" />

                                <EditText
                                    android:id="@+id/id_default_sel_price_exc_tax"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:hint="@string/string_default_price_vente_exc"
                                    android:inputType="numberDecimal" />
                            </LinearLayout>

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginHorizontal="@dimen/activity_left_margin"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/string_default_price_vente_inc"
                                android:textColor="@color/blue_txt" />

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:gravity="center"
                                android:orientation="horizontal"
                                android:paddingTop="@dimen/margin_vertical">

                                <ImageView
                                    android:layout_width="30px"
                                    android:layout_height="30px"
                                    android:src="@drawable/icons8_location" />

                                <EditText
                                    android:id="@+id/default_sell_inc_tax"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:hint="@string/string_default_price_vente_inc"
                                    android:inputType="numberDecimal" />
                            </LinearLayout>

                        </LinearLayout>


                    </LinearLayout>
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:orientation="horizontal"
                    android:padding="@dimen/padding_top">

                    <Button
                        android:id="@+id/add_btn"
                        style="@style/my_btn_style"
                        android:layout_width="wrap_content"
                        android:layout_height="70px"
                        android:text="@string/label_add" />

                    <Button
                        android:id="@+id/id_back"
                        style="@style/my_btn_style"
                        android:layout_width="wrap_content"
                        android:layout_height="70px"
                        android:background="@drawable/rounded_btn_blue"
                        android:text="@string/label_close" />

                </LinearLayout>

            </LinearLayout>
        </ScrollView>


    </LinearLayout>

</LinearLayout>