<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:padding="@dimen/activity_horizontal_margin">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="7dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"

                android:orientation="horizontal">

                <TextView
                    android:id="@+id/title_txt"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/label_add_new_purchase"
                    android:textColor="@color/black"
                    android:textSize="20dp" />

            </LinearLayout>


            <View
                android:layout_width="match_parent"
                android:layout_height="2dp"
                android:background="@color/blue_dark" />
        </LinearLayout>

        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/cell_shape_border"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:padding="@dimen/activity_vertical_margin">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginHorizontal="@dimen/activity_left_margin"
                            android:layout_weight="1"

                            android:orientation="vertical">

                            <TextView
                                android:id="@+id/textView2"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="@string/label_our_suppliers_required"
                                android:textColor="@color/blue_txt" />

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:gravity="center"
                                android:orientation="horizontal"
                                android:paddingTop="@dimen/margin_vertical">

                                <Spinner
                                    android:id="@+id/spinner_supplier"
                                    style="@style/my_spinner_style"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:entries="@array/array_barcode_type" />

                            </LinearLayout>

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginHorizontal="@dimen/activity_left_margin"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/label_purchase_Date"
                                android:textColor="@color/blue_txt" />

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:gravity="center"
                                android:orientation="horizontal"
                                android:paddingTop="@dimen/margin_vertical">

                                <ImageView
                                    android:layout_width="30px"
                                    android:layout_height="30px"
                                    android:layout_weight="1"
                                    android:src="@drawable/icons_contacts" />

                                <EditText
                                    android:id="@+id/purchase_date"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:focusableInTouchMode="false"
                                    android:inputType="numberDecimal" />
                            </LinearLayout>

                        </LinearLayout>


                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginHorizontal="@dimen/activity_left_margin"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/string_purchase_status"
                                android:textColor="@color/blue_txt" />

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:gravity="center"
                                android:orientation="horizontal"
                                android:paddingTop="@dimen/margin_vertical">


                                <Spinner
                                    android:id="@+id/spinner_purchase_status"
                                    style="@style/my_spinner_style"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:entries="@array/array_purchase_status" />

                            </LinearLayout>

                        </LinearLayout>


                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginHorizontal="@dimen/activity_left_margin"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/label_locationnn"
                                android:textColor="@color/blue_txt" />

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:gravity="center"
                                android:orientation="horizontal"
                                android:paddingTop="@dimen/margin_vertical">


                                <Spinner
                                    android:id="@+id/spinner_location"
                                    style="@style/my_spinner_style"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1" />

                            </LinearLayout>

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginHorizontal="@dimen/activity_left_margin"
                            android:layout_weight="1"
                            android:orientation="vertical"
                            android:visibility="gone">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="SKU:"
                                android:textColor="@color/black" />

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:gravity="center"
                                android:orientation="horizontal"
                                android:paddingTop="@dimen/margin_vertical">

                                <ImageView
                                    android:layout_width="30px"
                                    android:layout_height="30px"
                                    android:src="@drawable/icons8_user" />

                                <EditText
                                    android:id="@+id/first_name_txt"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1" />
                            </LinearLayout>

                        </LinearLayout>


                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:padding="@dimen/activity_vertical_margin">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginHorizontal="@dimen/activity_left_margin"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/text_pay_term"
                                android:textColor="@color/blue_txt" />

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:gravity="center"
                                android:orientation="horizontal"
                                android:paddingTop="@dimen/margin_vertical">

                                <androidx.appcompat.widget.AppCompatEditText
                                    android:id="@+id/pay_term_number"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:inputType="numberDecimal"
                                    android:layout_weight="1" />

                                <Spinner
                                    android:id="@+id/spinner_term_duration"
                                    style="@style/my_spinner_style"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"

                                    android:entries="@array/array_duration" />
                            </LinearLayout>

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginHorizontal="@dimen/activity_left_margin"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/string_attach_doc"
                                android:textColor="@color/blue_txt" />

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center"
                                android:layout_weight="1"
                                android:gravity="center"
                                android:orientation="horizontal"
                                android:paddingTop="@dimen/margin_vertical">

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:orientation="horizontal">

                                    <ImageView
                                        android:layout_width="30px"
                                        android:layout_height="30px"
                                        android:src="@drawable/icons8_compost_heap" />

                                    <EditText
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:layout_weight="1" />

                                    <ImageButton
                                        android:layout_width="20dp"
                                        android:layout_height="20dp"
                                        android:layout_gravity="right"
                                        android:layout_marginRight="0dp"
                                        android:background="@drawable/add"
                                        android:foregroundGravity="right" />
                                </LinearLayout>


                            </LinearLayout>

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginHorizontal="@dimen/activity_left_margin"
                            android:layout_weight="1"
                            android:orientation="vertical"
                            android:visibility="invisible">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/string_pcategory"
                                android:textColor="@color/blue_txt" />

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:gravity="center"
                                android:orientation="horizontal"
                                android:paddingTop="@dimen/margin_vertical">

                                <ImageView
                                    android:layout_width="30px"
                                    android:layout_height="30px"
                                    android:layout_weight="1"
                                    android:src="@drawable/icons_contacts" />

                                <Spinner
                                    android:id="@+id/spinner_category"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1" />
                            </LinearLayout>

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginHorizontal="@dimen/activity_left_margin"
                            android:layout_weight="1"
                            android:orientation="vertical"
                            android:visibility="invisible">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/string_sub_pcategory"
                                android:textColor="@color/blue_txt" />

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:gravity="center"
                                android:orientation="horizontal"
                                android:paddingTop="@dimen/margin_vertical">

                                <ImageView
                                    android:layout_width="30px"
                                    android:layout_height="30px"
                                    android:layout_weight="1"
                                    android:src="@drawable/icons_contacts" />

                                <Spinner
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:entries="@array/array_sub_category" />
                            </LinearLayout>

                        </LinearLayout>


                    </LinearLayout>

                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1px"
                    android:layout_marginHorizontal="50dp"
                    android:layout_marginVertical="@dimen/activity_vertical_margin"
                    android:background="@color/gray" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/cell_shape_border"
                    android:orientation="vertical">



                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:padding="@dimen/activity_vertical_margin">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_gravity="center"
                            android:layout_marginHorizontal="@dimen/padding_top"
                            android:background="@drawable/border_bg"
                            android:gravity="center"
                            android:orientation="vertical"
                            android:padding="@dimen/activity_left_margin">

                            <LinearLayout
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center"
                                android:gravity="center"
                                android:orientation="horizontal">

                                <TextView
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:text="@string/label_search"
                                    android:textSize="17dp" />

                                <ImageView
                                    android:id="@+id/id_more_product"
                                    android:layout_width="30dp"
                                    android:layout_height="30dp"
                                    android:layout_marginLeft="10dp"
                                    android:src="@drawable/more_ic" />
                            </LinearLayout>

                            <LinearLayout
                                android:id="@+id/container_product_search"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:orientation="vertical"
                                android:paddingTop="5dp"
                                android:visibility="visible">

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_gravity="center"
                                    android:gravity="center"
                                    android:orientation="horizontal">

                                    <ImageButton
                                        android:id="@+id/btn_scan"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_marginLeft="20dp"
                                        android:layout_marginRight="5dp"
                                        android:background="@drawable/scanner" />

                                    <AutoCompleteTextView
                                        android:id="@+id/search_edit"
                                        style="@style/EditTextStyle"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:layout_marginLeft="20dp"
                                        android:layout_marginRight="20dp"
                                        android:layout_weight="1"
                                        android:background="@color/white"
                                        android:hint="@string/label_search"
                                        android:lines="1"
                                        android:maxLines="1"
                                        android:textColorHint="@color/gray" />

                                </LinearLayout>

                                <LinearLayout
                                    android:id="@+id/container_sub_product"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_marginLeft="10dp"
                                    android:layout_marginTop="10dp"
                                    android:layout_marginRight="10dp"
                                    android:orientation="horizontal"
                                    android:visibility="visible">

                                    <TextView
                                        android:id="@+id/id_article_name"
                                        android:layout_width="0dp"
                                        android:layout_height="wrap_content"
                                        android:layout_weight="1"

                                        android:text="Product"
                                        android:textAppearance="@style/TextAppearance.AppCompat.Body2"
                                        android:textSize="12dp" />


                                    <TextView
                                        android:id="@+id/id_qt"
                                        android:layout_width="0dp"
                                        android:layout_height="wrap_content"
                                        android:layout_weight="1"
                                        android:text="Quantity"
                                        android:textAppearance="@style/TextAppearance.AppCompat.Body2"
                                        android:textSize="12dp" />


                                    <TextView
                                        android:id="@+id/id_unit_price"
                                        android:layout_width="0dp"
                                        android:layout_height="wrap_content"
                                        android:layout_weight="1"
                                        android:text="@string/lbl_unit_cost"
                                        android:textAppearance="@style/TextAppearance.AppCompat.Body2"
                                        android:textSize="12dp"
                                        android:textStyle="bold" />

                                    <TextView
                                        android:id="@+id/id_to"
                                        android:layout_width="0dp"
                                        android:layout_height="wrap_content"
                                        android:layout_weight="1"
                                        android:text="Subtotal"
                                        android:textAppearance="@style/TextAppearance.AppCompat.Body2"
                                        android:textSize="12dp"
                                        android:textStyle="bold" />


                                </LinearLayout>

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:orientation="vertical"
                                    android:padding="10dp"

                                    app:layout_constraintHeight_max="120px">

                                    <androidx.recyclerview.widget.RecyclerView
                                        android:id="@+id/recycle_product"
                                        android:layout_width="match_parent"
                                        android:layout_height="match_parent"

                                        app:layout_constrainedHeight="true" />


                                </LinearLayout>
                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:orientation="horizontal"
                                    android:paddingRight="5dp">

                                    <TextView
                                        android:layout_width="wrap_content"
                                        android:layout_height="match_parent"
                                        android:layout_weight="1"
                                        android:text="Total price"
                                        android:textAlignment="textEnd" />

                                    <TextView
                                        android:id="@+id/id_totoal_price"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:paddingLeft="5dp"
                                        android:text="00"
                                        android:textStyle="bold" />

                                </LinearLayout>
                            </LinearLayout>


                        </LinearLayout>


                    </LinearLayout>
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:padding="@dimen/activity_vertical_margin">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginHorizontal="@dimen/activity_left_margin"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <!--                            <TextView-->
                            <!--                                android:layout_width="wrap_content"-->
                            <!--                                android:layout_height="wrap_content"-->
                            <!--                                android:text="@string/label_products"-->
                            <!--                                android:textColor="@color/blue_txt" />-->

                            <!--                            <LinearLayout-->
                            <!--                                android:layout_width="match_parent"-->
                            <!--                                android:layout_height="wrap_content"-->
                            <!--                                android:gravity="center"-->
                            <!--                                android:orientation="horizontal"-->
                            <!--                                android:paddingTop="@dimen/margin_vertival">-->

                            <!--                                <ImageView-->
                            <!--                                    android:layout_width="wrap_content"-->
                            <!--                                    android:layout_height="wrap_content"-->
                            <!--                                    android:src="@drawable/ic_baseline_edit_20" />-->

                            <!--                                <LinearLayout-->
                            <!--                                    android:layout_width="match_parent"-->
                            <!--                                    android:layout_height="wrap_content"-->
                            <!--                                    android:layout_weight="1"-->
                            <!--                                    android:orientation="vertical">-->

                            <!--                                    <com.androidbuts.multispinnerfilter.MultiSpinnerSearch-->
                            <!--                                        android:id="@+id/multipleItemSelectionSpinner"-->
                            <!--                                        android:layout_width="match_parent"-->
                            <!--                                        android:layout_height="wrap_content"-->
                            <!--                                        android:layout_margin="10dp"-->
                            <!--                                        app:hintText="Enter Product name" />-->
                            <!--                                </LinearLayout>-->
                            <!--                            </LinearLayout>-->

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginHorizontal="@dimen/activity_left_margin"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:layout_marginHorizontal="@dimen/activity_left_margin"
                                android:layout_weight="1"
                                android:gravity="center"
                                android:orientation="horizontal">

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="@string/lbl_item_total"
                                    android:textColor="@color/blue_txt" />

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:gravity="center"
                                    android:orientation="horizontal"
                                    android:paddingTop="@dimen/margin_vertical">

                                    <TextView
                                        android:id="@+id/total_items"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_weight="1"
                                        android:hint="0"
                                        android:inputType="numberDecimal"
                                        android:paddingLeft="@dimen/activity_left_margin"
                                        android:textStyle="bold" />

                                </LinearLayout>

                            </LinearLayout>

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:layout_marginHorizontal="@dimen/activity_left_margin"
                                android:layout_weight="1"
                                android:gravity="center"
                                android:orientation="horizontal">

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="@string/lbl_net_total"
                                    android:textColor="@color/blue_txt" />

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:gravity="center"
                                    android:orientation="horizontal"
                                    android:paddingTop="@dimen/margin_vertical">

                                    <TextView
                                        android:id="@+id/net_total_amount"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_weight="1"
                                        android:inputType="numberDecimal"
                                        android:paddingLeft="@dimen/activity_left_margin"
                                        android:text="0.00"
                                        android:textStyle="bold" />

                                </LinearLayout>

                            </LinearLayout>
                        </LinearLayout>


                    </LinearLayout>

                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1px"
                    android:layout_marginHorizontal="50dp"
                    android:layout_marginVertical="@dimen/activity_vertical_margin"
                    android:background="@color/gray" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/cell_shape_border"

                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:padding="@dimen/activity_vertical_margin">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginHorizontal="@dimen/activity_left_margin"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/label_disc_type"
                                android:textColor="@color/blue_txt" />

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:gravity="center"
                                android:orientation="horizontal"
                                android:paddingTop="@dimen/margin_vertical">


                                <Spinner
                                    android:id="@+id/spinner_discount_type"
                                    style="@style/my_spinner_style"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:entries="@array/discount_type" />

                            </LinearLayout>

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginHorizontal="@dimen/activity_left_margin"
                            android:layout_weight="1"
                            android:orientation="vertical"
                            android:visibility="visible">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/text_label_amount"
                                android:textColor="@color/blue_txt" />

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:gravity="center"
                                android:orientation="horizontal"
                                android:paddingTop="@dimen/margin_vertical">

                                <ImageView
                                    android:layout_width="30px"
                                    android:layout_height="30px"
                                    android:layout_weight="1"
                                    android:src="@drawable/icons_contacts" />

                                <EditText
                                    android:id="@+id/discount_amnt_txt"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:inputType="numberDecimal"
                                    android:text="00.0" />

                            </LinearLayout>

                        </LinearLayout>


                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginHorizontal="@dimen/activity_left_margin"
                            android:layout_weight="1"
                            android:orientation="vertical"
                            android:visibility="invisible">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/label_product_type"
                                android:textColor="@color/blue_txt" />

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:gravity="center"
                                android:orientation="horizontal"
                                android:paddingTop="@dimen/margin_vertical">

                                <ImageView
                                    android:layout_width="30px"
                                    android:layout_height="30px"
                                    android:layout_weight="1"
                                    android:src="@drawable/icons_contacts" />

                                <Spinner
                                    android:id="@+id/product_type_spin"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:entries="@array/type_product_type" />
                            </LinearLayout>

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginHorizontal="@dimen/activity_left_margin"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/lbl_discount"
                                android:textColor="@color/blue_txt" />

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:gravity="center"
                                android:orientation="horizontal"
                                android:paddingTop="@dimen/margin_vertical">

                                <ImageView
                                    android:layout_width="30px"
                                    android:layout_height="30px"
                                    android:layout_weight="1"
                                    android:src="@drawable/icons_contacts" />

                                <TextView
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:hint="0.00"
                                    android:inputType="numberDecimal" />

                            </LinearLayout>

                        </LinearLayout>

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:padding="@dimen/activity_vertical_margin">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginHorizontal="@dimen/activity_left_margin"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/label_purchase_tax"
                                android:textColor="@color/blue_txt" />

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:gravity="center"
                                android:orientation="horizontal"
                                android:paddingTop="@dimen/margin_vertical">


                                <Spinner
                                    style="@style/my_spinner_style"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"

                                    android:entries="@array/array_action_tax" />

                            </LinearLayout>

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginHorizontal="@dimen/activity_left_margin"
                            android:layout_weight="1"
                            android:orientation="vertical"
                            android:visibility="invisible">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/text_label_amount"
                                android:textColor="@color/blue_txt" />

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:gravity="center"
                                android:orientation="horizontal"
                                android:paddingTop="@dimen/margin_vertical">

                                <ImageView
                                    android:layout_width="30px"
                                    android:layout_height="30px"
                                    android:layout_weight="1"
                                    android:src="@drawable/icons_contacts" />

                                <EditText
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:inputType="numberDecimal" />

                            </LinearLayout>

                        </LinearLayout>


                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginHorizontal="@dimen/activity_left_margin"
                            android:layout_weight="1"
                            android:orientation="vertical"
                            android:visibility="invisible">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/label_product_type"
                                android:textColor="@color/blue_txt" />

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:gravity="center"
                                android:orientation="horizontal"
                                android:paddingTop="@dimen/margin_vertical">

                                <ImageView
                                    android:layout_width="30px"
                                    android:layout_height="30px"
                                    android:layout_weight="1"
                                    android:src="@drawable/icons_contacts" />

                                <Spinner
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:entries="@array/type_product_type" />
                            </LinearLayout>

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginHorizontal="@dimen/activity_left_margin"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/lbl_discount"
                                android:textColor="@color/blue_txt" />

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:gravity="center"
                                android:orientation="horizontal"
                                android:paddingTop="@dimen/margin_vertical">

                                <ImageView
                                    android:layout_width="30px"
                                    android:layout_height="30px"
                                    android:src="@drawable/icons_contacts" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginLeft="5dp"
                                    android:inputType="numberDecimal"
                                    android:text="@string/lbl_discount" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:hint="0.00"
                                    android:inputType="numberDecimal" />

                            </LinearLayout>

                        </LinearLayout>

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:padding="@dimen/activity_vertical_margin">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginHorizontal="@dimen/activity_left_margin"
                            android:layout_weight="1"
                            android:orientation="vertical"
                            android:visibility="visible">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/lbl_shipping_detail"
                                android:textColor="@color/blue_txt" />

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:gravity="center"
                                android:orientation="horizontal"
                                android:paddingTop="@dimen/margin_vertical">

                                <ImageView
                                    android:layout_width="30px"
                                    android:layout_height="30px"
                                    android:layout_weight="1"
                                    android:src="@drawable/icons_contacts" />

                                <EditText
                                    android:id="@+id/shipping_detail_txt"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:inputType="numberDecimal" />

                            </LinearLayout>

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginHorizontal="@dimen/activity_left_margin"
                            android:layout_weight="1"
                            android:orientation="vertical"
                            android:visibility="invisible">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/lbl_shipping_detail"
                                android:textColor="@color/blue_txt" />

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:gravity="center"
                                android:orientation="horizontal"
                                android:paddingTop="@dimen/margin_vertical">

                                <ImageView
                                    android:layout_width="30px"
                                    android:layout_height="30px"
                                    android:layout_weight="1"
                                    android:src="@drawable/icons_contacts" />

                                <Spinner
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:entries="@array/array_action_tax" />

                            </LinearLayout>

                        </LinearLayout>


                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginHorizontal="@dimen/activity_left_margin"
                            android:layout_weight="1"
                            android:orientation="vertical"
                            android:visibility="invisible">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/label_product_type"
                                android:textColor="@color/blue_txt" />

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:gravity="center"
                                android:orientation="horizontal"
                                android:paddingTop="@dimen/margin_vertical">

                                <ImageView
                                    android:layout_width="30px"
                                    android:layout_height="30px"
                                    android:layout_weight="1"
                                    android:src="@drawable/icons_contacts" />

                                <Spinner
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:entries="@array/type_product_type" />
                            </LinearLayout>

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginHorizontal="@dimen/activity_left_margin"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/lbl_additional_shipping"
                                android:textColor="@color/blue_txt" />

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:gravity="center"
                                android:orientation="horizontal"
                                android:paddingTop="@dimen/margin_vertical">

                                <ImageView
                                    android:layout_width="30px"
                                    android:layout_height="30px"
                                    android:src="@drawable/icons_contacts" />


                                <EditText
                                    android:id="@+id/additional_shiping_ch"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:inputType="numberDecimal"
                                    android:text="0.00" />

                            </LinearLayout>

                        </LinearLayout>

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:padding="@dimen/activity_vertical_margin">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginHorizontal="@dimen/activity_left_margin"
                            android:layout_weight="1"
                            android:orientation="vertical"
                            android:visibility="visible">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/lbl_aaditonal_note"
                                android:textColor="@color/blue_txt" />

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:gravity="center"
                                android:orientation="horizontal"
                                android:paddingTop="@dimen/margin_vertical">

                                <ImageView
                                    android:layout_width="30px"
                                    android:layout_height="30px"
                                    android:layout_weight="1"
                                    android:src="@drawable/icons_contacts" />

                                <EditText
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:lines="2" />

                            </LinearLayout>

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginHorizontal="@dimen/activity_left_margin"
                            android:layout_weight="1"
                            android:orientation="vertical"
                            android:visibility="invisible">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/lbl_aaditonal_note"
                                android:textColor="@color/blue_txt" />

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:gravity="center"
                                android:orientation="horizontal"
                                android:paddingTop="@dimen/margin_vertical">

                                <ImageView
                                    android:layout_width="30px"
                                    android:layout_height="30px"
                                    android:layout_weight="1"
                                    android:src="@drawable/icons_contacts" />

                                <EditText
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:inputType="numberDecimal"
                                    android:lines="2" />

                            </LinearLayout>

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginHorizontal="@dimen/activity_left_margin"
                            android:layout_weight="1"
                            android:orientation="vertical"
                            android:visibility="invisible">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/lbl_shipping_detail"
                                android:textColor="@color/blue_txt" />

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:gravity="center"
                                android:orientation="horizontal"
                                android:paddingTop="@dimen/margin_vertical">

                                <ImageView
                                    android:layout_width="30px"
                                    android:layout_height="30px"
                                    android:layout_weight="1"
                                    android:src="@drawable/icons_contacts" />

                                <Spinner
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:entries="@array/array_action_tax" />

                            </LinearLayout>

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginHorizontal="@dimen/activity_left_margin"
                            android:layout_weight="1"
                            android:gravity="center"
                            android:orientation="horizontal">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/lbl_purchase_total"
                                android:textColor="@color/blue_txt" />

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:gravity="center"
                                android:orientation="horizontal"
                                android:paddingTop="@dimen/margin_vertical">


                                <TextView
                                    android:id="@+id/purchase_total"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:inputType="numberDecimal"
                                    android:paddingLeft="@dimen/activity_left_margin"
                                    android:text="0.00"
                                    android:textStyle="bold" />

                            </LinearLayout>

                        </LinearLayout>

                    </LinearLayout>

                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1px"
                    android:layout_marginHorizontal="50dp"
                    android:layout_marginVertical="@dimen/activity_vertical_margin"
                    android:background="@color/gray" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/cell_shape_border"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:padding="@dimen/activity_left_margin">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/label_ajout_contact" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:padding="@dimen/activity_vertical_margin">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginHorizontal="@dimen/activity_left_margin"
                            android:layout_weight="1"
                            android:orientation="vertical"
                            android:visibility="visible">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/label_montant_total_montant"
                                android:textColor="@color/blue_txt" />

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:gravity="center"
                                android:orientation="horizontal"
                                android:paddingTop="@dimen/margin_vertical">

                                <ImageView
                                    android:layout_width="30px"
                                    android:layout_height="30px"
                                    android:layout_weight="1"
                                    android:src="@drawable/icons_contacts" />

                                <EditText
                                    android:id="@+id/amount_txt"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:inputType="numberDecimal"
                                    android:text="00.0" />

                            </LinearLayout>

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginHorizontal="@dimen/activity_left_margin"
                            android:layout_weight="1"
                            android:orientation="vertical"
                            android:visibility="visible">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/image_ligne_paye"
                                android:textColor="@color/blue_txt" />

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:gravity="center"
                                android:orientation="horizontal"
                                android:paddingTop="@dimen/margin_vertical">

                                <ImageView
                                    android:layout_width="30px"
                                    android:layout_height="30px"
                                    android:layout_weight="1"
                                    android:src="@drawable/icons_contacts" />

                                <EditText
                                    android:id="@+id/paid_on_txt"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:focusableInTouchMode="false"

                                    android:inputType="numberDecimal" />
                            </LinearLayout>
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginHorizontal="@dimen/activity_left_margin"
                            android:layout_weight="1"
                            android:orientation="vertical"
                            android:visibility="visible">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/label_spaymenet_method"
                                android:textColor="@color/blue_txt" />

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:gravity="center"
                                android:orientation="horizontal"
                                android:paddingTop="@dimen/margin_vertical">


                                <Spinner
                                    android:id="@+id/spin_payment_method"
                                    style="@style/my_spinner_style"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"

                                    android:entries="@array/payment_method_array" />
                            </LinearLayout>

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginHorizontal="@dimen/activity_left_margin"
                            android:layout_weight="1"
                            android:orientation="vertical"
                            android:visibility="visible">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/label_note_paymnet"
                                android:textColor="@color/blue_txt" />

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:gravity="center"
                                android:orientation="horizontal"
                                android:paddingTop="@dimen/margin_vertical">

                                <ImageView
                                    android:layout_width="30px"
                                    android:layout_height="30px"
                                    android:layout_weight="1"
                                    android:src="@drawable/icons_contacts" />

                                <EditText
                                    android:id="@+id/payement_note"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:inputType="text" />
                            </LinearLayout>

                        </LinearLayout>

                    </LinearLayout>


                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:orientation="horizontal"
                        android:paddingVertical="@dimen/activity_left_margin">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_weight="1" />

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:layout_marginHorizontal="@dimen/activity_left_margin"
                            android:gravity="center"
                            android:orientation="horizontal">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/lbl_total_due"
                                android:textColor="@color/blue_txt" />

                            <TextView
                                android:id="@+id/total_due"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:inputType="numberDecimal"
                                android:paddingLeft="@dimen/activity_left_margin"
                                android:text="0.00"
                                android:textStyle="bold" />
                        </LinearLayout>
                    </LinearLayout>
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:orientation="horizontal"
                    android:padding="@dimen/padding_top">

                    <Button
                        android:id="@+id/add_btn"
                        style="@style/my_btn_style"
                        android:layout_width="wrap_content"
                        android:layout_height="70px"
                        android:text="@string/label_add" />

                    <Button
                        android:id="@+id/id_back"
                        style="@style/my_btn_style"
                        android:layout_width="wrap_content"
                        android:layout_height="70px"
                        android:background="@drawable/rounded_btn_blue"
                        android:text="@string/label_close" />

                </LinearLayout>

            </LinearLayout>
        </ScrollView>


    </LinearLayout>

</androidx.core.widget.NestedScrollView>