<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:padding="@dimen/dp10"
    android:layout_height="match_parent">

    <TextView
        android:id="@+id/title_txt"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/label_add_new_purchase_return"
        android:textColor="@color/black"
        android:textSize="20dp" />

    <View
        android:layout_width="match_parent"
        android:layout_height="2dp"
        android:layout_marginBottom="@dimen/dp20"
        android:background="@color/blue_dark" />


    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <LinearLayout
            android:layout_width="match_parent"
            android:orientation="vertical"
            android:padding="@dimen/dp10"
            android:layout_height="wrap_content">
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:elevation="8dp"
                android:visibility="visible"
                android:divider="@android:color/transparent"
                android:dividerHeight="0.0px"
                android:clipToPadding="false"
                android:clipChildren="false"
                app:cardUseCompatPadding="true"
                app:cardElevation="2dp"
                app:cardPreventCornerOverlap="false">
                <LinearLayout
                    android:layout_width="match_parent"
                    android:orientation="vertical"
                    android:paddingHorizontal="@dimen/dp10"
                    android:paddingVertical="@dimen/dp20"
                    android:layout_height="match_parent">
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:orientation="horizontal"
                        android:weightSum="10"
                        android:layout_height="wrap_content"
                        android:baselineAligned="false">
                        <FrameLayout
                            android:layout_width="0dp"
                            android:layout_weight="2"
                            android:layout_marginStart="@dimen/dp10"
                            android:layout_height="wrap_content">

                            <androidx.appcompat.widget.AppCompatSpinner
                                android:id="@+id/spinner_supplier"
                                android:layout_width="match_parent"
                                android:layout_height="@dimen/default_spinner_height"
                                android:layout_marginTop="5dp"
                                android:textSize="12sp"
                                android:textStyle="normal" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/spinner_type_tint"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/dp_10"
                                android:background="@color/white"
                                android:paddingLeft="4dp"
                                android:visibility="visible"
                                android:text="@string/label_our_suppliers_required"
                                android:paddingRight="4dp"
                                android:textSize="12sp" />
                        </FrameLayout>
                        <FrameLayout
                            android:layout_width="0dp"
                            android:layout_weight="2"
                            android:layout_marginStart="@dimen/dp10"
                            android:layout_height="wrap_content">

                            <androidx.appcompat.widget.AppCompatSpinner
                                android:id="@+id/spinner_location"
                                android:layout_width="match_parent"
                                android:layout_height="@dimen/default_spinner_height"
                                android:layout_marginTop="5dp"
                                />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/dp_10"
                                android:background="@color/white"
                                android:paddingLeft="4dp"
                                android:visibility="visible"
                                android:text="@string/label_locationnn"
                                android:paddingRight="4dp"
                                android:textSize="12sp" />
                        </FrameLayout>
                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="0dp"
                            android:layout_weight="2"
                            android:layout_marginStart="@dimen/dp10"
                            android:layout_height="wrap_content">

                            <androidx.appcompat.widget.AppCompatEditText
                                android:id="@+id/reference_no"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:gravity="start|center"
                                android:hint="@string/label_reference_num"
                                android:inputType="textCapWords"
                                android:textColor="@color/black" />
                        </com.google.android.material.textfield.TextInputLayout>
                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="0dp"
                            android:layout_weight="2"
                            android:layout_marginStart="@dimen/dp10"
                            android:layout_height="wrap_content">

                            <androidx.appcompat.widget.AppCompatEditText
                                android:id="@+id/date_txt"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:gravity="start|center"
                                android:hint="@string/label_date_req"
                                android:inputType="textCapWords"
                                android:textColor="@color/black" />
                        </com.google.android.material.textfield.TextInputLayout>

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/id_image_add_attach"
                            android:layout_width="0dp"
                            android:layout_weight="2"
                            android:text="@string/string_attach_doc"
                            android:layout_marginStart="@dimen/dp10"
                            android:textSize="16sp"
                            android:drawableEnd="@drawable/ic_baseline_add_24"
                            android:background="@drawable/border_gray"
                            android:gravity="center"
                            android:layout_height="match_parent">

                        </androidx.appcompat.widget.AppCompatTextView>

                    </LinearLayout>


                </LinearLayout>

            </androidx.cardview.widget.CardView>
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:elevation="8dp"
                android:layout_marginTop="@dimen/dp10"
                android:visibility="visible"
                android:divider="@android:color/transparent"
                android:dividerHeight="0.0px"
                android:clipToPadding="false"
                android:clipChildren="false"
                app:cardUseCompatPadding="true"
                app:cardElevation="2dp"
                app:cardPreventCornerOverlap="false">


                <LinearLayout
                    android:id="@+id/container_product_search"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:layout_margin="@dimen/dp10"
                    android:visibility="visible">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:gravity="center"
                        android:orientation="horizontal">

                        <ImageButton
                            android:id="@+id/btn_scan"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="20dp"
                            android:layout_marginRight="5dp"
                            android:background="@drawable/scanner" />

                        <AutoCompleteTextView
                            android:id="@+id/search_edit"
                            style="@style/EditTextStyle"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="20dp"
                            android:layout_weight="1"
                            android:hint="@string/label_enter_product_name"
                            android:lines="1"
                            android:maxLines="1"
                            android:textColorHint="@color/gray" />

                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/container_sub_product"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:padding="@dimen/dp10"
                        android:weightSum="4"
                        android:layout_marginTop="20dp"
                        android:background="@drawable/bg_gradient"
                        android:orientation="horizontal"
                        android:visibility="visible">

                        <TextView
                            android:id="@+id/id_article_name"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Product"
                            android:textColor="@color/white"
                            android:textAppearance="@style/TextAppearance.AppCompat.Body2"
                            android:textSize="12sp" />


                        <TextView
                            android:id="@+id/id_qt"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Quantity"
                            android:textColor="@color/white"
                            android:textAppearance="@style/TextAppearance.AppCompat.Body2"
                            android:textSize="12dp" />


                        <TextView
                            android:id="@+id/id_unit_price"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/lbl_unit_cost"
                            android:textColor="@color/white"
                            android:textAppearance="@style/TextAppearance.AppCompat.Body2"
                            android:textSize="12dp"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/id_to"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Subtotal"
                            android:textColor="@color/white"
                            android:textAppearance="@style/TextAppearance.AppCompat.Body2"
                            android:textSize="12dp"
                            android:textStyle="bold" />


                    </LinearLayout>
                    <androidx.cardview.widget.CardView
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:elevation="8dp"
                        android:visibility="visible"
                        android:divider="@android:color/transparent"
                        android:dividerHeight="0.0px"
                        android:clipToPadding="false"
                        android:clipChildren="false"
                        app:cardUseCompatPadding="true"
                        app:cardElevation="2dp"
                        app:cardPreventCornerOverlap="false">
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:orientation="vertical"
                            app:layout_constraintHeight_max="120dp">

                            <androidx.recyclerview.widget.RecyclerView
                                android:id="@+id/recycle_product"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:visibility="visible"
                                app:layout_constrainedHeight="true"
                                tools:listitem="@layout/d_short_product_item" />

                            <TextView
                                android:id="@+id/noItemFound"
                                android:layout_width="match_parent"
                                android:gravity="center"
                                android:textSize="16sp"
                                android:visibility="gone"
                                android:padding="@dimen/dp20"
                                android:layout_gravity="center"
                                android:textColor="@color/black"
                                android:text="Add Purchased product"
                                android:layout_height="match_parent">

                            </TextView>
                        </LinearLayout>
                    </androidx.cardview.widget.CardView>


                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginTop="@dimen/dp10"
                        android:layout_marginHorizontal="@dimen/activity_left_margin"
                        android:gravity="center"
                        android:orientation="horizontal">

                        <androidx.appcompat.widget.AppCompatTextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:gravity="center"
                            android:text="@string/lbl_item_total"
                            android:textColor="@color/black" />

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/total_items"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:hint="0"
                            android:textColor="@color/black"
                            android:gravity="center"
                            android:textStyle="bold" />

                    </LinearLayout>
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginTop="@dimen/dp5"
                        android:layout_marginHorizontal="@dimen/activity_left_margin"
                        android:gravity="center"
                        android:orientation="horizontal">

                        <androidx.appcompat.widget.AppCompatTextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:gravity="center|right"
                            android:text="@string/lbl_net_total"
                            android:textColor="@color/black" />

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/total_amount"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:hint="0"
                            android:textColor="@color/black"
                            android:gravity="center"
                            android:text="0.00"
                            android:textStyle="bold" />

                    </LinearLayout>

                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:background="@color/light_white_"
                android:orientation="horizontal"
                android:padding="@dimen/padding_top">


                <androidx.appcompat.widget.AppCompatButton
                    android:id="@+id/id_back"
                    android:layout_width="150dp"
                    android:layout_height="@dimen/dp50"
                    android:text="@string/label_close" />
                <androidx.appcompat.widget.AppCompatButton
                    android:id="@+id/add_btn"
                    android:layout_width="150dp"
                    android:layout_marginStart="@dimen/dp20"
                    android:layout_height="@dimen/dp50"
                    android:text="@string/label_add" />

            </LinearLayout>
        </LinearLayout>
    </ScrollView>




</LinearLayout>