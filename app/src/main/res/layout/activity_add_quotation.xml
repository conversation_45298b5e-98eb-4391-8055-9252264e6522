<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="@dimen/dp10">

    <TextView
        android:id="@+id/title_txt"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/label_add_new_quotation"
        android:textColor="@color/black"
        android:textSize="20dp" />

    <View
        android:layout_width="match_parent"
        android:layout_height="2dp"
        android:layout_marginBottom="@dimen/dp20"
        android:background="@color/blue_dark" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="@dimen/dp10">

            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:divider="@android:color/transparent"
                android:dividerHeight="0.0px"
                android:elevation="8dp"
                android:visibility="visible"
                app:cardElevation="2dp"
                app:cardPreventCornerOverlap="false"
                app:cardUseCompatPadding="true">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:padding="@dimen/dp10">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:baselineAligned="false"
                        android:orientation="horizontal"
                        android:weightSum="10">

                        <FrameLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp10"
                            android:layout_weight="2">

                            <androidx.appcompat.widget.AppCompatSpinner
                                android:id="@+id/spinner_station"
                                android:layout_width="match_parent"
                                android:layout_height="@dimen/default_spinner_height"
                                android:layout_marginTop="5dp"
                                android:textSize="12sp"
                                android:textStyle="normal" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/dp_10"
                                android:background="@color/white"
                                android:paddingLeft="4dp"
                                android:paddingRight="4dp"
                                android:text="@string/label_station_required"
                                android:textSize="12sp"
                                android:visibility="visible" />
                        </FrameLayout>

                        <FrameLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp10"
                            android:layout_weight="2">

                            <androidx.appcompat.widget.AppCompatSpinner
                                android:id="@+id/spinner_customer"
                                android:layout_width="match_parent"
                                android:layout_height="@dimen/default_spinner_height"
                                android:layout_marginTop="5dp"
                                android:textSize="12sp"
                                android:textStyle="normal" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/spinner_type_tint"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/dp_10"
                                android:background="@color/white"
                                android:paddingLeft="4dp"
                                android:paddingRight="4dp"
                                android:text="@string/label_cutomer_req"
                                android:textSize="12sp"
                                android:visibility="visible" />
                        </FrameLayout>

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp10"

                            android:layout_weight="1">

                            <androidx.appcompat.widget.AppCompatEditText
                                android:id="@+id/pay_term_number"
                                android:layout_width="match_parent"
                                android:layout_height="@dimen/default_spinner_height"
                                android:gravity="start|center"
                                android:hint="@string/text_pay_term"
                                android:inputType="textCapWords"
                                android:textColor="@color/black" />
                        </com.google.android.material.textfield.TextInputLayout>

                        <FrameLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp10"
                            android:layout_weight="1"
                            android:visibility="visible">

                            <androidx.appcompat.widget.AppCompatSpinner
                                android:id="@+id/spinner_term_duration"
                                android:layout_width="match_parent"
                                android:layout_height="@dimen/default_spinner_height"
                                android:layout_marginTop="5dp"
                                android:entries="@array/array_duration" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/pay_term_tint"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/dp_10"
                                android:background="@color/white"
                                android:paddingLeft="4dp"
                                android:paddingRight="4dp"
                                android:text="Pay Term In"
                                android:textSize="12sp"
                                android:visibility="visible" />
                        </FrameLayout>

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp10"
                            android:layout_weight="2">

                            <androidx.appcompat.widget.AppCompatEditText
                                android:id="@+id/quotation_date"
                                android:layout_width="match_parent"
                                android:layout_height="@dimen/default_spinner_height"
                                android:gravity="start|center"
                                android:hint="@string/label_sale_date"
                                android:inputType="textCapWords"
                                android:focusable="false"
                                android:textColor="@color/black" />
                        </com.google.android.material.textfield.TextInputLayout>

                        <FrameLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp10"
                            android:layout_weight="2">

                            <androidx.appcompat.widget.AppCompatSpinner
                                android:id="@+id/spinner_invoice_scheme"
                                android:layout_width="match_parent"
                                android:layout_height="@dimen/default_spinner_height"
                                android:layout_marginTop="5dp"
                                android:entries="@array/array_invoice_scheme" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/dp_10"
                                android:background="@color/white"
                                android:paddingLeft="4dp"
                                android:paddingRight="4dp"
                                android:text="@string/label_invoice_scheme"
                                android:textSize="12sp"
                                android:visibility="visible" />
                        </FrameLayout>
                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="@dimen/dp10"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:divider="@android:color/transparent"
                android:dividerHeight="0.0px"
                android:elevation="8dp"
                android:visibility="visible"
                app:cardElevation="2dp"
                app:cardPreventCornerOverlap="false"
                app:cardUseCompatPadding="true">


                <LinearLayout
                    android:id="@+id/container_product_search"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_margin="@dimen/dp10"
                    android:orientation="vertical"
                    android:visibility="visible">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:gravity="center"
                        android:orientation="horizontal">

                        <ImageButton
                            android:id="@+id/btn_scan"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="20dp"
                            android:layout_marginRight="5dp"
                            android:background="@drawable/scanner" />

                        <AutoCompleteTextView
                            android:id="@+id/search_edit"
                            style="@style/EditTextStyle"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginLeft="20dp"
                            android:layout_weight="1"
                            android:hint="@string/label_search"
                            android:lines="1"
                            android:maxLines="1"
                            android:textColorHint="@color/gray" />

                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/container_sub_product"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="20dp"
                        android:background="@drawable/bg_gradient"
                        android:orientation="horizontal"
                        android:padding="@dimen/dp10"
                        android:visibility="visible"
                        android:weightSum="4">

                        <TextView
                            android:id="@+id/id_article_name"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Product"
                            android:textAppearance="@style/TextAppearance.AppCompat.Body2"
                            android:textColor="@color/white"
                            android:textSize="12sp" />


                        <TextView
                            android:id="@+id/id_qt"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Quantity"
                            android:textAppearance="@style/TextAppearance.AppCompat.Body2"
                            android:textColor="@color/white"
                            android:textSize="12dp" />


                        <TextView
                            android:id="@+id/id_unit_price"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/lbl_unit_cost"
                            android:textAppearance="@style/TextAppearance.AppCompat.Body2"
                            android:textColor="@color/white"
                            android:textSize="12dp"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/id_to"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Subtotal"
                            android:textAppearance="@style/TextAppearance.AppCompat.Body2"
                            android:textColor="@color/white"
                            android:textSize="12dp"
                            android:textStyle="bold" />


                    </LinearLayout>

                    <androidx.cardview.widget.CardView
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:clipChildren="false"
                        android:clipToPadding="false"
                        android:divider="@android:color/transparent"
                        android:dividerHeight="0.0px"
                        android:elevation="8dp"
                        android:visibility="visible"
                        app:cardElevation="2dp"
                        app:cardPreventCornerOverlap="false"
                        app:cardUseCompatPadding="true">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:orientation="vertical"
                            app:layout_constraintHeight_max="120dp">

                            <androidx.recyclerview.widget.RecyclerView
                                android:id="@+id/recycle_product"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:visibility="visible"
                                app:layout_constrainedHeight="true"
                                tools:listitem="@layout/d_short_product_item" />

                            <TextView
                                android:id="@+id/noItemFound"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:layout_gravity="center"
                                android:gravity="center"
                                android:padding="@dimen/dp20"
                                android:text="Add Purchased product"
                                android:textColor="@color/black"
                                android:textSize="16sp"
                                android:visibility="gone">

                            </TextView>
                        </LinearLayout>
                    </androidx.cardview.widget.CardView>


                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginHorizontal="@dimen/activity_left_margin"
                        android:layout_marginTop="@dimen/dp10"
                        android:gravity="center"
                        android:orientation="horizontal">

                        <androidx.appcompat.widget.AppCompatTextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:gravity="center"
                            android:text="@string/lbl_item_total"
                            android:textColor="@color/black" />

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/total_items"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:hint="0"
                            android:textColor="@color/black"
                            android:textStyle="bold" />

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginHorizontal="@dimen/activity_left_margin"
                        android:layout_marginTop="@dimen/dp5"
                        android:gravity="center"
                        android:orientation="horizontal">

                        <androidx.appcompat.widget.AppCompatTextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:gravity="center|right"
                            android:text="@string/lbl_net_total"
                            android:textColor="@color/black" />

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/net_total_amount"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:hint="0"
                            android:text="0.00"
                            android:textColor="@color/black"
                            android:textStyle="bold" />
                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/net_total_amount_curr"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:gravity="center"

                            android:textColor="@color/black"
                            android:textStyle="bold" />

                    </LinearLayout>

                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="@dimen/dp10"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:divider="@android:color/transparent"
                android:dividerHeight="0.0px"
                android:elevation="8dp"
                android:visibility="visible"
                app:cardElevation="2dp"
                app:cardPreventCornerOverlap="false"
                app:cardUseCompatPadding="true">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="@dimen/dp10">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:weightSum="8">

                        <FrameLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp10"
                            android:layout_weight="2">

                            <androidx.appcompat.widget.AppCompatSpinner
                                android:id="@+id/spinner_discount_type"
                                android:layout_width="match_parent"
                                android:layout_height="@dimen/default_spinner_height"
                                android:layout_marginTop="5dp"
                                android:entries="@array/discount_type"
                                android:textSize="12sp"
                                android:textStyle="normal" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/dp_10"
                                android:background="@color/white"
                                android:paddingLeft="4dp"
                                android:paddingRight="4dp"
                                android:text="@string/label_disc_type"
                                android:textSize="12sp"
                                android:visibility="visible" />
                        </FrameLayout>

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp10"
                            android:layout_weight="2">

                            <androidx.appcompat.widget.AppCompatEditText
                                android:id="@+id/discount_amnt_txt"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:gravity="start|center"
                                android:hint="@string/text_label_amount"
                                android:inputType="numberDecimal"
                                android:text="00.0"
                                android:textColor="@color/black" />
                        </com.google.android.material.textfield.TextInputLayout>

                        <View
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="2"></View>

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp10"
                            android:layout_weight="2">

                            <androidx.appcompat.widget.AppCompatEditText
                                android:id="@+id/id_discount_amount"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:enabled="false"
                                android:focusable="false"
                                android:gravity="start|center"
                                android:hint="@string/lbl_discount"
                                android:inputType="textCapWords"
                                android:text="0.00"
                                android:textColor="@color/black" />
                        </com.google.android.material.textfield.TextInputLayout>
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp10"
                        android:weightSum="4">

                        <FrameLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp10"
                            android:layout_weight="1">

                            <androidx.appcompat.widget.AppCompatSpinner
                                android:id="@+id/spinner_tax"
                                android:layout_width="match_parent"
                                android:layout_height="@dimen/default_spinner_height"
                                android:layout_marginTop="5dp"
                                android:entries="@array/array_action_tax"
                                android:textSize="12sp"
                                android:textStyle="normal" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/dp_10"
                                android:background="@color/white"
                                android:paddingLeft="4dp"
                                android:paddingRight="4dp"
                                android:text="@string/label_order_tax"
                                android:textSize="12sp"
                                android:visibility="visible" />
                        </FrameLayout>

                        <View
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="2"></View>

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp10"
                            android:layout_weight="1">

                            <androidx.appcompat.widget.AppCompatEditText
                                android:id="@+id/id_tax_amount"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:enabled="false"
                                android:focusable="false"
                                android:gravity="start|center"
                                android:hint="@string/label_order_tax"
                                android:inputType="textCapWords"
                                android:text="0.00"
                                android:textColor="@color/black" />
                        </com.google.android.material.textfield.TextInputLayout>
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp10"
                        android:orientation="horizontal"
                        android:weightSum="4">

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp10"
                            android:layout_weight="1">

                            <androidx.appcompat.widget.AppCompatEditText
                                android:id="@+id/shipping_detail_txt"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:gravity="start|center"
                                android:hint="@string/lbl_shipping_detail"
                                android:inputType="textCapWords"
                                android:textColor="@color/black" />
                        </com.google.android.material.textfield.TextInputLayout>

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp10"
                            android:layout_weight="1">

                            <androidx.appcompat.widget.AppCompatEditText
                                android:id="@+id/shipping_adress_txt"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:gravity="start|center"
                                android:hint="@string/image_shipping_adress"
                                android:inputType="textCapWords"
                                android:textColor="@color/black" />
                        </com.google.android.material.textfield.TextInputLayout>


                        <View
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"></View>

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp10"
                            android:layout_weight="1">

                            <androidx.appcompat.widget.AppCompatEditText
                                android:id="@+id/shipping_charge"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:gravity="start|center"
                                android:hint="@string/lbl_shipping_charges"
                                android:inputType="textCapWords"
                                android:text="0.00"
                                android:textColor="@color/black" />
                        </com.google.android.material.textfield.TextInputLayout>
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp10"
                        android:orientation="horizontal"
                        android:weightSum="4">

                        <FrameLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp10"
                            android:layout_weight="1">

                            <androidx.appcompat.widget.AppCompatSpinner
                                android:id="@+id/spinner_purchase_status"
                                android:layout_width="match_parent"
                                android:layout_height="@dimen/default_spinner_height"
                                android:layout_marginTop="5dp"
                                android:entries="@array/array_quoation_status"
                                android:textSize="12sp"
                                android:textStyle="normal" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/dp_10"
                                android:background="@color/white"
                                android:paddingLeft="4dp"
                                android:paddingRight="4dp"
                                android:text="@string/string_purchase_status"
                                android:textSize="12sp"
                                android:visibility="visible" />
                        </FrameLayout>

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp10"
                            android:layout_weight="1">

                            <androidx.appcompat.widget.AppCompatEditText
                                android:id="@+id/id_delivered_to"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:gravity="start|center"
                                android:hint="@string/lbl_dilevered_to"
                                android:inputType="textCapWords"
                                android:textColor="@color/black" />
                        </com.google.android.material.textfield.TextInputLayout>

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp10"
                            android:layout_weight="1">

                            <androidx.appcompat.widget.AppCompatEditText
                                android:id="@+id/id_sell_note"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:gravity="start|center"
                                android:hint="@string/label_sell_note"
                                android:inputType="textCapWords"
                                android:textColor="@color/black" />
                        </com.google.android.material.textfield.TextInputLayout>

                        <com.google.android.material.textfield.TextInputLayout
                            android:visibility="invisible"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp10"
                            android:layout_weight="1">

                            <androidx.appcompat.widget.AppCompatEditText
                                android:id="@+id/purchase_total"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:gravity="start|center"
                                android:hint="@string/lbl_purchase_total"
                                android:inputType="textCapWords"
                                android:text="0.00"
                                android:textColor="@color/black" />
                        </com.google.android.material.textfield.TextInputLayout>
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_gravity="end"
                        android:layout_marginHorizontal="@dimen/activity_left_margin"
                        android:layout_marginVertical="@dimen/dp10"
                        android:gravity="end"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/label_total_payable"
                            android:textColor="@color/black"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/label_total_payable"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:inputType="numberDecimal"
                            android:paddingLeft="@dimen/activity_left_margin"
                            android:text="0.00"
                            android:textColor="@color/black"
                            android:textStyle="bold" />
                        <TextView
                            android:id="@+id/label_total_payable_curr"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:inputType="numberDecimal"
                            android:paddingLeft="@dimen/activity_left_margin"
                            android:textColor="@color/black"
                            android:textStyle="bold" />
                    </LinearLayout>

                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/light_white_"
                android:gravity="center"
                android:orientation="horizontal"
                android:padding="@dimen/padding_top">

                <androidx.appcompat.widget.AppCompatButton
                    android:id="@+id/id_back"
                    android:layout_width="150dp"
                    android:layout_height="@dimen/dp50"
                    android:text="@string/label_close" />

                <androidx.appcompat.widget.AppCompatButton
                    android:id="@+id/add_btn"
                    android:layout_width="150dp"
                    android:layout_height="@dimen/dp50"
                    android:layout_marginStart="@dimen/dp20"
                    android:text="@string/label_add" />

            </LinearLayout>
        </LinearLayout>
    </ScrollView>


</LinearLayout>