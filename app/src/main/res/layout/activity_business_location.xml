<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="7dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/label_business_location"
                android:textColor="@color/black"
                android:textSize="20dp" />

            <TextView
                android:id="@+id/id_sub_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="5dp"
                android:text="@string/label_add_new_business_locationt"
                android:textColor="@color/gray"
                android:textSize="14dp" />
        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="2dp"
            android:background="@color/blue_dark" />
    </LinearLayout>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp40">

        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:clipChildren="false"
            android:clipToPadding="false"
            android:divider="@android:color/transparent"
            android:dividerHeight="0.0px"
            android:elevation="8dp"
            android:visibility="visible"
            app:cardElevation="2dp"
            app:cardPreventCornerOverlap="false"
            app:cardUseCompatPadding="true">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:padding="@dimen/dp10">
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp20"
                    android:baselineAligned="false"
                    android:orientation="horizontal"
                    android:weightSum="8">

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="0dp"
                        android:layout_marginStart="@dimen/dp10"
                        android:layout_height="wrap_content"
                        android:layout_weight="2">

                        <androidx.appcompat.widget.AppCompatEditText
                            android:id="@+id/business_name"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:drawableStart="@drawable/ic_new_product"
                            android:drawablePadding="@dimen/dp10"
                            android:gravity="start|center"
                            android:hint="@string/string_name"
                            android:inputType="textCapWords"
                            android:textColor="@color/black" />
                    </com.google.android.material.textfield.TextInputLayout>


                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp10"
                        android:layout_weight="2">

                        <androidx.appcompat.widget.AppCompatEditText
                            android:id="@+id/bussine_id"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:drawableStart="@drawable/ic_sku"
                            android:drawablePadding="@dimen/dp10"
                            android:gravity="start|center"
                            android:hint="@string/label_location_id"
                            android:inputType="textCapWords"
                            android:textColor="@color/black" />
                    </com.google.android.material.textfield.TextInputLayout>

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp10"
                        android:layout_weight="2">

                        <androidx.appcompat.widget.AppCompatEditText
                            android:id="@+id/land_mark"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:drawableStart="@drawable/ic_sku"
                            android:drawablePadding="@dimen/dp10"
                            android:gravity="start|center"
                            android:hint="@string/label_land_mark"
                            android:inputType="textCapWords"
                            android:textColor="@color/black" />
                    </com.google.android.material.textfield.TextInputLayout>


                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="0dp"
                        android:layout_marginStart="@dimen/dp10"
                        android:layout_height="wrap_content"
                        android:layout_weight="2">

                        <androidx.appcompat.widget.AppCompatEditText
                            android:id="@+id/city"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:drawableStart="@drawable/ic_new_product"
                            android:drawablePadding="@dimen/dp10"
                            android:gravity="start|center"
                            android:hint="@string/image_ville_ob"
                            android:inputType="textCapWords"
                            android:textColor="@color/black" />
                    </com.google.android.material.textfield.TextInputLayout>


                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="0dp"
                        android:layout_marginStart="@dimen/dp10"
                        android:layout_height="wrap_content"
                        android:layout_weight="2">

                        <androidx.appcompat.widget.AppCompatEditText
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:drawableStart="@drawable/ic_new_product"
                            android:drawablePadding="@dimen/dp10"
                            android:gravity="start|center"
                            android:hint="@string/lbl_transaction_edit"
                            android:inputType="textCapWords"
                            android:textColor="@color/black" />
                    </com.google.android.material.textfield.TextInputLayout>
                </LinearLayout>
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp20"
                    android:baselineAligned="false"
                    android:orientation="horizontal"
                    android:weightSum="8">

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="0dp"
                        android:layout_marginStart="@dimen/dp10"
                        android:layout_height="wrap_content"
                        android:layout_weight="2">

                        <androidx.appcompat.widget.AppCompatEditText
                            android:id="@+id/zip_code"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:drawableStart="@drawable/ic_new_product"
                            android:drawablePadding="@dimen/dp10"
                            android:gravity="start|center"
                            android:hint="@string/text_zip_code_req"
                            android:inputType="number"
                            android:textColor="@color/black" />
                    </com.google.android.material.textfield.TextInputLayout>


                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp10"
                        android:layout_weight="2">

                        <androidx.appcompat.widget.AppCompatEditText
                            android:id="@+id/state_req"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:drawableStart="@drawable/ic_sku"
                            android:drawablePadding="@dimen/dp10"
                            android:gravity="start|center"
                            android:hint="@string/text_state_req"
                            android:inputType="textCapWords"
                            android:textColor="@color/black" />
                    </com.google.android.material.textfield.TextInputLayout>

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp10"
                        android:layout_weight="2">

                        <androidx.appcompat.widget.AppCompatEditText
                            android:id="@+id/country"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:drawableStart="@drawable/ic_sku"
                            android:drawablePadding="@dimen/dp10"
                            android:gravity="start|center"
                            android:hint="@string/text_country_req"
                            android:inputType="textCapWords"
                            android:textColor="@color/black" />
                    </com.google.android.material.textfield.TextInputLayout>


                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="0dp"
                        android:layout_marginStart="@dimen/dp10"
                        android:layout_height="wrap_content"
                        android:layout_weight="2">

                        <androidx.appcompat.widget.AppCompatEditText
                            android:id="@+id/mobile"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:drawableStart="@drawable/ic_new_product"
                            android:drawablePadding="@dimen/dp10"
                            android:gravity="start|center"
                            android:hint="@string/label_mobile"
                            android:inputType="textCapWords"
                            android:textColor="@color/black" />
                    </com.google.android.material.textfield.TextInputLayout>


                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="0dp"
                        android:layout_marginStart="@dimen/dp10"
                        android:layout_height="wrap_content"
                        android:layout_weight="2">

                        <androidx.appcompat.widget.AppCompatEditText
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:drawableStart="@drawable/ic_new_product"
                            android:drawablePadding="@dimen/dp10"
                            android:gravity="start|center"
                            android:hint="@string/lbl_transaction_edit"
                            android:inputType="textCapWords"
                            android:textColor="@color/black" />
                    </com.google.android.material.textfield.TextInputLayout>
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/dp20"
                    android:baselineAligned="false"
                    android:orientation="horizontal"
                    android:weightSum="8">

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="0dp"
                        android:layout_marginStart="@dimen/dp10"
                        android:layout_height="wrap_content"
                        android:layout_weight="2">

                        <androidx.appcompat.widget.AppCompatEditText
                            android:id="@+id/email"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:drawablePadding="@dimen/dp10"
                            android:gravity="start|center"
                            android:hint="@string/label_email"
                            android:inputType="textCapWords"
                            android:textColor="@color/black" />
                    </com.google.android.material.textfield.TextInputLayout>


                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp10"
                        android:layout_weight="2">

                        <androidx.appcompat.widget.AppCompatEditText
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:drawablePadding="@dimen/dp10"
                            android:gravity="start|center"
                            android:hint="@string/text_alternance_contact"
                            android:inputType="textCapWords"
                            android:textColor="@color/black" />
                    </com.google.android.material.textfield.TextInputLayout>

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp10"
                        android:layout_weight="2">

                        <androidx.appcompat.widget.AppCompatEditText
                            android:id="@+id/website"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:drawablePadding="@dimen/dp10"
                            android:gravity="start|center"
                            android:hint="@string/text_website"
                            android:inputType="textCapWords"
                            android:textColor="@color/black" />
                    </com.google.android.material.textfield.TextInputLayout>


                    <com.google.android.material.textfield.TextInputLayout
                     android:visibility="invisible"
                        android:layout_width="0dp"
                        android:layout_marginStart="@dimen/dp10"
                        android:layout_height="wrap_content"
                        android:layout_weight="2">

                        <androidx.appcompat.widget.AppCompatEditText
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:drawableStart="@drawable/ic_new_product"
                            android:drawablePadding="@dimen/dp10"
                            android:gravity="start|center"
                            android:hint="@string/label_mobile"
                            android:inputType="textCapWords"
                            android:textColor="@color/black" />
                    </com.google.android.material.textfield.TextInputLayout>


                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="0dp"
                        android:layout_marginStart="@dimen/dp10"
                        android:layout_height="wrap_content"
                        android:layout_weight="2">

                        <androidx.appcompat.widget.AppCompatEditText
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:drawableStart="@drawable/ic_new_product"
                            android:drawablePadding="@dimen/dp10"
                            android:gravity="start|center"
                            android:hint="@string/lbl_transaction_edit"
                            android:inputType="textCapWords"
                            android:textColor="@color/black" />
                    </com.google.android.material.textfield.TextInputLayout>
                </LinearLayout>




                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/light_white_"
                    android:gravity="center"
                    android:orientation="horizontal"
                    android:padding="@dimen/padding_top"
                    android:weightSum="2">


                    <androidx.appcompat.widget.AppCompatButton
                        android:id="@+id/id_back"
                        android:layout_width="0dp"
                        android:layout_height="@dimen/dp50"
                        android:layout_weight="0.4"
                        android:text="@string/label_close" />

                    <androidx.appcompat.widget.AppCompatButton
                        android:id="@+id/add_btn"
                        android:layout_width="0dp"
                        android:layout_height="@dimen/dp50"
                        android:layout_marginStart="@dimen/dp20"
                        android:layout_weight="0.4"
                        android:text="@string/label_add" />

                </LinearLayout>


            </LinearLayout>
        </androidx.cardview.widget.CardView>

    </ScrollView>

</RelativeLayout>