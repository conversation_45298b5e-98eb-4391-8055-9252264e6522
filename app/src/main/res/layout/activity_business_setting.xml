<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:padding="@dimen/dp20"
    android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/label_business_setting"
                android:textColor="@color/black"
                android:textSize="20sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/id_sub_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="5dp"
                android:text="@string/label_add_new_product"
                android:textColor="@color/gray"
                android:textSize="14sp"
                android:visibility="gone" />
        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="2dp"
            android:layout_marginVertical="@dimen/dp10"
            android:background="@color/blue_dark" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:baselineAligned="false"
                        android:orientation="horizontal"
                        android:weightSum="6">

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="2">

                            <androidx.appcompat.widget.AppCompatEditText
                                android:id="@+id/businessName"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:drawableStart="@drawable/ic_business_svg"
                                android:drawablePadding="@dimen/dp10"
                                android:gravity="start|center"
                                android:hint="@string/business_name"
                                android:inputType="textCapWords"
                                android:textColor="@color/black" />
                        </com.google.android.material.textfield.TextInputLayout>

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp20"
                            android:layout_weight="2">

                            <androidx.appcompat.widget.AppCompatEditText
                                android:id="@+id/startDate"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:drawableStart="@drawable/ic_calendar_svg"
                                android:drawablePadding="@dimen/dp10"
                                android:gravity="start|center"
                                android:focusable="false"
                                android:hint="@string/string_start_date"
                                android:inputType="textCapWords"
                                android:textColor="@color/black" />
                        </com.google.android.material.textfield.TextInputLayout>

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                             android:layout_marginStart="@dimen/dp20"
                            android:layout_weight="2">

                            <androidx.appcompat.widget.AppCompatEditText
                                android:id="@+id/default_percent_profit"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:drawableStart="@drawable/ic_percentage_svg"
                                android:drawablePadding="@dimen/dp10"
                                android:gravity="start|center"
                                android:hint="@string/string_default_profit_percent"
                                android:inputType="numberDecimal"
                                android:textColor="@color/black" />
                        </com.google.android.material.textfield.TextInputLayout>

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:baselineAligned="false"
                        android:layout_marginTop="@dimen/dp20"
                        android:orientation="horizontal"
                        android:weightSum="6">

                        <FrameLayout
                            android:layout_width="0dp"
                            android:layout_weight="2"
                            android:layout_marginTop="@dimen/dp5"
                            android:background="@drawable/bg_spinner"
                            android:layout_height="wrap_content">
                            <ImageView
                                android:layout_width="wrap_content"
                                android:src="@drawable/ic_money_24"
                                android:paddingHorizontal="10dp"
                                android:layout_height="match_parent"
                                android:contentDescription="@string/label_to">

                            </ImageView>

                            <androidx.appcompat.widget.AppCompatSpinner
                                android:id="@+id/spinnerCurrency"
                                android:layout_width="match_parent"
                                android:layout_height="@dimen/dp60"
                                android:layout_marginStart="@dimen/dp30"
                                android:background="@null"
                                android:textSize="12sp"
                                android:textStyle="normal" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/dp_10"
                                android:background="@color/white"
                                android:paddingLeft="4dp"
                                android:visibility="visible"
                                android:text="@string/lbl_currency"
                                android:paddingRight="4dp"
                                android:textSize="12sp" />
                        </FrameLayout>
                        <FrameLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                           android:layout_marginStart="@dimen/dp20"
                            android:layout_weight="2">

                            <androidx.appcompat.widget.AppCompatSpinner
                                android:id="@+id/currencySymbolPlacement"
                                android:layout_width="match_parent"
                                android:layout_height="@dimen/default_spinner_height"
                                android:layout_marginTop="5dp"
                                android:entries="@array/array_currency_symbol_placement"
                                android:textSize="12sp"
                                android:textStyle="normal" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/dp_10"
                                android:background="@color/white"
                                android:paddingLeft="4dp"
                                android:paddingRight="4dp"
                                android:text="@string/currency_symbol_placement"
                                android:textSize="12sp"
                                android:visibility="visible" />
                        </FrameLayout>
                        <FrameLayout
                            android:layout_width="0dp"
                           android:layout_marginStart="@dimen/dp20"
                            android:layout_height="wrap_content"
                            android:background="@drawable/bg_spinner"
                            android:layout_weight="2">
                            <ImageView
                                android:layout_width="wrap_content"
                                android:src="@drawable/ic_time_svg"
                                android:paddingHorizontal="10dp"
                                android:layout_height="match_parent"
                                android:contentDescription="@string/label_to">

                            </ImageView>
                            <androidx.appcompat.widget.AppCompatSpinner
                                android:id="@+id/timeZoneSpinner"
                                android:layout_width="match_parent"
                                android:layout_height="@dimen/default_spinner_height"
                                android:layout_marginTop="5dp"
                                android:layout_marginStart="@dimen/dp30"
                                android:background="@null"
                                android:textSize="12sp"
                                android:textStyle="normal" />


                            <androidx.appcompat.widget.AppCompatTextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/dp30"
                                android:background="@color/white"
                                android:paddingLeft="4dp"
                                android:paddingRight="4dp"
                                android:text="@string/string_time_zone"
                                android:textSize="12sp"
                                android:visibility="visible" />
                        </FrameLayout>
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp20"
                        android:baselineAligned="false"
                        android:orientation="horizontal"
                        android:weightSum="6">
                        <FrameLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:background="@drawable/bg_spinner"
                            android:layout_weight="2">
                            <ImageView
                                android:layout_width="wrap_content"
                                android:src="@drawable/ic_calendar_svg"
                                android:paddingHorizontal="10dp"
                                android:layout_height="match_parent"
                                android:contentDescription="@string/label_to">
                            </ImageView>
                            <androidx.appcompat.widget.AppCompatSpinner
                                android:id="@+id/financialYearSpinner"
                                android:layout_width="match_parent"
                                android:layout_height="@dimen/default_spinner_height"
                                android:layout_marginStart="@dimen/dp30"
                                android:entries="@array/array_year_list"
                                android:layout_marginTop="5dp"
                                android:background="@null"
                                android:textSize="12sp"
                                android:textStyle="normal" />
                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/spinner_category_tint"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/dp_10"
                                android:background="@color/white"
                                android:paddingLeft="4dp"
                                android:paddingRight="4dp"
                                android:text="@string/string_fiancial_years_start"
                                android:textSize="12sp"
                                android:visibility="visible" />
                        </FrameLayout>
                        <FrameLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp20"
                            android:background="@drawable/bg_spinner"
                            android:layout_weight="2">
                            <ImageView
                                android:layout_width="wrap_content"
                                android:src="@drawable/ic_accounting_svg"
                                android:paddingHorizontal="10dp"
                                android:layout_height="match_parent"
                                android:contentDescription="@string/label_to">
                            </ImageView>
                            <androidx.appcompat.widget.AppCompatSpinner
                                android:id="@+id/spinnerStockAccounting"
                                android:layout_width="match_parent"
                                android:layout_height="@dimen/default_spinner_height"
                                android:layout_marginStart="@dimen/dp30"
                                android:background="@null"
                                android:layout_marginTop="5dp"
                                android:entries="@array/array_accounting_method"
                                android:textSize="12sp"
                                android:textStyle="normal" />
                            <androidx.appcompat.widget.AppCompatTextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/dp_10"
                                android:background="@color/white"
                                android:paddingLeft="4dp"
                                android:paddingRight="4dp"
                                android:text="@string/string_stock_accounting"
                                android:textSize="12sp"
                                android:visibility="visible" />
                        </FrameLayout>
                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp20"
                            android:layout_marginTop="-5dp"
                            android:layout_weight="2">

                            <androidx.appcompat.widget.AppCompatEditText
                                android:id="@+id/transactionEditDays"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:drawableStart="@drawable/ic_edit_svg"
                                android:drawablePadding="@dimen/dp10"
                                android:gravity="start|center"
                                android:hint="@string/lbl_transaction_edit"
                                android:inputType="numberDecimal"
                                android:textColor="@color/black" />
                        </com.google.android.material.textfield.TextInputLayout>
                    </LinearLayout>
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp20"
                        android:baselineAligned="false"
                        android:orientation="horizontal"
                        android:weightSum="6">
                        <FrameLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:background="@drawable/bg_spinner"
                            android:layout_weight="2">
                            <ImageView
                                android:layout_width="wrap_content"
                                android:src="@drawable/ic_calendar_svg"
                                android:paddingHorizontal="10dp"
                                android:layout_height="match_parent"
                                android:contentDescription="@string/label_to">
                            </ImageView>
                            <androidx.appcompat.widget.AppCompatSpinner
                                android:id="@+id/dateFormat"
                                android:layout_width="match_parent"
                                android:layout_height="@dimen/default_spinner_height"
                                android:layout_marginTop="5dp"
                                android:layout_marginStart="@dimen/dp30"
                                android:background="@null"
                                android:entries="@array/type_date_format"
                                android:textSize="12sp"
                                android:textStyle="normal" />
                            <androidx.appcompat.widget.AppCompatTextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/dp_10"
                                android:background="@color/white"
                                android:paddingLeft="4dp"
                                android:paddingRight="4dp"
                                android:text="@string/string_date_format"
                                android:textSize="12sp"
                                android:visibility="visible" />
                        </FrameLayout>
                        <FrameLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:background="@drawable/bg_spinner"
                           android:layout_marginStart="@dimen/dp20"
                            android:layout_weight="2">
                            <ImageView
                                android:layout_width="wrap_content"
                                android:src="@drawable/ic_time_svg"
                                android:paddingHorizontal="10dp"
                                android:layout_height="match_parent"
                                android:contentDescription="@string/label_to">
                            </ImageView>
                            <androidx.appcompat.widget.AppCompatSpinner
                                android:id="@+id/timeFormat"
                                android:layout_width="match_parent"
                                android:layout_height="@dimen/default_spinner_height"
                                android:background="@null"
                                android:layout_marginStart="@dimen/dp30"
                                android:layout_marginTop="5dp"
                                android:entries="@array/type_time_format"
                                android:textSize="12sp"
                                android:textStyle="normal" />
                            <androidx.appcompat.widget.AppCompatTextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/dp_10"
                                android:background="@color/white"
                                android:paddingLeft="4dp"
                                android:paddingRight="4dp"
                                android:text="@string/string_time_format"
                                android:textSize="12sp"
                                android:visibility="visible" />
                        </FrameLayout>
                        <LinearLayout
                            android:layout_weight="2"
                            android:orientation="horizontal"
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_marginStart="@dimen/dp20"
                            android:weightSum="3">
                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/uploadLogo"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:layout_marginStart="@dimen/dp20"
                                android:layout_weight="1"
                                android:background="@drawable/border_gray"
                                android:drawablePadding="@dimen/dp20"
                                android:padding="@dimen/dp10"
                                android:drawableEnd="@drawable/ic_upload_file"
                                android:gravity="center"
                                android:text="@string/lbl_upload_logo"
                                android:textSize="16sp">
                            </androidx.appcompat.widget.AppCompatTextView>
                            <ImageView
                                android:id="@+id/logoImage"
                                android:layout_weight="2"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:layout_marginStart="@dimen/dp10"
                                android:background="@drawable/image_notavailable"
                                android:gravity="start" />
                        </LinearLayout>

                    </LinearLayout>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/light_white_"
                android:gravity="center"
                android:orientation="horizontal"
                android:layout_marginTop="@dimen/dp20"
                android:padding="@dimen/padding_top"
                android:weightSum="2">
                <androidx.appcompat.widget.AppCompatButton
                    android:id="@+id/idBack"
                    android:visibility="gone"
                    android:layout_width="150dp"
                    android:layout_height="@dimen/dp50"
                    android:text="@string/label_close" />
                <androidx.appcompat.widget.AppCompatButton
                    android:id="@+id/addBtn"
                    android:layout_width="150dp"
                    android:layout_height="@dimen/dp50"
                    android:layout_marginStart="@dimen/dp20"
                    android:text="@string/label_add" />
            </LinearLayout>

</LinearLayout>