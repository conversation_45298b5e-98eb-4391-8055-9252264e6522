<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:padding="@dimen/activity_horizontal_margin">

        <include
            android:id="@+id/include2"
            layout="@layout/toolbar"
            android:visibility="gone"
            app:layout_constraintTop_toTopOf="parent"
            tools:layout_editor_absoluteX="124dp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="7dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"

                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/lbl_all_expenses"
                    android:textColor="@color/black"
                    android:textSize="20dp" />

            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="2dp"
                android:background="@color/blue_dark" />


        </LinearLayout>

        <LinearLayout
            android:id="@+id/filter_header"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_gradient"
            android:paddingHorizontal="5dp"
            android:paddingVertical="@dimen/activity_left_margin">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawableLeft="@drawable/ic_baseline_filter_alt_24"
                android:drawablePadding="@dimen/dp10"
                android:text="Filtres"
                android:textColor="@color/white"
                android:textSize="16dp" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/filter_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="horizontal"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/include2">

            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:divider="@android:color/transparent"
                android:dividerHeight="0.0px"
                android:elevation="8dp"
                android:visibility="visible"
                app:cardElevation="2dp"
                app:cardPreventCornerOverlap="false"
                app:cardUseCompatPadding="true">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="start"
                        android:orientation="horizontal"
                        android:padding="@dimen/dp10"
                        android:weightSum="12"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/include2">

                        <FrameLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="2">

                            <androidx.appcompat.widget.AppCompatSpinner
                                android:id="@+id/spinnerStation"
                                android:layout_width="match_parent"
                                android:layout_height="@dimen/dp50"
                                android:layout_marginTop="5dp"
                                android:entries="@array/type_produc_type"
                                android:textSize="12sp"
                                android:textStyle="normal" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/dp_10"
                                android:background="@color/white"
                                android:paddingLeft="4dp"
                                android:paddingRight="4dp"
                                android:text="@string/label_station"
                                android:textSize="12sp"
                                android:visibility="visible" />
                        </FrameLayout>

                        <FrameLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp10"
                            android:layout_weight="2">

                            <androidx.appcompat.widget.AppCompatSpinner
                                android:id="@+id/spinner_payment_status"
                                android:layout_width="match_parent"
                                android:layout_height="@dimen/dp50"
                                android:layout_marginTop="5dp"
                                android:entries="@array/array_payement_status"
                                android:textSize="12sp"
                                android:textStyle="normal" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/spinner_prod_type_tintt"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/dp_10"
                                android:background="@color/white"
                                android:paddingLeft="4dp"
                                android:paddingRight="4dp"
                                android:text="@string/label_status_paimenent"
                                android:textSize="12sp"
                                android:visibility="visible" />
                        </FrameLayout>

                        <FrameLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp10"
                            android:layout_weight="2">

                            <androidx.appcompat.widget.AppCompatSpinner
                                android:id="@+id/spinner_contact"
                                android:layout_width="match_parent"
                                android:layout_height="@dimen/dp50"
                                android:layout_marginTop="5dp"
                                android:entries="@array/type_payement_status"
                                android:textSize="12sp"
                                android:textStyle="normal" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/spinner_prod_type_tint"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/dp_10"
                                android:background="@color/white"
                                android:paddingLeft="4dp"
                                android:paddingRight="4dp"
                                android:text="@string/label_contact"
                                android:textSize="12sp"
                                android:visibility="visible" />
                        </FrameLayout>

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="0dp"
                            android:layout_height="55dp"
                            android:layout_marginStart="@dimen/dp10"
                            android:layout_weight="2">

                            <androidx.appcompat.widget.AppCompatEditText
                                android:id="@+id/new_start_date"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:focusable="false"
                                android:gravity="start|center"
                                android:textSize="12sp"
                                android:hint="Start Date"
                                android:inputType="textCapWords"
                                android:textColor="@color/black" />
                        </com.google.android.material.textfield.TextInputLayout>

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="0dp"
                            android:layout_height="55dp"
                            android:layout_marginStart="@dimen/dp10"
                            android:layout_weight="2">

                            <androidx.appcompat.widget.AppCompatEditText
                                android:id="@+id/new_end_date"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:focusable="false"
                                android:gravity="start|center"
                                android:textSize="12sp"
                                android:hint="End date"
                                android:inputType="textCapWords"
                                android:textColor="@color/black" />
                        </com.google.android.material.textfield.TextInputLayout>

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_gravity="center|end"
                            android:layout_weight="2"
                            android:paddingTop="@dimen/dp5"
                            android:gravity="center|end"
                            android:orientation="horizontal">

                            <androidx.appcompat.widget.AppCompatButton
                                android:id="@+id/id_filter"
                                android:layout_width="0dp"
                                android:layout_height="@dimen/dp50"
                                android:layout_marginStart="@dimen/dp10"
                                android:layout_weight="1"
                                android:background="@drawable/bg_btn_blue"
                                android:text="@string/label_filter" />

                            <androidx.appcompat.widget.AppCompatButton
                                android:id="@+id/id_add"
                                android:layout_width="0dp"
                                android:layout_height="@dimen/dp50"
                                android:layout_marginStart="@dimen/dp5"
                                android:layout_weight="1"
                                android:background="@drawable/bg_btn_green"
                                android:text="@string/label_add" />

                        </LinearLayout>
                    </LinearLayout>

                </LinearLayout>


            </androidx.cardview.widget.CardView>

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="@dimen/dp20"
            android:background="@drawable/bg_gradient"
            android:gravity="center"
            android:orientation="horizontal"
            android:padding="@dimen/dp20"
            android:weightSum="13">
            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/label_date"
                android:textColor="@color/white"
                android:textStyle="bold" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/label_reference_num"
                android:textColor="@color/white"
                android:textStyle="bold" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/label_reccuring_details"
                android:textColor="@color/white"
                android:textStyle="bold" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/label_expense_categories"
                android:textColor="@color/white"
                android:textStyle="bold" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/label_location"
                android:textColor="@color/white"
                android:textStyle="bold" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/label_status_payement"
                android:textColor="@color/white"
                android:textStyle="bold" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/label_montant_total"
                android:textColor="@color/white"
                android:textStyle="bold" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/string_payement_due"
                android:textColor="@color/white"
                android:textStyle="bold" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/string_expense_for"
                android:textColor="@color/white"
                android:textStyle="bold" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/label_contact"
                android:textColor="@color/white"
                android:textStyle="bold" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/label_expense_note"
                android:textColor="@color/white"
                android:textStyle="bold" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/label_added_by"
                android:textColor="@color/white"
                android:textStyle="bold" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/label_btn_action"
                android:textColor="@color/white"
                android:textStyle="bold" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:divider="@android:color/transparent"
                android:dividerHeight="0.0px"
                android:elevation="8dp"
                android:visibility="visible"
                app:cardElevation="2dp"
                app:cardPreventCornerOverlap="false"
                app:cardUseCompatPadding="true">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recycle_vente"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:visibility="visible" />

                <TextView
                    android:id="@+id/noItemFound"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:gravity="center"
                    android:padding="@dimen/dp20"
                    android:text="@string/no_depense_found"
                    android:textColor="@color/black"
                    android:textSize="16sp"
                    android:visibility="gone">

                </TextView>
            </androidx.cardview.widget.CardView>
        </LinearLayout>

        <LinearLayout
            android:visibility="gone"
            android:id="@+id/total_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:background="@color/greyLight"
            android:gravity="center"
            android:orientation="horizontal"
            android:padding="20px">


            <TextView
                android:id="@+id/purchase_date"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/label_date"
                android:textColor="@color/white"
                android:textStyle="normal"
                android:visibility="invisible" />

            <TextView
                android:id="@+id/purchase_ref"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/label_total"
                android:textColor="@color/black"
                android:textSize="15dp"
                android:textStyle="bold"
                android:visibility="visible" />

            <TextView
                android:id="@+id/purchase_location"

                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/label_location"
                android:textColor="@color/black"
                android:textStyle="normal"
                android:visibility="invisible" />

            <TextView
                android:id="@+id/purchase_supplier"

                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/label_supplier"
                android:textColor="@color/black"
                android:textStyle="normal"
                android:visibility="invisible" />

            <TextView
                android:id="@+id/purchase_added_by"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/label_added_by"
                android:textColor="@color/black"
                android:textStyle="normal"
                android:visibility="gone" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <LinearLayout
                    android:id="@+id/container_ordered"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:paddingVertical="@dimen/margin_vertical"
                    android:visibility="gone">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/label_ordered"
                        android:textColor="@color/black"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/ordered_count"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="@dimen/padding_horizontal"
                        android:text="@string/label_filter"
                        android:textColor="@color/black"
                        android:textStyle="bold" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/container_received"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:paddingVertical="@dimen/margin_vertical"
                    android:visibility="gone">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/label_received"
                        android:textColor="@color/black"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/received_count"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="@dimen/padding_horizontal"

                        android:text="@string/label_filter"

                        android:textColor="@color/black"
                        android:textStyle="bold" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/container_pending"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:paddingVertical="@dimen/margin_vertical"
                    android:visibility="gone">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/label_pending"
                        android:textColor="@color/black"

                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/pending_count"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="@dimen/padding_horizontal"
                        android:text="@string/label_filter"

                        android:textColor="@color/black"
                        android:textStyle="bold" />
                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <LinearLayout
                    android:id="@+id/container_paid"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:paddingVertical="@dimen/margin_vertical"
                    android:visibility="gone">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/label_paid"
                        android:textColor="@color/black"

                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/paid_count"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="@dimen/padding_horizontal"
                        android:text="@string/label_filter"

                        android:textColor="@color/black"
                        android:textStyle="bold" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/container_partial"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:paddingVertical="@dimen/margin_vertical"
                    android:visibility="gone">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/label_partial"
                        android:textColor="@color/black"

                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/partial_count"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="@dimen/padding_horizontal"
                        android:text="@string/label_filter"

                        android:textColor="@color/black"
                        android:textStyle="bold" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/container_due"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:paddingVertical="@dimen/margin_vertical"
                    android:visibility="gone">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/label_due"
                        android:textColor="@color/black"

                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/due_count"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="@dimen/padding_horizontal"
                        android:text="@string/label_filter"
                        android:textColor="@color/black"
                        android:textStyle="bold" />
                </LinearLayout>

            </LinearLayout>

            <TextView
                android:id="@+id/purchase_grand_total"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:textColor="@color/black"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/purchase_payment_status"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/label_paiment_status_label"
                android:textColor="@color/black"
                android:textStyle="normal"
                android:visibility="invisible" />


            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:visibility="invisible">


            </LinearLayout>

        </LinearLayout>
    </LinearLayout>

</LinearLayout>