<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp60"
        android:background="@drawable/cart_header"
        android:gravity="center"
        android:orientation="horizontal"
        android:padding="5dp">

        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_weight="1"
            android:fontFamily="@font/poppins_semibold"
            android:text="@string/image_quick_sell"
            android:textColor="@color/white"
            android:textAlignment="center"
            android:textSize="14sp" />

        <androidx.appcompat.widget.AppCompatTextView
            android:visibility="gone"
            android:id="@+id/customer_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:fontFamily="@font/poppins_semibold"
            android:gravity="end"
            android:text="Customer: Walk-in"
            android:textColor="@color/white"
            android:textSize="14sp" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingTop="@dimen/dp10"
        android:weightSum="2">

        <androidx.appcompat.widget.AppCompatSpinner
            android:id="@+id/spinner_customers"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp50"
            android:layout_marginHorizontal="@dimen/dp5"
            android:layout_weight="1"/>

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="@dimen/dp10"
        android:background="@drawable/ic_rectangle_white_bg"
        android:orientation="vertical"
        android:padding="@dimen/dp5">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="@dimen/dp2">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/padding_horizontal"
                android:background="@color/gray_mince"
                android:orientation="horizontal"
                android:padding="@dimen/dp10">

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/texte_product"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="2"
                    android:fontFamily="@font/poppins_semibold"
                    android:text="@string/image_product_name"
                    android:textColor="@color/black" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:fontFamily="@font/poppins_semibold"
                    android:text="@string/label_product_price"
                    android:textColor="@color/black" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"

                    android:fontFamily="@font/poppins_semibold"
                    android:text="@string/label_product_qty"
                    android:textColor="@color/black" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1.1"
                    android:fontFamily="@font/poppins_semibold"
                    android:text="@string/labele_product_total"
                    android:textColor="@color/black" />
            </LinearLayout>
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_weight="1">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:padding="@dimen/padding_horizontal">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/list_item_product"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:listitem="@layout/detail_product_item" />

            </androidx.constraintlayout.widget.ConstraintLayout>
        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_marginHorizontal="@dimen/dp5"
            android:background="#00bff3" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingLeft="50dp"
            android:paddingRight="20dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <androidx.appcompat.widget.AppCompatTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="2"
                    android:padding="7dp"
                    android:text="@string/image_product_subtotal"
                    android:textColor="@color/black" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="00.0"
                    android:textColor="@color/black" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="0"
                    android:textColor="@color/black" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="end|center"
                    android:layout_weight="1"
                    android:text="00.0"
                    android:textAlignment="textEnd"
                    android:textColor="@color/black" />

            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="@color/gray" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:padding="@dimen/dp5"
                android:visibility="gone">

                <androidx.appcompat.widget.AppCompatTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="2"
                    android:text="@string/image_product_subtotal"
                    android:textColor="@color/black"
                    android:visibility="invisible" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="$8.00"
                    android:textColor="@color/black"
                    android:visibility="invisible" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Discount (0%)"
                    android:textColor="@color/black" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/discountAmountTxt"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="end"
                    android:layout_weight="1"
                    android:text="00.0"
                    android:textAlignment="textEnd"
                    android:textColor="@color/black" />

            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="@color/gray" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:padding="@dimen/dp5">

                <androidx.appcompat.widget.AppCompatTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="2"
                    android:text="@string/image_product_subtotal"
                    android:textColor="@color/black"
                    android:visibility="invisible" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="$8.00"
                    android:textColor="@color/black"
                    android:visibility="invisible" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Tax (0%)"
                    android:textColor="@color/black" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="end"
                    android:layout_weight="1"
                    android:text="00.0"
                    android:textAlignment="textEnd"
                    android:textColor="@color/black" />

            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="@color/gray" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:padding="@dimen/dp5">

                <androidx.appcompat.widget.AppCompatTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="2"
                    android:text="@string/image_product_subtotal"
                    android:textColor="@color/black"
                    android:visibility="invisible" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="$8.00"
                    android:textColor="@color/black"
                    android:visibility="invisible" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:fontFamily="@font/poppins_semibold"
                    android:text="@string/label_product_total"
                    android:textColor="@color/black"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/total_price"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="end"
                    android:layout_weight="1"
                    android:text="00.0"
                    android:textAlignment="textEnd"
                    android:textColor="@color/black"
                    android:textStyle="bold" />

            </LinearLayout>
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:weightSum="3"
                android:orientation="horizontal">

                <LinearLayout
                    android:id="@+id/id_pay_cash"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_margin="@dimen/padding_horizontal"
                    android:layout_weight="1"
                    android:background="@color/green_light"
                    android:gravity="center"
                    android:orientation="horizontal"
                    android:padding="@dimen/margin_vertical">

                    <ImageView
                        android:layout_width="30dp"
                        android:layout_height="30dp"
                        android:padding="@dimen/padding_horizontal"
                        android:src="@drawable/ic_money"
                        app:tint="@color/white" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/poppins_semibold"
                        android:text="@string/label_pay_cash_pro"
                        android:textColor="@color/white"
                        android:textSize="14sp" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_margin="@dimen/padding_horizontal"
                    android:layout_weight="1"
                    android:background="@color/green_light"
                    android:gravity="center"
                    android:orientation="horizontal"
                    android:padding="@dimen/margin_vertical">

                    <ImageView
                        android:layout_width="30dp"
                        android:layout_height="30dp"
                        android:padding="@dimen/padding_horizontal"
                        android:src="@drawable/ic_pay_card_svg" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/poppins_semibold"
                        android:text="@string/label_pay_card"
                        android:textColor="@color/white"
                        android:textSize="14sp" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_margin="@dimen/padding_horizontal"
                    android:layout_weight="1"
                    android:background="@color/green_light"
                    android:gravity="center"
                    android:orientation="horizontal"
                    android:padding="@dimen/margin_vertical">

                    <ImageView
                        android:layout_width="30dp"
                        android:layout_height="30dp"
                        android:padding="@dimen/padding_horizontal"
                        android:src="@drawable/ic_smartphone_svg" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/poppins_semibold"
                        android:text="@string/label_pay_mobile"

                        android:textColor="@color/white"
                        android:textSize="14sp" />

                </LinearLayout>
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:weightSum="3"
                android:orientation="horizontal">

                <LinearLayout
                    android:id="@+id/id_multi_pay"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_margin="@dimen/padding_horizontal"
                    android:layout_weight="1"
                    android:background="@color/white_blue_sky"
                    android:gravity="center"
                    android:orientation="horizontal"
                    android:padding="@dimen/margin_vertical">

                    <ImageView
                        android:layout_width="30dp"
                        android:layout_height="30dp"
                        android:padding="@dimen/padding_horizontal"
                        android:src="@drawable/ic_money"
                        app:tint="@color/white" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/poppins_semibold"
                        android:text="@string/label_pay_multi_pay"
                        android:textColor="@color/white"
                        android:textSize="14sp" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/id_save_btn"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_margin="@dimen/padding_horizontal"
                    android:layout_weight="1"
                    android:background="@color/btnBlue"
                    android:gravity="center"
                    android:orientation="horizontal"
                    android:padding="@dimen/margin_vertical">

                    <ImageView
                        android:layout_width="30dp"
                        android:layout_height="30dp"
                        android:padding="@dimen/padding_horizontal"
                        android:src="@drawable/ic_save_svg" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/poppins_semibold"
                        android:text="@string/label_save"
                        android:textColor="@color/white"
                        android:textSize="14sp" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/id_cancel_btn"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_margin="@dimen/padding_horizontal"
                    android:layout_weight="1"
                    android:background="@color/btnRed"
                    android:gravity="center"
                    android:orientation="horizontal"
                    android:padding="@dimen/margin_vertical">

                    <ImageView
                        android:layout_width="30dp"
                        android:layout_height="30dp"
                        android:padding="@dimen/padding_horizontal"
                        android:src="@drawable/ic_close_svg" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/poppins_semibold"
                        android:text="@string/string_close_clear"
                        android:textColor="@color/white"
                        android:textSize="14sp" />

                </LinearLayout>
            </LinearLayout>
        </LinearLayout>

    </LinearLayout>


</LinearLayout>