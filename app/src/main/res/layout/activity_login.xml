<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:id="@+id/linearLayout"
    android:background="@drawable/right_bg_menu"
    tools:context=".ui.startup.LoginActivity">
    <LinearLayout
        android:layout_width="match_parent"
        android:gravity="top"
        android:orientation="vertical"
        android:layout_marginVertical="100dp"
        android:layout_marginHorizontal="300dp"
        android:paddingBottom="@dimen/dp10"
        android:background="@drawable/ic_transparent_rectangle"
        android:layout_height="wrap_content">

        <LinearLayout
            android:id="@+id/use_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:visibility="gone"
            android:weightSum="3">
            <CheckBox
                android:id="@+id/localCheckbox"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="local use "
                android:onClick="onCheckboxClicked"/>
            <CheckBox
                android:id="@+id/servermasterCheckbox"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Server Master "
                android:onClick="onCheckboxClicked"/>
            <CheckBox
                android:id="@+id/localmasterCheckbox"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="local Master "
                android:onClick="onCheckboxClicked"/>
        </LinearLayout>
        <ImageView
            android:id="@+id/logoImage"
            android:layout_width="match_parent"
            android:layout_gravity="top"
            android:layout_marginTop="50dp"
            android:src="@drawable/app_logo"
            android:layout_height="100sp"
            android:contentDescription="@string/logo">

        </ImageView>
        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_marginTop="50dp"
            style="@style/Widget.MaterialComponents.TextInputLayout.FilledBox"
            android:layout_marginHorizontal="@dimen/dp50"
            app:hintTextColor="@color/colorPrimaryDark"
            android:layout_height="wrap_content">

            <androidx.appcompat.widget.AppCompatEditText
                android:id="@+id/etUsername"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="start|center"
                android:background="@color/white"
                android:hint="User Name"
                android:text="rht"
                android:textColor="@color/black"
                android:inputType="textPersonName"
                tools:ignore="HardcodedText" />
        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_marginTop="20dp"
            style="@style/Widget.MaterialComponents.TextInputLayout.FilledBox"
            android:layout_marginHorizontal="@dimen/dp50"
            app:hintTextColor="@color/colorPrimaryDark"
            android:layout_height="wrap_content">

            <androidx.appcompat.widget.AppCompatEditText
                android:id="@+id/etPassword"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="start|center"
                android:background="@color/white"
                android:hint="Password"
                android:text="qwerty"
                android:textColor="@color/black"
                android:inputType="textPassword"
                tools:ignore="HardcodedText" />
        </com.google.android.material.textfield.TextInputLayout>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_marginTop="@dimen/dp20"
            android:gravity="center"
            android:weightSum="10"
            android:layout_marginHorizontal="100dp"
            android:layout_height="match_parent">
            <Button
                android:layout_width="0dp"
                android:layout_weight="5"
                android:textColor="@color/white"
                android:backgroundTint="@color/redColor"
                android:text="@string/label_cancel"
                android:layout_height="@dimen/dp50"
                style="?android:attr/buttonBarButtonStyle">
            </Button>
            <Button
                android:id="@+id/loginBtn"
                android:layout_width="0dp"
                android:layout_weight="5"
                android:layout_marginStart="@dimen/dp20"
                android:textColor="@color/white"
                android:backgroundTint="@color/greenColor"
                android:text="@string/label_login"
                android:layout_height="@dimen/dp50"
                style="?android:attr/buttonBarButtonStyle">
            </Button>
        </LinearLayout>
        <LinearLayout
            android:layout_width="match_parent"
            android:gravity="center"
            android:orientation="horizontal"

            android:layout_height="wrap_content">
            <androidx.appcompat.widget.AppCompatTextView
                android:layout_width="wrap_content"
                android:text="@string/label_forgot_password"
                android:gravity="center"
                android:textColor="@color/black"
                android:textSize="14sp"
                android:layout_marginVertical="@dimen/dp10"
                android:fontFamily="@font/poppins_semibold"
                android:layout_height="wrap_content">
            </androidx.appcompat.widget.AppCompatTextView>

            <androidx.appcompat.widget.AppCompatTextView
                android:layout_width="wrap_content"
                android:text="@string/reset_here"
                android:gravity="center"
                android:textColor="@color/blue"
                android:layout_marginStart="@dimen/dp10"
                android:textSize="14sp"
                android:layout_marginVertical="@dimen/dp10"
                android:fontFamily="@font/poppins_semibold"
                android:layout_height="wrap_content">
            </androidx.appcompat.widget.AppCompatTextView>
        </LinearLayout>

    </LinearLayout>


</LinearLayout>