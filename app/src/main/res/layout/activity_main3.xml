<?xml version="1.0" encoding="utf-8"?>

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="@dimen/activity_horizontal_margin">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:gravity="center"
            android:orientation="horizontal">

            <ImageView
                android:layout_width="40px"
                android:layout_height="40px"
                android:src="@drawable/smart_contact_ic" />

            <TextView
                android:id="@+id/contact_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/activity_horizontal_margin"
                android:textColor="@color/blue_txt"
                android:textSize="14dp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/customer_type"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginLeft="@dimen/activity_left_margin"
                />

            <Button
                android:id="@+id/id_back"
                style="@style/my_btn_style"
                android:layout_width="wrap_content"
                android:layout_height="70px"
                android:background="@drawable/rounded_btn_blue"
                android:text="@string/label_close" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Adress "
                android:textColor="@color/blue_txt"
                android:textSize="12dp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/customer_adress"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/activity_left_margin"
                android:text=" " />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/label_mobile"
                android:textColor="@color/blue_txt"
                android:textSize="12dp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/customer_mobile"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/activity_left_margin"
                android:text=" " />
        </LinearLayout>
    </LinearLayout>
    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_weight="1"
        tools:context=".ui.dailyReports.DetailReport.DDetailReportMain">

        <com.google.android.material.appbar.AppBarLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/white">

            <com.google.android.material.appbar.CollapsingToolbarLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layout_scrollFlags="scroll|snap|exitUntilCollapsed">


                <com.google.android.material.tabs.TabLayout
                    android:id="@+id/MyTabs"
                    app:tabTextColor="@color/black"
                    app:tabTextAppearance="@style/MyCustomTextAppearance"
                    android:layout_width="match_parent"
                    android:layout_height="?attr/actionBarSize"
                    app:layout_collapseMode="parallax"
                    app:tabGravity="center"
                    app:tabIndicatorColor="@android:color/black"
                    app:tabMode="scrollable"
                    app:tabSelectedTextColor="@android:color/black" />
            </com.google.android.material.appbar.CollapsingToolbarLayout>
        </com.google.android.material.appbar.AppBarLayout>

        <LinearLayout
            android:id="@+id/linearLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            app:layout_behavior="@string/appbar_scrolling_view_behavior">

            <include
                layout="@layout/content_main_detail_report"
                app:layout_behavior="@string/appbar_scrolling_view_behavior" />
        </LinearLayout>


    </androidx.coordinatorlayout.widget.CoordinatorLayout>
</LinearLayout>