<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools">

    <LinearLayout
        android:id="@+id/linearLayout"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="3dp"
            android:backgroundTint="#445DCD"
            android:elevation="5dp"
            app:cardElevation="5dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:weightSum="8"
                android:orientation="horizontal"
                android:padding="10dp">
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="2"
                android:orientation="vertical">
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="2"
                    android:paddingHorizontal="3dp"
                    android:textSize="@dimen/dp12"
                    android:gravity="center"
                    android:text="@string/label_total_items"
                    android:textAppearance="@style/TextAppearance.AppCompat.Small"
                    android:textColor="@color/white" />
                <TextView
                    android:id="@+id/id_total_items"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="2"
                    android:gravity="center"
                    android:paddingHorizontal="3dp"
                    android:textSize="@dimen/dp12"
                    android:text="00"
                    android:textStyle="bold"
                    android:textAppearance="@style/TextAppearance.AppCompat.Small"
                    android:textColor="@color/excel_line2" />
            </LinearLayout>



                <View
                    android:layout_width="1dp"
                    android:layout_marginHorizontal="5dp"
                    android:layout_height="match_parent"
                    android:background="@color/white" />
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="2"
                android:orientation="vertical">
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="2"
                    android:gravity="center"
                    android:textSize="@dimen/dp12"
                    android:text="@string/label_total_payable_items"
                    android:textColor="@color/white" />
                <TextView
                    android:id="@+id/id_total_payable"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="2"
                    android:gravity="center"
                    android:paddingHorizontal="3dp"
                    android:textSize="@dimen/dp12"
                    android:text="00"
                    android:textStyle="bold"
                    android:textAppearance="@style/TextAppearance.AppCompat.Small"
                    android:textColor="@color/excel_line2" />
            </LinearLayout>

                <View
                    android:layout_width="1dp"
                    android:layout_height="match_parent"
                    android:background="@color/white" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="2"
                android:orientation="vertical">
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="2"
                    android:textSize="@dimen/dp12"
                    android:gravity="center"
                    android:text="@string/label_total_paying"
                    android:textColor="@color/white" />

                <TextView
                    android:id="@+id/id_total_paying"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="2"
                    android:gravity="center"
                    android:paddingHorizontal="3dp"
                    android:textSize="@dimen/dp12"
                    android:text="00"
                    android:textStyle="bold"
                    android:textAppearance="@style/TextAppearance.AppCompat.Small"
                    android:textColor="@color/excel_line2" />
            </LinearLayout>


                <View
                    android:layout_width="1dp"
                    android:layout_height="match_parent"
                    android:background="@color/white" />
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="2"
                android:orientation="vertical">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textSize="@dimen/dp12"
                    android:layout_weight="2"
                    android:gravity="center"
                    android:text="@string/label_return_change"
                    android:textColor="@color/white" />

                <TextView
                    android:id="@+id/id_return_change"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="2"
                    android:gravity="center"
                    android:paddingHorizontal="3dp"
                    android:textSize="@dimen/dp12"
                    android:text="00"
                    android:textStyle="bold"
                    android:textAppearance="@style/TextAppearance.AppCompat.Small"
                    android:textColor="@color/excel_line2" />

            </LinearLayout>

            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:paddingTop="10dp">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/id_recycle"
                    android:layout_width="match_parent"
                    tools:listitem="@layout/item_multy_pay"
                    android:layout_height="match_parent" />

                <Button
                    android:id="@+id/id_add_payement"
                    style="@style/my_btn_style"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:backgroundTint="#445DCD"
                    android:padding="5dp"
                    android:text="@string/label_add_payement_row"
                    android:textColor="@color/white" />
            </LinearLayout>
        </ScrollView>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <Button
                android:id="@+id/id_cancel_btn"
                style="@style/my_btn_style"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginRight="10dp"
                android:layout_weight="2"
                android:padding="5dp"
                android:backgroundTint="#445DCD"
                android:text="@string/label_cancel"
                android:textColor="@color/white" />

            <Button
                android:id="@+id/id_btn_multi_payement"
                style="@style/my_btn_style"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                android:layout_weight="1"
                android:backgroundTint="#445DCD"
                android:padding="5dp"
                android:text="@string/label_finalize_pay"
                android:textColor="@color/white" />

        </LinearLayout>
    </LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>