<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp60"
        android:background="@drawable/ic_seach_background"
        android:gravity="center"
        android:orientation="horizontal"
        android:padding="3dp">

        <ImageView
            android:id="@+id/btn_scan"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_margin="@dimen/dp5"
            android:layout_marginEnd="@dimen/activity_left_margin"
            android:contentDescription="@string/scan"
            android:src="@drawable/ic_barcode"
            app:tint="@color/white" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="@dimen/dp50"
            android:layout_margin="@dimen/dp5"
            android:layout_weight="1"
            android:orientation="horizontal">

            <androidx.appcompat.widget.AppCompatEditText
                android:id="@+id/id_search_edit"
                android:layout_width="0dp"
                android:layout_height="@dimen/dp50"
                android:layout_weight="1"
                android:background="@color/white"
                android:hint="@string/label_search_hint"
                android:maxLines="1"
                android:paddingStart="@dimen/dp10"
                tools:ignore="NestedWeights,RtlSymmetry" />

            <androidx.appcompat.widget.AppCompatImageView
                android:layout_width="@dimen/dp50"
                android:layout_height="match_parent"
                android:layout_gravity="center"
                android:background="@color/grey05"
                android:padding="8dp"
                android:src="@drawable/search"
                app:tint="@color/white" />
        </LinearLayout>


    </LinearLayout>

    <RelativeLayout
        android:id="@+id/no_report_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="visible">

        <androidx.appcompat.widget.AppCompatImageView
            android:layout_width="match_parent"
            android:layout_height="350dp"
            android:layout_marginTop="@dimen/dp50"
            android:src="@drawable/ic_product" />


        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_gravity="center"
            android:layout_marginBottom="80dp"
            android:fontFamily="@font/poppins_semibold"
            android:gravity="center"
            android:text="No Product Found"
            android:textColor="@color/black"
            android:textSize="25sp" />

        <LinearLayout
            android:id="@+id/addProductBtn"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp50"
            android:layout_alignParentBottom="true"
            android:layout_gravity="bottom|center"
            android:layout_marginTop="@dimen/dp60"
            android:layout_marginBottom="@dimen/dp10"
            android:gravity="bottom|center"
            android:orientation="horizontal">

            <androidx.appcompat.widget.AppCompatTextView
                android:layout_width="400dp"
                android:layout_height="match_parent"
                android:background="@drawable/bg_circle_button_box"
                android:gravity="center"
                android:text="Add Product"
                android:textColor="#fff"
                android:textStyle="bold" />


        </LinearLayout>

    </RelativeLayout>

    <LinearLayout
        android:id="@+id/productCategoryLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:visibility="visible">

        <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
            android:id="@+id/fragment_category_swipe_refresh"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginVertical="5dp"
            android:layout_weight="0.4">

            <GridView
                android:id="@+id/id_grid_category"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:listSelector="#0f0"
                android:numColumns="4"
                tools:listitem="@layout/dry_item_category" />
        </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="4">

            <GridView
                android:id="@+id/id_grid_product"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_weight="2"
                android:numColumns="4"
                android:paddingVertical="5dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.0"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.0"
                tools:listitem="@layout/dry_article_item" />

            <com.github.ybq.android.spinkit.SpinKitView
                android:id="@+id/spin_kit"
                style="@style/SpinKitView.Large.MultiplePulseRing"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:visibility="gone"
                app:SpinKit_Color="@color/colorPrimary"
                app:layout_constraintBottom_toBottomOf="@+id/id_grid_product"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="@+id/id_grid_product"
                app:layout_constraintTop_toTopOf="@+id/id_grid_product" />

            <TextView
                android:id="@+id/error_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/no_product_found"
                android:textColor="@color/red"
                android:textStyle="bold"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="@+id/id_grid_product"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="@+id/id_grid_product"
                app:layout_constraintTop_toTopOf="@+id/id_grid_product" />
        </androidx.constraintlayout.widget.ConstraintLayout>

    </LinearLayout>


</LinearLayout>
