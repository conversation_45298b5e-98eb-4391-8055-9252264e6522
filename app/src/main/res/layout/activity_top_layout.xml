<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/layer_top"
    android:gravity="center"
    android:orientation="vertical">


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center"
        android:orientation="horizontal"
        android:paddingHorizontal="@dimen/padding_top">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_weight="1"

            android:orientation="horizontal">

            <ImageView
                android:id="@+id/menu_button"
                android:layout_width="50dp"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_baseline_menu_24" />

            <ImageView
                android:id="@+id/logo"
                android:layout_width="92dp"
                android:layout_height="@dimen/dp50"
                android:layout_marginVertical="@dimen/dp10"
                android:layout_marginStart="@dimen/dp20"
                android:src="@drawable/app_logo" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:id="@+id/currentDateTime"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_weight="1"
                android:fontFamily="@font/poppins_semibold"
                android:text="08:00 AM / Feb. 16, 2021"
                android:textAlignment="center"
                android:textColor="@color/white"
                android:textSize="18sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/currentLocation"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_weight="1"
                android:fontFamily="@font/poppins_semibold"
                android:text="(CASABLANCA , MORROCO)"
                android:textAlignment="center"
                android:textColor="@color/white"
                android:textSize="14sp"
                android:textStyle="bold" />


        </LinearLayout>


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_weight="1"
            android:gravity="end"
            android:orientation="horizontal">


            <androidx.appcompat.widget.AppCompatSpinner
                android:id="@+id/spinner_station"
                android:layout_weight="1"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp50"
                android:layout_gravity="center"
                android:layout_marginHorizontal="@dimen/dp5"
                android:entries="@array/array_action_contact" />


            <LinearLayout
                android:id="@+id/profile_container"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:orientation="horizontal">
                <ImageView
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:layout_gravity="center"
                    android:padding="@dimen/padding_horizontal"
                    android:src="@drawable/profile_ic" />

                <TextView
                    android:id="@+id/user_txt"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:fontFamily="@font/poppins_semibold"
                    android:text="Administrator"
                    android:textColor="@color/white"
                    android:textSize="14dp" />


            </LinearLayout>
            <ImageView
                android:id="@+id/right_menu_button"
                android:layout_width="50dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginHorizontal="@dimen/dp10"
                android:src="@drawable/ic_baseline_apps_30" />
        </LinearLayout>


    </LinearLayout>

</LinearLayout>