<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/content"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/transparent"
    android:orientation="vertical"
    android:padding="20dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/rounded_white_bg"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="@dimen/activity_vertical_margin">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"

                android:layout_weight="1"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:weightSum="10"
                    android:layout_height="wrap_content"
                    android:paddingVertical="@dimen/activity_left_margin">

                    <androidx.appcompat.widget.AppCompatTextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:text="@string/label_ajout_contact"
                        android:layout_weight="9.5"
                        android:textSize="16sp"
                        android:textColor="@color/black"
                        android:textStyle="bold" />
                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/btn_close"
                        android:layout_width="0dp"
                        android:layout_weight="0.5"
                        app:tint="@color/blue"
                        android:src="@drawable/ic_close_svg"
                        android:layout_height="match_parent">

                    </androidx.appcompat.widget.AppCompatImageView>
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginRight="@dimen/activity_left_margin"
                        android:layout_weight="1"
                        android:background="@color/white_blue"

                        android:orientation="vertical"
                        android:padding="@dimen/padding_horizontal">

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:paddingTop="@dimen/margin_vertical">


                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/label_supplier"
                                android:textStyle="bold" />

                            <TextView
                                android:id="@+id/supplier_name"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginLeft="5dp" />

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:paddingTop="@dimen/margin_vertical">


                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/label_mobile"

                                android:textStyle="bold" />

                            <TextView
                                android:id="@+id/supplier_phone"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginLeft="5dp" />

                        </LinearLayout>

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginHorizontal="@dimen/activity_left_margin"
                        android:layout_weight="1"
                        android:background="@color/white_blue"
                        android:orientation="vertical"
                        android:padding="@dimen/padding_horizontal">

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:paddingTop="@dimen/margin_vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/label_business_location"
                                android:textStyle="bold" />

                            <TextView
                                android:id="@+id/business_location"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginLeft="5dp" />

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:orientation="horizontal"
                            android:paddingTop="@dimen/margin_vertical">


                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/label_mobile"

                                android:textStyle="bold" />

                            <TextView
                                android:id="@+id/business_phone"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginLeft="5dp" />

                        </LinearLayout>

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginLeft="@dimen/activity_left_margin"
                        android:layout_weight="1"
                        android:background="@color/white_blue"
                        android:orientation="vertical"
                        android:padding="@dimen/padding_horizontal">


                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"

                            android:orientation="horizontal"
                            android:paddingTop="@dimen/margin_vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/label_montant_total"
                                android:textStyle="bold" />

                            <TextView
                                android:id="@+id/total_amount"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginLeft="5dp" />

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:paddingTop="@dimen/margin_vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/string_payement_note"
                                android:textStyle="bold" />

                            <TextView
                                android:id="@+id/purchase_note"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginLeft="5dp" />

                        </LinearLayout>


                    </LinearLayout>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:weightSum="4">

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp20"
                        android:layout_weight="1">

                        <androidx.appcompat.widget.AppCompatEditText
                            android:id="@+id/amount_txt"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:hint="@string/label_montant_total_montant"
                            android:inputType="number" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp10"
                        android:layout_marginTop="@dimen/dp20"
                        android:layout_weight="1">

                        <androidx.appcompat.widget.AppCompatEditText
                            android:id="@+id/paid_on_txt"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:hint="@string/image_ligne_paye"
                            android:inputType="number" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <FrameLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp10"
                        android:layout_marginTop="@dimen/dp20"
                        android:layout_weight="1">

                        <androidx.appcompat.widget.AppCompatSpinner
                            android:id="@+id/spin_payment_method"
                            android:layout_width="match_parent"
                            android:layout_height="@dimen/default_spinner_height"
                            android:layout_marginTop="5dp"
                            android:entries="@array/payment_method_array"
                            android:textSize="12sp"
                            android:textStyle="normal" />

                        <androidx.appcompat.widget.AppCompatTextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp_10"
                            android:background="@color/white"
                            android:paddingLeft="4dp"
                            android:paddingRight="4dp"
                            android:text="@string/label_spaymenet_method"
                            android:textSize="12sp"
                            android:visibility="visible" />
                    </FrameLayout>

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp10"
                        android:layout_marginTop="@dimen/dp20"
                        android:layout_weight="1">

                        <androidx.appcompat.widget.AppCompatEditText
                            android:id="@+id/payment_note"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:hint="@string/string_payement_note" />

                    </com.google.android.material.textfield.TextInputLayout>
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:weightSum="5"
                    android:paddingTop="@dimen/activity_horizontal_margin">


                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/id_image_add_attach"
                        android:layout_width="0dp"
                        android:layout_height="60dp"
                        android:layout_weight="1.3"
                        android:background="@drawable/border_gray"
                        android:drawableEnd="@drawable/ic_baseline_add_24"
                        android:gravity="center"
                        android:text="@string/string_attach_doc"
                        android:textSize="16sp">

                    </androidx.appcompat.widget.AppCompatTextView>

                    <Gallery
                        android:id="@+id/gallery"
                        android:layout_width="0dp"
                        android:layout_height="60dp"
                        android:layout_marginStart="@dimen/dp10"
                        android:layout_weight="0.7"
                        android:gravity="start" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:orientation="horizontal">

                    <androidx.appcompat.widget.AppCompatButton
                        android:id="@+id/btn_save"
                        android:layout_width="wrap_content"
                        android:layout_height="@dimen/dp40"
                        android:background="@drawable/bg_gradient"
                        android:layout_margin="@dimen/dp10"

                        android:text="@string/label_save" />

                </LinearLayout>

            </LinearLayout>


        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/cell_shape_border"
            android:padding="@dimen/activity_vertical_margin"
            android:visibility="gone">


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginHorizontal="@dimen/activity_left_margin"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/lbl_alert_exc_tax"
                    android:textColor="@color/blue_txt" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:gravity="center"
                    android:orientation="horizontal"
                    android:paddingTop="@dimen/margin_vertical">

                    <ImageView
                        android:layout_width="30px"
                        android:layout_height="30px"
                        android:layout_weight="1"
                        android:src="@drawable/icons_contacts" />

                    <TextView
                        android:id="@+id/id_exc_tax"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="5dp"
                        android:layout_weight="1"
                        android:inputType="numberDecimal" />

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginHorizontal="@dimen/activity_left_margin"
                android:layout_weight="1"
                android:orientation="vertical"
                android:visibility="visible">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/lbl_alert_inc_tax"
                    android:textColor="@color/blue_txt" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:gravity="center"
                    android:orientation="horizontal"
                    android:paddingTop="@dimen/margin_vertical">

                    <ImageView
                        android:layout_width="30px"
                        android:layout_height="30px"
                        android:layout_weight="1"
                        android:src="@drawable/icons_contacts" />

                    <TextView
                        android:id="@+id/id_inc_tax"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="5dp"
                        android:layout_weight="1"

                        android:inputType="numberDecimal" />
                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginHorizontal="@dimen/activity_left_margin"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/label_product_margin"
                    android:textColor="@color/blue_txt" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:gravity="center"
                    android:orientation="horizontal"
                    android:paddingTop="@dimen/margin_vertical">

                    <ImageView
                        android:layout_width="30px"
                        android:layout_height="30px"
                        android:layout_weight="1"
                        android:src="@drawable/icons_contacts" />

                    <TextView
                        android:id="@+id/id_margin"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="5dp"
                        android:layout_weight="1"

                        android:inputType="numberDecimal"
                        android:text="25.00" />
                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginHorizontal="@dimen/activity_left_margin"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/string_default_price_vente_exc"
                    android:textColor="@color/blue_txt" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:orientation="horizontal"
                    android:paddingTop="@dimen/margin_vertical">

                    <ImageView
                        android:layout_width="30px"
                        android:layout_height="30px"
                        android:src="@drawable/icons8_location" />

                    <TextView
                        android:id="@+id/id_default_sel_price_exc_tax"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="5dp"
                        android:layout_weight="1"

                        android:inputType="numberDecimal" />
                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginHorizontal="@dimen/activity_left_margin"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/string_default_price_vente_inc"
                    android:textColor="@color/blue_txt" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:orientation="horizontal"
                    android:paddingTop="@dimen/margin_vertical">

                    <ImageView
                        android:layout_width="30px"
                        android:layout_height="30px"
                        android:src="@drawable/icons8_location" />

                    <TextView
                        android:id="@+id/default_sell_inc_tax"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="5dp"
                        android:layout_weight="1"

                        android:inputType="numberDecimal" />
                </LinearLayout>

            </LinearLayout>


        </LinearLayout>

    </LinearLayout>


</LinearLayout>