<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:gravity="center"
        android:background="@drawable/cell_shape_ntop"
        android:orientation="horizontal"
        android:padding="20px">


        <TextView
            android:id="@+id/id_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/city_id"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/state_id"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/country_id"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/zip_code_id"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textStyle="bold" />


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center">

            <androidx.appcompat.widget.AppCompatImageView
                android:visibility="gone"
                android:id="@+id/btn_delete"
                android:layout_width="@dimen/dp50"
                android:layout_height="50dp"
                android:layout_gravity="center"
                android:src="@drawable/ic_delete_svg"
                android:gravity="center"
                android:padding="@dimen/dp10"
                />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/btn_edit"
                android:layout_width="@dimen/dp50"
                android:layout_height="@dimen/dp50"
                android:layout_marginStart="@dimen/dp10"
                android:tint="@color/skyBlue"
                android:src="@drawable/ic_edit_filled"
                android:gravity="start"
                android:padding="@dimen/dp10"
                />
        </LinearLayout>

<!--        <LinearLayout-->
<!--            android:layout_width="match_parent"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:gravity="right"-->
<!--            android:layout_weight="1">-->
<!--            <Button-->
<!--                android:id="@+id/btn_delete"-->
<!--                android:layout_width="wrap_content"-->
<!--                android:layout_height="wrap_content"-->
<!--                android:drawableLeft="@drawable/ic_baseline_delete_outline_24"-->
<!--                style="@style/my_btn_style"-->
<!--                android:layout_gravity="center"-->
<!--                android:gravity="center"-->
<!--                android:text="@string/lbl_action_delete"/>-->

<!--            <Button-->
<!--                android:id="@+id/btn_edit"-->
<!--                android:layout_width="wrap_content"-->
<!--                android:layout_height="wrap_content"-->
<!--                android:gravity="center"-->
<!--                android:drawableLeft="@drawable/ic_baseline_edit_24"-->
<!--                android:background="@drawable/rounded_btn_bg_orange"-->
<!--                style="@style/my_btn_style"-->
<!--                android:text="@string/lbl_action_edit"/>-->
<!--        </LinearLayout>-->


    </LinearLayout>
</LinearLayout>