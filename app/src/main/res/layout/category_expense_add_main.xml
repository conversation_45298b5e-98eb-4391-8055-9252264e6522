<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
xmlns:app="http://schemas.android.com/apk/res-auto"
android:id="@+id/content"
android:layout_width="wrap_content"
android:layout_height="match_parent"
android:background="@color/transparent"
android:orientation="vertical"
android:padding="20dp">

<LinearLayout
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/rounded_white_bg"
    android:orientation="vertical"
    android:padding="@dimen/activity_vertical_margin">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingVertical="@dimen/activity_left_margin"
        android:weightSum="10">

        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="9.5"
            android:text="@string/label_add_category_expensee"
            android:textColor="@color/black"
            android:textSize="16sp"
            android:textStyle="bold" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/btn_close"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="0.5"
            android:src="@drawable/ic_close_svg"
            app:tint="@color/blue"></androidx.appcompat.widget.AppCompatImageView>
    </LinearLayout>

    <View
        android:layout_width="wrap_content"
        android:layout_height="1dp"
        android:background="@color/blue">

    </View>

    <com.google.android.material.textfield.TextInputLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"

        android:layout_marginTop="@dimen/dp20">

        <androidx.appcompat.widget.AppCompatEditText
            android:id="@+id/catgeroy_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:hint="@string/label_ucatg_nmae" />

    </com.google.android.material.textfield.TextInputLayout>

    <com.google.android.material.textfield.TextInputLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp20">

        <androidx.appcompat.widget.AppCompatEditText
            android:id="@+id/id_category_code"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:hint="@string/label_categiry_code"
            android:inputType="number" />

    </com.google.android.material.textfield.TextInputLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="horizontal">

        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/btn_save"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dp40"
            android:layout_marginHorizontal="@dimen/dp10"
            android:layout_marginTop="@dimen/dp5"
            android:background="@drawable/bg_gradient"
            android:text="@string/label_save" />


    </LinearLayout>

</LinearLayout>
</LinearLayout>

