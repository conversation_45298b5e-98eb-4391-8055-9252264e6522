<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:background="@drawable/cell_shape_ntop"
        android:gravity="center"
        android:orientation="horizontal"
        android:padding="20px">


        <TextView
            android:id="@+id/category_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/category_code"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textStyle="bold" />


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="right">

            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/btn_delete"
                android:layout_width="@dimen/dp100"
                android:layout_height="@dimen/dp40"
                android:layout_gravity="center"
                android:background="@drawable/red_button_gradient"
                android:drawableStart="@drawable/ic_baseline_delete_outline_24"
                android:gravity="center"
                android:padding="@dimen/dp10"
                android:text="@string/lbl_action_delete"
                android:textStyle="bold" />

            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/btn_edit"
                android:layout_width="@dimen/dp100"
                android:layout_height="@dimen/dp40"
                android:layout_marginStart="@dimen/dp10"
                android:drawableStart="@drawable/ic_baseline_edit_24"
                android:drawableTint="@color/white"
                android:gravity="center"
                android:padding="@dimen/dp10"
                android:text="@string/lbl_action_edit" />
        </LinearLayout>

    </LinearLayout>
</LinearLayout>