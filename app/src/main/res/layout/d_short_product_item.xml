<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/linearLayout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="horizontal"
        android:paddingVertical="@dimen/dp10"
        android:weightSum="4">

        <TextView
            android:id="@+id/id_article_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Article"
            android:textAppearance="@style/TextAppearance.AppCompat.Body2"
            android:textColor="@color/black"
            android:textSize="12sp" />

        <EditText
            android:id="@+id/id_qt"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="0.5"
            android:inputType="number"
            android:text="0"
            android:textColor="@color/black" />

        <TextView
            android:id="@+id/id_unit"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="0.5"
            android:gravity="bottom"
            android:textColor="@color/black"
            tools:text="TextView" />

        <EditText
            android:id="@+id/id_pu"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp10"
            android:layout_weight="1"
            android:inputType="numberDecimal"
            android:text="00"
            android:textColor="@color/black" />

        <LinearLayout
            android:layout_width="@dimen/dp0"
            android:layout_height="wrap_content"
            android:layout_weight="0.8"
            android:orientation="horizontal">
            <TextView
                android:id="@+id/id_sub_total"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp5"
                android:gravity="start"
                android:text="00"
                android:textColor="@color/black"
                android:textStyle="bold" />
            <TextView
                android:id="@+id/currency"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp5"
                android:gravity="start"
                android:textColor="@color/black"
                android:textStyle="bold" />

        </LinearLayout>


        <ImageView
            android:id="@+id/id_delete"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_gravity="end"
            android:layout_marginLeft="5dp"
            android:layout_marginRight="5dp"
            android:layout_weight="0.2"
            android:src="@drawable/quit" />

    </LinearLayout>
</LinearLayout>