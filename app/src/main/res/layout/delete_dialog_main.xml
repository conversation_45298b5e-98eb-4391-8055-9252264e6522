<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/content"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/transparent"
    android:orientation="vertical"

   >

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:background="@drawable/rounded_white_bg"
        android:orientation="vertical"
        android:padding="@dimen/activity_vertical_margin">
        <ImageView
            android:layout_width="@dimen/dp100"
            android:src="@drawable/ic_delete_bin"
            android:layout_height="@dimen/dp100">

        </ImageView>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center"
            android:layout_marginTop="@dimen/dp20"
            android:paddingVertical="@dimen/activity_left_margin">

            <androidx.appcompat.widget.AppCompatTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="20sp"
                android:text="@string/txt_add_are_you_sure"
                android:textColor="@color/black"
                android:textStyle="bold" />
            <androidx.appcompat.widget.AppCompatTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp20"
                android:textSize="16sp"
                android:text="@string/txt_add_are_you_sure_txt"
                android:textColor="@color/black"
             />
        </LinearLayout>


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:weightSum="10"
            android:layout_marginTop="@dimen/dp20"
            android:orientation="horizontal">

            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/btn_close"
                android:layout_width="200dp"
                android:layout_height="@dimen/dp50"
                android:textStyle="bold"
                android:backgroundTint="#c1c1c1"
                android:text="@string/label_cancel" />
            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/btn_save"
                android:backgroundTint="#f15e5e"
                android:layout_width="200dp"
                android:textStyle="bold"
                android:layout_marginStart="@dimen/dp10"
                android:layout_height="@dimen/dp50"
                android:text="@string/delete" />
        </LinearLayout>

    </LinearLayout>
</LinearLayout>