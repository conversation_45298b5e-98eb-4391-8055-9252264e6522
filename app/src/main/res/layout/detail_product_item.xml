<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <LinearLayout
        android:id="@+id/linearLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:gravity="center"
        android:orientation="horizontal"
        android:paddingHorizontal="@dimen/padding_horizontal"
        android:paddingVertical="@dimen/dp10"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/product_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textColor="@color/black" />

        <TextView
            android:id="@+id/id_price"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_weight="1"
            android:textAlignment="center"
            android:textColor="@color/black" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="0.9"
            android:gravity="center"
            android:orientation="horizontal"
            android:paddingHorizontal="5dp">

            <ImageButton
                android:id="@+id/image_button_rem"
                android:layout_width="@dimen/dp30"
                android:layout_height="@dimen/dp30"
                android:background="@drawable/ic_baseline_indeterminate_check_box_24" />

            <TextView
                android:id="@+id/id_quantity"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:enabled="false"
                android:gravity="center"
                android:minWidth="30dp"
                android:minHeight="30dp"
                android:text="1"
                android:textColor="@color/black" />

            <ImageButton
                android:id="@+id/image_button_add"
                android:layout_width="@dimen/dp30"
                android:layout_height="@dimen/dp30"
                android:background="@drawable/ic_baseline_add_box_24" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_weight="1"
            android:gravity="center"
            android:orientation="horizontal"
            android:paddingHorizontal="5dp">

            <TextView
                android:id="@+id/total_price"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_weight="1"
                android:gravity="center"
                android:lines="2"
                android:text="$1.00"
                android:textColor="@color/black" />

            <ImageButton
                android:id="@+id/btn_delete"
                android:layout_width="@dimen/dp30"
                android:layout_height="@dimen/dp30"
                android:background="@drawable/ic_baseline_clear_24" />

        </LinearLayout>


    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>