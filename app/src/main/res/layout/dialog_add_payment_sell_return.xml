<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/content"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/transparent"
    android:orientation="vertical"
  >

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/rounded_white_bg"
        android:orientation="vertical"
        android:padding="@dimen/activity_vertical_margin">
        <LinearLayout
            android:layout_width="match_parent"
            android:weightSum="10"
            android:layout_height="wrap_content"
            android:paddingVertical="@dimen/activity_left_margin">

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/addPaymentTxt"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:text="@string/label_ajout_contact"
                android:layout_weight="9.5"
                android:textSize="16sp"
                android:textColor="@color/black"
                android:textStyle="bold" />
            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/btnClose"
                android:layout_width="0dp"
                android:layout_weight="0.5"
                app:tint="@color/blue"
                android:src="@drawable/ic_close_svg"
                android:layout_height="match_parent">

            </androidx.appcompat.widget.AppCompatImageView>
        </LinearLayout>
        <View
            android:layout_width="wrap_content"
            android:background="@color/blue"
            android:layout_height="1dp">
        </View>

        <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_marginTop="@dimen/dp20"
                    android:weightSum="3"
                    android:layout_height="wrap_content">

                    <androidx.cardview.widget.CardView
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginHorizontal="@dimen/activity_left_margin"
                        android:layout_weight="1"
                        app:cardBackgroundColor="@color/greyLight"
                       >

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:orientation="horizontal"
                            android:padding="@dimen/dp10"
                            android:gravity="center|start"
                  >

                            <androidx.appcompat.widget.AppCompatTextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:textColor="@color/black"
                                android:text="@string/customer_label"
                                android:textStyle="bold" />

                              <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/customerName"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                  android:textColor="@color/black"
                                tools:text="TextView"
                                android:layout_marginStart="5dp" />

                        </LinearLayout>


                    </androidx.cardview.widget.CardView>

                    <androidx.cardview.widget.CardView
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginHorizontal="@dimen/activity_left_margin"
                        android:layout_weight="1"
                        app:cardBackgroundColor="@color/greyLight"
                        android:orientation="vertical"
                        >
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginHorizontal="@dimen/activity_left_margin"
                        android:layout_weight="1"
                        android:orientation="vertical"
                        android:padding="@dimen/dp10"
                        android:gravity="center|start">

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:paddingTop="@dimen/margin_vertical">

                              <androidx.appcompat.widget.AppCompatTextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/label_facture_no"
                                android:textColor="@color/black"
                                android:textStyle="bold" />

                              <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/invoiceNo"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                  android:textColor="@color/black"
                                tools:text="6757757"
                                android:layout_marginStart="5dp" />

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:orientation="horizontal"
                            android:paddingTop="@dimen/margin_vertical">


                              <androidx.appcompat.widget.AppCompatTextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                  android:textColor="@color/black"
                                android:text="@string/business_location_label"
                                android:textStyle="bold" />

                              <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/businessLocation"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="5dp" />

                        </LinearLayout>

                    </LinearLayout>
                    </androidx.cardview.widget.CardView>

                    <androidx.cardview.widget.CardView
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginHorizontal="@dimen/activity_left_margin"
                        android:layout_weight="1"
                        app:cardBackgroundColor="@color/greyLight"
                        android:orientation="vertical"
                        >
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginHorizontal="@dimen/activity_left_margin"
                            android:layout_weight="1"
                            android:orientation="vertical"
                            android:padding="@dimen/dp10"
                            android:gravity="center|start">
                            
                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:paddingTop="@dimen/margin_vertical">

                              <androidx.appcompat.widget.AppCompatTextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                  android:textColor="@color/black"
                                android:text="@string/label_montant_total"
                                android:textStyle="bold" />

                              <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/totalAmount"
                                android:layout_width="wrap_content"
                                  android:textColor="@color/black"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="5dp" />

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:paddingTop="@dimen/margin_vertical">

                              <androidx.appcompat.widget.AppCompatTextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                  android:textColor="@color/black"
                                android:text="@string/string_payement_note"
                                android:textStyle="bold" />

                              <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/paymentNote"
                                android:layout_width="wrap_content"
                                  android:textColor="@color/black"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="5dp" />

                        </LinearLayout>


                    </LinearLayout>
                    </androidx.cardview.widget.CardView>
                </LinearLayout>
        <LinearLayout
            android:layout_width="match_parent"
            android:orientation="horizontal"
            android:layout_marginTop="@dimen/dp20"
            android:layout_height="wrap_content"
            android:baselineAligned="false">
            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="0dp"
                android:layout_weight="1"
                android:layout_height="wrap_content">
                <androidx.appcompat.widget.AppCompatEditText
                    android:id="@+id/advanceBalanceEdt"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:drawableTint="@color/black"
                    android:textColor="@color/black"
                    android:drawablePadding="@dimen/dp20"
                    android:drawableStart="@drawable/ic_money_24"
                    android:hint="@string/advance_balance_amount" />

            </com.google.android.material.textfield.TextInputLayout>
            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="0dp"
                android:layout_weight="1"
                android:layout_marginStart="@dimen/dp20"
                android:layout_height="wrap_content">
                <androidx.appcompat.widget.AppCompatEditText
                    android:id="@+id/paidOnEdt"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:drawablePadding="@dimen/dp20"
                    android:drawableStart="@drawable/ic_baseline_calendar_today_24"
                    android:hint="@string/paid_n" />

            </com.google.android.material.textfield.TextInputLayout>
            <FrameLayout
                android:layout_width="0dp"
                android:layout_weight="1"
                android:layout_marginStart="@dimen/dp20"
                android:layout_marginTop="@dimen/dp5"
                android:background="@drawable/bg_spinner"
                android:layout_height="wrap_content">
                <ImageView
                    android:layout_width="wrap_content"
                    android:src="@drawable/ic_money_24"
                    android:paddingHorizontal="10dp"
                    android:layout_height="match_parent"
                    android:contentDescription="@string/label_to">

                </ImageView>

                <androidx.appcompat.widget.AppCompatSpinner
                    android:id="@+id/paymentMethod"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dp60"
                    android:layout_marginStart="@dimen/dp30"
                    android:entries="@array/payment_method_array"
                    android:textSize="12sp"
                    android:background="@null"
                    android:drawablePadding="@dimen/dp20"
                    android:drawableStart="@drawable/ic_money_24"
                    android:textStyle="normal" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp_10"
                    android:background="@color/white"
                    android:paddingLeft="4dp"
                    android:visibility="visible"
                    android:text="@string/label_payment_method"
                    android:paddingRight="4dp"
                    android:textSize="12sp" />
            </FrameLayout>
        </LinearLayout>
        <LinearLayout
            android:layout_width="match_parent"
            android:orientation="horizontal"
            android:layout_marginTop="@dimen/dp20"
            android:layout_height="wrap_content">
            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="0dp"
                android:layout_weight="1"
                android:layout_height="wrap_content">
                <androidx.appcompat.widget.AppCompatEditText
                    android:id="@+id/attachDocument"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:drawableTint="@color/black"
                    android:focusable="false"
                    android:drawablePadding="@dimen/dp20"
                    android:drawableEnd="@drawable/ic_baseline_add_circle_outline_24"
                    android:drawableStart="@drawable/ic_baseline_attach_file_24"
                    android:hint="@string/string_attach_doc" />

            </com.google.android.material.textfield.TextInputLayout>
            <androidx.appcompat.widget.AppCompatTextView
                android:layout_width="0dp"
                android:layout_weight="2"
                android:gravity="start|bottom"
                tools:text="@string/string_attach_doc"
                android:layout_marginStart="@dimen/dp20"
                android:textColor="@color/black"
                android:layout_height="match_parent">
                
            </androidx.appcompat.widget.AppCompatTextView>
        </LinearLayout>
        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_marginTop="@dimen/dp20"
            android:layout_height="wrap_content">
            <androidx.appcompat.widget.AppCompatEditText
                android:id="@+id/paymentNoteEdt"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:drawableTint="@color/black"
                android:inputType="textMultiLine"
                android:lines="4"
                android:gravity="start"
                android:layout_gravity="start"
                android:textColor="@color/black"
                android:hint="@string/label_payment_note" />

        </com.google.android.material.textfield.TextInputLayout>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginTop="@dimen/dp20"
            android:gravity="center">
            <Button
                android:id="@+id/btnSave"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp50"
                android:text="@string/label_save"
                style="@style/my_btn_style"/>

        </LinearLayout>


    </LinearLayout>






</LinearLayout>