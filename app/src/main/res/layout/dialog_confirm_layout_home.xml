<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:contentPaddingBottom="18dp"
        app:cardBackgroundColor="@color/white"
        app:contentPaddingTop="15dp">
        <ImageView
            android:layout_width="@dimen/dp50"
            android:layout_height="@dimen/dp50"
            android:layout_marginHorizontal="@dimen/dp20"
            android:layout_gravity="end"
            android:src="@drawable/app_logo_new">
        </ImageView>
        <LinearLayout
            android:layout_width="match_parent"
            android:orientation="vertical"
            android:weightSum="4"
            android:gravity="center"
            android:layout_marginTop="@dimen/dp50"
            android:layout_height="match_parent">
            <androidx.appcompat.widget.AppCompatImageView
                android:layout_width="200dp"
                android:src="@drawable/success_image"
                android:layout_height="200dp">
            </androidx.appcompat.widget.AppCompatImageView>

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:lineHeight="23dp"
                android:gravity="center"
                android:textStyle="bold"
                android:layout_marginTop="@dimen/dp20"
                android:text="@string/app_name"
                android:textColor="@color/colorPrimaryDark"
                android:textSize="30sp"
                tools:text="@string/app_name" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/message"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:padding="15dp"
                android:textColor="@color/light_black"
                android:textSize="16sp"
                tools:text="Message" />
            <LinearLayout
            android:layout_width="match_parent"
                android:gravity="center"
            android:layout_height="@dimen/dp100">

            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/noBtn"
                android:layout_width="130dp"
                android:layout_height="@dimen/dp50"
                android:layout_marginTop="@dimen/dp20"
                android:text="@string/label_btn_later"
                android:backgroundTint="@color/grey"
                android:textSize="20sp"
                android:textAllCaps="false"
                android:textColor="@color/white"
                />

            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/yesBtn"
                android:layout_width="130dp"
                android:layout_height="@dimen/dp50"
                android:layout_marginTop="@dimen/dp20"
                android:text="@string/label_btn_settup"
                android:layout_marginStart="@dimen/dp10"
                android:textSize="20sp"
                android:textAllCaps="false"
                android:textColor="@color/white"
                />
        </LinearLayout>
        </LinearLayout>

    </androidx.cardview.widget.CardView>
</LinearLayout>