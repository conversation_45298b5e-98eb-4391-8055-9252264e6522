<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/border_gray_noradius"
    android:padding="@dimen/dp20">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/date"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textColor="@color/black"
            android:text="@string/date_label" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/referenceNo"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textColor="@color/black"
            android:text="@string/label_reference_no" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/amount"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textColor="@color/black"
            android:text="@string/label_amount" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/paymentMethod"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="@color/black"
            android:layout_weight="1"
            android:text="@string/label_payment_method" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/paymentNote"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textColor="@color/black"
            android:text="@string/label_payment_note" />


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="end"
            android:layout_weight="1"
           >

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/editPayment"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_edit_svg" />
            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/deletePayment"
                android:layout_width="wrap_content"
                android:layout_marginHorizontal="@dimen/dp10"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_delete_svg" />


            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/viewPayment"
                android:layout_width="wrap_content"
                android:visibility="gone"
                android:layout_height="wrap_content"
                android:src="@drawable/ic_view_svg" />


        </LinearLayout>
    </LinearLayout>
</LinearLayout>