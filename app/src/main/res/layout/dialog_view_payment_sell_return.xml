<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/content"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/transparent"
    android:orientation="vertical"
    android:padding="20dp">


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/rounded_white_bg"
        android:orientation="vertical"
        android:padding="@dimen/activity_vertical_margin">
        <LinearLayout
            android:layout_width="match_parent"
            android:weightSum="10"
            android:layout_height="wrap_content"
            android:paddingVertical="@dimen/activity_left_margin">

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/viewPayment"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:text="@string/label_view_payments"
                android:layout_weight="9.5"
                android:textSize="16sp"
                android:textColor="@color/black"
                android:textStyle="bold" />
            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/btnClose"
                android:layout_width="0dp"
                android:layout_weight="0.5"
                app:tint="@color/blue"
                android:src="@drawable/ic_close_svg"
                android:layout_height="match_parent">

            </androidx.appcompat.widget.AppCompatImageView>
        </LinearLayout>
        <View
            android:layout_width="wrap_content"
            android:background="@color/blue"
            android:layout_height="1dp">
        </View>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="@dimen/activity_vertical_margin"
            android:baselineAligned="false">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">


                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:baselineAligned="false">


                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginHorizontal="@dimen/activity_left_margin"
                        android:layout_weight="1"
                        android:orientation="vertical">
                            <androidx.appcompat.widget.AppCompatTextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/customer_label"
                                android:textColor="@color/black"
                              />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/customerName"
                                android:layout_width="wrap_content"
                                tools:text="@string/textview"
                                android:textStyle="bold"
                                android:textSize="14sp"
                                android:textColor="@color/black"
                                android:layout_height="wrap_content"
                      />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginHorizontal="@dimen/activity_left_margin"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical"
                            android:paddingTop="@dimen/margin_vertical">

                            <androidx.appcompat.widget.AppCompatTextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/label_business_location"
                                android:textColor="@color/black"
                                android:textStyle="bold" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/businessLocation"
                                android:layout_width="wrap_content"
                                tools:text="@string/textview"
                                android:textSize="16sp"
                                android:textColor="@color/black"
                                android:layout_height="wrap_content"
                                />

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:paddingTop="@dimen/margin_vertical">

                            <androidx.appcompat.widget.AppCompatTextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/mobile_label"
                                android:textColor="@color/black"
                          />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/businessMobile"
                                android:layout_width="wrap_content"
                                tools:text="98978979"
                                android:textColor="@color/black"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="5dp" />

                        </LinearLayout>
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:paddingTop="@dimen/margin_vertical">

                            <androidx.appcompat.widget.AppCompatTextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/email_label"
                                android:textColor="@color/black"
                               />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/businessEmail"
                                android:layout_width="wrap_content"
                                tools:text="<EMAIL>"
                                android:textColor="@color/black"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="5dp" />

                        </LinearLayout>
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginHorizontal="@dimen/activity_left_margin"
                        android:layout_weight="1"
                        android:orientation="vertical">


                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"

                            android:orientation="horizontal"
                            android:paddingTop="@dimen/margin_vertical">

                            <androidx.appcompat.widget.AppCompatTextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:textColor="@color/black"
                                android:text="@string/label_facture_no"
                                android:textStyle="bold" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/invoiceNo"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                tools:text="12323e223"
                                android:textColor="@color/black"
                                android:layout_marginStart="5dp"
                               />

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:paddingTop="@dimen/margin_vertical">

                            <androidx.appcompat.widget.AppCompatTextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/date_label"
                                android:textColor="@color/black"
                                android:textStyle="bold" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/paymentDate"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:textColor="@color/black"

                                android:layout_marginStart="5dp" />

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:paddingTop="@dimen/margin_vertical">

                            <androidx.appcompat.widget.AppCompatTextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:textColor="@color/black"
                                android:text="@string/payment_status_label"
                                android:textStyle="bold" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/paymentStatus"
                                android:layout_width="wrap_content"
                                android:textColor="@color/black"

                                android:layout_height="wrap_content"
                                android:layout_marginStart="5dp" />

                        </LinearLayout>

                    </LinearLayout>

                </LinearLayout>



                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="@dimen/dp20"
                    android:layout_marginTop="@dimen/dp20"
                    android:background="@color/lightGrey"
                   >

                    <androidx.appcompat.widget.AppCompatTextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/label_date"
                        android:textColor="@color/black" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/label_reference_num"
                        android:textColor="@color/black" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/label_amount_not"


                        android:textColor="@color/black" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/label_payment_method_not"


                        android:textColor="@color/black" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/string_payement_note"


                        android:textColor="@color/black" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:textAlignment="center"
                        android:text="@string/label_btn_action"
                        android:textColor="@color/black" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:layout_constrainedHeight="true">

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/recycleSubList"
                        android:layout_width="match_parent"
                        tools:listitem="@layout/dialog_list_sub_payment_sell_return"
                        android:layout_height="wrap_content" />
                </LinearLayout>


            </LinearLayout>


        </LinearLayout>


    </LinearLayout>




</LinearLayout>