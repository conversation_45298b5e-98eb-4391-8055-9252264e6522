<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:padding="@dimen/dp10"
    android:background="@drawable/rounded_white_bg"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:weightSum="10"
        android:layout_height="wrap_content"
        android:paddingVertical="@dimen/activity_left_margin">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/sellReturnTxt"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="@string/sell_return"
            android:layout_weight="9.8"
            android:textSize="22sp"
            android:textColor="@color/black"
            android:textStyle="bold" />
        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/btnClose"
            android:layout_width="0dp"
            android:layout_weight="0.2"
            app:tint="@color/blue"
            android:src="@drawable/ic_close_svg"
            android:layout_height="match_parent">

        </androidx.appcompat.widget.AppCompatImageView>
    </LinearLayout>
    <View
        android:layout_width="match_parent"
        android:layout_height="2dp"
        android:background="@color/blue_dark" />
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <LinearLayout
            android:layout_width="match_parent"
            android:orientation="vertical"
            android:layout_height="wrap_content">
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:divider="@android:color/transparent"
                android:dividerHeight="0.0px"
                android:elevation="8dp"
                android:visibility="visible"
                app:cardElevation="2dp"
                android:layout_marginTop="@dimen/dp10"
                app:cardPreventCornerOverlap="false"
                app:cardUseCompatPadding="true">
                <LinearLayout
                    android:layout_width="match_parent"
                    android:orientation="vertical"
                    android:paddingHorizontal="@dimen/dp10"
                    android:paddingVertical="@dimen/dp30"
                    android:layout_height="wrap_content">
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="start"
                        android:orientation="horizontal"
                        android:weightSum="10">
                        <androidx.appcompat.widget.AppCompatTextView
                            android:layout_width="0dp"
                            android:layout_weight="5"
                            android:textStyle="normal"
                            android:textSize="20sp"
                            android:textColor="@color/black"
                            android:text="Sell Return Details: "
                            android:layout_height="wrap_content">

                        </androidx.appcompat.widget.AppCompatTextView>
                        <androidx.appcompat.widget.AppCompatTextView
                            android:layout_width="0dp"
                            android:layout_weight="5"
                            android:textStyle="normal"
                            android:textSize="20sp"
                            android:textColor="@color/black"
                            android:text="Sale Details: "
                            android:layout_height="wrap_content">

                        </androidx.appcompat.widget.AppCompatTextView>


                    </LinearLayout>
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="start"
                        android:layout_marginTop="@dimen/dp5"
                        android:orientation="horizontal"
                        android:weightSum="10">
                        <androidx.appcompat.widget.AppCompatTextView
                            android:layout_width="0dp"
                            android:layout_weight="0.8"
                            android:textColor="@color/black"
                            android:text="Return Date:"
                            android:layout_height="wrap_content">

                        </androidx.appcompat.widget.AppCompatTextView>
                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/returnDate"
                            android:layout_width="0dp"
                            android:layout_weight="4.2"
                            android:textColor="@color/black"
                            tools:text="0005"
                            android:layout_height="wrap_content">

                        </androidx.appcompat.widget.AppCompatTextView>

                        <androidx.appcompat.widget.AppCompatTextView
                            android:layout_width="0dp"
                            android:layout_weight="0.8"
                            android:textColor="@color/black"
                            android:text="Invoice No. :"
                            android:layout_height="wrap_content">

                        </androidx.appcompat.widget.AppCompatTextView>
                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/invoiceNoTxt"
                            android:layout_width="0dp"
                            android:layout_weight="4.2"
                            android:textColor="@color/black"
                            tools:text="0005"
                            android:layout_height="wrap_content">

                        </androidx.appcompat.widget.AppCompatTextView>


                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="start"

                        android:orientation="horizontal"
                        android:weightSum="10">
                        <androidx.appcompat.widget.AppCompatTextView
                            android:layout_width="0dp"
                            android:layout_weight="0.8"
                            android:textColor="@color/black"
                            android:text="Customer :"
                            android:layout_height="wrap_content">

                        </androidx.appcompat.widget.AppCompatTextView>
                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/customerName"
                            android:layout_width="0dp"
                            android:layout_weight="4.2"
                            android:textColor="@color/black"
                            tools:text="Walk in Customer"
                            android:layout_height="wrap_content">

                        </androidx.appcompat.widget.AppCompatTextView>

                        <androidx.appcompat.widget.AppCompatTextView
                            android:layout_width="0dp"
                            android:layout_weight="0.8"
                            android:textColor="@color/black"
                            android:text="Date :"
                            android:layout_height="wrap_content">

                        </androidx.appcompat.widget.AppCompatTextView>
                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/date"
                            android:layout_width="0dp"
                            android:layout_weight="4.2"
                            android:textColor="@color/black"
                            tools:text="12-06-2233"
                            android:layout_height="wrap_content">

                        </androidx.appcompat.widget.AppCompatTextView>



                    </LinearLayout>
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="start"
                        android:orientation="horizontal"
                        android:weightSum="10">
                        <androidx.appcompat.widget.AppCompatTextView
                            android:layout_width="wrap_content"
                            android:textColor="@color/black"
                            android:text="Business Location :"
                            android:layout_height="wrap_content">

                        </androidx.appcompat.widget.AppCompatTextView>
                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/location"
                            android:layout_width="wrap_content"
                            android:textColor="@color/black"
                            android:layout_marginStart="@dimen/dp5"
                            tools:text="Walk in Customer"
                            android:layout_height="wrap_content">

                        </androidx.appcompat.widget.AppCompatTextView>


                    </LinearLayout>
                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:divider="@android:color/transparent"
                android:dividerHeight="0.0px"
                android:elevation="8dp"
                android:visibility="visible"
                app:cardElevation="2dp"
                app:cardPreventCornerOverlap="false"
                app:cardUseCompatPadding="true">
                <LinearLayout
                    android:layout_width="match_parent"
                    android:orientation="vertical"
                    android:paddingHorizontal="@dimen/dp10"
                    android:paddingVertical="@dimen/dp10"
                    android:layout_height="wrap_content">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_marginTop="@dimen/dp20"
                        android:background="@color/green_light"
                        android:gravity="start"
                        android:paddingVertical="15dp"
                        android:paddingHorizontal="@dimen/dp10"
                        android:orientation="horizontal"
                        android:weightSum="7">

                        <androidx.appcompat.widget.AppCompatTextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="0.5"
                            android:gravity="start"
                            android:text="@string/hash"
                            android:textColor="@color/white"
                            android:textStyle="bold" />

                        <androidx.appcompat.widget.AppCompatTextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1.1"
                            android:gravity="center"
                            android:text="@string/product_name"
                            android:textColor="@color/white"
                            android:textStyle="bold" />

                        <androidx.appcompat.widget.AppCompatTextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1.1"
                            android:text="@string/label_unit_price"
                            android:gravity="center"
                            android:textColor="@color/white"
                            android:textStyle="bold" />


                        <androidx.appcompat.widget.AppCompatTextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1.2"
                            android:text="@string/sell_quantity"
                            android:gravity="center"
                            android:textColor="@color/white"
                            android:textStyle="bold" />

                        <androidx.appcompat.widget.AppCompatTextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="2"
                            android:text="@string/return_quantity"
                            android:textColor="@color/white"
                            android:textStyle="bold" />


                        <androidx.appcompat.widget.AppCompatTextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1.1"
                            android:gravity="center"
                            android:text="@string/return_subtotal"
                            android:textColor="@color/white"
                            android:textStyle="bold" />


                    </LinearLayout>
                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/productRecycler"
                        android:layout_width="match_parent"
                        tools:itemCount="2"
                        tools:listitem="@layout/list_view_sell_return_item"
                        android:layout_height="match_parent">

                    </androidx.recyclerview.widget.RecyclerView>
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:weightSum="3"
                        android:gravity="end"
                        android:layout_marginBottom="@dimen/dp10"
                        android:layout_marginTop="@dimen/dp30"
                        android:layout_height="wrap_content">
                        <View
                            android:layout_width="0dp"
                            android:layout_gravity="end"
                            android:layout_weight="1.5"
                            android:background="@color/grey"
                            android:layout_height="1dp">
                        </View>
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="end"
                        android:weightSum="3"
                        android:layout_gravity="end"
                        android:orientation="horizontal"
                        >
                        <View
                            android:layout_width="0dp"
                            android:layout_weight="1.5"
                            android:layout_height="match_parent">

                        </View>
                        <androidx.appcompat.widget.AppCompatTextView
                            android:layout_width="0dp"
                            android:layout_weight="0.5"
                            android:textStyle="bold"
                            android:gravity="start"
                            android:textColor="@color/black"
                            android:text="Net Total Amount: "
                            android:layout_height="wrap_content">

                        </androidx.appcompat.widget.AppCompatTextView>
                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/netTotalAmount"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:gravity="end"
                            android:textColor="@color/black"
                            tools:text="AED 0.00"
                            android:layout_height="wrap_content">

                        </androidx.appcompat.widget.AppCompatTextView>


                    </LinearLayout>
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:weightSum="3"
                        android:gravity="end"
                        android:layout_marginBottom="@dimen/dp10"
                        android:layout_marginTop="@dimen/dp10"
                        android:layout_height="wrap_content">
                        <View
                            android:layout_width="0dp"
                            android:layout_gravity="end"
                            android:layout_weight="1.5"
                            android:background="@color/grey"
                            android:layout_height="1dp">
                        </View>
                    </LinearLayout>
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="end"
                        android:weightSum="3"
                        android:layout_gravity="end"
                        android:orientation="horizontal"
                        >
                        <View
                            android:layout_width="0dp"
                            android:layout_weight="1.5"
                            android:layout_height="match_parent">

                        </View>
                        <androidx.appcompat.widget.AppCompatTextView
                            android:layout_width="0dp"
                            android:textStyle="bold"
                            android:layout_weight="0.5"
                            android:layout_gravity="start"
                            android:textColor="@color/black"
                            android:text="Return Discount: (-) "
                            android:layout_height="wrap_content">

                        </androidx.appcompat.widget.AppCompatTextView>
                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/totalReturnDiscount"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:gravity="end"
                            android:textColor="@color/black"
                            tools:text="AED 0.00"
                            android:layout_height="wrap_content">

                        </androidx.appcompat.widget.AppCompatTextView>


                    </LinearLayout>
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:weightSum="3"
                        android:gravity="end"
                        android:layout_marginBottom="@dimen/dp10"
                        android:layout_marginTop="@dimen/dp10"
                        android:layout_height="wrap_content">
                        <View
                            android:layout_width="0dp"
                            android:layout_gravity="end"
                            android:layout_weight="1.5"
                            android:background="@color/grey"
                            android:layout_height="1dp">
                        </View>
                    </LinearLayout>
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="end"
                        android:weightSum="3"
                        android:layout_gravity="end"
                        android:orientation="horizontal"
                        >
                        <View
                            android:layout_width="0dp"
                            android:layout_weight="1.5"
                            android:layout_height="match_parent">

                        </View>
                        <androidx.appcompat.widget.AppCompatTextView
                            android:layout_width="0dp"
                            android:layout_weight="0.5"
                            android:textStyle="bold"
                            android:textColor="@color/black"
                            android:text="Total Return Tax: (+) "
                            android:layout_height="wrap_content">

                        </androidx.appcompat.widget.AppCompatTextView>
                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/totalReturnTax"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:gravity="end"
                            android:textColor="@color/black"
                            tools:text="AED 0.00"
                            android:layout_height="wrap_content">

                        </androidx.appcompat.widget.AppCompatTextView>


                    </LinearLayout>
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:weightSum="3"
                        android:gravity="end"
                        android:layout_marginBottom="@dimen/dp10"
                        android:layout_marginTop="@dimen/dp10"
                        android:layout_height="wrap_content">
                        <View
                            android:layout_width="0dp"
                            android:layout_gravity="end"
                            android:layout_weight="1.5"
                            android:background="@color/grey"
                            android:layout_height="1dp">
                        </View>
                    </LinearLayout>
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="end"
                        android:weightSum="3"
                        android:layout_gravity="end"
                        android:orientation="horizontal"
                        >
                        <View
                            android:layout_width="0dp"
                            android:layout_weight="1.5"
                            android:layout_height="match_parent">

                        </View>
                        <androidx.appcompat.widget.AppCompatTextView
                            android:layout_width="0dp"
                            android:textStyle="bold"
                            android:layout_weight="0.5"
                            android:gravity="start"
                            android:textColor="@color/black"
                            android:text="Return Total: "
                            android:layout_height="wrap_content">

                        </androidx.appcompat.widget.AppCompatTextView>
                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/returnTotal"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:gravity="end"
                            android:textColor="@color/black"
                            tools:text="AED 0.00"
                            android:layout_height="wrap_content">

                        </androidx.appcompat.widget.AppCompatTextView>


                    </LinearLayout>

                </LinearLayout>


            </androidx.cardview.widget.CardView>

        </LinearLayout>
    </ScrollView>



</LinearLayout>