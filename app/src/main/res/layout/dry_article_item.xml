<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:gravity="center"

    android:id="@+id/linearLayout"
    android:padding="10dp">


    <LinearLayout
        android:id="@+id/container_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="@dimen/dp10"
        android:gravity="center"
        android:layout_margin="@dimen/padding_horizontal"
        android:background="@drawable/item_rectangle_bg">

        <TextView
            android:id="@+id/product_price"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textStyle="bold"
            android:textColor="@color/black"
            android:textSize="14sp"
            android:fontFamily="@font/poppins_semibold"
            tools:text="$500"
            android:paddingLeft="@dimen/padding_horizontal"/>

        <ImageView
            android:id="@+id/imageArticle"
            android:layout_width="70dp"
            android:layout_height="70dp"
            android:layout_marginVertical="@dimen/dp5"
            android:layout_weight="3" />

        <TextView
            android:id="@+id/titleArticle"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:textSize="14sp"
            android:maxLines="2"
            android:textColor="@color/black"
            android:fontFamily="@font/poppins_semibold"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@string/product_name" />
    </LinearLayout>



</LinearLayout>