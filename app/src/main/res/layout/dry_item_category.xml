<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/id_container"
    android:layout_width="match_parent"
    android:layout_marginHorizontal="@dimen/dp10"
    android:layout_height="wrap_content"
    android:orientation="vertical">
        <TextView
            android:id="@+id/titleCategory"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:padding="10dp"
            android:textColor="@color/black"
            android:textStyle="bold"
            android:layout_weight="3"
            android:maxLines="2"
            android:text="@string/product_category"
            android:gravity="center" />
        <TextView
            android:id="@+id/titleBorder"
            android:layout_width="match_parent"
            android:layout_height="4dp"
            android:layout_marginHorizontal="@dimen/dp10"
            android:gravity="center" />
</LinearLayout>