<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/content"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/rounded_white_bg"
    android:orientation="vertical">
    <LinearLayout
        android:id="@+id/container_sub_product"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:weightSum="5"
        android:background="@drawable/bg_gradient"
        android:orientation="horizontal"
        android:paddingVertical="@dimen/dp10"
        android:paddingHorizontal="@dimen/dp5"
        android:visibility="visible">

        <TextView
            android:id="@+id/id_article_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="2"
            android:text="Product"
            android:textColor="@color/white"
            android:textAppearance="@style/TextAppearance.AppCompat.Body2"
            android:textSize="12dp" />


        <TextView
            android:id="@+id/id_qt"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/label_quantity"
            android:textColor="@color/white"

            android:textAppearance="@style/TextAppearance.AppCompat.Body2"
            android:textSize="12dp" />


        <TextView
            android:id="@+id/id_unit_price"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/lbl_unit_cost"
            android:textColor="@color/white"

            android:textAppearance="@style/TextAppearance.AppCompat.Body2"
            android:textSize="12dp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/id_to"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Subtotal"
            android:textColor="@color/white"

            android:textAppearance="@style/TextAppearance.AppCompat.Body2"
            android:textSize="12dp"
            android:textStyle="bold" />


    </LinearLayout>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="@dimen/activity_left_margin">

        <LinearLayout
            android:visibility="gone"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"

            android:layout_weight="1"
            android:orientation="vertical">
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingVertical="@dimen/activity_left_margin">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/label_add_opening_stock"
                    android:textColor="@color/black"
                    android:textStyle="bold" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:padding="@dimen/nav_header_vertical_spacing">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/label_prod_name"
                    android:textColor="@color/black" />

                <TextView
                    android:id="@+id/product_name"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="1" />


            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:padding="@dimen/nav_header_vertical_spacing">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/label_qty_remaining"
                    android:textColor="@color/black" />

                <EditText
                    android:id="@+id/qntity_remaining"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="number"
                    android:layout_weight="1"
                    android:hint="00.0" />


            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:padding="@dimen/nav_header_vertical_spacing">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/label_unit_cost"
                    android:textColor="@color/black" />

                <EditText
                    android:id="@+id/unit_cost"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:inputType="numberDecimal"
                    android:hint="00.0" />


            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:padding="@dimen/nav_header_vertical_spacing">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/label_subtotal"
                    android:textColor="@color/black" />

                <TextView
                    android:id="@+id/subtotal_product"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:hint="00.0" />


            </LinearLayout>


        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">



            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"

                app:layout_constraintHeight_max="120px">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recycler_variation_location"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    tools:listitem="@layout/list_short_product_item"
                    app:layout_constrainedHeight="true" />


            </LinearLayout>
        </LinearLayout>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center">

            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/btn_save"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp40"
                android:background="@drawable/bg_gradient"
                android:layout_margin="@dimen/dp5"

                android:text="@string/label_save" />

            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/btn_close"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp40"
                android:background="@drawable/bg_gradient"
                android:layout_margin="@dimen/dp5"
                android:text="@string/label_close" />

        </LinearLayout>

    </LinearLayout>


</LinearLayout>