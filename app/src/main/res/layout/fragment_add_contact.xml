<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/relativeLayuot"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/light_white_"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="7dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/label_contact"
                android:textColor="@color/black"
                android:textSize="20sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/sub_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="5dp"
                android:text="@string/label_add_new_contact"
                android:textColor="@color/gray"
                android:textSize="14sp" />
        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="2dp"
            android:background="@color/blue_dark" />
    </LinearLayout>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp40">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="@dimen/dp10">

            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:divider="@android:color/transparent"
                android:dividerHeight="0.0px"
                android:elevation="8dp"
                android:visibility="visible"
                app:cardElevation="2dp"
                app:cardPreventCornerOverlap="false"
                app:cardUseCompatPadding="true">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:padding="@dimen/dp10">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:baselineAligned="false"
                        android:orientation="horizontal"
                        android:weightSum="8">

                        <FrameLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp10"
                            android:layout_weight="2">

                            <androidx.appcompat.widget.AppCompatSpinner
                                android:id="@+id/spinner_type"
                                android:layout_width="match_parent"
                                android:layout_height="@dimen/default_spinner_height"
                                android:layout_marginTop="5dp"
                                android:entries="@array/array_contact_type"
                                android:textSize="12sp"
                                android:textStyle="normal" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/dp_10"
                                android:background="@color/white"
                                android:paddingLeft="4dp"
                                android:paddingRight="4dp"
                                android:text="@string/lbl_contact_type"
                                android:textSize="12sp"
                                android:visibility="visible" />
                        </FrameLayout>

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp10"
                            android:layout_weight="2">

                            <androidx.appcompat.widget.AppCompatEditText
                                android:id="@+id/bussiness_name_txt"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:drawableStart="@drawable/ic_business_svg"
                                android:drawablePadding="@dimen/dp10"
                                android:gravity="start|center"
                                android:inputType="textCapWords"
                                android:textColor="@color/black" />
                        </com.google.android.material.textfield.TextInputLayout>

                        <FrameLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp10"
                            android:layout_weight="2">

                            <androidx.appcompat.widget.AppCompatSpinner
                                android:id="@+id/spinner_prefix"
                                android:layout_width="match_parent"
                                android:layout_height="@dimen/default_spinner_height"
                                android:layout_marginTop="5dp"
                                android:entries="@array/array_prefix"
                                android:textSize="12sp"
                                android:textStyle="normal" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/dp_10"
                                android:background="@color/white"
                                android:paddingLeft="4dp"
                                android:paddingRight="4dp"
                                android:text="@string/image_prefix"
                                android:textSize="12sp"
                                android:visibility="visible" />
                        </FrameLayout>


                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp10"
                            android:layout_weight="2">

                            <androidx.appcompat.widget.AppCompatEditText
                                android:id="@+id/first_name_txt"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:drawableStart="@drawable/ic_user_svg"
                                android:drawablePadding="@dimen/dp10"
                                android:gravity="start|center"
                                android:hint="@string/image_prenom"
                                android:inputType="textCapWords"
                                android:textColor="@color/black" />
                        </com.google.android.material.textfield.TextInputLayout>
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp20"
                        android:baselineAligned="false"
                        android:orientation="horizontal"
                        android:weightSum="8">

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp10"
                            android:layout_weight="2">

                            <androidx.appcompat.widget.AppCompatEditText
                                android:id="@+id/last_name_txt"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:drawableStart="@drawable/ic_user_svg"
                                android:drawablePadding="@dimen/dp10"
                                android:gravity="start|center"
                                android:hint="@string/text_nom_famile"
                                android:inputType="textCapWords"
                                android:textColor="@color/black" />
                        </com.google.android.material.textfield.TextInputLayout>

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp10"
                            android:layout_weight="2">

                            <androidx.appcompat.widget.AppCompatEditText
                                android:id="@+id/phone_txt"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:drawableStart="@drawable/pay_mobile"
                                android:drawablePadding="@dimen/dp10"
                                android:drawableTint="@color/black"
                                android:gravity="start|center"
                                android:hint="@string/image_mobile"
                                android:inputType="number"
                                android:textColor="@color/black" />
                        </com.google.android.material.textfield.TextInputLayout>

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp10"
                            android:layout_weight="2">

                            <androidx.appcompat.widget.AppCompatEditText
                                android:id="@+id/line_fixe_txt"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:drawableStart="@drawable/ic_baseline_phone_24"
                                android:drawablePadding="@dimen/dp10"
                                android:gravity="start|center"
                                android:hint="@string/image_ligne_fixe"
                                android:inputType="number"
                                android:textColor="@color/black" />
                        </com.google.android.material.textfield.TextInputLayout>

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp10"
                            android:layout_weight="2">

                            <androidx.appcompat.widget.AppCompatEditText
                                android:id="@+id/email_txt"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:drawableStart="@drawable/ic_baseline_alternate_email_24"
                                android:drawablePadding="@dimen/dp10"
                                android:gravity="start|center"
                                android:hint="@string/image_nom_email_req"
                                android:inputType="textEmailAddress"
                                android:textColor="@color/black" />
                        </com.google.android.material.textfield.TextInputLayout>
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp20"
                        android:baselineAligned="false"
                        android:orientation="horizontal"
                        android:weightSum="8">

                        <com.google.android.material.textfield.TextInputLayout
                            android:id="@+id/manage_stock_container"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp10"
                            android:layout_weight="2">

                            <androidx.appcompat.widget.AppCompatEditText
                                android:id="@+id/birth_date_txt"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:drawableStart="@drawable/ic_baseline_calendar_today_24"
                                android:drawablePadding="@dimen/dp10"
                                android:focusable="false"
                                android:gravity="start|center"
                                android:hint="@string/label_date_naissance"
                                android:inputType="textCapWords"
                                android:textColor="@color/black" />
                        </com.google.android.material.textfield.TextInputLayout>

                        <FrameLayout
                            android:id="@+id/frame_customer_groups"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp10"
                            android:layout_weight="2">

                            <androidx.appcompat.widget.AppCompatSpinner
                                android:id="@+id/spinner_customer_groups"
                                android:layout_width="match_parent"
                                android:layout_height="@dimen/default_spinner_height"
                                android:layout_marginTop="5dp"
                                android:textSize="12sp"
                                android:textStyle="normal" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/dp_10"
                                android:background="@color/white"
                                android:paddingLeft="4dp"
                                android:paddingRight="4dp"
                                android:text="@string/string_customer_groups"
                                android:textSize="12sp"
                                android:visibility="visible" />
                        </FrameLayout>

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp10"
                            android:layout_weight="2"
                            android:visibility="invisible">

                            <androidx.appcompat.widget.AppCompatEditText
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:drawableStart="@drawable/ic_baseline_phone_24"
                                android:drawablePadding="@dimen/dp10"
                                android:gravity="start|center"
                                android:hint="@string/image_ligne_fixe"
                                android:inputType="number"
                                android:textColor="@color/black" />
                        </com.google.android.material.textfield.TextInputLayout>

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp10"
                            android:layout_weight="2"
                            android:visibility="invisible">

                            <androidx.appcompat.widget.AppCompatEditText
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:drawableStart="@drawable/ic_baseline_phone_24"
                                android:drawablePadding="@dimen/dp10"
                                android:gravity="start|center"
                                android:hint="@string/image_ligne_fixe"
                                android:inputType="number"
                                android:textColor="@color/black" />
                        </com.google.android.material.textfield.TextInputLayout>

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:divider="@android:color/transparent"
                android:dividerHeight="0.0px"
                android:elevation="8dp"
                android:visibility="visible"
                app:cardElevation="2dp"
                app:cardPreventCornerOverlap="false"
                app:cardUseCompatPadding="true">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:padding="@dimen/dp10">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:baselineAligned="false"
                        android:orientation="horizontal"
                        android:weightSum="8">

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="2">

                            <androidx.appcompat.widget.AppCompatEditText
                                android:id="@+id/tax_number"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:drawableStart="@drawable/ic_taxes"
                                android:drawablePadding="@dimen/dp10"
                                android:gravity="start|center"
                                android:hint="@string/image_tax_number"
                                android:inputType="textCapWords"
                                android:textColor="@color/black" />
                        </com.google.android.material.textfield.TextInputLayout>

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp10"
                            android:layout_weight="2">

                            <androidx.appcompat.widget.AppCompatEditText
                                android:id="@+id/opening_balance"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:drawableStart="@drawable/ic_baseline_monetization_on_24"
                                android:drawablePadding="@dimen/dp10"
                                android:gravity="start|center"
                                android:hint="@string/txt_opening_balance"
                                android:inputType="numberDecimal"
                                android:textColor="@color/black" />
                        </com.google.android.material.textfield.TextInputLayout>

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_marginStart="@dimen/dp10"
                            android:layout_weight="2"
                            android:weightSum="2">

                            <com.google.android.material.textfield.TextInputLayout
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1">

                                <androidx.appcompat.widget.AppCompatEditText
                                    android:id="@+id/pay_term_number"
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:gravity="start|center"
                                    android:hint="@string/text_pay_term"
                                    android:inputType="number"
                                    android:textColor="@color/black" />
                            </com.google.android.material.textfield.TextInputLayout>

                            <FrameLayout
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/dp10"
                                android:layout_weight="1"
                                android:visibility="visible">

                                <androidx.appcompat.widget.AppCompatSpinner
                                    android:id="@+id/pay_term_type"
                                    android:layout_width="match_parent"
                                    android:layout_height="@dimen/default_spinner_height"
                                    android:layout_marginTop="5dp"
                                    android:entries="@array/array_duration" />

                                <androidx.appcompat.widget.AppCompatTextView
                                    android:id="@+id/pay_term_tint"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginStart="@dimen/dp_10"
                                    android:background="@color/white"
                                    android:paddingLeft="4dp"
                                    android:paddingRight="4dp"
                                    android:text="Pay Term In"
                                    android:textSize="12sp"
                                    android:visibility="visible" />
                            </FrameLayout>
                        </LinearLayout>

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp10"
                            android:layout_weight="2">

                            <androidx.appcompat.widget.AppCompatEditText
                                android:id="@+id/credit_limit"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:drawableStart="@drawable/ic_credit_limit"
                                android:drawablePadding="@dimen/dp10"
                                android:gravity="start|center"
                                android:hint="@string/image_credit_limit"
                                android:inputType="textCapWords"
                                android:textColor="@color/black" />
                        </com.google.android.material.textfield.TextInputLayout>
                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:divider="@android:color/transparent"
                android:dividerHeight="0.0px"
                android:elevation="8dp"
                android:visibility="visible"
                app:cardElevation="2dp"
                app:cardPreventCornerOverlap="false"
                app:cardUseCompatPadding="true">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:padding="@dimen/dp10">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp20"
                        android:baselineAligned="false"
                        android:orientation="horizontal"
                        android:weightSum="8">

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="2">

                            <androidx.appcompat.widget.AppCompatEditText
                                android:id="@+id/adress_txt"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:drawableStart="@drawable/ic_location_address"
                                android:drawablePadding="@dimen/dp10"
                                android:gravity="start|center"
                                android:hint="@string/image_adresse"
                                android:inputType="textCapWords"
                                android:textColor="@color/black" />
                        </com.google.android.material.textfield.TextInputLayout>

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp10"
                            android:layout_weight="2">

                            <androidx.appcompat.widget.AppCompatEditText
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:drawableStart="@drawable/ic_location_address"
                                android:drawablePadding="@dimen/dp10"
                                android:gravity="start|center"
                                android:hint="@string/image_adresse1"
                                android:inputType="textCapWords"
                                android:textColor="@color/black" />
                        </com.google.android.material.textfield.TextInputLayout>

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp10"
                            android:layout_weight="2">

                            <androidx.appcompat.widget.AppCompatEditText
                                android:id="@+id/country_txt"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:drawableStart="@drawable/ic_country_svg"
                                android:drawablePadding="@dimen/dp10"
                                android:gravity="start|center"
                                android:hint="@string/text_pays"
                                android:inputType="textCapWords"
                                android:textColor="@color/black" />
                        </com.google.android.material.textfield.TextInputLayout>


                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp10"
                            android:layout_weight="2">

                            <androidx.appcompat.widget.AppCompatEditText
                                android:id="@+id/state"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:drawableStart="@drawable/ic_location"
                                android:drawablePadding="@dimen/dp10"
                                android:gravity="start|center"
                                android:hint="@string/image_statee"
                                android:inputType="textCapWords"
                                android:textColor="@color/black" />
                        </com.google.android.material.textfield.TextInputLayout>
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp20"
                        android:baselineAligned="false"
                        android:orientation="horizontal"
                        android:weightSum="8">

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="2">

                            <androidx.appcompat.widget.AppCompatEditText
                                android:id="@+id/city_txt"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:drawableStart="@drawable/ic_location"
                                android:drawablePadding="@dimen/dp10"
                                android:gravity="start|center"
                                android:hint="@string/image_ville"
                                android:inputType="textCapWords"
                                android:textColor="@color/black" />
                        </com.google.android.material.textfield.TextInputLayout>

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp10"
                            android:layout_weight="2">

                            <androidx.appcompat.widget.AppCompatEditText
                                android:id="@+id/zip_code"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:drawableStart="@drawable/ic_location_address"
                                android:drawablePadding="@dimen/dp10"
                                android:gravity="start|center"
                                android:hint="@string/image_code_postal"
                                android:inputType="textCapWords"
                                android:textColor="@color/black" />
                        </com.google.android.material.textfield.TextInputLayout>

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp10"
                            android:layout_weight="2">

                            <androidx.appcompat.widget.AppCompatEditText
                                android:id="@+id/shipping_adresse"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:drawableStart="@drawable/ic_shipping_address"
                                android:drawablePadding="@dimen/dp10"
                                android:gravity="start|center"
                                android:hint="@string/image_shipping_adress"
                                android:inputType="textCapWords"
                                android:textColor="@color/black" />
                        </com.google.android.material.textfield.TextInputLayout>


                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/light_white_"
                android:gravity="center"
                android:orientation="horizontal"
                android:padding="@dimen/padding_top">


                <androidx.appcompat.widget.AppCompatButton
                    android:id="@+id/id_back"
                    android:layout_width="150dp"
                    android:layout_height="@dimen/dp50"
                    android:text="@string/label_close" />

                <androidx.appcompat.widget.AppCompatButton
                    android:id="@+id/add_btn"
                    android:layout_width="150dp"
                    android:layout_height="@dimen/dp50"
                    android:layout_marginStart="@dimen/dp20"
                    android:text="@string/label_add" />

            </LinearLayout>
        </LinearLayout>
    </ScrollView>

</RelativeLayout>