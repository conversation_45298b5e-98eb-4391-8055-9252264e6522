<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:id="@+id/linearLayout"
    android:background="@color/light_white_"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="7dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/label_oadd_disc"
                android:textColor="@color/black"
                android:textSize="20sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/sub_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="5dp"
                android:text="@string/label_add_new_discount"
                android:textColor="@color/gray"
                android:textSize="14sp" />
        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="2dp"
            android:background="@color/blue_dark" />
    </LinearLayout>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp40">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="@dimen/dp10">

            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:divider="@android:color/transparent"
                android:dividerHeight="0.0px"
                android:elevation="8dp"
                android:visibility="visible"
                app:cardElevation="2dp"
                app:cardPreventCornerOverlap="false"
                app:cardUseCompatPadding="true">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:padding="@dimen/dp10">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:baselineAligned="false"
                        android:orientation="horizontal"
                        android:weightSum="8">

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp10"
                            android:layout_weight="2">

                            <androidx.appcompat.widget.AppCompatEditText
                                android:id="@+id/name_discount"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:gravity="start|center"
                                android:hint="@string/string_name"
                                android:inputType="textCapWords"
                                android:textColor="@color/black" />
                        </com.google.android.material.textfield.TextInputLayout>

                        <FrameLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp10"
                            android:layout_weight="2">

                            <!--                            <androidx.appcompat.widget.AppCompatSpinner-->
                            <!--                                android:id="@+id/spinner_type"-->
                            <!--                                android:layout_width="match_parent"-->
                            <!--                                android:layout_height="@dimen/default_spinner_height"-->
                            <!--                                android:layout_marginTop="5dp"-->
                            <!--                                android:entries="@array/array_contact_type"-->
                            <!--                                android:textSize="12sp"-->
                            <!--                                android:textStyle="normal" />-->

                            <androidx.appcompat.widget.AppCompatSpinner
                                android:id="@+id/multipleItemSelectionSpinner"
                                android:layout_width="match_parent"
                                android:layout_height="@dimen/default_spinner_height"
                                android:layout_marginTop="5dp"
                                android:textSize="12sp"
                                android:textStyle="normal" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"

                                android:layout_marginStart="@dimen/dp_10"
                                android:background="@color/white"
                                android:paddingLeft="4dp"
                                android:paddingRight="4dp"
                                android:text="Enter Product name"
                                android:textSize="12sp"
                                android:visibility="visible" />
                        </FrameLayout>

                        <FrameLayout
                            android:id="@+id/spin_brand_container"

                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp10"
                            android:layout_weight="2">

                            <androidx.appcompat.widget.AppCompatSpinner
                                android:id="@+id/spinner_brand"
                                android:layout_width="match_parent"
                                android:layout_height="@dimen/default_spinner_height"
                                android:layout_marginTop="5dp"
                                android:entries="@array/array_contact_type"
                                android:textSize="12sp"
                                android:textStyle="normal" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/dp_10"
                                android:background="@color/white"
                                android:paddingLeft="4dp"
                                android:paddingRight="4dp"
                                android:text="@string/label_brand"
                                android:textSize="12sp"
                                android:visibility="visible" />
                        </FrameLayout>

                        <FrameLayout
                            android:id="@+id/spin_category_container"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp10"
                            android:layout_weight="2">

                            <androidx.appcompat.widget.AppCompatSpinner
                                android:id="@+id/spinner_category"
                                android:layout_width="match_parent"
                                android:layout_height="@dimen/default_spinner_height"
                                android:layout_marginTop="5dp"
                                android:entries="@array/array_contact_type"
                                android:textSize="12sp"
                                android:textStyle="normal" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/dp_10"
                                android:background="@color/white"
                                android:paddingLeft="4dp"
                                android:paddingRight="4dp"
                                android:text="@string/label_category"
                                android:textSize="12sp"
                                android:visibility="visible" />
                        </FrameLayout>
                    </LinearLayout>


                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp20"
                        android:baselineAligned="false"
                        android:orientation="horizontal"

                        android:weightSum="8">

                        <FrameLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp10"
                            android:layout_weight="2">

                            <androidx.appcompat.widget.AppCompatSpinner
                                android:id="@+id/spinner_location"
                                android:layout_width="match_parent"
                                android:layout_height="@dimen/default_spinner_height"
                                android:layout_marginTop="5dp"
                                android:entries="@array/array_contact_type"
                                android:textSize="12sp"
                                android:textStyle="normal" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/dp_10"
                                android:background="@color/white"
                                android:paddingLeft="4dp"
                                android:paddingRight="4dp"
                                android:text="@string/label_locationnn"
                                android:textSize="12sp"
                                android:visibility="visible" />
                        </FrameLayout>

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp10"
                            android:layout_weight="2">

                            <androidx.appcompat.widget.AppCompatEditText
                                android:id="@+id/priority"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:gravity="start|center"
                                android:hint="@string/image_priority"
                                android:inputType="number"
                                android:textColor="@color/black" />
                        </com.google.android.material.textfield.TextInputLayout>

                        <FrameLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp10"
                            android:layout_weight="2">

                            <androidx.appcompat.widget.AppCompatSpinner
                                android:id="@+id/discount_type_spinner"
                                android:layout_width="match_parent"
                                android:layout_height="@dimen/default_spinner_height"
                                android:layout_marginTop="5dp"
                                android:entries="@array/discount_type"
                                android:textSize="12sp"
                                android:textStyle="normal" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/dp_10"
                                android:background="@color/white"
                                android:paddingLeft="4dp"
                                android:paddingRight="4dp"
                                android:text="@string/image_disc_type"
                                android:textSize="12sp"
                                android:visibility="visible" />
                        </FrameLayout>

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp10"
                            android:layout_weight="2">

                            <androidx.appcompat.widget.AppCompatEditText
                                android:id="@+id/discount_amnt"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:gravity="start|center"
                                android:hint="@string/text_dic_amount"
                                android:inputType="numberDecimal"
                                android:textColor="@color/black" />
                        </com.google.android.material.textfield.TextInputLayout>
                    </LinearLayout>


                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp20"
                        android:baselineAligned="false"
                        android:orientation="horizontal"

                        android:weightSum="8">

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp10"
                            android:layout_weight="2">

                            <androidx.appcompat.widget.AppCompatEditText
                                android:id="@+id/starts_at"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:gravity="start|center"
                                android:hint="@string/label_starts_at_req"
                                android:inputType="textCapWords"
                                android:focusableInTouchMode="false"
                                android:textColor="@color/black" />
                        </com.google.android.material.textfield.TextInputLayout>

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp10"
                            android:layout_weight="2">

                            <androidx.appcompat.widget.AppCompatEditText
                                android:id="@+id/ends_at"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:gravity="start|center"
                                android:hint="@string/label_ends_At_req"
                                android:inputType="textCapWords"
                                android:focusableInTouchMode="false"
                                android:textColor="@color/black" />
                        </com.google.android.material.textfield.TextInputLayout>

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_marginStart="@dimen/dp10"
                            android:layout_weight="2"
                            android:background="@drawable/border_gray"
                            android:orientation="horizontal">

                            <androidx.appcompat.widget.AppCompatCheckBox
                                android:id="@+id/check_spg"
                                android:layout_width="@dimen/dp30"
                                android:layout_height="@dimen/default_spinner_height"
                                android:checked="true" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/label_apply_in_selling_p"
                                android:textColor="@color/black"
                                android:textSize="14sp" />
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_marginStart="@dimen/dp10"
                            android:layout_weight="2"
                            android:background="@drawable/border_gray"
                            android:orientation="horizontal">

                            <androidx.appcompat.widget.AppCompatCheckBox
                                android:id="@+id/is_active"
                                android:layout_width="@dimen/dp30"
                                android:layout_height="@dimen/default_spinner_height"
                                android:checked="true" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/text_is_active"
                                android:textColor="@color/black"
                                android:textSize="14sp" />
                        </LinearLayout>
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@color/light_white_"
                        android:gravity="center"
                        android:orientation="horizontal"
                        android:padding="@dimen/padding_top">

                        <androidx.appcompat.widget.AppCompatButton
                            android:id="@+id/id_back"
                            android:layout_width="150dp"
                            android:layout_height="@dimen/dp50"
                            android:text="@string/label_close" />

                        <androidx.appcompat.widget.AppCompatButton
                            android:id="@+id/add_btn"
                            android:layout_width="150dp"
                            android:layout_height="@dimen/dp50"
                            android:layout_marginStart="@dimen/dp20"
                            android:text="@string/label_add" />

                    </LinearLayout>
                </LinearLayout>
            </androidx.cardview.widget.CardView>
        </LinearLayout>
    </ScrollView>

</RelativeLayout>
