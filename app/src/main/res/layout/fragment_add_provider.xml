<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:padding="@dimen/activity_horizontal_margin">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingVertical="@dimen/nav_header_vertical_spacing">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/label_our_provider"
                    android:textColor="@color/black"
                    android:textSize="18dp"
                    android:textStyle="bold"/>
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/black"
                    android:textSize="16dp"
                    android:text="@string/label_add_new_provider"  />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:padding="@dimen/activity_vertical_margin">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    android:layout_marginHorizontal="@dimen/activity_left_margin">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/lbl_contact_type"
                        android:textColor="@color/black" />

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:gravity="center"
                        android:orientation="horizontal"
                        android:paddingTop="@dimen/margin_vertical">

                        <ImageView
                            android:layout_width="30px"
                            android:layout_height="30px"
                            android:layout_weight="1"
                            android:src="@drawable/icons_contacts" />

                        <Spinner
                            android:id="@+id/spinner_type"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:entries="@array/array_provider_type" />
                    </LinearLayout>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:orientation="vertical"

                    android:layout_marginHorizontal="@dimen/activity_left_margin">

                    <TextView
                        android:id="@+id/company_name_hint"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/image_nom_entrprise_requiered"
                        android:textColor="@color/black" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:orientation="horizontal"
                        android:paddingTop="@dimen/margin_vertical">

                        <ImageView
                            android:layout_width="30px"
                            android:layout_height="30px"
                            android:src="@drawable/icons8_compost_heap" />

                        <EditText
                            android:id="@+id/bussiness_name_txt"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_weight="1" />
                    </LinearLayout>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    android:layout_marginHorizontal="@dimen/activity_left_margin">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/image_prenom"
                        android:textColor="@color/black" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:orientation="horizontal"
                        android:paddingTop="@dimen/margin_vertical">

                        <ImageView
                            android:layout_width="30px"
                            android:layout_height="30px"
                            android:src="@drawable/icons8_user" />

                        <EditText
                            android:id="@+id/first_name_txt"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_weight="1" />
                    </LinearLayout>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    android:layout_marginHorizontal="@dimen/activity_left_margin">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/text_nom_famile"
                        android:textColor="@color/black" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:orientation="horizontal"
                        android:paddingTop="@dimen/margin_vertical">

                        <ImageView
                            android:layout_width="30px"
                            android:layout_height="30px"
                            android:src="@drawable/icons8_user" />

                        <EditText
                            android:id="@+id/last_name_txt"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_weight="1" />
                    </LinearLayout>

                </LinearLayout>

            </LinearLayout>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:padding="@dimen/activity_vertical_margin">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:orientation="vertical"

                    android:layout_marginHorizontal="@dimen/activity_left_margin">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/image_mobile"
                        android:textColor="@color/black" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:orientation="horizontal"
                        android:paddingTop="@dimen/margin_vertical">

                        <ImageView
                            android:layout_width="30px"
                            android:layout_height="30px"
                            android:src="@drawable/icons8_touchscreen" />

                        <EditText
                            android:id="@+id/phone_txt"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_weight="1" />
                    </LinearLayout>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    android:layout_marginHorizontal="@dimen/activity_left_margin">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/image_ligne_fixe"
                        android:textColor="@color/black" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:orientation="horizontal"
                        android:paddingTop="@dimen/margin_vertical">

                        <ImageView
                            android:layout_width="30px"
                            android:layout_height="30px"
                            android:src="@drawable/icons8_phone" />

                        <EditText
                            android:id="@+id/line_fixe_txt"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:hint="@string/image_ligne_fixe"
                            android:layout_weight="1" />
                    </LinearLayout>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    android:layout_marginHorizontal="@dimen/activity_left_margin">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/image_nom_email"
                        android:textColor="@color/black" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:orientation="horizontal"
                        android:paddingTop="@dimen/margin_vertical">

                        <ImageView
                            android:layout_width="30px"
                            android:layout_height="30px"
                            android:src="@drawable/icons8_email" />

                        <EditText
                            android:id="@+id/email_txt"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_weight="1" />
                    </LinearLayout>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    android:layout_marginHorizontal="@dimen/activity_left_margin">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/label_date_naissance"
                        android:textColor="@color/black" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:orientation="horizontal"
                        android:paddingTop="@dimen/margin_vertical">

                        <ImageView
                            android:layout_width="30px"
                            android:layout_height="30px"
                            android:src="@drawable/icons8_calendar" />

                        <EditText
                            android:id="@+id/birth_date_txt"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_weight="1" />
                    </LinearLayout>

                </LinearLayout>

            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="1px"
                android:layout_marginVertical="@dimen/activity_vertical_margin"
                android:layout_marginHorizontal="50dp"
                android:background="@color/gray"/>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:padding="@dimen/activity_vertical_margin">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:orientation="vertical"

                    android:layout_marginHorizontal="@dimen/activity_left_margin">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/image_adresse"
                        android:textColor="@color/black" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:orientation="horizontal"
                        android:paddingTop="@dimen/margin_vertical">

                        <ImageView
                            android:layout_width="30px"
                            android:layout_height="30px"
                            android:src="@drawable/icons8_world_map" />

                        <EditText
                            android:id="@+id/adress_txt"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_weight="1" />
                    </LinearLayout>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    android:layout_marginHorizontal="@dimen/activity_left_margin">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/image_ville"
                        android:textColor="@color/black" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:orientation="horizontal"
                        android:paddingTop="@dimen/margin_vertical">

                        <ImageView
                            android:layout_width="30px"
                            android:layout_height="30px"
                            android:src="@drawable/icons8_user_location" />

                        <EditText
                            android:id="@+id/city_txt"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:hint="@string/image_ligne_fixe"
                            android:layout_weight="1" />
                    </LinearLayout>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    android:layout_marginHorizontal="@dimen/activity_left_margin">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/text_pays"
                        android:textColor="@color/black" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:orientation="horizontal"
                        android:paddingTop="@dimen/margin_vertical">

                        <ImageView
                            android:layout_width="30px"
                            android:layout_height="30px"
                            android:src="@drawable/icons8_location" />

                        <EditText
                            android:id="@+id/country_txt"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_weight="1" />
                    </LinearLayout>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    android:layout_marginHorizontal="@dimen/activity_left_margin">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/image_code_postal"
                        android:textColor="@color/black" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:orientation="horizontal"
                        android:paddingTop="@dimen/margin_vertical">

                        <ImageView
                            android:layout_width="30px"
                            android:layout_height="30px"
                            android:src="@drawable/icons8_user_location" />

                        <EditText
                            android:id="@+id/code_postal_txt"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_weight="1" />
                    </LinearLayout>

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="horizontal"
                android:padding="@dimen/padding_top">

                <Button
                    android:id="@+id/add_btn"
                        style="@style/my_btn_style"
                        android:layout_width="wrap_content"
                        android:layout_height="70px"

                    android:text="@string/label_add" />

                <Button
                    android:id="@+id/id_back"
                        style="@style/my_btn_style"
                        android:layout_width="wrap_content"
                        android:layout_height="70px"

                    android:text="@string/label_close" />

            </LinearLayout>
        </LinearLayout>

    </LinearLayout>

</ScrollView>