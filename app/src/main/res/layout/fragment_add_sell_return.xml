<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="@dimen/dp10">


    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/sell_return"
        android:textColor="@color/black"
        android:textSize="22sp" />

    <View
        android:layout_width="match_parent"
        android:layout_height="2dp"
        android:background="@color/blue_dark" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp10"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:divider="@android:color/transparent"
                android:dividerHeight="0.0px"
                android:elevation="8dp"
                android:visibility="visible"
                app:cardElevation="2dp"
                app:cardPreventCornerOverlap="false"
                app:cardUseCompatPadding="true">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:paddingHorizontal="@dimen/dp10"
                    android:paddingVertical="@dimen/dp30">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="start"
                        android:orientation="horizontal"
                        android:weightSum="10">

                        <androidx.appcompat.widget.AppCompatTextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="0.8"
                            android:text="Invoice No. :"
                            android:textColor="@color/black"
                            android:textStyle="bold">

                        </androidx.appcompat.widget.AppCompatTextView>

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/invoiceNoTxt"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="4.2"
                            android:textColor="@color/black"
                            tools:text="0005">

                        </androidx.appcompat.widget.AppCompatTextView>

                        <androidx.appcompat.widget.AppCompatTextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="0.8"
                            android:text="Customer :"
                            android:textColor="@color/black"
                            android:textStyle="bold">

                        </androidx.appcompat.widget.AppCompatTextView>

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/customerName"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="4.2"
                            android:textColor="@color/black"
                            tools:text="Walk in Customer">

                        </androidx.appcompat.widget.AppCompatTextView>


                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp5"
                        android:gravity="start"
                        android:orientation="horizontal"
                        android:weightSum="10">

                        <androidx.appcompat.widget.AppCompatTextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="0.8"
                            android:text="Date:"
                            android:textColor="@color/black"
                            android:textStyle="bold">

                        </androidx.appcompat.widget.AppCompatTextView>

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/dateTxt"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="4.2"
                            android:textColor="@color/black"
                            tools:text="0005">

                        </androidx.appcompat.widget.AppCompatTextView>

                        <androidx.appcompat.widget.AppCompatTextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1.2"
                            android:text="Business Location :"
                            android:textColor="@color/black"
                            android:textStyle="bold">

                        </androidx.appcompat.widget.AppCompatTextView>

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/location"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="3.8"
                            android:textColor="@color/black"
                            tools:text="Walk in Customer">

                        </androidx.appcompat.widget.AppCompatTextView>


                    </LinearLayout>
                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp10"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:divider="@android:color/transparent"
                android:dividerHeight="0.0px"
                android:elevation="8dp"
                android:visibility="visible"
                app:cardElevation="2dp"
                app:cardPreventCornerOverlap="false"
                app:cardUseCompatPadding="true">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:paddingHorizontal="@dimen/dp10"
                    android:paddingVertical="@dimen/dp30">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:baselineAligned="false"
                        android:orientation="horizontal"
                        android:weightSum="3">

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1">

                            <androidx.appcompat.widget.AppCompatEditText
                                android:id="@+id/invoiceNoEdt"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:focusable="false"
                                android:gravity="start|center"
                                android:hint="@string/label_facture_no"
                                android:inputType="textCapWords"
                                android:textColor="@color/black" />
                        </com.google.android.material.textfield.TextInputLayout>

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp10"
                            android:layout_weight="1">

                            <androidx.appcompat.widget.AppCompatEditText
                                android:id="@+id/dateEdt"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:drawableStart="@drawable/ic_baseline_calendar_today_24"
                                android:drawablePadding="@dimen/dp10"
                                android:focusable="false"
                                android:gravity="start|center"
                                android:hint="@string/label_date"
                                android:inputType="textCapWords"
                                android:textColor="@color/black" />
                        </com.google.android.material.textfield.TextInputLayout>
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_marginTop="@dimen/dp20"
                        android:background="@color/green_light"
                        android:gravity="start"
                        android:orientation="horizontal"
                        android:paddingHorizontal="@dimen/dp10"
                        android:paddingVertical="15dp"
                        android:weightSum="7">

                        <androidx.appcompat.widget.AppCompatTextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="0.5"
                            android:gravity="start"
                            android:text="@string/hash"
                            android:textColor="@color/white"
                            android:textStyle="bold" />

                        <androidx.appcompat.widget.AppCompatTextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1.1"
                            android:gravity="center"
                            android:text="@string/product_name"
                            android:textColor="@color/white"
                            android:textStyle="bold" />

                        <androidx.appcompat.widget.AppCompatTextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1.1"
                            android:gravity="center"
                            android:text="@string/label_unit_price"
                            android:textColor="@color/white"
                            android:textStyle="bold" />


                        <androidx.appcompat.widget.AppCompatTextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1.2"
                            android:gravity="center"
                            android:text="@string/sell_quantity"
                            android:textColor="@color/white"
                            android:textStyle="bold" />

                        <androidx.appcompat.widget.AppCompatTextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="2"
                            android:text="@string/return_quantity"
                            android:textColor="@color/white"
                            android:textStyle="bold" />


                        <androidx.appcompat.widget.AppCompatTextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1.1"
                            android:gravity="center"
                            android:text="@string/return_subtotal"
                            android:textColor="@color/white"
                            android:textStyle="bold" />


                    </LinearLayout>

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/productRecycler"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        tools:itemCount="2"
                        tools:listitem="@layout/list_add_sell_return_item">

                    </androidx.recyclerview.widget.RecyclerView>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp20"
                        android:orientation="horizontal"
                        android:weightSum="3">

                        <FrameLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1">

                            <androidx.appcompat.widget.AppCompatSpinner
                                android:id="@+id/spinnerDiscountType"
                                android:layout_width="match_parent"
                                android:layout_height="@dimen/default_spinner_height"
                                android:layout_marginTop="5dp"
                                android:entries="@array/discount_type"
                                android:textSize="12sp"
                                android:textStyle="normal" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/dp_10"
                                android:background="@color/white"
                                android:paddingLeft="4dp"
                                android:paddingRight="4dp"
                                android:text="@string/label_disc_type"
                                android:textSize="12sp"
                                android:visibility="visible" />
                        </FrameLayout>

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp10"
                            android:layout_weight="1">

                            <androidx.appcompat.widget.AppCompatEditText
                                android:id="@+id/discountAmountTxt"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:gravity="start|center"
                                android:hint="Discount"
                                android:inputType="numberDecimal"
                                android:text="@string/_00_0"
                                android:textColor="@color/black" />
                        </com.google.android.material.textfield.TextInputLayout>

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="end"
                        android:gravity="end"
                        android:orientation="horizontal">

                        <androidx.appcompat.widget.AppCompatTextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Total Return Discount: (-) "
                            android:textColor="@color/black"
                            android:textStyle="bold">

                        </androidx.appcompat.widget.AppCompatTextView>

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/totalReturnDiscount"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/black"
                            tools:text="AED 0.00">

                        </androidx.appcompat.widget.AppCompatTextView>


                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="end"
                        android:gravity="end"
                        android:orientation="horizontal">

                        <androidx.appcompat.widget.AppCompatTextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Total Return Tax: (+) "
                            android:textColor="@color/black"
                            android:textStyle="bold">

                        </androidx.appcompat.widget.AppCompatTextView>

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/totalReturnTax"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/black"
                            tools:text="AED 0.00">

                        </androidx.appcompat.widget.AppCompatTextView>


                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="end"
                        android:gravity="end"
                        android:orientation="horizontal">

                        <androidx.appcompat.widget.AppCompatTextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Return Total: "
                            android:textColor="@color/black"
                            android:textStyle="bold">

                        </androidx.appcompat.widget.AppCompatTextView>

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/returnTotal"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/black"
                            tools:text="AED 0.00">

                        </androidx.appcompat.widget.AppCompatTextView>


                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="end"
                        android:layout_marginTop="@dimen/dp20"
                        android:gravity="end"
                        android:orientation="horizontal">

                        <androidx.appcompat.widget.AppCompatButton
                            android:id="@+id/saveBtn"
                            android:layout_width="100dp"
                            android:layout_height="@dimen/dp50"
                            android:layout_marginStart="@dimen/dp20"
                            android:background="@drawable/bg_gradient"
                            android:text="@string/label_save" />
                    </LinearLayout>

                </LinearLayout>


            </androidx.cardview.widget.CardView>

        </LinearLayout>
    </ScrollView>


</LinearLayout>