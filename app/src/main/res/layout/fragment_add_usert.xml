<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/light_white_"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="7dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/label_users"
                android:textColor="@color/black"
                android:textSize="20sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/sub_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="5dp"
                android:text="@string/label_add_new_user"
                android:textColor="@color/gray"
                android:textSize="14sp" />
        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="2dp"
            android:background="@color/blue_dark" />
    </LinearLayout>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp40">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="@dimen/dp10">

            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:divider="@android:color/transparent"
                android:dividerHeight="0.0px"
                android:elevation="8dp"
                android:visibility="visible"
                app:cardElevation="2dp"
                app:cardPreventCornerOverlap="false"
                app:cardUseCompatPadding="true">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:padding="@dimen/dp10">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:baselineAligned="false"
                        android:orientation="horizontal"
                        android:weightSum="6">

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp10"
                            android:layout_weight="2">

                            <androidx.appcompat.widget.AppCompatEditText
                                android:id="@+id/prefix_edittext"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:drawableStart="@drawable/ic_business_svg"
                                android:drawablePadding="@dimen/dp10"
                                android:gravity="start|center"
                                android:hint="@string/image_nom_prefix"
                                android:inputType="textCapWords"
                                android:textColor="@color/black" />
                        </com.google.android.material.textfield.TextInputLayout>

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp10"
                            android:layout_weight="2">

                            <androidx.appcompat.widget.AppCompatEditText
                                android:id="@+id/first_name_edittext"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:drawableStart="@drawable/ic_business_svg"
                                android:drawablePadding="@dimen/dp10"
                                android:gravity="start|center"
                                android:hint="@string/image_prenom"
                                android:inputType="textCapWords"
                                android:textColor="@color/black" />
                        </com.google.android.material.textfield.TextInputLayout>

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp10"
                            android:layout_weight="2">

                            <androidx.appcompat.widget.AppCompatEditText
                                android:id="@+id/last_name"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:drawableStart="@drawable/ic_user_svg"
                                android:drawablePadding="@dimen/dp10"
                                android:gravity="start|center"
                                android:hint="@string/text_nom_famile"
                                android:inputType="textCapWords"
                                android:textColor="@color/black" />
                        </com.google.android.material.textfield.TextInputLayout>

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp20"
                        android:baselineAligned="false"
                        android:orientation="horizontal"
                        android:weightSum="6">
                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp10"
                            android:layout_weight="2">

                            <androidx.appcompat.widget.AppCompatEditText
                                android:id="@+id/email_edittext"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:drawableStart="@drawable/ic_baseline_alternate_email_24"
                                android:drawablePadding="@dimen/dp10"
                                android:gravity="start|center"
                                android:hint="@string/image_nom_email_req"
                                android:inputType="textCapWords"
                                android:textColor="@color/black" />
                        </com.google.android.material.textfield.TextInputLayout>


                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_marginStart="@dimen/dp10"
                            android:layout_weight="2"
                            android:background="@drawable/border_gray"
                            android:orientation="horizontal">

                            <androidx.appcompat.widget.AppCompatCheckBox
                                android:id="@+id/is_active_checkbox"
                                android:layout_width="@dimen/dp30"
                                android:layout_height="@dimen/default_spinner_height"
                                android:checked="false" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/string_is_active"
                                android:textColor="@color/blue_txt"
                                android:textSize="14sp" />
                        </LinearLayout>

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:divider="@android:color/transparent"
                android:dividerHeight="0.0px"
                android:elevation="8dp"
                android:visibility="visible"
                app:cardElevation="2dp"
                app:cardPreventCornerOverlap="false"
                app:cardUseCompatPadding="true">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:padding="@dimen/dp10">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@color/black"
                        android:text="@string/text_roles_and_permission"/>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:baselineAligned="false"
                        android:orientation="horizontal"
                        android:weightSum="6">

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="2">

                            <androidx.appcompat.widget.AppCompatEditText
                                android:id="@+id/username_edittext"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:drawableStart="@drawable/ic_user_svg"
                                android:drawablePadding="@dimen/dp10"
                                android:gravity="start|center"
                                android:hint="@string/label_userrname_req"
                                android:inputType="textCapWords"
                                android:textColor="@color/black" />
                        </com.google.android.material.textfield.TextInputLayout>

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp10"
                            android:layout_weight="2">

                            <androidx.appcompat.widget.AppCompatEditText
                                android:id="@+id/password_edittext"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:drawableStart="@drawable/ic_baseline_monetization_on_24"
                                android:drawablePadding="@dimen/dp10"
                                android:gravity="start|center"
                                android:hint="@string/image_password"
                                android:inputType="textPassword"
                                android:textColor="@color/black" />
                        </com.google.android.material.textfield.TextInputLayout>

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp10"
                            android:layout_weight="2">

                            <androidx.appcompat.widget.AppCompatEditText
                                android:id="@+id/password_confirm_edittext"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:drawableStart="@drawable/ic_credit_limit"
                                android:drawablePadding="@dimen/dp10"
                                android:gravity="start|center"
                                android:hint="@string/image_confirm_password"
                                android:inputType="textPassword"
                                android:textColor="@color/black" />
                        </com.google.android.material.textfield.TextInputLayout>
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp20"
                        android:baselineAligned="false"
                        android:orientation="horizontal"
                        android:weightSum="6">

                        <FrameLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="2">

                            <androidx.appcompat.widget.AppCompatSpinner
                                android:id="@+id/spinner_role"
                                android:layout_width="match_parent"
                                android:layout_height="@dimen/default_spinner_height"
                                android:layout_marginTop="5dp"
                                android:entries="@array/type_admin_spinner"
                                android:textSize="12sp"
                                android:textStyle="normal" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/spinner_unit_tint"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/dp_10"
                                android:background="@color/white"
                                android:paddingLeft="4dp"
                                android:paddingRight="4dp"
                                android:text="@string/label_role_req"
                                android:textSize="12sp"
                                android:visibility="visible" />
                        </FrameLayout>

                        <FrameLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp10"
                            android:layout_weight="2">

                            <com.rising.high.tech.bigultimatenavdraw.util.MultiSelectSpinner
                                android:id="@+id/spinner_station"
                                android:layout_width="match_parent"
                                android:layout_height="@dimen/default_spinner_height"
                                android:layout_marginTop="5dp" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/spinner_station_tint"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/dp_10"
                                android:background="@color/white"
                                android:paddingLeft="4dp"
                                android:paddingRight="4dp"
                                android:text="@string/label_locationnn"
                                android:textSize="12sp"
                                android:visibility="visible" />
                        </FrameLayout>
                    </LinearLayout>
                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:divider="@android:color/transparent"
                android:dividerHeight="0.0px"
                android:elevation="8dp"
                android:visibility="visible"
                app:cardElevation="2dp"
                app:cardPreventCornerOverlap="false"
                app:cardUseCompatPadding="true">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:padding="@dimen/dp10">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@color/black"
                        android:text="@string/label_more_info"/>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp20"
                        android:baselineAligned="false"
                        android:orientation="horizontal"
                        android:weightSum="6">


                        <com.google.android.material.textfield.TextInputLayout
                            android:id="@+id/manage_stock_container"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="2">

                            <androidx.appcompat.widget.AppCompatEditText
                                android:id="@+id/birth_date_txt"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:drawableStart="@drawable/ic_baseline_calendar_today_24"
                                android:drawablePadding="@dimen/dp10"
                                android:focusable="false"
                                android:gravity="start|center"
                                android:hint="@string/label_date_naissance"
                                android:inputType="textCapWords"
                                android:textColor="@color/black" />
                        </com.google.android.material.textfield.TextInputLayout>

                        <FrameLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp10"
                            android:layout_weight="2">

                            <androidx.appcompat.widget.AppCompatSpinner
                                android:id="@+id/spinner_gender"
                                android:layout_width="match_parent"
                                android:layout_height="@dimen/default_spinner_height"
                                android:layout_marginTop="5dp"
                                android:entries="@array/array_gender_type"
                                android:textSize="12sp"
                                android:textStyle="normal" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/dp_10"
                                android:background="@color/white"
                                android:paddingLeft="4dp"
                                android:paddingRight="4dp"
                                android:text="@string/label_gender"
                                android:textSize="12sp"
                                android:visibility="visible" />
                        </FrameLayout>
                        <FrameLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp10"
                            android:layout_weight="2">

                            <androidx.appcompat.widget.AppCompatSpinner
                                android:id="@+id/spinner_marital_status"
                                android:layout_width="match_parent"
                                android:layout_height="@dimen/default_spinner_height"
                                android:layout_marginTop="5dp"
                                android:entries="@array/array_marital_status"
                                android:textSize="12sp"
                                android:textStyle="normal" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/dp_10"
                                android:background="@color/white"
                                android:paddingLeft="4dp"
                                android:paddingRight="4dp"
                                android:text="@string/label_material_status"
                                android:textSize="12sp"
                                android:visibility="visible" />
                        </FrameLayout>
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp20"
                        android:baselineAligned="false"
                        android:orientation="horizontal"
                        android:weightSum="6">

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="2">

                            <androidx.appcompat.widget.AppCompatEditText
                                android:id="@+id/blood_group"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:drawableStart="@drawable/ic_country_svg"
                                android:drawablePadding="@dimen/dp10"
                                android:gravity="start|center"
                                android:hint="@string/label_blood_group"
                                android:inputType="textCapWords"
                                android:textColor="@color/black" />
                        </com.google.android.material.textfield.TextInputLayout>

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp10"
                            android:layout_weight="2">

                            <androidx.appcompat.widget.AppCompatEditText
                                android:id="@+id/contact_number"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:drawableStart="@drawable/ic_location_address"
                                android:drawablePadding="@dimen/dp10"
                                android:gravity="start|center"
                                android:hint="@string/image_contact_number"
                                android:inputType="textCapWords"
                                android:textColor="@color/black" />
                        </com.google.android.material.textfield.TextInputLayout>

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp10"
                            android:layout_weight="2">

                            <androidx.appcompat.widget.AppCompatEditText
                                android:id="@+id/social_media"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:drawableStart="@drawable/ic_shipping_address"
                                android:drawablePadding="@dimen/dp10"
                                android:gravity="start|center"
                                android:hint="@string/image_social_media_link"
                                android:inputType="textCapWords"
                                android:textColor="@color/black" />
                        </com.google.android.material.textfield.TextInputLayout>


                    </LinearLayout>
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/dp20"
                        android:baselineAligned="false"
                        android:orientation="horizontal"
                        android:weightSum="6">

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="2">

                            <androidx.appcompat.widget.AppCompatEditText
                                android:id="@+id/permanante_adress"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:drawableStart="@drawable/ic_country_svg"
                                android:drawablePadding="@dimen/dp10"
                                android:gravity="start|center"
                                android:hint="@string/label_par_adress"
                                android:inputType="textCapWords"
                                android:textColor="@color/black" />
                        </com.google.android.material.textfield.TextInputLayout>

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp10"
                            android:layout_weight="2">

                            <androidx.appcompat.widget.AppCompatEditText
                                android:id="@+id/current_adress"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:drawableStart="@drawable/ic_location_address"
                                android:drawablePadding="@dimen/dp10"
                                android:gravity="start|center"
                                android:hint="@string/label_current_adress"
                                android:inputType="textCapWords"
                                android:textColor="@color/black" />
                        </com.google.android.material.textfield.TextInputLayout>

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/light_white_"
                android:gravity="center"
                android:orientation="horizontal"
                android:padding="@dimen/padding_top">

                <androidx.appcompat.widget.AppCompatButton
                    android:id="@+id/id_back"
                    android:layout_width="150dp"
                    android:layout_height="@dimen/dp50"
                    android:text="@string/label_close" />

                <androidx.appcompat.widget.AppCompatButton
                    android:id="@+id/add_btn"
                    android:layout_width="150dp"
                    android:layout_height="@dimen/dp50"
                    android:layout_marginStart="@dimen/dp20"
                    android:text="@string/label_add" />

            </LinearLayout>
        </LinearLayout>
    </ScrollView>

</RelativeLayout>