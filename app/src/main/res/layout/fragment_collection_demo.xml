<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context=".ui.contact.staps.CollectionFragment">
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="@dimen/activity_horizontal_margin">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:gravity="center"
            android:orientation="horizontal">

            <ImageView
                android:layout_width="40px"
                android:layout_height="40px"
                android:src="@drawable/smart_contact_ic" />

            <TextView
                android:id="@+id/contact_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/activity_horizontal_margin"
                android:textColor="@color/blue_txt"
                android:textSize="14dp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/customer_type"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginLeft="@dimen/activity_left_margin"
                />
            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/id_back"
                android:layout_width="100dp"
                android:layout_height="@dimen/dp50"
                android:background="@drawable/bg_gradient"
                android:layout_marginStart="@dimen/dp20"
                android:text="@string/label_close" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Adress "
                android:textColor="@color/blue_txt"
                android:textSize="12dp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/customer_adress"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/activity_left_margin"
                android:text=" " />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/label_mobile"
                android:textColor="@color/blue_txt"
                android:textSize="12dp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/customer_mobile"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/activity_left_margin"
                android:text=" " />
        </LinearLayout>
    </LinearLayout>
    <androidx.viewpager.widget.ViewPager
        xmlns:android="http://schemas.android.com/apk/res/android"
        android:id="@+id/pager"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.google.android.material.tabs.TabLayout
            android:id="@+id/tab_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

    </androidx.viewpager.widget.ViewPager>

</LinearLayout>