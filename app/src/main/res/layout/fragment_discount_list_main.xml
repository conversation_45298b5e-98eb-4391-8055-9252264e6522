<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:padding="@dimen/activity_horizontal_margin">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="7dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"

                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/label_discounts"
                    android:textColor="@color/black"
                    android:textSize="20dp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="5dp"
                    android:text="@string/label_all_dsicounts"
                    android:textColor="@color/gray"
                    android:textSize="14dp" />
            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="2dp"
                android:background="@color/blue_dark" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/filter_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_gradient"
            android:paddingHorizontal="5dp"
            android:paddingVertical="@dimen/activity_left_margin">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawableLeft="@drawable/ic_baseline_filter_alt_24"
                android:drawablePadding="@dimen/dp10"
                android:text="Filtres"
                android:textColor="@color/white"
                android:textSize="16dp" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp10"
            android:gravity="start"
            android:layout_gravity="center"
            android:orientation="horizontal"
            android:weightSum="4"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/include2">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/activity_horizontal_margin"
                android:layout_weight="3.5"
                android:background="@drawable/ic_seach_background"
                android:gravity="center"
                android:orientation="horizontal"
                android:padding="3dp">

                <androidx.appcompat.widget.SearchView
                    android:id="@+id/id_search_edit"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="#fff"
                    android:elevation="2dp"
                    android:focusable="true"
                    app:queryHint="Search Here"></androidx.appcompat.widget.SearchView>

            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_gravity="center|end"
                android:layout_weight="0.5"
                android:gravity="center|end"
                android:orientation="horizontal">

                <androidx.appcompat.widget.AppCompatButton
                    android:id="@+id/id_add"
                    android:layout_width="0dp"
                    android:layout_height="@dimen/dp50"
                    android:layout_marginStart="@dimen/dp5"
                    android:layout_weight="1"
                    android:background="@drawable/bg_btn_green"
                    android:text="@string/label_add" />

            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="horizontal"
            android:paddingTop="10dp"
            android:visibility="gone"

            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/include2">

            <Spinner
                style="@style/my_spinner_style"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:entries="@array/type_select_marque" />

            <Spinner
                style="@style/my_spinner_style"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:entries="@array/type_select_affaire_lieu" />

            <Spinner
                style="@style/my_spinner_style"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:entries="@array/type_select_active" />

            <CheckBox
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/activity_horizontal_margin"
                android:layout_weight="1"
                android:text="@string/label_pas_avendre" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="horizontal"
            android:padding="@dimen/padding_top"
            android:visibility="gone">

            <Button
                style="@style/my_btn_style"
                android:layout_width="wrap_content"
                android:layout_height="70px"

                android:text="@string/label_add" />

            <Button
                style="@style/my_btn_style"
                android:layout_width="wrap_content"
                android:layout_height="70px"

                android:text="@string/label_search" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="@dimen/dp20"
            android:background="@drawable/bg_gradient"
            android:gravity="center"
            android:orientation="horizontal"
            android:padding="@dimen/dp20"
            android:weightSum="11">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/text_name"
                android:textColor="@color/white"
                android:textStyle="bold" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/label_starts_at"
                android:textColor="@color/white"
                android:textStyle="bold" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/label_ends_At"
                android:textColor="@color/white"
                android:textStyle="bold" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/label_discount_amount"
                android:textColor="@color/white"
                android:textStyle="bold" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/label_discount_type"
                android:textColor="@color/white"
                android:textStyle="bold" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/label_priority"
                android:textColor="@color/white"
                android:textStyle="bold" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/label_brands"
                android:textColor="@color/white"
                android:textStyle="bold" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/string_pcategory"
                android:textColor="@color/white"
                android:textStyle="bold" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/image_product_name"
                android:textColor="@color/white"
                android:textStyle="bold" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/label_location"
                android:textColor="@color/white"
                android:textStyle="bold" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/label_btn_action"
                android:textColor="@color/white"
                android:textStyle="bold" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:divider="@android:color/transparent"
                android:dividerHeight="0.0px"
                android:elevation="8dp"
                android:visibility="visible"
                app:cardElevation="2dp"
                app:cardPreventCornerOverlap="false"
                app:cardUseCompatPadding="true">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recycle_customers"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"/>

                <TextView
                    android:id="@+id/noItemFound"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:gravity="center"
                    android:padding="@dimen/dp20"
                    android:text="@string/no_discounts_found"
                    android:textColor="@color/black"
                    android:textSize="16sp">

                </TextView>
            </androidx.cardview.widget.CardView>
        </LinearLayout>

    </LinearLayout>

</LinearLayout>