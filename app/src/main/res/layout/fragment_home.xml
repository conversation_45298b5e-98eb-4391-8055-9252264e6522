<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/linearLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_margin="@dimen/dp10"
    android:orientation="vertical"
    tools:context=".ui.home.HomeFragment">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="horizontal">

        <LinearLayout
            android:id="@+id/layout_right_side"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:orientation="vertical">
            <include layout="@layout/activity_right_side" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/layout_left_side"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginStart="@dimen/dp10"
            android:layout_weight="2"
            android:visibility="visible">
            <include layout="@layout/activity_left_side" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/layout_left_side_multi_pay"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginStart="@dimen/dp10"
            android:layout_weight="2"
            android:visibility="gone">
            <include layout="@layout/activity_multi_pay" />
        </LinearLayout>

    </LinearLayout>
</LinearLayout>