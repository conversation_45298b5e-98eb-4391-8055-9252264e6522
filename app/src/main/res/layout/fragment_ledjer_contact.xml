<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:padding="@dimen/activity_horizontal_margin">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="7dp">

                <View
                    android:layout_width="match_parent"
                    android:layout_height="2dp"
                    android:background="@color/blue_dark" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/cell_shape_border"
                android:orientation="horizontal">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_weight="2"
                    android:orientation="vertical"
                    android:padding="@dimen/activity_vertical_margin">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/activity_left_margin"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/label_date_range"
                            android:textColor="@color/blue_txt" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:orientation="horizontal"
                            android:weightSum="3"
                            android:paddingTop="@dimen/margin_vertical">

                            <EditText
                                android:id="@+id/new_start_date"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:focusable="false"
                                android:textSize="14sp"
                                android:inputType="textCapWords"
                                android:hint="@string/label_starts_at" />

                            <EditText
                                android:id="@+id/new_end_date"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:focusable="false"
                                android:textSize="14sp"
                                android:inputType="textCapWords"
                                android:hint="@string/label_ends_At" />

                            <androidx.appcompat.widget.AppCompatButton
                                android:id="@+id/id_filter"
                                android:layout_width="match_parent"
                                android:layout_height="@dimen/dp40"
                                android:layout_weight="1"
                                android:background="@drawable/bg_gradient"
                                android:layout_marginStart="@dimen/dp20"
                                android:text="@string/label_filter" />

                        </LinearLayout>

                    </LinearLayout>


                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/activity_left_margin"
                        android:background="@color/blue_txt"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:text="@string/label_to"
                            android:textColor="@color/white"
                            android:textStyle="bold" />


                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/activity_left_margin"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/contact_name"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="Walk In Customer"
                            android:textColor="@color/blue_txt" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:orientation="horizontal"
                            android:paddingTop="@dimen/margin_vertical">

                            <ImageView
                                android:layout_width="30px"
                                android:layout_height="30px"
                                android:src="@drawable/icons8_user" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="@string/label_mobile" />
                            <TextView
                                android:id="@+id/customer_mobile"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="@string/image_mobile" />
                        </LinearLayout>

                    </LinearLayout>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    android:padding="@dimen/activity_vertical_margin">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="end"
                        android:layout_marginHorizontal="@dimen/activity_left_margin"
                        android:gravity="right"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/tv_company_name"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/blue_txt" />


                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/activity_left_margin"
                        android:orientation="vertical">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginHorizontal="@dimen/activity_left_margin"
                            android:background="@color/blue_txt"
                            android:gravity="end"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="match_parent"
                                android:text="@string/label_acc_summary"
                                android:textColor="@color/white"
                                android:textSize="14dp"
                                android:textStyle="bold" />


                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginHorizontal="@dimen/activity_left_margin"
                            android:gravity="center"
                            android:orientation="horizontal"
                            android:paddingTop="@dimen/margin_vertical">

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="@string/txt_opening_balance" />

                            <TextView
                                android:id="@+id/opening_balance_txt"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_gravity="end"
                                android:layout_weight="1"
                                android:inputType="numberDecimal"
                                android:text="00.0$"
                                android:textAlignment="textEnd" />
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginHorizontal="@dimen/activity_left_margin"
                            android:gravity="center"
                            android:orientation="horizontal"
                            android:paddingTop="@dimen/margin_vertical">

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="@string/text_total_invoice" />

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_gravity="end"
                                android:layout_weight="1"
                                android:inputType="numberDecimal"
                                android:text="00.0$"
                                android:textAlignment="textEnd" />
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginHorizontal="@dimen/activity_left_margin"
                            android:gravity="center"
                            android:orientation="horizontal"
                            android:paddingTop="@dimen/margin_vertical">

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="@string/string_total_paid" />

                            <TextView
                                android:id="@+id/total_paid_txt"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_gravity="end"
                                android:layout_weight="1"
                                android:inputType="numberDecimal"
                                android:text="00.0$"
                                android:textAlignment="textEnd" />
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginHorizontal="@dimen/activity_left_margin"
                            android:gravity="center"
                            android:orientation="horizontal"
                            android:paddingTop="@dimen/margin_vertical">

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="@string/string_advence_balance" />

                            <TextView
                                android:id="@+id/advanced_balance_txt"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_gravity="end"
                                android:layout_weight="1"
                                android:inputType="numberDecimal"
                                android:text="00.0$"
                                android:textAlignment="textEnd" />
                        </LinearLayout>

                    </LinearLayout>


                </LinearLayout>


            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="1px"
                android:layout_marginHorizontal="50dp"
                android:layout_marginVertical="@dimen/activity_vertical_margin"
                android:background="@color/gray" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/cell_shape_border"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:background="@drawable/cell_shape"
                    android:gravity="center"
                    android:orientation="horizontal"
                    android:padding="20px">


                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/label_date"
                        android:textColor="@color/white"
                        android:textStyle="bold" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/txt_typpe"
                        android:textColor="@color/white"
                        android:textStyle="bold" />

                    <TextView
                        android:visibility="gone"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/string_customer_name"
                        android:textColor="@color/white"
                        android:textStyle="bold" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/label_location"
                        android:textColor="@color/white"
                        android:textStyle="bold" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/label_status_payement"
                        android:textColor="@color/white"
                        android:textStyle="bold" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/label_status_method"
                        android:textColor="@color/white"
                        android:textStyle="bold" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/label_montant_total"
                        android:textColor="@color/white"
                        android:textStyle="bold" />

                    <LinearLayout

                        android:visibility="gone"
                        android:id="@+id/btn_detail"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_weight="1"

                        android:gravity="center"
                        android:orientation="horizontal">

                        <ImageView
                            android:layout_width="30px"
                            android:layout_height="30px"
                            android:src="@drawable/icons8_eye"
                            android:visibility="invisible"
                            app:tint="@color/black" />

                        <TextView
                            android:visibility="gone"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:paddingLeft="3dp"
                            android:text="@string/label_btn_action"
                            android:textColor="@color/black"
                            android:textStyle="bold" />
                    </LinearLayout>

                </LinearLayout>
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <androidx.cardview.widget.CardView
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:clipChildren="false"
                        android:clipToPadding="false"
                        android:divider="@android:color/transparent"
                        android:dividerHeight="0.0px"
                        android:elevation="8dp"
                        android:visibility="visible"
                        app:cardElevation="2dp"
                        app:cardPreventCornerOverlap="false"
                        app:cardUseCompatPadding="true">

                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/recycle_ledger"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:visibility="visible" />

                        <TextView
                            android:id="@+id/noItemFound"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_gravity="center"
                            android:gravity="center"
                            android:padding="@dimen/dp20"
                            android:text="@string/no_sales_found"
                            android:textColor="@color/black"
                            android:textSize="16sp"
                            android:visibility="gone">

                        </TextView>
                    </androidx.cardview.widget.CardView>
                </LinearLayout>


            </LinearLayout>


        </LinearLayout>

    </LinearLayout>

</ScrollView>