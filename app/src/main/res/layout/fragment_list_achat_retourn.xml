<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:background="@color/white">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:padding="@dimen/activity_horizontal_margin">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="7dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/label_unit_list_retourn"
                    android:textColor="@color/black"
                    android:textSize="20dp" />

            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="2dp"
                android:background="@color/blue_dark" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/filter_header"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_gradient"
            android:paddingHorizontal="5dp"
            android:paddingVertical="@dimen/activity_left_margin">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawableLeft="@drawable/ic_baseline_filter_alt_24"
                android:text="Filtres"
                android:drawablePadding="@dimen/dp10"
                android:textColor="@color/white"
                android:textSize="16dp" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/filter_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="horizontal"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/include2">

            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:elevation="8dp"
                android:visibility="visible"
                android:divider="@android:color/transparent"
                android:dividerHeight="0.0px"
                android:clipToPadding="false"
                android:clipChildren="false"
                app:cardUseCompatPadding="true"
                app:cardElevation="2dp"
                app:cardPreventCornerOverlap="false">
                <LinearLayout
                    android:layout_width="match_parent"
                    android:orientation="vertical"
                    android:layout_height="wrap_content">
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="start"
                        android:orientation="horizontal"
                        android:padding="@dimen/dp10"
                        android:weightSum="12"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/include2">
                        <FrameLayout
                            android:layout_width="0dp"
                            android:layout_weight="2"
                            android:layout_height="wrap_content">

                            <androidx.appcompat.widget.AppCompatSpinner
                                android:id="@+id/spinner_station"
                                android:layout_width="match_parent"
                                android:layout_height="@dimen/dp50"
                                android:layout_marginTop="5dp"
                                android:entries="@array/type_produc_type"
                                android:textSize="12sp"
                                android:textStyle="normal" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/spinner_prod_type_tint"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/dp_10"
                                android:background="@color/white"
                                android:paddingLeft="4dp"
                                android:visibility="visible"
                                android:text="@string/label_station"
                                android:paddingRight="4dp"
                                android:textSize="12sp" />
                        </FrameLayout>
                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="0dp"
                            android:layout_weight="2"
                            android:layout_marginStart="@dimen/dp10"
                            android:layout_height="55dp">
                            <androidx.appcompat.widget.AppCompatEditText
                                android:id="@+id/new_start_date"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:gravity="start|center"
                                android:focusable="false"
                                android:textColor="@color/black"
                                android:hint="Start Date"
                                android:inputType="textCapWords"
                                />
                        </com.google.android.material.textfield.TextInputLayout>
                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="0dp"
                            android:layout_weight="2"
                            android:layout_marginStart="@dimen/dp10"
                            android:layout_height="55dp">
                            <androidx.appcompat.widget.AppCompatEditText
                                android:id="@+id/new_end_date"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:gravity="start|center"
                                android:focusable="false"
                                android:textColor="@color/black"
                                android:hint="End date"
                                android:inputType="textCapWords"
                                />
                        </com.google.android.material.textfield.TextInputLayout>

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_weight="2"
                            android:gravity="center|end"
                            android:paddingTop="@dimen/dp5"
                            android:layout_gravity="center|end"
                            android:orientation="horizontal">

                            <androidx.appcompat.widget.AppCompatButton
                                android:id="@+id/id_filter"
                                android:layout_width="0dp"
                                android:layout_weight="1"
                                android:background="@drawable/bg_btn_blue"
                                android:layout_marginStart="@dimen/dp10"
                                android:layout_height="@dimen/dp50"
                                android:text="@string/label_filter" />

                            <androidx.appcompat.widget.AppCompatButton
                                android:id="@+id/id_add"
                                android:layout_width="0dp"
                                android:layout_height="@dimen/dp50"
                                android:layout_weight="1"
                                android:background="@drawable/bg_btn_green"
                                android:layout_marginStart="@dimen/dp5"
                                android:text="@string/label_add" />

                        </LinearLayout>
                    </LinearLayout>

                </LinearLayout>


            </androidx.cardview.widget.CardView>

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="horizontal"
            android:paddingTop="10dp"
            android:visibility="gone"

            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/include2">

            <Spinner
                style="@style/my_spinner_style"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:entries="@array/type_select_marque" />

            <Spinner
                style="@style/my_spinner_style"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:entries="@array/type_select_affaire_lieu" />

            <Spinner
                style="@style/my_spinner_style"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:entries="@array/type_select_active" />

            <CheckBox
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/activity_horizontal_margin"
                android:layout_weight="1"
                android:text="@string/label_pas_avendre" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="@dimen/activity_left_margin"
            android:background="@drawable/bg_gradient"
            android:gravity="center"
            android:orientation="horizontal"
            android:padding="@dimen/dp5">


            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/label_date"
                android:textColor="@color/white"
                android:textStyle="bold" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/label_reference_no"
                android:textColor="@color/white"
                android:textStyle="bold" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/label_location"
                android:textColor="@color/white"
                android:textStyle="bold" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/label_supplier"
                android:textColor="@color/white"
                android:textStyle="bold" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/label_added_by"
                android:textColor="@color/white"
                android:textStyle="bold"
                android:visibility="gone" />

            <TextView
                android:visibility="gone"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/label_purchase_status"
                android:textColor="@color/white"
                android:textStyle="bold" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/label_paiment_status_label"
                android:textColor="@color/white"
                android:textStyle="bold" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/labele_grand_total"
                android:textColor="@color/white"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/textView11"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/image_payement_du"
                android:textColor="@color/white"
                android:textStyle="bold" />

            <LinearLayout
                android:id="@+id/btn_detail"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_weight="1"

                android:gravity="center"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="30px"
                    android:layout_height="30px"

                    android:src="@drawable/icons8_eye"
                    app:tint="@color/white"
                    android:visibility="invisible" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:paddingLeft="3dp"
                    android:text="@string/label_btn_action"
                    android:textColor="@color/white"
                    android:textStyle="bold" />
            </LinearLayout>

        </LinearLayout>
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:elevation="8dp"
            android:visibility="visible"
            android:divider="@android:color/transparent"
            android:dividerHeight="0.0px"
            android:clipToPadding="false"
            android:clipChildren="false"
            app:cardUseCompatPadding="true"
            app:cardElevation="2dp"
            app:cardPreventCornerOverlap="false">
        <LinearLayout
            android:layout_width="match_parent"
            android:orientation="vertical"
            android:layout_height="wrap_content"
          >

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recycler_purchases"
                android:layout_width="match_parent"
                tools:itemCount="2"
                tools:listitem="@layout/list_purchase_return_item"
                android:layout_height="wrap_content" />
            <LinearLayout
                android:id="@+id/total_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:background="@color/greyLight"
                android:gravity="center"
                android:orientation="horizontal"
                android:padding="20px">


                <TextView
                    android:id="@+id/purchase_date"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/label_date"
                    android:textColor="@color/black"
                    android:textStyle="normal"
                    android:visibility="invisible" />

                <TextView
                    android:id="@+id/purchase_ref"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/label_total"
                    android:textColor="@color/black"
                    android:textSize="15dp"
                    android:textStyle="bold"
                    android:visibility="visible" />

                <TextView
                    android:id="@+id/purchase_location"

                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/label_location"
                    android:textColor="@color/black"
                    android:textStyle="normal"
                    android:visibility="invisible" />

                <TextView
                    android:id="@+id/purchase_supplier"

                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/label_supplier"
                    android:textColor="@color/black"
                    android:textStyle="normal"
                    android:visibility="invisible" />

                <TextView
                    android:id="@+id/purchase_added_by"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/label_added_by"
                    android:textColor="@color/black"
                    android:textStyle="normal"
                    android:visibility="gone" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <LinearLayout
                        android:id="@+id/container_ordered"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:paddingVertical="@dimen/margin_vertical"
                        android:orientation="horizontal"
                        android:visibility="gone">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/black"
                            android:textStyle="bold"
                            android:text="@string/label_ordered" />

                        <TextView
                            android:id="@+id/ordered_count"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textStyle="bold"
                            android:textColor="@color/black"
                            android:layout_marginLeft="@dimen/padding_horizontal"
                            android:text="@string/label_filter" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/container_received"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:paddingVertical="@dimen/margin_vertical"
                        android:orientation="horizontal"
                        android:visibility="gone">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/black"
                            android:textStyle="bold"
                            android:text="@string/label_received" />

                        <TextView
                            android:id="@+id/received_count"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textStyle="bold"

                            android:textColor="@color/black"

                            android:layout_marginLeft="@dimen/padding_horizontal"
                            android:text="@string/label_filter" />
                    </LinearLayout>
                    <LinearLayout
                        android:id="@+id/container_pending"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:paddingVertical="@dimen/margin_vertical"
                        android:orientation="horizontal"
                        android:visibility="gone">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/black"
                            android:textStyle="bold"

                            android:text="@string/label_pending" />

                        <TextView
                            android:id="@+id/pending_count"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/black"
                            android:textStyle="bold"

                            android:layout_marginLeft="@dimen/padding_horizontal"
                            android:text="@string/label_filter" />
                    </LinearLayout>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <LinearLayout
                        android:id="@+id/container_paid"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:paddingVertical="@dimen/margin_vertical"
                        android:orientation="horizontal"
                        android:visibility="gone">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/black"
                            android:textStyle="bold"

                            android:text="@string/label_paid" />

                        <TextView
                            android:id="@+id/paid_count"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/black"
                            android:textStyle="bold"

                            android:layout_marginLeft="@dimen/padding_horizontal"
                            android:text="@string/label_filter" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/container_partial"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:paddingVertical="@dimen/margin_vertical"
                        android:orientation="horizontal"
                        android:visibility="gone">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/black"
                            android:textStyle="bold"

                            android:text="@string/label_partial" />

                        <TextView
                            android:id="@+id/partial_count"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/black"
                            android:textStyle="bold"

                            android:layout_marginLeft="@dimen/padding_horizontal"
                            android:text="@string/label_filter" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/container_due"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:paddingVertical="@dimen/margin_vertical"
                        android:orientation="horizontal"
                        android:visibility="gone">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/black"
                            android:textStyle="bold"

                            android:text="@string/label_due" />

                        <TextView
                            android:id="@+id/due_count"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/black"
                            android:textStyle="bold"
                            android:layout_marginLeft="@dimen/padding_horizontal"
                            android:text="@string/label_filter" />
                    </LinearLayout>

                </LinearLayout>
                <TextView
                    android:id="@+id/purchase_grand_total"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:textColor="@color/black"
                    android:textStyle="bold" />
                <TextView
                    android:id="@+id/purchase_payment_status"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/label_paiment_status_label"
                    android:textColor="@color/black"
                    android:textStyle="normal"
                    android:visibility="invisible" />




                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:visibility="invisible">


                </LinearLayout>

            </LinearLayout>

            <TextView
                android:id="@+id/noItemFound"
                android:layout_width="match_parent"
                android:gravity="center"
                android:textSize="16sp"
                android:padding="@dimen/dp20"
                android:layout_gravity="center"
                android:textColor="@color/black"
                android:text="@string/no_purchase_return_found"
                android:layout_height="wrap_content">

            </TextView>
        </LinearLayout>

        </androidx.cardview.widget.CardView>
    </LinearLayout>

</LinearLayout>