<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:padding="@dimen/activity_horizontal_margin">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="7dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"

                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/string_customer_groups"
                    android:textColor="@color/black"
                    android:textSize="20dp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="5dp"
                    android:text="@string/label_manage_customer_groups"
                    android:textColor="@color/gray"
                    android:textSize="14dp" />
            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="2dp"
                android:background="@color/blue_dark" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_gradient"
            android:padding="5dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawableLeft="@drawable/ic_baseline_filter_alt_24"
                android:drawablePadding="@dimen/dp10"
                android:text="@string/filters"
                android:textColor="@color/white"
                android:textSize="16dp" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/cell_shape_border"
            android:gravity="center"
            android:orientation="horizontal"
            android:padding="10dp"
            android:weightSum="4"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/include2">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginRight="@dimen/activity_horizontal_margin"
                android:layout_weight="3"
                android:background="@drawable/ic_seach_background"
                android:gravity="center"
                android:orientation="horizontal"
                android:padding="3dp">

                <androidx.appcompat.widget.AppCompatEditText
                    android:id="@+id/id_search_edit"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="#fff"></androidx.appcompat.widget.AppCompatEditText>


            </LinearLayout>

            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/id_filter"
                android:layout_width="0dp"
                android:layout_height="@dimen/dp50"
                android:layout_marginStart="@dimen/dp10"
                android:layout_weight="0.5"
                android:background="@drawable/bg_btn_blue"
                android:text="@string/label_search" />

            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/id_add"
                android:layout_width="0dp"
                android:layout_height="@dimen/dp50"
                android:layout_marginStart="@dimen/dp10"
                android:layout_weight="0.5"
                android:background="@drawable/bg_btn_green"
                android:text="@string/label_add" />

        </LinearLayout>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="horizontal"
            android:paddingTop="10dp"
            android:visibility="gone"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/include2">

            <Spinner
                style="@style/my_spinner_style"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:entries="@array/type_select_marque" />

            <Spinner
                style="@style/my_spinner_style"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:entries="@array/type_select_affaire_lieu" />

            <Spinner
                style="@style/my_spinner_style"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:entries="@array/type_select_active" />

            <CheckBox
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/activity_horizontal_margin"
                android:layout_weight="1"
                android:text="@string/label_pas_avendre" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="@dimen/dp20"
            android:background="@drawable/bg_gradient"
            android:gravity="center"
            android:orientation="horizontal"
            android:padding="@dimen/dp20"
            android:weightSum="3">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/lbl_customer_group_name"
                android:textColor="@color/white"
                android:textStyle="bold" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/string_calculation_percentage"
                android:textColor="@color/white"
                android:textStyle="bold" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/label_btn_action"
                android:textAlignment="center"
                android:textColor="@color/white"
                android:textStyle="bold" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:divider="@android:color/transparent"
                android:dividerHeight="0.0px"
                android:elevation="8dp"
                android:visibility="visible"
                app:cardElevation="2dp"
                app:cardPreventCornerOverlap="false"
                app:cardUseCompatPadding="true">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recycler"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:visibility="gone"
                    tools:listitem="@layout/list_product_item" />

                <TextView
                    android:id="@+id/noProductFound"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:gravity="center"
                    android:padding="@dimen/dp20"
                    android:text="@string/no_customer_groups_found"
                    android:textColor="@color/black"
                    android:textSize="16sp">

                </TextView>
            </androidx.cardview.widget.CardView>
        </LinearLayout>

    </LinearLayout>

</LinearLayout>