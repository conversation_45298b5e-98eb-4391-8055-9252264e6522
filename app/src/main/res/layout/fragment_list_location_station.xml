<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:padding="@dimen/activity_horizontal_margin">


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="7dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"

                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/label_business_location"
                    android:textColor="@color/black"
                    android:textSize="20dp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="5dp"
                    android:text="@string/label_our_business_location_gerer"
                    android:textColor="@color/gray"
                    android:textSize="14dp" />
            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="2dp"
                android:background="@color/blue_dark" />
        </LinearLayout>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="start"
            android:layout_gravity="center"
            android:orientation="horizontal"
            android:layout_marginTop="@dimen/dp10"
            android:weightSum="4"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/include2">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/activity_horizontal_margin"
                android:layout_weight="3.5"
                android:background="@drawable/ic_seach_background"
                android:gravity="center"
                android:orientation="horizontal"
                android:padding="3dp">
                <androidx.appcompat.widget.SearchView
                    android:id="@+id/id_search_edit"
                    android:layout_width="match_parent"
                    android:elevation="2dp"
                    android:background="#fff"
                    android:focusable="true"
                    app:queryHint="Search Here"
                    android:layout_height="wrap_content">
                </androidx.appcompat.widget.SearchView>


            </LinearLayout>
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="0.5"
                android:gravity="center|end"
                android:layout_gravity="center|end"
                android:orientation="horizontal">

                <androidx.appcompat.widget.AppCompatButton
                    android:id="@+id/id_filter"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:visibility="gone"
                    android:layout_height="@dimen/dp40"
                    android:text="@string/label_filter" />

                <androidx.appcompat.widget.AppCompatButton
                    android:id="@+id/btn_add"
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:layout_height="@dimen/dp50"
                    android:background="@drawable/bg_btn_green"
                    android:layout_marginStart="@dimen/dp5"
                    android:text="@string/label_add" />

            </LinearLayout>

        </LinearLayout>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="horizontal"
            android:paddingTop="10dp"
            android:visibility="gone"

            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/include2">

            <Spinner
                style="@style/my_spinner_style"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:entries="@array/type_select_marque" />

            <Spinner
                style="@style/my_spinner_style"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:entries="@array/type_select_affaire_lieu" />

            <Spinner
                style="@style/my_spinner_style"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:entries="@array/type_select_active" />

            <CheckBox
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/activity_horizontal_margin"
                android:layout_weight="1"
                android:text="@string/label_pas_avendre" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="45dp"
            android:layout_gravity="center"
            android:layout_marginTop="@dimen/activity_left_margin"
            android:background="@drawable/bg_gradient"
            android:gravity="center"
            android:orientation="horizontal"
            android:weightSum="6">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp10"
                android:layout_weight="1"
                android:gravity="center|start"
                android:text="@string/label_name"
                android:textColor="@color/white"
                android:textStyle="bold" />


            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/image_ville_txt"
                android:textColor="@color/white"
                android:textStyle="bold" />
            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/image_statee_txt"
                android:textColor="@color/white"
                android:textStyle="bold" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/text_pays_txt"
                android:textColor="@color/white"
                android:textStyle="bold" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/image_code_postal_txt"
                android:textColor="@color/white"
                android:textStyle="bold" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Action"
                android:textAlignment="center"
                android:textColor="@color/white"
                android:textStyle="bold" />


        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:divider="@android:color/transparent"
                android:dividerHeight="0.0px"
                android:elevation="8dp"
                android:visibility="visible"
                app:cardElevation="2dp"
                app:cardPreventCornerOverlap="false"
                app:cardUseCompatPadding="true">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recycle_lcoation_station"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:visibility="visible"/>

                <TextView
                    android:id="@+id/noItemFound"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:gravity="center"
                    android:padding="@dimen/dp20"
                    android:text="@string/no_location_station_found"
                    android:textColor="@color/black"
                    android:textSize="16sp"
                    android:visibility="gone">

                </TextView>
            </androidx.cardview.widget.CardView>
        </LinearLayout>

    </LinearLayout>

</LinearLayout>