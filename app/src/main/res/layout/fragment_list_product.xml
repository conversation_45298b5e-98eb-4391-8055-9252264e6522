<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:padding="@dimen/activity_horizontal_margin">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="7dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/label_products"
                    android:textColor="@color/black"
                    android:textSize="20sp"
                    android:textStyle="bold" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="5dp"
                    android:text="@string/label_products_gerer"
                    android:textColor="@color/gray"
                    android:textSize="14sp" />
            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="2dp"
                android:background="@color/blue_dark" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/filter_header"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp40"
            android:background="@drawable/bg_gradient"
            android:baselineAligned="false"
            android:gravity="center"
            android:paddingHorizontal="@dimen/padding_horizontal"
            android:paddingVertical="@dimen/activity_left_margin"
            android:weightSum="10">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp10"
                android:layout_weight="9.5"
                android:drawablePadding="@dimen/dp10"
                android:text="@string/filters"
                android:textColor="@color/white"
                android:textSize="16sp"
                app:drawableStartCompat="@drawable/ic_baseline_filter_alt_24" />

            <ImageView
                android:id="@+id/filterArrow"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="0.5"
                android:src="@drawable/ic_baseline_expand_less_24"
                app:tint="@color/white" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/filter_container"

            android:layout_width="match_parent"
            android:layout_height="wrap_content"

            android:orientation="vertical">

            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:divider="@android:color/transparent"
                android:dividerHeight="0.0px"
                android:elevation="8dp"
                android:visibility="visible"
                app:cardElevation="2dp"
                app:cardPreventCornerOverlap="false"
                app:cardUseCompatPadding="true">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="start"
                        android:orientation="horizontal"
                        android:padding="@dimen/dp10"
                        android:weightSum="10"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/include2">

                        <FrameLayout
                            android:visibility="gone"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1.5">

                            <androidx.appcompat.widget.AppCompatSpinner
                                android:id="@+id/spinner_prod_type"
                                android:layout_width="match_parent"
                                android:layout_height="@dimen/dp50"
                                android:layout_marginTop="5dp"
                                android:entries="@array/type_produc_type"
                                android:textSize="12sp"
                                android:textStyle="normal" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/spinner_prod_type_tint"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/dp_10"
                                android:background="@color/white"
                                android:paddingLeft="4dp"
                                android:paddingRight="4dp"
                                android:text="@string/label_product_typee"
                                android:textSize="12sp"
                                android:visibility="visible" />
                        </FrameLayout>

                        <FrameLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp10"
                            android:layout_weight="1.5">

                            <androidx.appcompat.widget.AppCompatSpinner
                                android:id="@+id/spinner_category"
                                android:layout_width="match_parent"
                                android:layout_height="@dimen/dp50"
                                android:layout_marginTop="5dp"
                                android:textSize="12sp"
                                android:textStyle="normal" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/spinner_category_tint"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/dp_10"
                                android:background="@color/white"
                                android:paddingLeft="4dp"
                                android:paddingRight="4dp"
                                android:text="@string/label_category_name"
                                android:textSize="12sp"
                                android:visibility="visible" />
                        </FrameLayout>

                        <FrameLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp10"
                            android:layout_weight="1.5">

                            <androidx.appcompat.widget.AppCompatSpinner
                                android:id="@+id/spinner_unit"
                                android:layout_width="match_parent"
                                android:layout_height="@dimen/dp50"
                                android:layout_marginTop="5dp"
                                android:entries="@array/type_select_unite"
                                android:textSize="12sp"
                                android:textStyle="normal" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/spinner_unit_tint"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/dp_10"
                                android:background="@color/white"
                                android:paddingLeft="4dp"
                                android:paddingRight="4dp"
                                android:text="@string/label_unit"
                                android:textSize="12sp"
                                android:visibility="visible" />
                        </FrameLayout>

                        <FrameLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp10"
                            android:layout_weight="1.5">

                            <androidx.appcompat.widget.AppCompatSpinner
                                android:id="@+id/spinnerTaxRate"
                                android:layout_width="match_parent"
                                android:layout_height="@dimen/dp50"
                                android:layout_marginTop="5dp"
                                android:entries="@array/array_action_tax"
                                android:textSize="12sp"
                                android:textStyle="normal" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/dp_10"
                                android:background="@color/white"
                                android:paddingLeft="4dp"
                                android:paddingRight="4dp"
                                android:text="@string/label_tax"
                                android:textSize="12sp"
                                android:visibility="visible" />
                        </FrameLayout>

                        <FrameLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp10"
                            android:layout_weight="1.5">

                            <androidx.appcompat.widget.AppCompatSpinner
                                android:id="@+id/spinner_brand"
                                android:layout_width="match_parent"
                                android:layout_height="@dimen/dp50"
                                android:layout_marginTop="5dp"
                                android:textSize="12sp"
                                android:textStyle="normal" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/spinner_brand_tint"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/dp_10"
                                android:background="@color/white"
                                android:paddingLeft="4dp"
                                android:paddingRight="4dp"
                                android:text="@string/string_ubranndt"
                                android:textSize="12sp"
                                android:visibility="visible" />
                        </FrameLayout>

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_gravity="center|end"
                            android:paddingTop="@dimen/dp5"
                            android:layout_weight="2"
                            android:gravity="center|end"
                            android:orientation="horizontal">

                            <androidx.appcompat.widget.AppCompatButton
                                android:id="@+id/id_filter"
                                android:layout_width="0dp"
                                android:layout_height="@dimen/dp50"
                                android:layout_marginStart="@dimen/dp10"
                                android:layout_weight="1"
                                android:background="@drawable/bg_btn_blue"
                                android:text="@string/label_filter" />

                            <androidx.appcompat.widget.AppCompatButton
                                android:id="@+id/id_add"
                                android:layout_width="0dp"
                                android:layout_height="@dimen/dp50"
                                android:layout_marginStart="@dimen/dp5"
                                android:layout_weight="1"
                                android:background="@drawable/bg_btn_green"
                                android:text="@string/label_add" />

                        </LinearLayout>
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:padding="@dimen/dp10"
                        android:visibility="gone"
                        android:weightSum="10">

                        <Spinner
                            android:id="@+id/spinner_station"
                            android:layout_width="0dp"
                            android:layout_height="@dimen/dp50"
                            android:layout_weight="1.5" />
                    </LinearLayout>

                </LinearLayout>


            </androidx.cardview.widget.CardView>
        </LinearLayout>


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="@dimen/dp20"
            android:background="@drawable/bg_gradient"
            android:gravity="center"
            android:orientation="horizontal"
            android:padding="20px"
            android:weightSum="10">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/product_image"
                android:textColor="@color/white"
                android:textStyle="bold" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Name product "
                android:textColor="@color/white"
                android:textStyle="bold" />


            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/label_business_location"
                android:textColor="@color/white"
                android:textStyle="bold" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Categories"
                android:textColor="@color/white"
                android:textStyle="bold" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/label_unit_purchase_pricee"
                android:textColor="@color/white"
                android:textStyle="bold" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/label_selling_pricee"
                android:textColor="@color/white"
                android:textStyle="bold" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="SKU"
                android:textColor="@color/white"
                android:textStyle="bold" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Current Stock"

                android:textColor="@color/white"
                android:textStyle="bold" />
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Tax"
                android:gravity="center"
                android:textColor="@color/white"
                android:textStyle="bold" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Action"
                    android:textColor="@color/white"
                    android:textStyle="bold" />
            </LinearLayout>


        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:divider="@android:color/transparent"
                android:dividerHeight="0.0px"
                android:elevation="8dp"
                android:visibility="visible"
                app:cardElevation="2dp"
                app:cardPreventCornerOverlap="false"
                app:cardUseCompatPadding="true">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recycler_purchases"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    tools:listitem="@layout/list_product_item" />

                <TextView
                    android:id="@+id/noProductFound"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:gravity="center"
                    android:padding="@dimen/dp20"
                    android:text="@string/no_product_found"
                    android:textColor="@color/black"
                    android:textSize="16sp">

                </TextView>
            </androidx.cardview.widget.CardView>
        </LinearLayout>

    </LinearLayout>

</LinearLayout>