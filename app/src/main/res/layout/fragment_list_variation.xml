<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="@dimen/dp_15">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/label_variation"
            android:textColor="@color/black"
            android:textSize="20sp"
            android:textStyle="bold" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="5dp"
            android:text="@string/label_our_variation_gerer"
            android:textColor="@color/gray"
            android:textSize="14sp" />
    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="2dp"
        android:background="@color/blue_dark" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/dp10"
        android:gravity="start"
        android:orientation="horizontal"
        android:weightSum="4"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/include2">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/activity_horizontal_margin"
            android:layout_weight="3.5"
            android:background="@drawable/ic_seach_background"
            android:gravity="center"
            android:orientation="horizontal"
            android:padding="3dp">

            <androidx.appcompat.widget.SearchView
                android:id="@+id/id_search_edit"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="#fff"
                android:elevation="2dp"
                android:focusable="true"
                app:queryHint="Search Here"></androidx.appcompat.widget.SearchView>


        </LinearLayout>

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_gravity="center|end"
            android:layout_weight="0.5"
            android:gravity="center|end"
            android:orientation="horizontal">

            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/id_filter"
                android:layout_width="0dp"
                android:layout_height="@dimen/dp40"
                android:layout_weight="1"
                android:text="@string/label_filter"
                android:visibility="gone" />

            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/id_add"
                android:layout_width="0dp"
                android:layout_height="@dimen/dp50"
                android:layout_marginStart="@dimen/dp5"
                android:layout_weight="1"
                android:background="@drawable/bg_btn_green"
                android:text="@string/label_add" />

        </LinearLayout>

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="45dp"
        android:layout_gravity="center"
        android:layout_marginTop="@dimen/activity_left_margin"
        android:background="@drawable/bg_gradient"
        android:gravity="center"
        android:paddingHorizontal="@dimen/dp20"
        android:orientation="horizontal"
        android:weightSum="4">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp10"
            android:layout_weight="1.5"
            android:gravity="center|start"
            android:text="@string/label_variation"
            android:textColor="@color/white"
            android:textStyle="bold" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1.5"
            android:gravity="start"
            android:text="@string/label_short_value"
            android:textColor="@color/white"
            android:textStyle="bold" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@string/label_btn_action"
            android:textColor="@color/white"
            android:textStyle="bold" />


    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:clipChildren="false"
            android:clipToPadding="false"
            android:divider="@android:color/transparent"
            android:dividerHeight="0.0px"
            android:elevation="8dp"
            android:visibility="visible"
            app:cardElevation="2dp"
            app:cardPreventCornerOverlap="false"
            app:cardUseCompatPadding="true">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recycle_variation"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:visibility="visible"
                tools:listitem="@layout/variation_item" />

            <TextView
                android:id="@+id/noItemFound"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_gravity="center"
                android:gravity="center"
                android:padding="@dimen/dp20"
                android:text="@string/no_variations_found"
                android:textColor="@color/black"
                android:textSize="16sp"
                android:visibility="gone">

            </TextView>
        </androidx.cardview.widget.CardView>
    </LinearLayout>

</LinearLayout>