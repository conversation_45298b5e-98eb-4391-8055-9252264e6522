<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:padding="@dimen/activity_vertical_margin">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="7dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"

                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/label_report_profit_loss"
                        android:textColor="@color/black"
                        android:textSize="20dp" />

                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="2dp"
                    android:background="@color/blue_dark" />


            </LinearLayout>



            <View
                android:layout_width="match_parent"
                android:layout_height="1px"
                android:layout_marginHorizontal="50dp"
                android:layout_marginVertical="@dimen/activity_vertical_margin"
                android:background="@color/gray" />


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="horizontal"
                android:padding="@dimen/activity_vertical_margin">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginRight="@dimen/activity_left_margin"
                    android:layout_weight="1"
                    android:background="@drawable/rounded_btn_white"
                    android:orientation="vertical"
                    android:padding="@dimen/activity_left_margin">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="@dimen/activity_padding_horizontal"
                        android:orientation="horizontal"
                        android:paddingVertical="@dimen/padding_horizontal">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/lbl_stock_opening"
                            android:textColor="@color/gray" />

                        <TextView
                            android:id="@+id/opening_stock_txt"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_weight="3"
                            android:text="10000"
                            android:textColor="@color/black" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="@dimen/activity_padding_horizontal"
                        android:background="@drawable/rounded_btn_gray"

                        android:orientation="horizontal"
                        android:paddingVertical="@dimen/padding_horizontal">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/lbl_stock_opening_vente"
                            android:textColor="@color/gray" />

                        <TextView
                            android:id="@+id/opening_stock_by_sp_txt"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_weight="3"
                            android:text="10000"
                            android:textColor="@color/black" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="@dimen/activity_padding_horizontal"

                        android:orientation="horizontal"
                        android:paddingVertical="@dimen/padding_horizontal">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/label_total_achat"
                            android:textColor="@color/gray" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_weight="3"
                            android:text="0.00 $"
                            android:textColor="@color/black" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="@dimen/activity_padding_horizontal"
                        android:background="@drawable/rounded_btn_gray"

                        android:orientation="horizontal"
                        android:paddingVertical="@dimen/padding_horizontal">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/label_total_adjustment"
                            android:textColor="@color/gray" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_weight="3"
                            android:text="0.00 $"
                            android:textColor="@color/black" />
                    </LinearLayout>


                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="@dimen/activity_padding_horizontal"

                        android:orientation="horizontal"
                        android:paddingVertical="@dimen/padding_horizontal">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/label_tota_depnese"
                            android:textColor="@color/gray" />

                        <TextView
                            android:id="@+id/total_expense_txt"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_weight="3"
                            android:text="0000"
                            android:textColor="@color/black" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="@dimen/activity_padding_horizontal"
                        android:background="@drawable/rounded_btn_gray"

                        android:orientation="horizontal"
                        android:paddingVertical="@dimen/padding_horizontal">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/label_tota_remise_total"
                            android:textColor="@color/gray" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_weight="3"
                            android:text="0.00 $"
                            android:textColor="@color/black" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="@dimen/activity_padding_horizontal"

                        android:orientation="horizontal"
                        android:paddingVertical="@dimen/padding_horizontal">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/label_total_sell"
                            android:textColor="@color/gray" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_weight="3"
                            android:text="0.00 $"
                            android:textColor="@color/black" />
                    </LinearLayout>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/activity_left_margin"
                    android:layout_weight="1"

                    android:background="@drawable/rounded_btn_white"
                    android:orientation="vertical"
                    android:padding="@dimen/activity_left_margin">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="@dimen/activity_padding_horizontal"
                        android:orientation="horizontal"
                        android:paddingVertical="@dimen/padding_horizontal">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/label_stock_cloture"
                            android:textColor="@color/gray" />

                        <TextView
                            android:id="@+id/closing_stock_txt"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_weight="3"
                            android:text="10000"
                            android:textColor="@color/black" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="@dimen/activity_padding_horizontal"
                        android:background="@drawable/rounded_btn_gray"

                        android:orientation="horizontal"
                        android:paddingVertical="@dimen/padding_horizontal">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/label_stock_venteee"
                            android:textColor="@color/gray" />

                        <TextView
                            android:id="@+id/closing_stock_by_sp_txt"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_weight="3"
                            android:text="10000"
                            android:textColor="@color/black" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="@dimen/activity_padding_horizontal"
                        android:orientation="horizontal"
                        android:paddingVertical="@dimen/padding_horizontal">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/label_total_ventes"
                            android:textColor="@color/gray" />

                        <TextView
                            android:id="@+id/total_sell_txt"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_weight="3"
                            android:text="10000"
                            android:textColor="@color/black" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="@dimen/activity_padding_horizontal"
                        android:background="@drawable/rounded_btn_gray"

                        android:orientation="horizontal"
                        android:paddingVertical="@dimen/padding_horizontal">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/label_total_achat_expedition"
                            android:textColor="@color/gray" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_weight="3"
                            android:text="0.00 $"
                            android:textColor="@color/black" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="@dimen/activity_padding_horizontal"
                        android:orientation="horizontal"
                        android:paddingVertical="@dimen/padding_horizontal">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/label_tota_recuperere"
                            android:textColor="@color/gray" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_weight="3"
                            android:text="0.00 $"
                            android:textColor="@color/black" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="@dimen/activity_padding_horizontal"
                        android:background="@drawable/rounded_btn_gray"

                        android:orientation="horizontal"
                        android:paddingVertical="@dimen/padding_horizontal">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/label_retour_achat"
                            android:textColor="@color/gray" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_weight="3"
                            android:text="0.00 $"
                            android:textColor="@color/black" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="@dimen/activity_padding_horizontal"
                        android:orientation="horizontal"
                        android:paddingVertical="@dimen/padding_horizontal">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/label_totaremse_chat"
                            android:textColor="@color/gray" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_weight="3"
                            android:text="0.00 $"
                            android:textColor="@color/black" />
                    </LinearLayout>

                </LinearLayout>
            </LinearLayout>

        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1px"
            android:layout_marginHorizontal="50dp"
            android:layout_marginVertical="@dimen/activity_vertical_margin"
            android:background="@color/gray" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/activity_vertical_margin"
            android:layout_weight="1"
            android:background="@drawable/rounded_btn_white"
            android:orientation="vertical"
            android:padding="@dimen/activity_vertical_margin">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/activity_padding_horizontal"
                android:orientation="horizontal"
                android:paddingTop="@dimen/padding_horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/lbl_benifice_brut"
                    android:textColor="@color/gray"
                    android:textSize="18dp" />

                <TextView
                    android:id="@+id/benfice_brute_txt"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/margin_vertical"
                    android:textColor="@color/black" />
                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/margin_vertical"
                    android:textColor="@color/black" />

            </LinearLayout>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/lbl_prix_vente_total"
                android:textColor="@color/gray" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:background="@drawable/rounded_btn_gray"
                android:orientation="horizontal"

                android:paddingVertical="@dimen/padding_horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/lbl_benifice_net"
                    android:textColor="@color/gray"
                    android:textSize="18dp" />

                <TextView
                    android:id="@+id/benefice_net_txt"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/margin_vertical"

                    android:textColor="@color/black" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/margin_vertical"
                    android:textColor="@color/black" />

            </LinearLayout>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/lbl_prix_vente_tnett"
                android:textColor="@color/gray" />
        </LinearLayout>


    </LinearLayout>


</ScrollView>