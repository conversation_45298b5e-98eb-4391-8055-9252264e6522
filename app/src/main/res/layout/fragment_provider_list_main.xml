<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:padding="@dimen/activity_horizontal_margin">
        <include
            android:id="@+id/include2"
            layout="@layout/toolbar"
            android:visibility="gone"
            app:layout_constraintTop_toTopOf="parent"
            tools:layout_editor_absoluteX="124dp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingVertical="@dimen/nav_header_vertical_spacing"
            android:orientation="horizontal">
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textStyle="bold"
                android:text="@string/label_our_provider"
                android:textColor="@color/black"
                android:textSize="18dp"/>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/label_our_provider_gerer"                android:textColor="@color/black"
                android:textSize="16dp"/>
        </LinearLayout>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="horizontal"
            android:paddingTop="10dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/include2">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center"
                android:layout_weight="1"
                android:layout_marginRight="@dimen/activity_horizontal_margin"
                android:background="@drawable/ic_seach_background"
                android:padding="7dp">

                <EditText
                    android:id="@+id/id_search_edit"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/white"
                    android:layout_weight="1"
                    android:padding="@dimen/margin_edit_text"
                    android:hint="@string/label_search"
                    android:maxLines="1"/>

                <ImageView
                    android:layout_width="30dp"
                    android:layout_height="30dp"
                    android:src="@drawable/search"
                    android:tint="@color/white"
                    android:padding="3dp"/>


            </LinearLayout>
            <Button
                android:id="@+id/id_add"
                    style="@style/my_btn_style"
                        android:layout_width="wrap_content"
                        android:layout_height="70px"

                android:text="@string/label_add" />
        </LinearLayout>

        <LinearLayout
            android:visibility="gone"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="horizontal"
            android:paddingTop="10dp"

            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/include2">

            <Spinner
                style="@style/my_spinner_style"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:entries="@array/type_select_marque" />

            <Spinner
                style="@style/my_spinner_style"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:entries="@array/type_select_affaire_lieu" />

            <Spinner
                style="@style/my_spinner_style"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:entries="@array/type_select_active" />

            <CheckBox
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/activity_horizontal_margin"
                android:layout_weight="1"
                android:text="@string/label_pas_avendre" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:visibility="gone"
            android:orientation="horizontal"
            android:padding="@dimen/padding_top">

            <Button
                    style="@style/my_btn_style"
                        android:layout_width="wrap_content"
                        android:layout_height="70px"

                android:text="@string/label_add" />

            <Button
                android:id="@+id/id_filter"
                    style="@style/my_btn_style"
                        android:layout_width="wrap_content"
                        android:layout_height="70px"

                android:text="@string/label_search" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:padding="@dimen/fab_margin">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:textStyle="bold"
                android:text="@string/label_tout_contact"
                android:textColor="@color/black" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:gravity="center"
            android:orientation="horizontal"
            android:padding="20px">

            <ImageView
                android:id="@+id/image_product"
                android:layout_width="match_parent"
                android:layout_height="70px"
                android:layout_weight="1"
                android:src="@drawable/smart_contact_ic"
                android:visibility="invisible" />
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/label_id_contact"
                android:textColor="@color/black"
                android:textStyle="bold" />
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/label_name"
                android:textColor="@color/black"
                android:textStyle="bold" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/label_email"
                android:textColor="@color/black"
                android:textStyle="bold" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/label_pay_mobile"
                android:textColor="@color/black"
                android:textStyle="bold" />



            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/label_status"

                android:textColor="@color/black"
                android:textStyle="bold" />


        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/white_blue">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recycle_customers"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />
        </LinearLayout>
    </LinearLayout>

</LinearLayout>