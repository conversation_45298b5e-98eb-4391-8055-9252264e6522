<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:padding="@dimen/activity_horizontal_margin">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="7dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"

                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/label_stock_report"
                    android:textColor="@color/black"
                    android:textSize="20dp" />

            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="2dp"
                android:background="@color/blue_dark" />


        </LinearLayout>

        <LinearLayout
            android:id="@+id/filter_header"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_gradient"
            android:paddingHorizontal="5dp"
            android:paddingVertical="@dimen/activity_left_margin">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawableLeft="@drawable/ic_baseline_filter_alt_24"
                android:drawablePadding="@dimen/dp10"
                android:text="Filtres"
                android:textColor="@color/white"
                android:textSize="16dp" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/filter_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="horizontal"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/include2">

            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:divider="@android:color/transparent"
                android:dividerHeight="0.0px"
                android:elevation="8dp"
                android:visibility="visible"
                app:cardElevation="2dp"
                app:cardPreventCornerOverlap="false"
                app:cardUseCompatPadding="true">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="start"
                        android:orientation="horizontal"
                        android:padding="@dimen/dp10"
                        android:weightSum="12"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/include2">

                        <FrameLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="2">

                            <androidx.appcompat.widget.AppCompatSpinner
                                android:id="@+id/spinner_station"
                                android:layout_width="match_parent"
                                android:layout_height="@dimen/dp50"
                                android:layout_marginTop="5dp"
                                android:entries="@array/type_produc_type"
                                android:textSize="12sp"
                                android:textStyle="normal" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/spinner_prod_type_tint"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/dp_10"
                                android:background="@color/white"
                                android:paddingLeft="4dp"
                                android:paddingRight="4dp"
                                android:text="@string/label_station"
                                android:textSize="12sp"
                                android:visibility="visible" />
                        </FrameLayout>

                        <FrameLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp10"
                            android:layout_weight="2">

                            <androidx.appcompat.widget.AppCompatSpinner
                                android:id="@+id/spinner_category"
                                android:layout_width="match_parent"
                                android:layout_height="@dimen/dp50"
                                android:layout_marginTop="5dp"
                                android:textSize="12sp"
                                android:textStyle="normal" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/spinner_prod_type_tintt"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/dp_10"
                                android:background="@color/white"
                                android:paddingLeft="4dp"
                                android:paddingRight="4dp"
                                android:text="@string/label_category_name"
                                android:textSize="12sp"
                                android:visibility="visible" />
                        </FrameLayout>

                        <FrameLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="@dimen/dp10"
                            android:layout_weight="2">

                            <androidx.appcompat.widget.AppCompatSpinner
                                android:id="@+id/spinner_unit"
                                android:layout_width="match_parent"
                                android:layout_height="@dimen/dp50"
                                android:layout_marginTop="5dp"
                                android:entries="@array/type_payement_status"
                                android:textSize="12sp"
                                android:textStyle="normal" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/dp_10"
                                android:background="@color/white"
                                android:paddingLeft="4dp"
                                android:paddingRight="4dp"
                                android:text="@string/label_unit"
                                android:textSize="12sp"
                                android:visibility="visible" />
                        </FrameLayout>

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_gravity="center|end"
                            android:layout_weight="2"
                            android:paddingTop="@dimen/dp5"
                            android:gravity="center|end"
                            android:orientation="horizontal">

                            <androidx.appcompat.widget.AppCompatButton
                                android:id="@+id/id_filter"
                                android:layout_width="0dp"
                                android:layout_height="@dimen/dp50"
                                android:layout_marginStart="@dimen/dp10"
                                android:layout_weight="1"
                                android:background="@drawable/bg_btn_blue"
                                android:text="@string/label_filter" />

                            <androidx.appcompat.widget.AppCompatButton
                                android:visibility="gone"
                                android:id="@+id/id_add"
                                android:layout_width="0dp"
                                android:layout_height="@dimen/dp50"
                                android:layout_marginStart="@dimen/dp5"
                                android:layout_weight="1"
                                android:background="@drawable/bg_btn_green"
                                android:text="@string/label_add" />

                        </LinearLayout>
                    </LinearLayout>

                </LinearLayout>


            </androidx.cardview.widget.CardView>

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="@dimen/dp20"
            android:background="@drawable/bg_gradient"
            android:gravity="center"
            android:orientation="horizontal"
            android:padding="@dimen/dp20"
            android:weightSum="10">

            <TextView
                android:visibility="gone"
                android:id="@+id/sku_id"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="SKU"
                android:textColor="@color/white"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/product_name"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/image_product_name"

                android:textColor="@color/white"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/location_txt"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/label_location"

                android:textColor="@color/white"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/product_price"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"

                android:text="@string/label_unit_price"
                android:textColor="@color/white"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/stock_actual"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/label_current_stock"
                android:textColor="@color/white"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/valeur_en_stock"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"

                android:text="@string/lbl_current_stock_value"
                android:textColor="@color/white"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/total_sold"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"

                android:text="@string/lbl_current_stock_value_by_sale"
                android:textColor="@color/white"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/potential_profit"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/lbl_potential_profit"
                android:textColor="@color/white"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/total_unit_sold"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/lbl_total_unit_sold"
                android:textColor="@color/white"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/total_unit_transfered"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/lbl_total_unit_transfered"
                android:textColor="@color/white"
                android:textStyle="bold" />


            <TextView
                android:id="@+id/total_unit_adjusted"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/lbl_total_unit_adjusted"
                android:textColor="@color/white"
                android:textStyle="bold" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:divider="@android:color/transparent"
                android:dividerHeight="0.0px"
                android:elevation="8dp"
                android:visibility="visible"
                app:cardElevation="2dp"
                app:cardPreventCornerOverlap="false"
                app:cardUseCompatPadding="true">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/report_stock_recycle"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:visibility="visible" />

                <TextView
                    android:id="@+id/noItemFound"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:gravity="center"
                    android:padding="@dimen/dp20"
                    android:text="@string/no_report_stock_found"
                    android:textColor="@color/black"
                    android:textSize="16sp"
                    android:visibility="gone">

                </TextView>
            </androidx.cardview.widget.CardView>
        </LinearLayout>


    </LinearLayout>


</ScrollView>