<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/content"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/right_bg_menu"
    android:orientation="horizontal">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_weight="2" />

    <LinearLayout
        android:id="@+id/main_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_margin="@dimen/activity_vertical_margin"
                android:layout_weight="1"
                android:gravity="center">

                <LinearLayout
                    android:id="@+id/id_home"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:background="@drawable/rounded_white_bg"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        app:srcCompat="@drawable/products_smart_ic" />

                    <TextView
                        style="@style/text_btn_theme"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="4"
                        android:text="@string/label_pos_title" />

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_margin="@dimen/activity_vertical_margin"
                android:layout_weight="1"
                android:gravity="center">

                <LinearLayout
                    android:id="@+id/id_prudcts"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:background="@drawable/rounded_white_bg"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        app:srcCompat="@drawable/stock_adjustement_ic" />

                    <TextView
                        style="@style/text_btn_theme"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="4"
                        android:text="@string/label_products" />

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_margin="@dimen/activity_vertical_margin"
                android:layout_weight="1"

                android:gravity="center">

                <LinearLayout
                    android:id="@+id/id_stock"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:background="@drawable/rounded_white_bg"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        app:srcCompat="@drawable/smart_contact_ic" />

                    <TextView
                        style="@style/text_btn_theme"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="4"
                        android:text="@string/label_contact" />

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_margin="@dimen/activity_vertical_margin"
                android:layout_weight="1"
                android:gravity="center">

                <LinearLayout
                    android:id="@+id/id_purchaes_products"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:background="@drawable/rounded_white_bg"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        app:srcCompat="@drawable/purchases_prodct_ic" />

                    <TextView
                        style="@style/text_btn_theme"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="4"
                        android:text="@string/label_purchases" />

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_margin="@dimen/activity_vertical_margin"
                android:layout_weight="1"

                android:gravity="center">

                <LinearLayout
                    android:id="@+id/id_sales"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:background="@drawable/rounded_white_bg"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        app:srcCompat="@drawable/sales_smart_ic" />

                    <TextView
                        style="@style/text_btn_theme"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="4"
                        android:text="@string/label_sales_title" />

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_margin="@dimen/activity_vertical_margin"
                android:layout_weight="1"
                android:gravity="center">

                <LinearLayout
                    android:id="@+id/id_stock_transfer"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:background="@drawable/rounded_white_bg"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        app:srcCompat="@drawable/smart_stock_transfer_ic" />

                    <TextView
                        style="@style/text_btn_theme"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="4"
                        android:text="@string/label_stock_transfers" />

                </LinearLayout>

            </LinearLayout>
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_margin="@dimen/activity_vertical_margin"
                android:layout_weight="1"
                android:gravity="center">

                <LinearLayout
                    android:id="@+id/id_stock_adjustement"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:background="@drawable/rounded_white_bg"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        app:srcCompat="@drawable/provider_ic" />

                    <TextView
                        style="@style/text_btn_theme"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="4"
                        android:text="@string/label_stock_adjustement" />

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_margin="@dimen/activity_vertical_margin"
                android:layout_weight="1"

                android:gravity="center">

                <LinearLayout
                    android:id="@+id/id_expenses"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:background="@drawable/rounded_white_bg"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        app:srcCompat="@drawable/stock_expenses_ic" />

                    <TextView
                        style="@style/text_btn_theme"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="4"
                        android:text="@string/label_expense" />

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_margin="@dimen/activity_vertical_margin"
                android:layout_weight="1"
                android:gravity="center">

                <LinearLayout
                    android:id="@+id/id_reports"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:background="@drawable/rounded_white_bg"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        app:srcCompat="@drawable/stock_reports_ic" />

                    <TextView
                        style="@style/text_btn_theme"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="4"
                        android:text="@string/label_reports" />

                </LinearLayout>

            </LinearLayout>
        </LinearLayout>


    </LinearLayout>

    <LinearLayout
        android:id="@+id/product_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:orientation="vertical"
        android:visibility="gone">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_margin="@dimen/activity_vertical_margin"
                android:layout_weight="1"
                android:gravity="center">

                <LinearLayout
                    android:id="@+id/id_list_product"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:background="@drawable/rounded_white_bg"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        app:srcCompat="@drawable/box_ic" />

                    <TextView
                        style="@style/text_btn_theme"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="4"
                        android:text="@string/label_product_list" />

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_margin="@dimen/activity_vertical_margin"
                android:layout_weight="1"
                android:gravity="center">

                <LinearLayout
                    android:id="@+id/id_variation"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:background="@drawable/rounded_white_bg"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        app:srcCompat="@drawable/variation_menu_ic" />

                    <TextView
                        style="@style/text_btn_theme"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="4"
                        android:text="@string/label_variation" />

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_margin="@dimen/activity_vertical_margin"
                android:layout_weight="1"
                android:gravity="center">

                <LinearLayout
                    android:id="@+id/id_units"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:background="@drawable/rounded_white_bg"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        app:srcCompat="@drawable/measuring_tape_ic" />

                    <TextView
                        style="@style/text_btn_theme"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="4"
                        android:text="@string/label_unit" />

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_margin="@dimen/activity_vertical_margin"
                android:layout_weight="1"
                android:gravity="center">

                <LinearLayout
                    android:id="@+id/id_categories"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:background="@drawable/rounded_white_bg"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        app:srcCompat="@drawable/bag_categories_ic" />

                    <TextView
                        style="@style/text_btn_theme"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="4"
                        android:text="@string/label_category" />

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_margin="@dimen/activity_vertical_margin"
                android:layout_weight="1"
                android:gravity="center">

                <LinearLayout
                    android:id="@+id/id_warranties"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:background="@drawable/rounded_white_bg"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        app:srcCompat="@drawable/warranty_ic" />

                    <TextView
                        style="@style/text_btn_theme"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="4"
                        android:text="@string/label_warranty" />

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_margin="@dimen/activity_vertical_margin"
                android:layout_weight="1"
                android:gravity="center">

                <LinearLayout
                    android:id="@+id/id_brands"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:background="@drawable/rounded_white_bg"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        app:srcCompat="@drawable/flyers_brand_ic" />

                    <TextView
                        style="@style/text_btn_theme"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="4"
                        android:text="@string/label_brand" />

                </LinearLayout>

            </LinearLayout>
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="horizontal"
            android:visibility="invisible">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_margin="@dimen/activity_vertical_margin"
                android:layout_weight="1"
                android:gravity="center">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:background="@drawable/rounded_white_bg"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        app:srcCompat="@drawable/provider_ic" />

                    <TextView
                        style="@style/text_btn_theme"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="4"
                        android:text="@string/label_stock_adjustement" />

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_margin="@dimen/activity_vertical_margin"
                android:layout_weight="1"
                android:gravity="center">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:background="@drawable/rounded_white_bg"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        app:srcCompat="@drawable/stock_expenses_ic" />

                    <TextView
                        style="@style/text_btn_theme"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="4"
                        android:text="@string/label_expense" />

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_margin="@dimen/activity_vertical_margin"
                android:layout_weight="1"
                android:gravity="center">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:background="@drawable/rounded_white_bg"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        app:srcCompat="@drawable/stock_reports_ic" />

                    <TextView
                        style="@style/text_btn_theme"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="4"
                        android:text="@string/label_reports" />

                </LinearLayout>

            </LinearLayout>
        </LinearLayout>


    </LinearLayout>

    <LinearLayout
        android:id="@+id/sell_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:orientation="vertical"
        android:visibility="gone">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_margin="@dimen/activity_vertical_margin"
                android:layout_weight="1"
                android:gravity="center">

                <LinearLayout
                    android:id="@+id/id_all_sell"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:background="@drawable/rounded_white_bg"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        app:srcCompat="@drawable/all_sales_ic" />

                    <TextView
                        style="@style/text_btn_theme"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="4"
                        android:text="@string/label_all_salles" />

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_margin="@dimen/activity_vertical_margin"
                android:layout_weight="1"
                android:gravity="center">

                <LinearLayout
                    android:id="@+id/id_draft"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:background="@drawable/rounded_white_bg"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        app:srcCompat="@drawable/sketching_draft_ic" />

                    <TextView
                        style="@style/text_btn_theme"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="4"
                        android:text="@string/label_draft" />

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_margin="@dimen/activity_vertical_margin"
                android:layout_weight="1"
                android:gravity="center">

                <LinearLayout
                    android:id="@+id/id_quotation"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:background="@drawable/rounded_white_bg"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        app:srcCompat="@drawable/payment_ic" />

                    <TextView
                        style="@style/text_btn_theme"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="4"
                        android:text="@string/label_quotation" />

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_margin="@dimen/activity_vertical_margin"
                android:layout_weight="1"
                android:gravity="center">

                <LinearLayout
                    android:id="@+id/id_sell_return"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:background="@drawable/rounded_white_bg"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        app:srcCompat="@drawable/return_sell_ic" />

                    <TextView
                        style="@style/text_btn_theme"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="4"
                        android:text="@string/label_lsit_return_sell" />

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_margin="@dimen/activity_vertical_margin"
                android:layout_weight="1"
                android:gravity="center">

                <LinearLayout
                    android:id="@+id/id_discount"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:background="@drawable/rounded_white_bg"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        app:srcCompat="@drawable/gift_ic" />

                    <TextView
                        style="@style/text_btn_theme"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="4"
                        android:text="@string/label_discounts" />

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_margin="@dimen/activity_vertical_margin"
                android:layout_weight="1"
                android:gravity="center"
                android:visibility="invisible">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:background="@drawable/rounded_white_bg"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        app:srcCompat="@drawable/flyers_brand_ic" />

                    <TextView
                        style="@style/text_btn_theme"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="4"
                        android:text="@string/label_brand" />

                </LinearLayout>

            </LinearLayout>
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="horizontal"
            android:visibility="invisible">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_margin="@dimen/activity_vertical_margin"
                android:layout_weight="1"
                android:gravity="center">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:background="@drawable/rounded_white_bg"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        app:srcCompat="@drawable/provider_ic" />

                    <TextView
                        style="@style/text_btn_theme"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="4"
                        android:text="@string/label_stock_adjustement" />

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_margin="@dimen/activity_vertical_margin"
                android:layout_weight="1"
                android:gravity="center">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:background="@drawable/rounded_white_bg"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        app:srcCompat="@drawable/stock_expenses_ic" />

                    <TextView
                        style="@style/text_btn_theme"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="4"
                        android:text="@string/label_expense" />

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_margin="@dimen/activity_vertical_margin"
                android:layout_weight="1"
                android:gravity="center">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:background="@drawable/rounded_white_bg"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        app:srcCompat="@drawable/stock_reports_ic" />

                    <TextView
                        style="@style/text_btn_theme"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="4"
                        android:text="@string/label_reports" />

                </LinearLayout>

            </LinearLayout>
        </LinearLayout>


    </LinearLayout>

    <LinearLayout
        android:id="@+id/expense_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:orientation="vertical"
        android:visibility="gone">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_margin="@dimen/activity_vertical_margin"
                android:layout_weight="1"
                android:gravity="center">

                <LinearLayout
                    android:id="@+id/id_list_expenses"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:background="@drawable/rounded_white_bg"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        app:srcCompat="@drawable/expenses_list_ic" />

                    <TextView
                        style="@style/text_btn_theme"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="4"
                        android:text="@string/label_lsit_expenses" />

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_margin="@dimen/activity_vertical_margin"
                android:layout_weight="1"
                android:gravity="center">

                <LinearLayout
                    android:id="@+id/id_expense_categories"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:background="@drawable/rounded_white_bg"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        app:srcCompat="@drawable/bill_ic" />

                    <TextView
                        style="@style/text_btn_theme"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="4"
                        android:text="@string/label_expense_categoriess" />

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_margin="@dimen/activity_vertical_margin"
                android:layout_weight="1"
                android:gravity="center"
                android:visibility="invisible">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:background="@drawable/rounded_white_bg"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        app:srcCompat="@drawable/payment_ic" />

                    <TextView
                        style="@style/text_btn_theme"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="4"
                        android:text="@string/label_quotation" />

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_margin="@dimen/activity_vertical_margin"
                android:layout_weight="1"
                android:gravity="center"
                android:visibility="invisible">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:background="@drawable/rounded_white_bg"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        app:srcCompat="@drawable/return_sell_ic" />

                    <TextView
                        style="@style/text_btn_theme"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="4"
                        android:text="@string/label_lsit_return_sell" />

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_margin="@dimen/activity_vertical_margin"
                android:layout_weight="1"
                android:gravity="center"
                android:visibility="invisible">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:background="@drawable/rounded_white_bg"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        app:srcCompat="@drawable/gift_ic" />

                    <TextView
                        style="@style/text_btn_theme"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="4"
                        android:text="@string/label_discounts" />

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_margin="@dimen/activity_vertical_margin"
                android:layout_weight="1"
                android:gravity="center"
                android:visibility="invisible">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:background="@drawable/rounded_white_bg"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        app:srcCompat="@drawable/flyers_brand_ic" />

                    <TextView
                        style="@style/text_btn_theme"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="4"
                        android:text="@string/label_brand" />

                </LinearLayout>

            </LinearLayout>
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="horizontal"
            android:visibility="invisible">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_margin="@dimen/activity_vertical_margin"
                android:layout_weight="1"
                android:gravity="center">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:background="@drawable/rounded_white_bg"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        app:srcCompat="@drawable/provider_ic" />

                    <TextView
                        style="@style/text_btn_theme"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="4"
                        android:text="@string/label_stock_adjustement" />

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_margin="@dimen/activity_vertical_margin"
                android:layout_weight="1"
                android:gravity="center">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:background="@drawable/rounded_white_bg"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        app:srcCompat="@drawable/stock_expenses_ic" />

                    <TextView
                        style="@style/text_btn_theme"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="4"
                        android:text="@string/label_expense" />

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_margin="@dimen/activity_vertical_margin"
                android:layout_weight="1"
                android:gravity="center">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:background="@drawable/rounded_white_bg"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        app:srcCompat="@drawable/stock_reports_ic" />

                    <TextView
                        style="@style/text_btn_theme"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="4"
                        android:text="@string/label_reports" />

                </LinearLayout>

            </LinearLayout>
        </LinearLayout>

    </LinearLayout>
    <LinearLayout
        android:id="@+id/id_purchase_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:orientation="vertical"
        android:visibility="gone">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_margin="@dimen/activity_vertical_margin"
                android:layout_weight="1"
                android:gravity="center">

                <LinearLayout
                    android:id="@+id/id_list_purchase"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:background="@drawable/rounded_white_bg"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        app:srcCompat="@drawable/purchasess_list_ic" />

                    <TextView
                        style="@style/text_btn_theme"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="4"
                        android:text="@string/label_list_purchases" />

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_margin="@dimen/activity_vertical_margin"
                android:layout_weight="1"
                android:gravity="center">

                <LinearLayout
                    android:id="@+id/id_purchase_return"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:background="@drawable/rounded_white_bg"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        app:srcCompat="@drawable/return_purchase_ret_ic" />

                    <TextView
                        style="@style/text_btn_theme"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="4"
                        android:text="@string/label_list_return_purchase" />

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_margin="@dimen/activity_vertical_margin"
                android:layout_weight="1"
                android:gravity="center"
                android:visibility="invisible">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:background="@drawable/rounded_white_bg"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        app:srcCompat="@drawable/payment_ic" />

                    <TextView
                        style="@style/text_btn_theme"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="4"
                        android:text="@string/label_quotation" />

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_margin="@dimen/activity_vertical_margin"
                android:layout_weight="1"
                android:gravity="center"
                android:visibility="invisible">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:background="@drawable/rounded_white_bg"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        app:srcCompat="@drawable/return_sell_ic" />

                    <TextView
                        style="@style/text_btn_theme"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="4"
                        android:text="@string/label_lsit_return_sell" />

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_margin="@dimen/activity_vertical_margin"
                android:layout_weight="1"
                android:gravity="center"
                android:visibility="invisible">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:background="@drawable/rounded_white_bg"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        app:srcCompat="@drawable/gift_ic" />

                    <TextView
                        style="@style/text_btn_theme"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="4"
                        android:text="@string/label_discounts" />

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_margin="@dimen/activity_vertical_margin"
                android:layout_weight="1"
                android:gravity="center"
                android:visibility="invisible">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:background="@drawable/rounded_white_bg"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        app:srcCompat="@drawable/flyers_brand_ic" />

                    <TextView
                        style="@style/text_btn_theme"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="4"
                        android:text="@string/label_brand" />

                </LinearLayout>

            </LinearLayout>
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="horizontal"
            android:visibility="invisible">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_margin="@dimen/activity_vertical_margin"
                android:layout_weight="1"
                android:gravity="center">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:background="@drawable/rounded_white_bg"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        app:srcCompat="@drawable/provider_ic" />

                    <TextView
                        style="@style/text_btn_theme"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="4"
                        android:text="@string/label_stock_adjustement" />

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_margin="@dimen/activity_vertical_margin"
                android:layout_weight="1"
                android:gravity="center">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:background="@drawable/rounded_white_bg"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        app:srcCompat="@drawable/stock_expenses_ic" />

                    <TextView
                        style="@style/text_btn_theme"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="4"
                        android:text="@string/label_expense" />

                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_margin="@dimen/activity_vertical_margin"
                android:layout_weight="1"
                android:gravity="center">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:background="@drawable/rounded_white_bg"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        app:srcCompat="@drawable/stock_reports_ic" />

                    <TextView
                        style="@style/text_btn_theme"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_weight="4"
                        android:text="@string/label_reports" />

                </LinearLayout>

            </LinearLayout>
        </LinearLayout>

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_weight="2" />

</LinearLayout>