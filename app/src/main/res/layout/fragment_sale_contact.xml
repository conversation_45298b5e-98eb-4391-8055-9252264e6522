<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:padding="@dimen/activity_horizontal_margin">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@drawable/cell_shape_border"
                android:padding="@dimen/activity_vertical_margin">


                <FrameLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp10"
                    android:layout_weight="1">

                    <androidx.appcompat.widget.AppCompatSpinner
                        android:id="@+id/spinner_payment_status"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dp50"
                        android:layout_marginTop="5dp"
                        android:entries="@array/array_payement_status"
                        android:textSize="12sp"
                        android:textStyle="normal" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/spinner_prod_type_tintt"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp_10"
                        android:background="@color/white"
                        android:paddingLeft="4dp"
                        android:paddingRight="4dp"
                        android:text="@string/label_status_paimenent"
                        android:textSize="12sp"
                        android:visibility="visible" />
                </FrameLayout>


                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/activity_left_margin"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:visibility="gone"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/label_date_range"
                        android:textColor="@color/blue_txt" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:orientation="horizontal"
                        android:paddingTop="@dimen/margin_vertical">

                        <EditText
                            android:id="@+id/new_start_date"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:focusable="false"
                            android:textSize="14sp"
                            android:inputType="textCapWords"
                            android:hint="@string/label_starts_at" />

                        <EditText
                            android:id="@+id/new_end_date"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:focusable="false"
                            android:textSize="14sp"
                            android:inputType="textCapWords"
                            android:hint="@string/label_ends_At" />
                    </LinearLayout>

                </LinearLayout>


                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginHorizontal="@dimen/activity_left_margin"
                    android:layout_weight="1"
                    android:layout_gravity="center"
                    android:gravity="center"
                    android:orientation="horizontal">

<!--                    <TextView-->
<!--                        android:layout_width="wrap_content"-->
<!--                        android:layout_height="wrap_content"-->
<!--                        android:text="@string/label_subscreption"-->
<!--                        android:textColor="@color/blue_txt" />-->

<!--                    <LinearLayout-->
<!--                        android:layout_width="wrap_content"-->
<!--                        android:layout_height="wrap_content"-->
<!--                        android:gravity="center"-->
<!--                        android:orientation="horizontal"-->
<!--                        android:paddingTop="@dimen/margin_vertical">-->

<!--                        <ImageView-->
<!--                            android:layout_width="30px"-->
<!--                            android:layout_height="30px"-->
<!--                            android:src="@drawable/icons8_user_location" />-->

<!--                        <CheckBox-->
<!--                            android:layout_width="wrap_content"-->
<!--                            android:layout_height="wrap_content"-->
<!--                            android:layout_weight="1" />-->

<!--                    </LinearLayout>-->

                </LinearLayout>


                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/activity_left_margin"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <androidx.appcompat.widget.AppCompatButton
                        android:id="@+id/id_filter"
                        android:layout_width="100dp"
                        android:layout_height="@dimen/dp50"
                        android:layout_weight="1"
                        android:background="@drawable/bg_gradient"
                        android:layout_marginStart="@dimen/dp20"
                        android:text="@string/label_filter" />

                </LinearLayout>

            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="1px"
                android:layout_marginHorizontal="50dp"
                android:layout_marginVertical="@dimen/activity_vertical_margin"
                android:background="@color/gray" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/cell_shape_border"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:background="@drawable/cell_shape"
                    android:gravity="center"
                    android:orientation="horizontal"
                    android:padding="20px">


                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/label_date"
                        android:textColor="@color/white"
                        android:textStyle="bold" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/label_facture_no"
                        android:textColor="@color/white"
                        android:textStyle="bold" />

                    <TextView
                        android:visibility="gone"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/string_customer_name"
                        android:textColor="@color/white"
                        android:textStyle="bold" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/label_location"
                        android:textColor="@color/white"
                        android:textStyle="bold" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/label_status_payement"
                        android:textColor="@color/white"
                        android:textStyle="bold" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/label_status_method"
                        android:textColor="@color/white"
                        android:textStyle="bold" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/label_montant_total"
                        android:textColor="@color/white"
                        android:textStyle="bold" />

                    <LinearLayout

                        android:visibility="gone"
                        android:id="@+id/btn_detail"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_weight="1"

                        android:gravity="center"
                        android:orientation="horizontal">

                        <ImageView
                            android:layout_width="30px"
                            android:layout_height="30px"
                            android:src="@drawable/icons8_eye"
                            android:visibility="invisible"
                            app:tint="@color/black" />

                        <TextView
                            android:visibility="gone"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:paddingLeft="3dp"
                            android:text="@string/label_btn_action"
                            android:textColor="@color/black"
                            android:textStyle="bold" />
                    </LinearLayout>

                </LinearLayout>
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <androidx.cardview.widget.CardView
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:clipChildren="false"
                        android:clipToPadding="false"
                        android:divider="@android:color/transparent"
                        android:dividerHeight="0.0px"
                        android:elevation="8dp"
                        android:visibility="visible"
                        app:cardElevation="2dp"
                        app:cardPreventCornerOverlap="false"
                        app:cardUseCompatPadding="true">

                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/recycle_vente"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:visibility="visible" />

                        <TextView
                            android:id="@+id/noItemFound"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_gravity="center"
                            android:gravity="center"
                            android:padding="@dimen/dp20"
                            android:text="@string/no_sales_found"
                            android:textColor="@color/black"
                            android:textSize="16sp"
                            android:visibility="gone">

                        </TextView>
                    </androidx.cardview.widget.CardView>
                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

</ScrollView>