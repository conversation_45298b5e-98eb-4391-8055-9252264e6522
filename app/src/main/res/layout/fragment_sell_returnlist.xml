<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp">

    <androidx.appcompat.widget.AppCompatTextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/sell_return"
        android:textColor="@color/black"
        android:textSize="22sp" />

    <View
        android:layout_width="match_parent"
        android:layout_height="2dp"
        android:background="@color/blue_dark" />

    <LinearLayout
        android:id="@+id/filter_header"

        android:layout_width="match_parent"
        android:layout_height="@dimen/dp40"
        android:layout_marginTop="@dimen/dp10"
        android:background="@drawable/bg_gradient"
        android:gravity="center"
        android:paddingHorizontal="@dimen/padding_horizontal"
        android:paddingVertical="@dimen/activity_left_margin"
        android:weightSum="10">

        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp10"
            android:layout_weight="9.5"
            android:drawablePadding="@dimen/dp10"
            android:text="@string/filters"
            android:textColor="@color/white"
            android:textSize="16sp"
            app:drawableStartCompat="@drawable/ic_baseline_filter_alt_24" />

        <ImageView
            android:id="@+id/filterArrow"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="0.5"
            android:contentDescription="@string/imageDescription"
            android:src="@drawable/ic_baseline_expand_less_24"
            app:tint="@color/white" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/filter_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="horizontal"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/include2">

        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:clipChildren="false"
            android:clipToPadding="false"
            android:divider="@android:color/transparent"
            android:dividerHeight="0.0px"
            android:elevation="8dp"
            android:visibility="visible"
            app:cardElevation="2dp"
            app:cardPreventCornerOverlap="false"
            app:cardUseCompatPadding="true">


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:baselineAligned="false"
                android:gravity="start"
                android:orientation="horizontal"
                android:padding="@dimen/dp10"
                android:weightSum="10"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/include2">

                <FrameLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="2">

                    <androidx.appcompat.widget.AppCompatSpinner
                        android:id="@+id/spinnerStation"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dp50"
                        android:layout_marginTop="5dp"
                        android:entries="@array/type_produc_type"
                        android:textSize="12sp"
                        android:textStyle="normal" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/spinner_prod_type_tint"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp_10"
                        android:background="@color/white"
                        android:paddingLeft="4dp"
                        android:paddingRight="4dp"
                        android:text="@string/label_business_location"
                        android:textSize="12sp"
                        android:visibility="visible" />
                </FrameLayout>

                <FrameLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp10"
                    android:layout_weight="2">

                    <androidx.appcompat.widget.AppCompatSpinner
                        android:id="@+id/spinnerCustomer"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dp50"
                        android:layout_marginTop="5dp"
                        android:textSize="12sp"
                        android:textStyle="normal" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/spinner_prod_type_tintt"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp_10"
                        android:background="@color/white"
                        android:paddingLeft="4dp"
                        android:paddingRight="4dp"
                        android:text="@string/label_customer"
                        android:textSize="12sp"
                        android:visibility="visible" />
                </FrameLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_marginStart="@dimen/dp10"
                    android:layout_weight="2">

                    <androidx.appcompat.widget.AppCompatEditText
                        android:id="@+id/new_start_date"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:focusable="false"
                        android:gravity="start|center"
                        android:hint="@string/string_pick_date_start"
                        android:textSize="12sp"
                        android:inputType="textCapWords"
                        android:textColor="@color/black" />
                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_marginStart="@dimen/dp10"
                    android:layout_weight="2">

                    <androidx.appcompat.widget.AppCompatEditText
                        android:id="@+id/new_end_date"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:focusable="false"
                        android:gravity="start|center"
                        android:hint="@string/string_pick_date_fin"
                        android:textSize="12sp"
                        android:inputType="textCapWords"
                        android:textColor="@color/black" />
                </com.google.android.material.textfield.TextInputLayout>

                <FrameLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp10"
                    android:layout_weight="2"
                    android:visibility="gone">

                    <androidx.appcompat.widget.AppCompatSpinner
                        android:id="@+id/userSpinner"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dp50"
                        android:layout_marginTop="5dp"
                        android:textSize="12sp"
                        android:textStyle="normal" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/dp_10"
                        android:background="@color/white"
                        android:paddingLeft="4dp"
                        android:paddingRight="4dp"
                        android:text="@string/user"
                        android:textSize="12sp"
                        android:visibility="visible" />
                </FrameLayout>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_gravity="center|end"
                    android:layout_weight="2"
                    android:paddingTop="@dimen/dp5"
                    android:gravity="center|end"
                    android:orientation="horizontal">

                    <androidx.appcompat.widget.AppCompatButton
                        android:id="@+id/btnSearch"
                        android:layout_width="0dp"
                        android:layout_height="@dimen/dp50"
                        android:layout_marginStart="@dimen/dp10"
                        android:layout_weight="1"
                        android:background="@drawable/bg_btn_blue"
                        android:text="@string/label_filter"
                        tools:ignore="NestedWeights" />

                    <androidx.appcompat.widget.AppCompatButton
                        android:id="@+id/id_add"
                        android:layout_width="0dp"
                        android:layout_height="@dimen/dp50"
                        android:layout_marginStart="@dimen/dp5"
                        android:layout_weight="1"
                        android:background="@drawable/bg_btn_green"
                        android:text="@string/label_add"
                        android:visibility="gone" />

                </LinearLayout>
            </LinearLayout>


        </androidx.cardview.widget.CardView>

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="horizontal"
        android:paddingTop="10dp"
        android:visibility="gone"

        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/include2">

        <Spinner
            style="@style/my_spinner_style"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:entries="@array/type_select_marque" />

        <Spinner
            style="@style/my_spinner_style"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:entries="@array/type_select_affaire_lieu" />

        <Spinner
            style="@style/my_spinner_style"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:entries="@array/type_select_active" />

        <CheckBox
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/activity_horizontal_margin"
            android:layout_weight="1"
            android:text="@string/label_pas_avendre" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginTop="@dimen/dp20"
        android:background="@drawable/bg_gradient"
        android:gravity="center"
        android:orientation="horizontal"
        android:paddingHorizontal="@dimen/dp10"
        android:paddingVertical="15dp"
        android:weightSum="9">

        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/label_date"
            android:textColor="@color/white"
            android:textStyle="bold" />

        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@string/label_facture_no"
            android:textColor="@color/white"
            android:textStyle="bold" />

        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@string/parent_sale"
            android:textColor="@color/white"
            android:textStyle="bold" />

        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@string/string_customer_name"
            android:textColor="@color/white"
            android:textStyle="bold" />

        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@string/label_location"
            android:textColor="@color/white"
            android:textStyle="bold" />

        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/label_payement_status"
            android:textColor="@color/white"
            android:textStyle="bold" />


        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@string/label_montant_total"
            android:textColor="@color/white"
            android:textStyle="bold" />

        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@string/string_payement_due"
            android:textColor="@color/white"
            android:textStyle="bold" />

        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="@string/label_btn_action"
            android:textColor="@color/white"
            android:textStyle="bold" />


    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:clipChildren="false"
            android:clipToPadding="false"
            android:divider="@android:color/transparent"
            android:dividerHeight="0.0px"
            android:elevation="8dp"
            android:visibility="visible"
            app:cardElevation="2dp"
            app:cardPreventCornerOverlap="false"
            app:cardUseCompatPadding="true">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recycleSellReturn"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:visibility="visible"
                tools:listitem="@layout/list_sell_return_item" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/noItemFound"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_gravity="center"
                android:gravity="center"
                android:padding="@dimen/dp20"
                android:text="@string/no_sell_return"
                android:textColor="@color/black"
                android:textSize="16sp"
                android:visibility="gone" />


        </androidx.cardview.widget.CardView>
    </LinearLayout>


</LinearLayout>