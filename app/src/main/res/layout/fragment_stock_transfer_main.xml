<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:background="@color/white">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:padding="@dimen/activity_horizontal_margin">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="7dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/label_stock_tranfer"
                    android:textColor="@color/black"
                    android:textSize="20dp" />

            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="2dp"
                android:background="@color/blue_dark" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/cell_shape_border"
            android:gravity="center"
            android:orientation="horizontal"
            android:padding="10dp"
            android:weightSum="4"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/include2">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginRight="@dimen/activity_horizontal_margin"
                android:layout_weight="3"
                android:background="@drawable/ic_seach_background"
                android:gravity="center"
                android:orientation="horizontal"
                android:padding="3dp">

                <androidx.appcompat.widget.AppCompatEditText
                    android:id="@+id/id_search_edit"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="#fff"></androidx.appcompat.widget.AppCompatEditText>


            </LinearLayout>

            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/id_filter"
                android:layout_width="0dp"
                android:layout_height="@dimen/dp50"
                android:layout_marginStart="@dimen/dp10"
                android:layout_weight="0.5"
                android:background="@drawable/bg_btn_blue"
                android:text="@string/label_search" />

            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/btn_add"
                android:layout_width="0dp"
                android:layout_height="@dimen/dp50"
                android:layout_marginStart="@dimen/dp10"
                android:layout_weight="0.5"
                android:background="@drawable/bg_btn_green"
                android:text="@string/label_add" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="horizontal"
            android:paddingTop="10dp"
            android:visibility="gone"

            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/include2">

            <Spinner
                style="@style/my_spinner_style"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:entries="@array/type_select_marque" />

            <Spinner
                style="@style/my_spinner_style"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:entries="@array/type_select_affaire_lieu" />

            <Spinner
                style="@style/my_spinner_style"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:entries="@array/type_select_active" />

            <CheckBox
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/activity_horizontal_margin"
                android:layout_weight="1"
                android:text="@string/label_pas_avendre" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="@dimen/dp20"
            android:background="@drawable/bg_gradient"
            android:gravity="center"
            android:orientation="horizontal"
            android:padding="@dimen/dp10"
            android:weightSum="8">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/label_date"
                android:textColor="@color/white"
                android:textStyle="bold" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/label_facture_no"
                android:textColor="@color/white"
                android:textStyle="bold" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/label_location_from_txt"
                android:textColor="@color/white"
                android:textStyle="bold" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/label_location_to_txt"
                android:textColor="@color/white"
                android:textStyle="bold" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/label_status_payement"
                android:textColor="@color/white"
                android:textStyle="bold" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/label_montant_total"
                android:textColor="@color/white"
                android:textStyle="bold" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/label_payment_additional_not"
                android:textColor="@color/white"
                android:textStyle="bold" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/label_btn_action"
                android:textColor="@color/white"
                android:textStyle="bold" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:divider="@android:color/transparent"
                android:dividerHeight="0.0px"
                android:elevation="8dp"
                android:visibility="visible"
                app:cardElevation="2dp"
                app:cardPreventCornerOverlap="false"
                app:cardUseCompatPadding="true">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recycler_list"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    tools:itemCount="5"
                    android:visibility="visible" />

                <TextView
                    android:id="@+id/noItemFound"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:gravity="center"
                    android:padding="@dimen/dp20"
                    android:text="@string/no_stock_transfer_found"
                    android:textColor="@color/black"
                    android:textSize="16sp"
                    android:visibility="gone">

                </TextView>
            </androidx.cardview.widget.CardView>
        </LinearLayout>

        <LinearLayout
            android:id="@+id/total_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:background="@color/greyLight"
            android:gravity="center"
            android:orientation="horizontal"
            android:padding="20px">


            <TextView
                android:id="@+id/purchase_date"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/label_date"
                android:textColor="@color/black"
                android:textStyle="normal"
                android:visibility="invisible" />

            <TextView
                android:id="@+id/purchase_ref"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/label_total"
                android:textColor="@color/black"
                android:textSize="15dp"
                android:textStyle="bold"
                android:visibility="visible" />

            <TextView
                android:id="@+id/purchase_location"

                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/label_location"
                android:textColor="@color/black"
                android:textStyle="normal"
                android:visibility="invisible" />

            <TextView
                android:id="@+id/purchase_supplier"

                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/label_supplier"
                android:textColor="@color/black"
                android:textStyle="normal"
                android:visibility="invisible" />

            <TextView
                android:id="@+id/purchase_added_by"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/label_added_by"
                android:textColor="@color/black"
                android:textStyle="normal"
                android:visibility="gone" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <LinearLayout
                    android:id="@+id/container_ordered"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:paddingVertical="@dimen/margin_vertical"
                    android:visibility="gone">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/label_ordered"
                        android:textColor="@color/black"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/ordered_count"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="@dimen/padding_horizontal"
                        android:text="@string/label_filter"
                        android:textColor="@color/black"
                        android:textStyle="bold" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/container_received"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:paddingVertical="@dimen/margin_vertical"
                    android:visibility="gone">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/label_received"
                        android:textColor="@color/black"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/received_count"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="@dimen/padding_horizontal"

                        android:text="@string/label_filter"

                        android:textColor="@color/black"
                        android:textStyle="bold" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/container_pending"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:paddingVertical="@dimen/margin_vertical"
                    android:visibility="gone">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/label_pending"
                        android:textColor="@color/black"

                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/pending_count"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="@dimen/padding_horizontal"
                        android:text="@string/label_filter"

                        android:textColor="@color/black"
                        android:textStyle="bold" />
                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <LinearLayout
                    android:id="@+id/container_paid"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:paddingVertical="@dimen/margin_vertical"
                    android:visibility="gone">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/label_paid"
                        android:textColor="@color/black"

                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/paid_count"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="@dimen/padding_horizontal"
                        android:text="@string/label_filter"

                        android:textColor="@color/black"
                        android:textStyle="bold" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/container_partial"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:paddingVertical="@dimen/margin_vertical"
                    android:visibility="gone">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/label_partial"
                        android:textColor="@color/black"

                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/partial_count"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="@dimen/padding_horizontal"
                        android:text="@string/label_filter"

                        android:textColor="@color/black"
                        android:textStyle="bold" />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/container_due"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:paddingVertical="@dimen/margin_vertical"
                    android:visibility="gone">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/label_due"
                        android:textColor="@color/black"

                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/due_count"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="@dimen/padding_horizontal"
                        android:text="@string/label_filter"
                        android:textColor="@color/black"
                        android:textStyle="bold" />
                </LinearLayout>

            </LinearLayout>

            <TextView
                android:id="@+id/purchase_grand_total"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:textColor="@color/black"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/purchase_payment_status"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/label_paiment_status_label"
                android:textColor="@color/black"
                android:textStyle="normal"
                android:visibility="invisible" />


            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:visibility="invisible">


            </LinearLayout>

        </LinearLayout>


    </LinearLayout>

</androidx.core.widget.NestedScrollView>