<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.sync.SyncFragment">

    <LinearLayout
        android:id="@+id/linearLayout2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.86">

        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/sync_btn"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp50"
            android:layout_marginStart="@dimen/dp10"
            android:layout_weight="1"
            android:background="@drawable/bg_btn_blue"
            android:text="Synchronize To Server " />

        <androidx.appcompat.widget.AppCompatButton
android:visibility="gone"
            android:id="@+id/id_btn_all_data"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp50"
            android:layout_marginStart="@dimen/dp10"
            android:layout_weight="1"
            android:background="@drawable/bg_btn_blue"
            android:text="Sync from server"

            />

        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/id_btn_all_data_from_server"
            android:layout_width="0dp"
            android:layout_height="@dimen/dp50"
            android:layout_marginStart="@dimen/dp10"
            android:layout_weight="1"
            android:background="@drawable/bg_btn_blue"
            android:text="Get Data From Server"

            />

    </LinearLayout>


    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/linearLayout3"
        app:layout_constraintVertical_bias="0.0">


        <LinearLayout
            android:id="@+id/layout_switch"
            android:layout_width="300dp"
            android:layout_height="wrap_content"
            android:layout_weight="3"
            android:orientation="vertical"
            android:padding="@dimen/fab_margin">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:padding="5dp">

                <ImageView
                    android:layout_width="25dp"
                    android:layout_height="25dp"
                    android:src="@drawable/ic_ordddder" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="5dp"
                    android:layout_weight="1"
                    android:text="@string/label_products"
                    android:textColor="@color/black" />

                <Switch
                    android:id="@+id/product_switch"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="1" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:orientation="horizontal"
                android:padding="5dp"
                android:visibility="gone">

                <ImageView
                    android:layout_width="25dp"
                    android:layout_height="25dp"
                    android:src="@drawable/list" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="5dp"
                    android:layout_weight="1"
                    android:text="Category"
                    android:textColor="@color/black" />

                <Switch
                    android:id="@+id/category_switch"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="1" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:orientation="horizontal"
                android:padding="5dp">

                <ImageView
                    android:layout_width="25dp"
                    android:layout_height="25dp"
                    android:src="@drawable/profile" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="5dp"
                    android:layout_weight="1"
                    android:text="@string/label_contact"
                    android:textColor="@color/black" />

                <Switch
                    android:id="@+id/costumer_switch"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="1" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:orientation="horizontal"
                android:padding="5dp">

                <ImageView
                    android:layout_width="25dp"
                    android:layout_height="25dp"
                    android:src="@drawable/transfer" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="5dp"
                    android:layout_weight="1"
                    android:text="Transaction"
                    android:textColor="@color/black" />

                <Switch
                    android:id="@+id/transaction_switch"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="1" />
            </LinearLayout>
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1" />
    </LinearLayout>

    <ImageView
        android:id="@+id/imageView4"
        android:layout_width="300dp"
        android:layout_height="300dp"
        android:layout_margin="@dimen/dp20"
        android:src="@drawable/reseau_cloud"
        app:layout_constraintBottom_toTopOf="@+id/linearLayout2"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <LinearLayout
        android:id="@+id/linearLayout3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/dp20"
        android:orientation="vertical"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"

            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Synchronization"
                android:textColor="@color/black"
                android:textSize="20sp"
                android:textStyle="bold" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="5dp"
                android:text="Manage and Sync Your Data with Server easily"
                android:textColor="@color/gray"
                android:textSize="14sp" />
        </LinearLayout>

        <View
            android:visibility="gone"
            android:layout_width="match_parent"
            android:layout_height="2dp"
            android:background="@color/blue_dark" />
    </LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>