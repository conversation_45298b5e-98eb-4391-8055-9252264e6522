<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:padding="@dimen/activity_horizontal_margin">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/cell_shape_border"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginTop="@dimen/activity_left_margin"
                    android:background="@drawable/cell_shape"
                    android:gravity="center"
                    android:orientation="horizontal"
                    android:padding="20px">


                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/label_ligne_paye"
                        android:textColor="@color/black"
                        android:textStyle="bold" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/label_reference_num"
                        android:textColor="@color/black"
                        android:textStyle="bold" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/label_amount_not"
                        android:textColor="@color/black"
                        android:textStyle="bold" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/label_payment_method_not"
                        android:textColor="@color/black"
                        android:textStyle="bold" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/label_payment_for"
                        android:textColor="@color/black"
                        android:textStyle="bold" />

                    <LinearLayout
                        android:id="@+id/btn_detail"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:orientation="horizontal">

                        <ImageView
                            android:layout_width="30px"
                            android:layout_height="30px"
                            android:src="@drawable/icons8_eye"
                            android:visibility="invisible"
                            app:tint="@color/black" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:paddingLeft="3dp"
                            android:text="@string/label_btn_action"
                            android:textColor="@color/black"
                            android:textStyle="bold" />

                    </LinearLayout>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/recycle_payement"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent" />

                </LinearLayout>
            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

</ScrollView>