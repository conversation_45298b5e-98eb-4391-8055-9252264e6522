<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:padding="@dimen/activity_horizontal_margin">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/cell_shape_border"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:background="@drawable/cell_shape"
                    android:gravity="center"
                    android:orientation="horizontal"
                    android:padding="20px">


                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/label_ligne_paye"
                        android:textColor="@color/white"
                        android:textStyle="bold" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/label_reference_num"
                        android:textColor="@color/white"
                        android:textStyle="bold" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/label_amount_not"
                        android:textColor="@color/white"
                        android:textStyle="bold" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/label_payment_method_not"
                        android:textColor="@color/white"
                        android:textStyle="bold" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/label_payment_for"
                        android:textColor="@color/white"
                        android:textStyle="bold" />

<!--                    <LinearLayout-->
<!--                        android:id="@+id/btn_detail"-->
<!--                        android:layout_width="match_parent"-->
<!--                        android:layout_height="wrap_content"-->
<!--                        android:layout_gravity="center"-->
<!--                        android:layout_weight="1"-->
<!--                        android:gravity="center"-->
<!--                        android:orientation="horizontal">-->

<!--                        <ImageView-->
<!--                            android:layout_width="30px"-->
<!--                            android:layout_height="30px"-->
<!--                            android:src="@drawable/icons8_eye"-->
<!--                            android:visibility="invisible"-->
<!--                            app:tint="@color/white" />-->

<!--                        <TextView-->
<!--                            android:layout_width="wrap_content"-->
<!--                            android:layout_height="wrap_content"-->
<!--                            android:layout_gravity="center"-->
<!--                            android:paddingLeft="3dp"-->
<!--                            android:text="@string/label_btn_action"-->
<!--                            android:textColor="@color/white"-->
<!--                            android:textStyle="bold" />-->

<!--                    </LinearLayout>-->

                </LinearLayout>
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <androidx.cardview.widget.CardView
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:clipChildren="false"
                        android:clipToPadding="false"
                        android:divider="@android:color/transparent"
                        android:dividerHeight="0.0px"
                        android:elevation="8dp"
                        android:visibility="visible"
                        app:cardElevation="2dp"
                        app:cardPreventCornerOverlap="false"
                        app:cardUseCompatPadding="true">

                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/recycle_payement"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:visibility="visible" />

                        <TextView
                            android:id="@+id/noItemFound"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_gravity="center"
                            android:gravity="center"
                            android:padding="@dimen/dp20"
                            android:text="@string/no_sales_found"
                            android:textColor="@color/black"
                            android:textSize="16sp"
                            android:visibility="gone">

                        </TextView>
                    </androidx.cardview.widget.CardView>
                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

</ScrollView>