<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:padding="@dimen/activity_horizontal_margin">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="7dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/label_our_client"
                        android:textColor="@color/black"
                        android:textSize="20dp" />


                    <TextView
                        android:id="@+id/sub_title"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="5dp"
                        android:text="@string/label_add_new_contact"
                        android:textColor="@color/gray"
                        android:textSize="14dp" />
                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="2dp"
                    android:background="@color/blue_dark" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/cell_shape_border"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:padding="@dimen/activity_vertical_margin">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginHorizontal="@dimen/activity_left_margin"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/lbl_contact_type"
                            android:textColor="@color/blue_txt" />

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:gravity="center"
                            android:orientation="horizontal"
                            android:paddingTop="@dimen/margin_vertical">

                            <ImageView
                                android:layout_width="30px"
                                android:layout_height="30px"
                                android:layout_weight="1"
                                android:src="@drawable/icons_contacts" />

                            <Spinner
                                android:id="@+id/spinner_type"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:entries="@array/array_contact_type" />
                        </LinearLayout>

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginHorizontal="@dimen/activity_left_margin"
                        android:layout_weight="1"

                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/company_name_hint"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/image_nom_entrprise"
                            android:textColor="@color/blue_txt" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:orientation="horizontal"
                            android:paddingTop="@dimen/margin_vertical">

                            <ImageView
                                android:layout_width="30px"
                                android:layout_height="30px"
                                android:src="@drawable/icons8_compost_heap" />

                            <EditText
                                android:id="@+id/bussiness_name_txt"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_weight="1" />
                        </LinearLayout>

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginHorizontal="@dimen/activity_left_margin"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/image_prefix"
                            android:textColor="@color/blue_txt" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:orientation="horizontal"
                            android:paddingTop="@dimen/margin_vertical">

                            <ImageView
                                android:layout_width="30px"
                                android:layout_height="30px"
                                android:src="@drawable/icons8_user" />

                            <EditText
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_weight="1" />
                        </LinearLayout>

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginHorizontal="@dimen/activity_left_margin"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/image_prenom"
                            android:textColor="@color/blue_txt" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:orientation="horizontal"
                            android:paddingTop="@dimen/margin_vertical">

                            <ImageView
                                android:layout_width="30px"
                                android:layout_height="30px"
                                android:src="@drawable/icons8_user" />

                            <EditText
                                android:id="@+id/first_name_txt"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_weight="1" />
                        </LinearLayout>

                    </LinearLayout>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:padding="@dimen/activity_vertical_margin">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginHorizontal="@dimen/activity_left_margin"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/text_nom_famile"
                            android:textColor="@color/blue_txt" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:orientation="horizontal"
                            android:paddingTop="@dimen/margin_vertical">

                            <ImageView
                                android:layout_width="30px"
                                android:layout_height="30px"
                                android:src="@drawable/icons8_user" />

                            <EditText
                                android:id="@+id/last_name_txt"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_weight="1" />
                        </LinearLayout>

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginHorizontal="@dimen/activity_left_margin"
                        android:layout_weight="1"

                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/image_mobile"
                            android:textColor="@color/blue_txt" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:orientation="horizontal"
                            android:paddingTop="@dimen/margin_vertical">

                            <ImageView
                                android:layout_width="30px"
                                android:layout_height="30px"
                                android:src="@drawable/icons8_touchscreen" />

                            <EditText
                                android:id="@+id/phone_txt"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:inputType="numberDecimal" />
                        </LinearLayout>

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginHorizontal="@dimen/activity_left_margin"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/image_ligne_fixe"
                            android:textColor="@color/blue_txt" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:orientation="horizontal"
                            android:paddingTop="@dimen/margin_vertical">

                            <ImageView
                                android:layout_width="30px"
                                android:layout_height="30px"
                                android:src="@drawable/icons8_phone" />

                            <EditText
                                android:id="@+id/line_fixe_txt"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:hint="@string/image_ligne_fixe" />
                        </LinearLayout>

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginHorizontal="@dimen/activity_left_margin"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/image_nom_email"
                            android:textColor="@color/blue_txt" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:orientation="horizontal"
                            android:paddingTop="@dimen/margin_vertical">

                            <ImageView
                                android:layout_width="30px"
                                android:layout_height="30px"
                                android:src="@drawable/icons8_email" />

                            <EditText
                                android:id="@+id/email_txt"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_weight="1" />
                        </LinearLayout>

                    </LinearLayout>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:padding="@dimen/activity_vertical_margin">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginHorizontal="@dimen/activity_left_margin"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/label_date_naissance"
                            android:textColor="@color/blue_txt" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:orientation="horizontal"
                            android:paddingTop="@dimen/margin_vertical">

                            <ImageView
                                android:layout_width="30px"
                                android:layout_height="30px"
                                android:src="@drawable/icons8_calendar" />

                            <androidx.appcompat.widget.AppCompatEditText
                                android:id="@+id/birth_date_txt"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:focusableInTouchMode="false"
                                android:layout_weight="1" />
                        </LinearLayout>

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginHorizontal="@dimen/activity_left_margin"
                        android:layout_weight="1"
                        android:orientation="vertical"
                        android:visibility="invisible">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/image_mobile"
                            android:textColor="@color/blue_txt" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:orientation="horizontal"
                            android:paddingTop="@dimen/margin_vertical">

                            <ImageView
                                android:layout_width="30px"
                                android:layout_height="30px"
                                android:src="@drawable/icons8_touchscreen" />

                            <EditText
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:inputType="numberDecimal" />
                        </LinearLayout>

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginHorizontal="@dimen/activity_left_margin"
                        android:layout_weight="1"
                        android:orientation="vertical"
                        android:visibility="invisible">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/image_ligne_fixe"
                            android:textColor="@color/blue_txt" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:orientation="horizontal"
                            android:paddingTop="@dimen/margin_vertical">

                            <ImageView
                                android:layout_width="30px"
                                android:layout_height="30px"
                                android:src="@drawable/icons8_phone" />

                            <EditText
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:hint="@string/image_ligne_fixe" />
                        </LinearLayout>

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginHorizontal="@dimen/activity_left_margin"
                        android:layout_weight="1"
                        android:orientation="vertical"
                        android:visibility="invisible">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/image_nom_email"
                            android:textColor="@color/blue_txt" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:orientation="horizontal"
                            android:paddingTop="@dimen/margin_vertical">

                            <ImageView
                                android:layout_width="30px"
                                android:layout_height="30px"
                                android:src="@drawable/icons8_email" />

                            <EditText
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_weight="1" />
                        </LinearLayout>

                    </LinearLayout>

                </LinearLayout>
            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="1px"
                android:layout_marginHorizontal="50dp"
                android:layout_marginVertical="@dimen/activity_vertical_margin"
                android:background="@color/gray" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@drawable/cell_shape_border"
                android:padding="@dimen/activity_vertical_margin">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginHorizontal="@dimen/activity_left_margin"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/image_tax_number"
                        android:textColor="@color/blue_txt" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:orientation="horizontal"
                        android:paddingTop="@dimen/margin_vertical">

                        <ImageView
                            android:layout_width="30px"
                            android:layout_height="30px"
                            android:src="@drawable/icons8_world_map" />

                        <EditText
                            android:id="@+id/tax_number"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="numberDecimal"
                            android:layout_weight="1" />
                    </LinearLayout>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginHorizontal="@dimen/activity_left_margin"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/txt_opening_balance"
                        android:textColor="@color/blue_txt" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:orientation="horizontal"
                        android:paddingTop="@dimen/margin_vertical">

                        <ImageView
                            android:layout_width="30px"
                            android:layout_height="30px"
                            android:src="@drawable/icons8_user_location" />

                        <EditText
                            android:id="@+id/opening_balance"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:inputType="numberDecimal"
                            android:hint="@string/image_ligne_fixe" />

                    </LinearLayout>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginHorizontal="@dimen/activity_left_margin"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/text_pay_term"
                        android:textColor="@color/blue_txt" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:orientation="horizontal"
                        android:paddingTop="@dimen/margin_vertical">

                        <EditText
                            android:id="@+id/pay_term_number"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="numberDecimal"
                            android:layout_weight="1" />

                        <Spinner
                            android:id="@+id/pay_term_type"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:entries="@array/array_duration" />
                    </LinearLayout>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginHorizontal="@dimen/activity_left_margin"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/image_credit_limit"
                        android:textColor="@color/blue_txt" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:orientation="horizontal"
                        android:paddingTop="@dimen/margin_vertical">

                        <ImageView
                            android:layout_width="30px"
                            android:layout_height="30px"
                            android:src="@drawable/icons8_user_location" />

                        <EditText
                            android:id="@+id/credit_limit"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="numberDecimal"
                            android:layout_weight="1" />
                    </LinearLayout>

                </LinearLayout>

            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="1px"
                android:layout_marginHorizontal="50dp"
                android:layout_marginVertical="@dimen/activity_vertical_margin"
                android:background="@color/gray" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/cell_shape_border"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:padding="@dimen/activity_vertical_margin">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginHorizontal="@dimen/activity_left_margin"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/image_adresse"
                            android:textColor="@color/blue_txt" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:orientation="horizontal"
                            android:paddingTop="@dimen/margin_vertical">

                            <ImageView
                                android:layout_width="30px"
                                android:layout_height="30px"
                                android:src="@drawable/icons8_world_map" />

                            <EditText
                                android:id="@+id/adress_txt"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_weight="1" />
                        </LinearLayout>

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginHorizontal="@dimen/activity_left_margin"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/image_adresse1"
                            android:textColor="@color/blue_txt" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:orientation="horizontal"
                            android:paddingTop="@dimen/margin_vertical">

                            <ImageView
                                android:layout_width="30px"
                                android:layout_height="30px"
                                android:src="@drawable/icons8_world_map" />

                            <EditText
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_weight="1" />
                        </LinearLayout>

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginHorizontal="@dimen/activity_left_margin"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/image_ville"
                            android:textColor="@color/blue_txt" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:orientation="horizontal"
                            android:paddingTop="@dimen/margin_vertical">

                            <ImageView
                                android:layout_width="30px"
                                android:layout_height="30px"
                                android:src="@drawable/icons8_user_location" />

                            <EditText
                                android:id="@+id/city_txt"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:hint="@string/image_ligne_fixe" />
                        </LinearLayout>

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginHorizontal="@dimen/activity_left_margin"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/image_statee"
                            android:textColor="@color/blue_txt" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:orientation="horizontal"
                            android:paddingTop="@dimen/margin_vertical">

                            <ImageView
                                android:layout_width="30px"
                                android:layout_height="30px"
                                android:src="@drawable/icons8_user_location" />

                            <EditText
                                android:id="@+id/state"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_weight="1" />
                        </LinearLayout>

                    </LinearLayout>


                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:padding="@dimen/activity_vertical_margin">


                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginHorizontal="@dimen/activity_left_margin"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/text_pays"
                            android:textColor="@color/blue_txt" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:orientation="horizontal"
                            android:paddingTop="@dimen/margin_vertical">

                            <ImageView
                                android:layout_width="30px"
                                android:layout_height="30px"
                                android:src="@drawable/icons8_location" />

                            <EditText
                                android:id="@+id/country_txt"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_weight="1" />
                        </LinearLayout>

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginHorizontal="@dimen/activity_left_margin"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/image_code_postal"
                            android:textColor="@color/blue_txt" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:orientation="horizontal"
                            android:paddingTop="@dimen/margin_vertical">

                            <ImageView
                                android:layout_width="30px"
                                android:layout_height="30px"
                                android:src="@drawable/icons8_user_location" />

                            <EditText
                                android:id="@+id/zip_code"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_weight="1" />
                        </LinearLayout>

                    </LinearLayout>



                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginHorizontal="@dimen/activity_left_margin"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/image_shipping_adress"
                            android:textColor="@color/blue_txt" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:orientation="horizontal"
                            android:paddingTop="@dimen/margin_vertical">

                            <ImageView
                                android:layout_width="30px"
                                android:layout_height="30px"
                                android:src="@drawable/icons8_user_location" />

                            <EditText
                                android:id="@+id/shipping_adresse"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_weight="1" />
                        </LinearLayout>

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginHorizontal="@dimen/activity_left_margin"
                        android:layout_weight="1"
                        android:orientation="vertical"
                        android:visibility="invisible">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/image_ville"
                            android:textColor="@color/blue_txt" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:orientation="horizontal"
                            android:paddingTop="@dimen/margin_vertical">

                            <ImageView
                                android:layout_width="30px"
                                android:layout_height="30px"
                                android:src="@drawable/icons8_user_location" />

                            <EditText
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:hint="@string/image_ligne_fixe" />
                        </LinearLayout>

                    </LinearLayout>
                </LinearLayout>
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="horizontal"
                android:padding="@dimen/padding_top">

                <Button
                    android:id="@+id/add_btn"
                    style="@style/my_btn_style"
                    android:layout_width="wrap_content"
                    android:layout_height="70px"
                    android:text="@string/label_add" />

                <Button
                    android:id="@+id/id_back"
                    style="@style/my_btn_style"
                    android:layout_width="wrap_content"
                    android:layout_height="70px"
                    android:background="@drawable/rounded_btn_blue"
                    android:text="@string/label_close" />

            </LinearLayout>
        </LinearLayout>

    </LinearLayout>

</ScrollView>