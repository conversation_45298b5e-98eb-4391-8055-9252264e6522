<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:padding="@dimen/activity_horizontal_margin">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="7dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"

                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/label_products"
                    android:textColor="@color/black"
                    android:textSize="20dp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="5dp"
                    android:text="@string/label_products_gerer"
                    android:textColor="@color/gray"
                    android:textSize="14dp" />
            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="2dp"
                android:background="@color/blue_dark" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/filter_header"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/cell_shape_header"
            android:paddingHorizontal="@dimen/padding_horizontal"
            android:paddingVertical="@dimen/activity_left_margin">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:drawableLeft="@drawable/ic_baseline_filter_alt_24"
                android:text="Filtres"
                android:textColor="@color/white"
                android:textSize="16dp" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/filter_container"

            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/cell_shape_border"

            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="horizontal"
                android:paddingTop="10dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/include2">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    android:paddingHorizontal="@dimen/activity_horizontal_margin">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/label_product_typee" />

                    <Spinner
                        android:id="@+id/spinner_prod_type"
                        style="@style/my_spinner_style"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:entries="@array/type_produc_type" />
                </LinearLayout>


                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    android:paddingHorizontal="@dimen/activity_horizontal_margin">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/label_category_name"

                        />

                    <Spinner
                        android:id="@+id/spinner_category"
                        style="@style/my_spinner_style"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    android:paddingHorizontal="@dimen/activity_horizontal_margin">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/label_unit"

                        />

                    <Spinner
                        android:id="@+id/spinner_unit"
                        style="@style/my_spinner_style"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:entries="@array/type_select_unite" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    android:paddingHorizontal="@dimen/activity_horizontal_margin">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/label_tax" />

                    <Spinner
                        style="@style/my_spinner_style"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:entries="@array/array_action_tax" />
                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="horizontal"
                android:paddingTop="10dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/include2">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    android:paddingHorizontal="@dimen/activity_horizontal_margin">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/string_ubranndt" />

                    <Spinner
                        android:id="@+id/spinner_brand"
                        style="@style/my_spinner_style"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="1" />
                </LinearLayout>

                <LinearLayout
                    android:visibility="invisible"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    android:paddingHorizontal="@dimen/activity_horizontal_margin">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/label_business_location" />

                    <Spinner
                        android:id="@+id/spinner_station"
                        style="@style/my_spinner_style"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="1" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    android:paddingHorizontal="@dimen/activity_horizontal_margin"
                    android:visibility="invisible">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/label_unit"

                        />

                    <Spinner
                        style="@style/my_spinner_style"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:entries="@array/type_select_unite" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    android:paddingHorizontal="@dimen/activity_horizontal_margin">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/label_unit"
                        android:visibility="invisible" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:gravity="right"
                        android:orientation="horizontal">

                        <Button
                            android:id="@+id/id_filter"
                            style="@style/my_btn_style"
                            android:layout_width="wrap_content"
                            android:layout_height="70px"
                            android:background="@drawable/rounded_btn_bg_orange"
                            android:text="@string/label_filter" />

                        <Button
                            android:id="@+id/id_add"
                            style="@style/my_btn_style"
                            android:layout_width="wrap_content"
                            android:layout_height="70px"
                            android:text="@string/label_add" />

                    </LinearLayout>
                </LinearLayout>


            </LinearLayout>
        </LinearLayout>


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="horizontal"
            android:paddingTop="10dp"
            android:visibility="gone"

            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/include2">

            <Spinner
                style="@style/my_spinner_style"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:entries="@array/type_select_marque" />

            <Spinner
                style="@style/my_spinner_style"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:entries="@array/type_select_affaire_lieu" />

            <Spinner
                style="@style/my_spinner_style"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:entries="@array/type_select_active" />

            <CheckBox
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/activity_horizontal_margin"
                android:layout_weight="1"
                android:text="@string/label_pas_avendre" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="@dimen/activity_left_margin"
            android:background="@drawable/cell_shape"
            android:gravity="center"
            android:orientation="horizontal"
            android:padding="20px">

            <ImageView
                android:id="@+id/image_product"
                android:layout_width="match_parent"
                android:layout_height="70px"
                android:layout_weight="1"
                android:src="@drawable/smart_contact_ic"
                android:visibility="invisible" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Name product "
                android:textColor="@color/black"
                android:textStyle="bold" />


            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/label_business_location"
                android:textColor="@color/black"
                android:textStyle="bold" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Categorie"
                android:textColor="@color/black"
                android:textStyle="bold" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/label_unit_purchase_pricee"
                android:textColor="@color/black"
                android:textStyle="bold" />
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/label_selling_pricee"
                android:textColor="@color/black"
                android:textStyle="bold" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="SKU"
                android:textColor="@color/black"
                android:textStyle="bold" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Stock actuel"

                android:textColor="@color/black"
                android:textStyle="bold" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Action"

                    android:textColor="@color/black"
                    android:textStyle="bold" />
            </LinearLayout>


        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/white_blue">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recycler_purchases"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />
        </LinearLayout>

    </LinearLayout>

</LinearLayout>