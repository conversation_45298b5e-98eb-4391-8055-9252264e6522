<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layoutDirection="ltr"
    android:layout_height="match_parent">

    <androidx.cardview.widget.CardView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        app:cardCornerRadius="8dp"
        app:cardElevation="0dp"
        app:cardBackgroundColor="@color/white">

        <!-- Replaced CamomileSpinner with a standard ProgressBar -->
        <ProgressBar
            android:id="@+id/loader"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_margin="16dp" />

    </androidx.cardview.widget.CardView>

</RelativeLayout>
