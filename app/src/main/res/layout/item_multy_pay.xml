<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:backgroundTint="@color/gray_back"
    android:elevation="@dimen/cardview_default_radius"
    android:layout_margin="7dp"
    app:cardCornerRadius="5dp"
    android:orientation="vertical"
    app:cardElevation="@dimen/cardview_default_radius">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="10dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="end">

            <ImageView
                android:id="@+id/id_quit"
                android:layout_width="25dp"
                android:layout_height="25dp"
                android:src="@drawable/quit" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"

            android:orientation="horizontal">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/label_amount"
                android:textStyle="bold"
                android:fontFamily="@font/poppins_semibold"
                android:layout_weight="1"
                android:textColor="@color/black" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/label_payment_method"
                android:textStyle="bold"
                android:fontFamily="@font/poppins_semibold"
                android:layout_weight="1"
                android:textColor="@color/black" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="start"
            android:gravity="start"
            android:paddingTop="5dp"
            android:orientation="horizontal">

            <EditText
                android:id="@+id/id_amount"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:inputType="number"
                android:text="00.00"
                android:padding="@dimen/dp5"
                android:layout_marginRight="7dp"
                android:layout_weight="1"
                android:background="@color/white"
                android:drawableLeft="@drawable/is_cash_out"
                android:drawablePadding="3dp"
                android:textColor="@color/black" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:paddingLeft="5dp"
                android:backgroundTint="@color/white"
                android:paddingTop="5dp"
                android:paddingBottom="5dp"
                android:background="@color/white"
                android:orientation="horizontal"
                android:gravity="center">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/is_cash_out" />

                <Spinner
                    android:id="@+id/id_spinner_product"
                    android:layout_width="match_parent"
                    android:layout_height="40dp"
                    android:textStyle="bold"
                    android:layout_weight="1"
                    android:entries="@array/array_apply_pay" />
            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginTop="10dp">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/label_payment_note"
                android:textStyle="bold"
                android:fontFamily="@font/poppins_semibold"
                android:layout_weight="1"
                android:textColor="@color/black" />

            <EditText
                android:id="@+id/id_text_note"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp5"
                android:padding="@dimen/dp5"
                android:background="@color/white"
                android:lines="3" />
        </LinearLayout>

    </LinearLayout>


</androidx.cardview.widget.CardView>