<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/linearLayout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    tools:background="@color/white">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:gravity="center"
        android:orientation="horizontal"
        android:padding="20px">


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:paddingLeft="10dp">

            <ImageView
                android:id="@+id/image_product"
                android:layout_width="70dp"
                android:layout_height="70dp"
                android:src="@drawable/image_notavailable" />
        </LinearLayout>


        <TextView
            android:id="@+id/product_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textColor="@color/black"
            tools:text="Text " />

        <TextView
            android:id="@+id/business_location"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textColor="@color/black"

            tools:text="Text " />

        <TextView
            android:id="@+id/category_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textColor="@color/black"
            tools:text="Text " />

        <TextView
            android:id="@+id/purchase_price"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textColor="@color/black"
            tools:text="Text " />

        <TextView
            android:id="@+id/selling_price"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textColor="@color/black"
            tools:text="Text " />

        <TextView
            android:id="@+id/id_sku"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textColor="@color/black"
            tools:text="Text " />

        <TextView
            android:id="@+id/stock_actual"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textColor="@color/black"
            tools:text="Text " />
        <TextView
            android:id="@+id/taxAmount"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:textColor="@color/black"
            tools:text="Tax " />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center">

            <Spinner
                android:id="@+id/spinner_action"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"/>

        </LinearLayout>


    </LinearLayout>

</LinearLayout>