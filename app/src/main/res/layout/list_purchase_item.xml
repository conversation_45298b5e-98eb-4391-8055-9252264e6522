<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/linearLayout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:gravity="center"
        android:orientation="horizontal"
        android:paddingVertical="@dimen/dp15"
        android:paddingHorizontal="5dp">

        <TextView
            android:id="@+id/purchase_date"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textColor="@color/black"
            android:textStyle="normal" />

        <TextView
            android:id="@+id/purchase_ref"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textColor="@color/black"
            android:textStyle="normal" />

        <TextView
            android:id="@+id/purchase_location"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textColor="@color/black"
            android:textStyle="normal" />

        <TextView
            android:id="@+id/purchase_supplier"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textColor="@color/black"
            android:textStyle="normal" />

        <TextView
            android:id="@+id/purchase_added_by"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/label_added_by"
            android:textColor="@color/black"
            android:textStyle="normal"
            android:visibility="gone" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:padding="7dp">

            <TextView
                android:id="@+id/purchase_status"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/black"
                android:textStyle="normal" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:padding="7dp">

            <TextView
                android:id="@+id/purchase_payment_status"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/black"
                android:textStyle="normal" />

        </LinearLayout>

        <TextView
            android:id="@+id/purchase_grand_total"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textColor="@color/black"
            android:textStyle="normal" />

        <TextView
            android:id="@+id/purchase_payment_due"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textColor="@color/black"
            android:textStyle="normal" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center">

            <Spinner
                android:id="@+id/spinner_action"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:entries="@array/array_action_purchase" />

        </LinearLayout>

    </LinearLayout>
</LinearLayout>