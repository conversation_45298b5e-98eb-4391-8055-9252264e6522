<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:id="@+id/linearLayout"
    android:layout_height="wrap_content">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:gravity="center"
        android:orientation="horizontal"
        android:paddingHorizontal="5dp"
        android:paddingVertical="5dp">


        <TextView
            android:id="@+id/purchase_date"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/label_date"
            android:textColor="@color/black"
            android:textStyle="normal" />

        <TextView
            android:id="@+id/purchase_ref"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textColor="@color/black"
            android:textStyle="normal" />

        <TextView
            android:id="@+id/purchase_location"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/label_location"
            android:textColor="@color/black"
            android:textStyle="normal" />

        <TextView
            android:id="@+id/purchase_supplier"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textColor="@color/black"
            android:textStyle="normal" />

        <TextView
            android:visibility="gone"
            android:id="@+id/purchase_added_by"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/label_added_by"
            android:textColor="@color/black"
            android:textStyle="normal" />

        <TextView
            android:id="@+id/purchase_status"
            android:layout_width="0dp"
            android:visibility="gone"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textColor="@color/black"
            android:textStyle="normal" />

        <TextView
            android:id="@+id/purchase_payment_status"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textColor="@color/black"
            android:textStyle="normal" />

        <TextView
            android:id="@+id/purchase_grand_total"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/labele_product_total"
            android:textColor="@color/black"
            android:textStyle="normal" />


        <TextView
            android:id="@+id/purchase_payment_due"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textColor="@color/black"
            android:textStyle="normal" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="@dimen/dp50"
            android:layout_weight="1"
            android:gravity="center">

            <Spinner
                android:id="@+id/spinner_action"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:entries="@array/array_action_purchase_return" />

        </LinearLayout>

    </LinearLayout>
</LinearLayout>