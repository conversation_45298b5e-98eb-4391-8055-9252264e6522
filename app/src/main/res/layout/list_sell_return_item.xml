<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:id="@+id/linearLayout"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:weightSum="9"
        android:padding="7dp"
        android:gravity="center"
        android:orientation="horizontal"
        >

           <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/txtDate"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            tools:text="Date"
            android:textColor="@color/black" />

           <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/invoiceNo"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
               android:gravity="center"
            tools:text="InvoiceNo"
            android:textColor="@color/black" />

           <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/parentSale"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
               android:gravity="center"
            tools:text="parent Sale"
            android:textColor="@color/black" />

           <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/customerName"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
               android:gravity="center"
            tools:text="Customer Name"
            android:textColor="@color/black" />
        

           <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/location"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
               android:gravity="center"
            tools:text="TextView"
            android:textColor="@color/black" />
        <LinearLayout
            android:layout_width="0dp"
            android:layout_weight="1"
            android:gravity="center"
            android:layout_height="wrap_content">

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/paymentStatus"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                tools:text="Paid"
                android:paddingHorizontal="@dimen/dp10"
                android:paddingVertical="@dimen/dp5"
                android:background="@drawable/green_bg_no_padding"
                android:gravity="center"
                android:textColor="@color/white"
                />
        </LinearLayout>

           <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/totalAmount"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            tools:text="TextView"
               android:gravity="center"
            android:textColor="@color/black" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/paymentDue"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            tools:text="TextView"
            android:gravity="center"
            android:textColor="@color/black" />

     
            <androidx.appcompat.widget.AppCompatSpinner
                android:id="@+id/spinnerAction"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:gravity="center"
                android:layout_height="wrap_content"
             />

        

    </LinearLayout>

</LinearLayout>