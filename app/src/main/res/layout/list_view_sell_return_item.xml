<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="@dimen/dp10"
    android:gravity="start"
    android:paddingVertical="15dp"
    android:background="@color/lightGrey"
    android:paddingHorizontal="@dimen/dp10"
    android:orientation="horizontal"
    android:weightSum="7">

    <androidx.appcompat.widget.AppCompatTextView
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="0.5"
        android:gravity="start"
        android:text="@string/hash"
        android:textColor="@color/black"
        android:textStyle="normal" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/productName"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1.1"
        android:gravity="center"
        android:text="@string/product_name"
        android:textColor="@color/black"
        android:textStyle="normal" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/unitPrice"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1.1"
        android:text="@string/label_unit_price"
        android:gravity="center"
        android:textColor="@color/black"
        android:textStyle="normal" />


    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/sellQty"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1.2"
        android:text="@string/sell_quantity"
        android:gravity="center"
        android:textColor="@color/black"
        android:textStyle="normal" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/returnQty"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="2"
        android:textSize="14sp"
        android:text="@string/return_quantity"
        android:padding="@dimen/dp_15"
        android:textColor="@color/black"
        android:textStyle="normal" />


    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/returnSubTotal"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1.1"
        android:gravity="center"
        android:text="@string/return_subtotal"
        android:textColor="@color/black"
        android:textStyle="normal" />


</LinearLayout>

