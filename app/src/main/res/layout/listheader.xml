<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="2dp"
    android:id="@+id/container"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginLeft="20dp"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/iconimage"
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:paddingBottom="10dp"
            android:paddingLeft="10dp"
            android:paddingTop="10dp"/>

        <TextView
            android:id="@+id/submenu"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="10dp"
            android:textColor="#000000"
            android:textSize="20sp"/>

    </LinearLayout>

</LinearLayout>