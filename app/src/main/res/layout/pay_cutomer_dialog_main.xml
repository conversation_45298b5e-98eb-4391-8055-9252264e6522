<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/content"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/transparent"
    android:orientation="vertical"
    android:padding="20dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/rounded_white_bg"
        android:orientation="vertical"
        android:padding="@dimen/activity_vertical_margin">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="horizontal"
            android:paddingVertical="@dimen/activity_left_margin">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:layout_margin="@dimen/margin_vertical"
                android:background="@color/gray_white"
                android:orientation="horizontal"
                android:padding="@dimen/fab_margin">


                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/customer_label"
                    android:textColor="@color/black"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/customer_name"

                    android:textColor="@color/black"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="@dimen/fab_margin" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_margin="@dimen/margin_vertical"
                android:background="@color/gray_white"
                android:orientation="vertical"
                android:padding="@dimen/fab_margin">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textStyle="bold"
                        android:textColor="@color/black"
                        android:text="@string/string_total_sell" />
                    <TextView
                        android:id="@+id/total_sale"
                        android:textColor="@color/black"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="@dimen/fab_margin"
                        android:text="00.0" />
                </LinearLayout>
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@color/black"

                        android:textStyle="bold"
                        android:text="@string/string_total_paid" />
                    <TextView
                        android:id="@+id/total_paid"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@color/black"

                        android:layout_marginLeft="@dimen/fab_margin"
                        android:text="00.0" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textStyle="bold"
                        android:textColor="@color/black"

                        android:text="@string/string_total_sale_due" />
                    <TextView
                        android:id="@+id/total_sell_due"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="@dimen/fab_margin"
                        android:textColor="@color/black"

                        android:text="00.0" />
                </LinearLayout>
            </LinearLayout>
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:weightSum="3"
            android:orientation="horizontal"
            android:layout_height="wrap_content">
            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="0dp"
                android:layout_marginTop="@dimen/dp20"
                android:layout_weight="1"
                android:layout_height="wrap_content">
                <androidx.appcompat.widget.AppCompatEditText
                    android:id="@+id/amount"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="number"
                    android:layout_weight="1"
                    android:hint="@string/label_montant_total_montant" />

            </com.google.android.material.textfield.TextInputLayout>
            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="0dp"
                android:layout_marginStart="@dimen/dp10"
                android:layout_marginTop="@dimen/dp20"
                android:layout_weight="1"
                android:layout_height="wrap_content">

                <androidx.appcompat.widget.AppCompatEditText
                    android:id="@+id/paid_on"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:hint="@string/image_ligne_paye"
                    android:inputType="number" />

            </com.google.android.material.textfield.TextInputLayout>
            <FrameLayout
                android:layout_width="0dp"
                android:layout_weight="1"
                android:layout_marginStart="@dimen/dp10"
                android:layout_marginTop="@dimen/dp20"
                android:layout_height="wrap_content">

                <androidx.appcompat.widget.AppCompatSpinner
                    android:id="@+id/spinner_type"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/default_spinner_height"
                    android:layout_marginTop="5dp"
                    android:entries="@array/payment_method_array"
                    android:textSize="12sp"
                    android:textStyle="normal" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp_10"
                    android:background="@color/white"
                    android:paddingLeft="4dp"
                    android:visibility="visible"
                    android:text="@string/label_spaymenet_method"
                    android:paddingRight="4dp"
                    android:textSize="12sp" />
            </FrameLayout>
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">


            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_marginTop="@dimen/dp20"

                android:layout_height="wrap_content">
                <androidx.appcompat.widget.AppCompatEditText
                    android:id="@+id/payement_note"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:hint="@string/string_payement_note" />

            </com.google.android.material.textfield.TextInputLayout>
        </LinearLayout>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="horizontal">

            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/btn_save"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp40"
                android:background="@drawable/bg_gradient"
                android:layout_marginHorizontal="@dimen/dp10"
                android:layout_marginTop="@dimen/dp5"
                android:text="@string/label_save" />
            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/btn_close"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp40"
                android:background="@drawable/bg_gradient"
                android:layout_marginHorizontal="@dimen/dp10"
                android:layout_marginTop="@dimen/dp5"
                android:text="@string/label_close" />


        </LinearLayout>
    </LinearLayout>
</LinearLayout>