<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:background="@drawable/cell_shape_ntop"
        android:gravity="center"
        android:orientation="horizontal"
        android:paddingVertical="30px"
        android:paddingHorizontal="@dimen/dp5"
        android:weightSum="5">

        <TextView
            android:id="@+id/paid_on"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textColor="@color/black" />

        <TextView
            android:id="@+id/reference_no"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textColor="@color/black" />


        <TextView
            android:id="@+id/amount"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textColor="@color/black" />

        <TextView
            android:id="@+id/payement_method"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textColor="@color/black" />

        <TextView
            android:id="@+id/payment_for"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textColor="@color/black" />

<!--        <LinearLayout-->
<!--            android:layout_width="match_parent"-->
<!--            android:layout_height="wrap_content"-->
<!--            android:layout_weight="1"-->
<!--            android:gravity="center">-->

<!--            <androidx.appcompat.widget.AppCompatImageView-->
<!--                android:id="@+id/btn_delete"-->
<!--                android:layout_width="@dimen/dp50"-->
<!--                android:layout_height="50dp"-->
<!--                android:layout_gravity="center"-->
<!--                android:src="@drawable/ic_delete_svg"-->
<!--                android:gravity="center"-->
<!--                android:padding="@dimen/dp10"-->
<!--                />-->

<!--            <androidx.appcompat.widget.AppCompatImageView-->
<!--                android:id="@+id/btn_edit"-->
<!--                android:layout_width="@dimen/dp50"-->
<!--                android:layout_height="@dimen/dp50"-->
<!--                android:layout_marginStart="@dimen/dp10"-->
<!--                android:tint="@color/skyBlue"-->
<!--                android:src="@drawable/ic_edit_filled"-->
<!--                android:gravity="start"-->
<!--                android:padding="@dimen/dp10"-->
<!--                />-->
<!--        </LinearLayout>-->


    </LinearLayout>

</LinearLayout>