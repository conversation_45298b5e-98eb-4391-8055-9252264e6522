<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:background="@drawable/cell_shape_ntop"
        android:gravity="center"
        android:orientation="horizontal"
        android:paddingVertical="@dimen/dp10"
        android:paddingHorizontal="@dimen/dp5"
        android:weightSum="4">

        <TextView
            android:id="@+id/paid_on"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"

            android:textColor="@color/black" />

        <TextView
            android:id="@+id/amount"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textColor="@color/black" />

        <TextView
            android:id="@+id/payement_method"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textColor="@color/black" />

        <TextView
            android:id="@+id/contact_id"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textColor="@color/black" />


    </LinearLayout>

</LinearLayout>