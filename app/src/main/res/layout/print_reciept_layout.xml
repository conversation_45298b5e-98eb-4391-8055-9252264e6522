<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
    android:id="@+id/linearLayout"
        android:orientation="vertical"
        tools:ignore="MissingDefaultResource">
    <androidx.appcompat.widget.AppCompatTextView
        android:layout_width="match_parent"
        android:text="Rising Hightech"
        android:gravity="center"
        android:textSize="30sp"
        android:textColor="@color/black"
        android:layout_height="wrap_content">

    </androidx.appcompat.widget.AppCompatTextView>
    <androidx.appcompat.widget.AppCompatTextView
        android:layout_width="match_parent"
        android:text="Goldcrest Executive - Cluster C \n Jumeirah Lakes Towers"
        android:gravity="center"
        android:textSize="20sp"
        android:textColor="@color/black"
        android:layout_height="wrap_content">

    </androidx.appcompat.widget.AppCompatTextView>
    <LinearLayout
        android:layout_width="match_parent"
        android:weightSum="2"
        android:layout_marginHorizontal="@dimen/dp20"
        android:layout_height="wrap_content">
        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="0dp"
            android:layout_weight="1"
            android:text="Bill No : 1223232"
            android:gravity="start"
            android:textSize="20sp"
            android:textColor="@color/black"
            android:layout_height="wrap_content">

        </androidx.appcompat.widget.AppCompatTextView>
        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="0dp"
            android:layout_weight="1"
            android:text="Date Time: 2016/06/13 12:12:12"
            android:gravity="end"
            android:textSize="20sp"
            android:textColor="@color/black"
            android:layout_height="wrap_content">

        </androidx.appcompat.widget.AppCompatTextView>

    </LinearLayout>
    <LinearLayout
        android:layout_width="match_parent"
        android:weightSum="4"
        android:layout_marginTop="@dimen/dp20"
        android:layout_marginHorizontal="@dimen/dp20"
        android:layout_height="wrap_content">
        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="0dp"
            android:layout_weight="1"
            android:text="Product"
            android:gravity="start"
            android:textStyle="bold"
            android:textSize="20sp"
            android:textColor="@color/black"
            android:layout_height="wrap_content">

        </androidx.appcompat.widget.AppCompatTextView>
        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="0dp"
            android:layout_weight="1"
            android:text="Price"
            android:gravity="end"
            android:textStyle="bold"
            android:textSize="20sp"
            android:textColor="@color/black"
            android:layout_height="wrap_content">

        </androidx.appcompat.widget.AppCompatTextView>
        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="0dp"
            android:layout_weight="1"
            android:text="Qty"
            android:gravity="end"
            android:textStyle="bold"
            android:textSize="20sp"
            android:textColor="@color/black"
            android:layout_height="wrap_content">

        </androidx.appcompat.widget.AppCompatTextView>
        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="0dp"
            android:layout_weight="1"
            android:text="Total"
            android:textStyle="bold"
            android:gravity="end"
            android:textSize="20sp"
            android:textColor="@color/black"
            android:layout_height="wrap_content">

        </androidx.appcompat.widget.AppCompatTextView>

    </LinearLayout>
    <LinearLayout
        android:layout_width="match_parent"
        android:weightSum="4"
        android:layout_marginTop="@dimen/dp20"
        android:layout_marginHorizontal="@dimen/dp20"
        android:layout_height="wrap_content">
        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="0dp"
            android:layout_weight="1"
            android:text="Product 1"
            android:gravity="start"
            android:textSize="20sp"
            android:textColor="@color/black"
            android:layout_height="wrap_content">

        </androidx.appcompat.widget.AppCompatTextView>
        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="0dp"
            android:layout_weight="1"
            android:text="100"
            android:gravity="end"
            android:textSize="20sp"
            android:textColor="@color/black"
            android:layout_height="wrap_content">

        </androidx.appcompat.widget.AppCompatTextView>
        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="0dp"
            android:layout_weight="1"
            android:text="5"
            android:gravity="end"
            android:textSize="20sp"
            android:textColor="@color/black"
            android:layout_height="wrap_content">

        </androidx.appcompat.widget.AppCompatTextView>
        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="0dp"
            android:layout_weight="1"
            android:text="500"
            android:gravity="end"
            android:textSize="20sp"
            android:textColor="@color/black"
            android:layout_height="wrap_content">

        </androidx.appcompat.widget.AppCompatTextView>

    </LinearLayout>
    <LinearLayout
        android:layout_width="match_parent"
        android:weightSum="4"
        android:layout_marginTop="@dimen/dp20"
        android:layout_marginHorizontal="@dimen/dp20"
        android:layout_height="wrap_content">
        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="0dp"
            android:layout_weight="1"
            android:text="Product 1"
            android:gravity="start"
            android:textSize="20sp"
            android:textColor="@color/black"
            android:layout_height="wrap_content">

        </androidx.appcompat.widget.AppCompatTextView>
        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="0dp"
            android:layout_weight="1"
            android:text="100"
            android:gravity="end"
            android:textSize="20sp"
            android:textColor="@color/black"
            android:layout_height="wrap_content">

        </androidx.appcompat.widget.AppCompatTextView>
        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="0dp"
            android:layout_weight="1"
            android:text="5"
            android:gravity="end"
            android:textSize="20sp"
            android:textColor="@color/black"
            android:layout_height="wrap_content">

        </androidx.appcompat.widget.AppCompatTextView>
        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="0dp"
            android:layout_weight="1"
            android:text="500"
            android:gravity="end"
            android:textSize="20sp"
            android:textColor="@color/black"
            android:layout_height="wrap_content">

        </androidx.appcompat.widget.AppCompatTextView>

    </LinearLayout> <LinearLayout
    android:layout_width="match_parent"
    android:weightSum="4"
    android:layout_marginTop="@dimen/dp20"
    android:layout_marginHorizontal="@dimen/dp20"
    android:layout_height="wrap_content">
    <androidx.appcompat.widget.AppCompatTextView
        android:layout_width="0dp"
        android:layout_weight="1"
        android:text="Product 1"
        android:gravity="start"
        android:textSize="20sp"
        android:textColor="@color/black"
        android:layout_height="wrap_content">

    </androidx.appcompat.widget.AppCompatTextView>
    <androidx.appcompat.widget.AppCompatTextView
        android:layout_width="0dp"
        android:layout_weight="1"
        android:text="100"
        android:gravity="end"
        android:textSize="20sp"
        android:textColor="@color/black"
        android:layout_height="wrap_content">

    </androidx.appcompat.widget.AppCompatTextView>
    <androidx.appcompat.widget.AppCompatTextView
        android:layout_width="0dp"
        android:layout_weight="1"
        android:text="5"
        android:gravity="end"
        android:textSize="20sp"
        android:textColor="@color/black"
        android:layout_height="wrap_content">

    </androidx.appcompat.widget.AppCompatTextView>
    <androidx.appcompat.widget.AppCompatTextView
        android:layout_width="0dp"
        android:layout_weight="1"
        android:text="500"
        android:gravity="end"
        android:textSize="20sp"
        android:textColor="@color/black"
        android:layout_height="wrap_content">

    </androidx.appcompat.widget.AppCompatTextView>

</LinearLayout> <LinearLayout
    android:layout_width="match_parent"
    android:weightSum="4"
    android:layout_marginTop="@dimen/dp20"
    android:layout_marginHorizontal="@dimen/dp20"
    android:layout_height="wrap_content">
    <androidx.appcompat.widget.AppCompatTextView
        android:layout_width="0dp"
        android:layout_weight="1"
        android:text="Product 1"
        android:gravity="start"
        android:textSize="20sp"
        android:textColor="@color/black"
        android:layout_height="wrap_content">

    </androidx.appcompat.widget.AppCompatTextView>
    <androidx.appcompat.widget.AppCompatTextView
        android:layout_width="0dp"
        android:layout_weight="1"
        android:text="100"
        android:gravity="end"
        android:textSize="20sp"
        android:textColor="@color/black"
        android:layout_height="wrap_content">

    </androidx.appcompat.widget.AppCompatTextView>
    <androidx.appcompat.widget.AppCompatTextView
        android:layout_width="0dp"
        android:layout_weight="1"
        android:text="5"
        android:gravity="end"
        android:textSize="20sp"
        android:textColor="@color/black"
        android:layout_height="wrap_content">

    </androidx.appcompat.widget.AppCompatTextView>
    <androidx.appcompat.widget.AppCompatTextView
        android:layout_width="0dp"
        android:layout_weight="1"
        android:text="500"
        android:gravity="end"
        android:textSize="20sp"
        android:textColor="@color/black"
        android:layout_height="wrap_content">

    </androidx.appcompat.widget.AppCompatTextView>

</LinearLayout> <LinearLayout
    android:layout_width="match_parent"
    android:weightSum="4"
    android:layout_marginTop="@dimen/dp20"
    android:layout_marginHorizontal="@dimen/dp20"
    android:layout_height="wrap_content">
    <androidx.appcompat.widget.AppCompatTextView
        android:layout_width="0dp"
        android:layout_weight="1"
        android:text="Product 1"
        android:gravity="start"
        android:textSize="20sp"
        android:textColor="@color/black"
        android:layout_height="wrap_content">

    </androidx.appcompat.widget.AppCompatTextView>
    <androidx.appcompat.widget.AppCompatTextView
        android:layout_width="0dp"
        android:layout_weight="1"
        android:text="100"
        android:gravity="end"
        android:textSize="20sp"
        android:textColor="@color/black"
        android:layout_height="wrap_content">

    </androidx.appcompat.widget.AppCompatTextView>
    <androidx.appcompat.widget.AppCompatTextView
        android:layout_width="0dp"
        android:layout_weight="1"
        android:text="5"
        android:gravity="end"
        android:textSize="20sp"
        android:textColor="@color/black"
        android:layout_height="wrap_content">

    </androidx.appcompat.widget.AppCompatTextView>
    <androidx.appcompat.widget.AppCompatTextView
        android:layout_width="0dp"
        android:layout_weight="1"
        android:text="500"
        android:gravity="end"
        android:textSize="20sp"
        android:textColor="@color/black"
        android:layout_height="wrap_content">

    </androidx.appcompat.widget.AppCompatTextView>

</LinearLayout> <LinearLayout
    android:layout_width="match_parent"
    android:weightSum="4"
    android:layout_marginTop="@dimen/dp20"
    android:layout_marginHorizontal="@dimen/dp20"
    android:layout_height="wrap_content">
    <androidx.appcompat.widget.AppCompatTextView
        android:layout_width="0dp"
        android:layout_weight="1"
        android:text="Product 1"
        android:gravity="start"
        android:textSize="20sp"
        android:textColor="@color/black"
        android:layout_height="wrap_content">

    </androidx.appcompat.widget.AppCompatTextView>
    <androidx.appcompat.widget.AppCompatTextView
        android:layout_width="0dp"
        android:layout_weight="1"
        android:text="100"
        android:gravity="end"
        android:textSize="20sp"
        android:textColor="@color/black"
        android:layout_height="wrap_content">

    </androidx.appcompat.widget.AppCompatTextView>
    <androidx.appcompat.widget.AppCompatTextView
        android:layout_width="0dp"
        android:layout_weight="1"
        android:text="5"
        android:gravity="end"
        android:textSize="20sp"
        android:textColor="@color/black"
        android:layout_height="wrap_content">

    </androidx.appcompat.widget.AppCompatTextView>
    <androidx.appcompat.widget.AppCompatTextView
        android:layout_width="0dp"
        android:layout_weight="1"
        android:text="500"
        android:gravity="end"
        android:textSize="20sp"
        android:textColor="@color/black"
        android:layout_height="wrap_content">

    </androidx.appcompat.widget.AppCompatTextView>

</LinearLayout> <LinearLayout
    android:layout_width="match_parent"
    android:weightSum="4"
    android:layout_marginTop="@dimen/dp20"
    android:layout_marginHorizontal="@dimen/dp20"
    android:layout_height="wrap_content">
    <androidx.appcompat.widget.AppCompatTextView
        android:layout_width="0dp"
        android:layout_weight="1"
        android:text="Product 1"
        android:gravity="start"
        android:textSize="20sp"
        android:textColor="@color/black"
        android:layout_height="wrap_content">

    </androidx.appcompat.widget.AppCompatTextView>
    <androidx.appcompat.widget.AppCompatTextView
        android:layout_width="0dp"
        android:layout_weight="1"
        android:text="100"
        android:gravity="end"
        android:textSize="20sp"
        android:textColor="@color/black"
        android:layout_height="wrap_content">

    </androidx.appcompat.widget.AppCompatTextView>
    <androidx.appcompat.widget.AppCompatTextView
        android:layout_width="0dp"
        android:layout_weight="1"
        android:text="5"
        android:gravity="end"
        android:textSize="20sp"
        android:textColor="@color/black"
        android:layout_height="wrap_content">

    </androidx.appcompat.widget.AppCompatTextView>
    <androidx.appcompat.widget.AppCompatTextView
        android:layout_width="0dp"
        android:layout_weight="1"
        android:text="500"
        android:gravity="end"
        android:textSize="20sp"
        android:textColor="@color/black"
        android:layout_height="wrap_content">

    </androidx.appcompat.widget.AppCompatTextView>

</LinearLayout> <LinearLayout
    android:layout_width="match_parent"
    android:weightSum="4"
    android:layout_marginTop="@dimen/dp20"
    android:layout_marginHorizontal="@dimen/dp20"
    android:layout_height="wrap_content">
    <androidx.appcompat.widget.AppCompatTextView
        android:layout_width="0dp"
        android:layout_weight="1"
        android:text="Product 1"
        android:gravity="start"
        android:textSize="20sp"
        android:textColor="@color/black"
        android:layout_height="wrap_content">

    </androidx.appcompat.widget.AppCompatTextView>
    <androidx.appcompat.widget.AppCompatTextView
        android:layout_width="0dp"
        android:layout_weight="1"
        android:text="100"
        android:gravity="end"
        android:textSize="20sp"
        android:textColor="@color/black"
        android:layout_height="wrap_content">

    </androidx.appcompat.widget.AppCompatTextView>
    <androidx.appcompat.widget.AppCompatTextView
        android:layout_width="0dp"
        android:layout_weight="1"
        android:text="5"
        android:gravity="end"
        android:textSize="20sp"
        android:textColor="@color/black"
        android:layout_height="wrap_content">

    </androidx.appcompat.widget.AppCompatTextView>
    <androidx.appcompat.widget.AppCompatTextView
        android:layout_width="0dp"
        android:layout_weight="1"
        android:text="500"
        android:gravity="end"
        android:textSize="20sp"
        android:textColor="@color/black"
        android:layout_height="wrap_content">

    </androidx.appcompat.widget.AppCompatTextView>

</LinearLayout> <LinearLayout
    android:layout_width="match_parent"
    android:weightSum="4"
    android:layout_marginTop="@dimen/dp20"
    android:layout_marginHorizontal="@dimen/dp20"
    android:layout_height="wrap_content">
    <androidx.appcompat.widget.AppCompatTextView
        android:layout_width="0dp"
        android:layout_weight="1"
        android:text="Product 1"
        android:gravity="start"
        android:textSize="20sp"
        android:textColor="@color/black"
        android:layout_height="wrap_content">

    </androidx.appcompat.widget.AppCompatTextView>
    <androidx.appcompat.widget.AppCompatTextView
        android:layout_width="0dp"
        android:layout_weight="1"
        android:text="100"
        android:gravity="end"
        android:textSize="20sp"
        android:textColor="@color/black"
        android:layout_height="wrap_content">

    </androidx.appcompat.widget.AppCompatTextView>
    <androidx.appcompat.widget.AppCompatTextView
        android:layout_width="0dp"
        android:layout_weight="1"
        android:text="5"
        android:gravity="end"
        android:textSize="20sp"
        android:textColor="@color/black"
        android:layout_height="wrap_content">

    </androidx.appcompat.widget.AppCompatTextView>
    <androidx.appcompat.widget.AppCompatTextView
        android:layout_width="0dp"
        android:layout_weight="1"
        android:text="500"
        android:gravity="end"
        android:textSize="20sp"
        android:textColor="@color/black"
        android:layout_height="wrap_content">

    </androidx.appcompat.widget.AppCompatTextView>

</LinearLayout> <LinearLayout
    android:layout_width="match_parent"
    android:weightSum="4"
    android:layout_marginTop="@dimen/dp20"
    android:layout_marginHorizontal="@dimen/dp20"
    android:layout_height="wrap_content">
    <androidx.appcompat.widget.AppCompatTextView
        android:layout_width="0dp"
        android:layout_weight="1"
        android:text="Product 1"
        android:gravity="start"
        android:textSize="20sp"
        android:textColor="@color/black"
        android:layout_height="wrap_content">

    </androidx.appcompat.widget.AppCompatTextView>
    <androidx.appcompat.widget.AppCompatTextView
        android:layout_width="0dp"
        android:layout_weight="1"
        android:text="100"
        android:gravity="end"
        android:textSize="20sp"
        android:textColor="@color/black"
        android:layout_height="wrap_content">

    </androidx.appcompat.widget.AppCompatTextView>
    <androidx.appcompat.widget.AppCompatTextView
        android:layout_width="0dp"
        android:layout_weight="1"
        android:text="5"
        android:gravity="end"
        android:textSize="20sp"
        android:textColor="@color/black"
        android:layout_height="wrap_content">

    </androidx.appcompat.widget.AppCompatTextView>
    <androidx.appcompat.widget.AppCompatTextView
        android:layout_width="0dp"
        android:layout_weight="1"
        android:text="500"
        android:gravity="end"
        android:textSize="20sp"
        android:textColor="@color/black"
        android:layout_height="wrap_content">

    </androidx.appcompat.widget.AppCompatTextView>

</LinearLayout> <LinearLayout
    android:layout_width="match_parent"
    android:weightSum="4"
    android:layout_marginTop="@dimen/dp20"
    android:layout_marginHorizontal="@dimen/dp20"
    android:layout_height="wrap_content">
    <androidx.appcompat.widget.AppCompatTextView
        android:layout_width="0dp"
        android:layout_weight="1"
        android:text="Product 1"
        android:gravity="start"
        android:textSize="20sp"
        android:textColor="@color/black"
        android:layout_height="wrap_content">

    </androidx.appcompat.widget.AppCompatTextView>
    <androidx.appcompat.widget.AppCompatTextView
        android:layout_width="0dp"
        android:layout_weight="1"
        android:text="100"
        android:gravity="end"
        android:textSize="20sp"
        android:textColor="@color/black"
        android:layout_height="wrap_content">

    </androidx.appcompat.widget.AppCompatTextView>
    <androidx.appcompat.widget.AppCompatTextView
        android:layout_width="0dp"
        android:layout_weight="1"
        android:text="5"
        android:gravity="end"
        android:textSize="20sp"
        android:textColor="@color/black"
        android:layout_height="wrap_content">

    </androidx.appcompat.widget.AppCompatTextView>
    <androidx.appcompat.widget.AppCompatTextView
        android:layout_width="0dp"
        android:layout_weight="1"
        android:text="500"
        android:gravity="end"
        android:textSize="20sp"
        android:textColor="@color/black"
        android:layout_height="wrap_content">

    </androidx.appcompat.widget.AppCompatTextView>

</LinearLayout> <LinearLayout
    android:layout_width="match_parent"
    android:weightSum="4"
    android:layout_marginTop="@dimen/dp20"
    android:layout_marginHorizontal="@dimen/dp20"
    android:layout_height="wrap_content">
    <androidx.appcompat.widget.AppCompatTextView
        android:layout_width="0dp"
        android:layout_weight="1"
        android:text="Product 1"
        android:gravity="start"
        android:textSize="20sp"
        android:textColor="@color/black"
        android:layout_height="wrap_content">

    </androidx.appcompat.widget.AppCompatTextView>
    <androidx.appcompat.widget.AppCompatTextView
        android:layout_width="0dp"
        android:layout_weight="1"
        android:text="100"
        android:gravity="end"
        android:textSize="20sp"
        android:textColor="@color/black"
        android:layout_height="wrap_content">

    </androidx.appcompat.widget.AppCompatTextView>
    <androidx.appcompat.widget.AppCompatTextView
        android:layout_width="0dp"
        android:layout_weight="1"
        android:text="5"
        android:gravity="end"
        android:textSize="20sp"
        android:textColor="@color/black"
        android:layout_height="wrap_content">

    </androidx.appcompat.widget.AppCompatTextView>
    <androidx.appcompat.widget.AppCompatTextView
        android:layout_width="0dp"
        android:layout_weight="1"
        android:text="500"
        android:gravity="end"
        android:textSize="20sp"
        android:textColor="@color/black"
        android:layout_height="wrap_content">

    </androidx.appcompat.widget.AppCompatTextView>

</LinearLayout> <LinearLayout
    android:layout_width="match_parent"
    android:weightSum="4"
    android:layout_marginTop="@dimen/dp20"
    android:layout_marginHorizontal="@dimen/dp20"
    android:layout_height="wrap_content">
    <androidx.appcompat.widget.AppCompatTextView
        android:layout_width="0dp"
        android:layout_weight="1"
        android:text="Product 1"
        android:gravity="start"
        android:textSize="20sp"
        android:textColor="@color/black"
        android:layout_height="wrap_content">

    </androidx.appcompat.widget.AppCompatTextView>
    <androidx.appcompat.widget.AppCompatTextView
        android:layout_width="0dp"
        android:layout_weight="1"
        android:text="100"
        android:gravity="end"
        android:textSize="20sp"
        android:textColor="@color/black"
        android:layout_height="wrap_content">

    </androidx.appcompat.widget.AppCompatTextView>
    <androidx.appcompat.widget.AppCompatTextView
        android:layout_width="0dp"
        android:layout_weight="1"
        android:text="5"
        android:gravity="end"
        android:textSize="20sp"
        android:textColor="@color/black"
        android:layout_height="wrap_content">

    </androidx.appcompat.widget.AppCompatTextView>
    <androidx.appcompat.widget.AppCompatTextView
        android:layout_width="0dp"
        android:layout_weight="1"
        android:text="500"
        android:gravity="end"
        android:textSize="20sp"
        android:textColor="@color/black"
        android:layout_height="wrap_content">

    </androidx.appcompat.widget.AppCompatTextView>

</LinearLayout> <LinearLayout
    android:layout_width="match_parent"
    android:weightSum="4"
    android:layout_marginTop="@dimen/dp20"
    android:layout_marginHorizontal="@dimen/dp20"
    android:layout_height="wrap_content">
    <androidx.appcompat.widget.AppCompatTextView
        android:layout_width="0dp"
        android:layout_weight="1"
        android:text="Product 1"
        android:gravity="start"
        android:textSize="20sp"
        android:textColor="@color/black"
        android:layout_height="wrap_content">

    </androidx.appcompat.widget.AppCompatTextView>
    <androidx.appcompat.widget.AppCompatTextView
        android:layout_width="0dp"
        android:layout_weight="1"
        android:text="100"
        android:gravity="end"
        android:textSize="20sp"
        android:textColor="@color/black"
        android:layout_height="wrap_content">

    </androidx.appcompat.widget.AppCompatTextView>
    <androidx.appcompat.widget.AppCompatTextView
        android:layout_width="0dp"
        android:layout_weight="1"
        android:text="5"
        android:gravity="end"
        android:textSize="20sp"
        android:textColor="@color/black"
        android:layout_height="wrap_content">

    </androidx.appcompat.widget.AppCompatTextView>
    <androidx.appcompat.widget.AppCompatTextView
        android:layout_width="0dp"
        android:layout_weight="1"
        android:text="500"
        android:gravity="end"
        android:textSize="20sp"
        android:textColor="@color/black"
        android:layout_height="wrap_content">

    </androidx.appcompat.widget.AppCompatTextView>

</LinearLayout> <LinearLayout
    android:layout_width="match_parent"
    android:weightSum="4"
    android:layout_marginTop="@dimen/dp20"
    android:layout_marginHorizontal="@dimen/dp20"
    android:layout_height="wrap_content">
    <androidx.appcompat.widget.AppCompatTextView
        android:layout_width="0dp"
        android:layout_weight="1"
        android:text="Product 1"
        android:gravity="start"
        android:textSize="20sp"
        android:textColor="@color/black"
        android:layout_height="wrap_content">

    </androidx.appcompat.widget.AppCompatTextView>
    <androidx.appcompat.widget.AppCompatTextView
        android:layout_width="0dp"
        android:layout_weight="1"
        android:text="100"
        android:gravity="end"
        android:textSize="20sp"
        android:textColor="@color/black"
        android:layout_height="wrap_content">

    </androidx.appcompat.widget.AppCompatTextView>
    <androidx.appcompat.widget.AppCompatTextView
        android:layout_width="0dp"
        android:layout_weight="1"
        android:text="5"
        android:gravity="end"
        android:textSize="20sp"
        android:textColor="@color/black"
        android:layout_height="wrap_content">

    </androidx.appcompat.widget.AppCompatTextView>
    <androidx.appcompat.widget.AppCompatTextView
        android:layout_width="0dp"
        android:layout_weight="1"
        android:text="500"
        android:gravity="end"
        android:textSize="20sp"
        android:textColor="@color/black"
        android:layout_height="wrap_content">

    </androidx.appcompat.widget.AppCompatTextView>

</LinearLayout> <LinearLayout
    android:layout_width="match_parent"
    android:weightSum="4"
    android:layout_marginTop="@dimen/dp20"
    android:layout_marginHorizontal="@dimen/dp20"
    android:layout_height="wrap_content">
    <androidx.appcompat.widget.AppCompatTextView
        android:layout_width="0dp"
        android:layout_weight="1"
        android:text="Product 1"
        android:gravity="start"
        android:textSize="20sp"
        android:textColor="@color/black"
        android:layout_height="wrap_content">

    </androidx.appcompat.widget.AppCompatTextView>
    <androidx.appcompat.widget.AppCompatTextView
        android:layout_width="0dp"
        android:layout_weight="1"
        android:text="100"
        android:gravity="end"
        android:textSize="20sp"
        android:textColor="@color/black"
        android:layout_height="wrap_content">

    </androidx.appcompat.widget.AppCompatTextView>
    <androidx.appcompat.widget.AppCompatTextView
        android:layout_width="0dp"
        android:layout_weight="1"
        android:text="5"
        android:gravity="end"
        android:textSize="20sp"
        android:textColor="@color/black"
        android:layout_height="wrap_content">

    </androidx.appcompat.widget.AppCompatTextView>
    <androidx.appcompat.widget.AppCompatTextView
        android:layout_width="0dp"
        android:layout_weight="1"
        android:text="500"
        android:gravity="end"
        android:textSize="20sp"
        android:textColor="@color/black"
        android:layout_height="wrap_content">

    </androidx.appcompat.widget.AppCompatTextView>

</LinearLayout> <LinearLayout
    android:layout_width="match_parent"
    android:weightSum="4"
    android:layout_marginTop="@dimen/dp20"
    android:layout_marginHorizontal="@dimen/dp20"
    android:layout_height="wrap_content">
    <androidx.appcompat.widget.AppCompatTextView
        android:layout_width="0dp"
        android:layout_weight="1"
        android:text="Product 1"
        android:gravity="start"
        android:textSize="20sp"
        android:textColor="@color/black"
        android:layout_height="wrap_content">

    </androidx.appcompat.widget.AppCompatTextView>
    <androidx.appcompat.widget.AppCompatTextView
        android:layout_width="0dp"
        android:layout_weight="1"
        android:text="100"
        android:gravity="end"
        android:textSize="20sp"
        android:textColor="@color/black"
        android:layout_height="wrap_content">

    </androidx.appcompat.widget.AppCompatTextView>
    <androidx.appcompat.widget.AppCompatTextView
        android:layout_width="0dp"
        android:layout_weight="1"
        android:text="5"
        android:gravity="end"
        android:textSize="20sp"
        android:textColor="@color/black"
        android:layout_height="wrap_content">

    </androidx.appcompat.widget.AppCompatTextView>
    <androidx.appcompat.widget.AppCompatTextView
        android:layout_width="0dp"
        android:layout_weight="1"
        android:text="500"
        android:gravity="end"
        android:textSize="20sp"
        android:textColor="@color/black"
        android:layout_height="wrap_content">

    </androidx.appcompat.widget.AppCompatTextView>

</LinearLayout>
    <LinearLayout
        android:layout_width="match_parent"
        android:weightSum="4"
        android:layout_marginTop="@dimen/dp20"
        android:layout_marginHorizontal="@dimen/dp20"
        android:layout_height="wrap_content">
        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="0dp"
            android:layout_weight="1"
            android:text="Product 1"
            android:gravity="start"
            android:textSize="20sp"
            android:textColor="@color/black"
            android:layout_height="wrap_content">

        </androidx.appcompat.widget.AppCompatTextView>
        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="0dp"
            android:layout_weight="1"
            android:text="100"
            android:gravity="end"
            android:textSize="20sp"
            android:textColor="@color/black"
            android:layout_height="wrap_content">

        </androidx.appcompat.widget.AppCompatTextView>
        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="0dp"
            android:layout_weight="1"
            android:text="5"
            android:gravity="end"
            android:textSize="20sp"
            android:textColor="@color/black"
            android:layout_height="wrap_content">

        </androidx.appcompat.widget.AppCompatTextView>
        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="0dp"
            android:layout_weight="1"
            android:text="500"
            android:gravity="end"
            android:textSize="20sp"
            android:textColor="@color/black"
            android:layout_height="wrap_content">

        </androidx.appcompat.widget.AppCompatTextView>

    </LinearLayout>
</LinearLayout>