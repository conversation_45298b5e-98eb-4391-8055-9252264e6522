<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/content"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/transparent"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/rounded_white_bg"
        android:gravity="center"
        android:orientation="vertical"
        android:padding="@dimen/activity_vertical_margin">

        <ImageView
            android:layout_width="@dimen/dp100"
            android:layout_height="@dimen/dp100"
            android:src="@drawable/profile_ic">

        </ImageView>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp20"
            android:gravity="center"
            android:orientation="vertical"
            android:paddingVertical="@dimen/activity_left_margin">

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/user_name_txt"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Administrator"
                android:textColor="@color/black"
                android:textSize="20sp"
                android:textStyle="bold" />

            <androidx.appcompat.widget.AppCompatTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp20"
                android:text="@string/txt_add_are_you_sure_txt"
                android:textColor="@color/black"
                android:textSize="16sp"
                android:visibility="gone" />
        </LinearLayout>


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/dp20"
            android:gravity="center"
            android:orientation="horizontal"
            android:weightSum="10">

            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/btn_close"
                android:layout_width="200dp"
                android:layout_height="@dimen/dp50"
                android:backgroundTint="#c1c1c1"
                android:text="@string/label_cancel"
                android:textStyle="bold" />

            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/btn_logout"
                android:layout_width="200dp"
                android:layout_height="@dimen/dp50"
                android:layout_marginStart="@dimen/dp10"
                android:backgroundTint="#f15e5e"
                android:text="@string/lbl_sign_out"
                android:textStyle="bold" />
        </LinearLayout>

    </LinearLayout>
</LinearLayout>