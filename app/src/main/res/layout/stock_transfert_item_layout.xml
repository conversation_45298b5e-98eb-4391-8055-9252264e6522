<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:background="@drawable/cell_shape_ntop"
        android:gravity="center"
        android:orientation="horizontal"
        android:paddingHorizontal="10px"
        android:paddingVertical="30px"
        android:weightSum="8">

        <TextView
            android:id="@+id/id_date"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="@color/black"
            android:layout_weight="1"/>

        <TextView
            android:id="@+id/facture_no"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textColor="@color/black"
            />

        <TextView
            android:id="@+id/location_from"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textColor="@color/black"
            />

        <TextView
            android:id="@+id/location_to"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textColor="@color/black"
            />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1">

            <TextView
                android:id="@+id/payement_status"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingHorizontal="5dp"
                android:text="SKU 001"
                android:textColor="@color/white"
                android:textStyle="bold" />
        </LinearLayout>


        <TextView
            android:id="@+id/montant_total"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textColor="@color/black"
            />

        <TextView
            android:id="@+id/note"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textColor="@color/black"
            />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center">

            <Spinner
                android:id="@+id/spinner_action"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:entries="@array/array_stock_transfert" />

        </LinearLayout>

    </LinearLayout>

</LinearLayout>