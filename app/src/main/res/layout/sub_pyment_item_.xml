<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@drawable/cell_shape_ntop"
    android:padding="@dimen/padding_horizontal">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingVertical="@dimen/dp10">

        <TextView
            android:id="@+id/date"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textColor="@color/black"
            android:text="@string/image_product_name" />

        <TextView
            android:id="@+id/reference_no"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textColor="@color/black"
            android:text="@string/label_quantity" />

        <TextView
            android:id="@+id/account"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textColor="@color/black"
            android:text="@string/label_unit_price" />

        <TextView
            android:id="@+id/payement_method"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="@color/black"
            android:layout_weight="1"
            android:text="@string/labele_product_total" />

        <TextView
            android:id="@+id/payment_note"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textColor="@color/black"
            android:text="@string/labele_product_total" />


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_gravity="center"
            android:gravity="center"
            android:text="action">


            <ImageView
                android:visibility="gone"
                android:id="@+id/delete_payment"
                android:layout_width="24dp"
                android:layout_height="wrap_content"
                app:tint="@color/black"
                android:layout_weight="1"
                android:src="@drawable/ic_baseline_delete_forever_24" />

            <ImageView
                android:id="@+id/edit_payment"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:tint="@color/black"
                android:layout_marginRight="@dimen/dp5"
                android:src="@drawable/ic_baseline_black_edit_24" />


            <ImageView
                android:id="@+id/view_item"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:tint="@color/black"
                android:layout_marginLeft="@dimen/dp5"
                android:src="@drawable/ic_baseline_remove_red_eye_24" />


        </LinearLayout>
    </LinearLayout>
</LinearLayout>