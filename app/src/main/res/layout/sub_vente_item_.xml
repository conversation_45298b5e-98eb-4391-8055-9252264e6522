<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/cell_shape_ntop"
    android:padding="@dimen/padding_horizontal">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/product_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textColor="@color/black"
            android:text="@string/image_product_name" />

        <TextView
            android:id="@+id/quantity"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textColor="@color/black"
            android:text="@string/label_quantity" />

        <TextView
            android:id="@+id/unit_price"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textColor="@color/black"
            android:text="@string/label_unit_price" />

        <TextView
            android:id="@+id/selling_price"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textColor="@color/black"

            android:visibility="gone" />

        <TextView
            android:id="@+id/total"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="@color/black"
            android:layout_weight="1" />
    </LinearLayout>
</LinearLayout>