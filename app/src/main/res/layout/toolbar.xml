<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_alignParentTop="true"
    android:minHeight="?android:attr/actionBarSize"
    android:orientation="vertical"
    android:popupTheme="@style/ThemeOverlay.AppCompat.Light"
    android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">
    <!--Themetop-->

    <androidx.appcompat.widget.Toolbar
        android:visibility="visible"
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentTop="true"
        android:background="@color/colorPrimary"
        android:theme="@style/ToolBarStyle"
        android:minHeight="?android:attr/actionBarSize"
        android:popupTheme="@style/ThemeOverlay.AppCompat.Light"/>

    <LinearLayout
        android:visibility="gone"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <ImageView
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:layout_gravity="center"
            android:layout_marginLeft="8dp"
            android:src="@drawable/ic_navigate_before"
            android:tint="@color/black"/>

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_weight="1"
            android:gravity="center"
            android:text="New Ticket"
            android:fontFamily="@font/montserrat_regular"
            android:textStyle="bold"
            android:textColor="@color/black"
            android:textSize="18dp" />

        <ImageView
            android:visibility="invisible"
            android:layout_width="25dp"
            android:layout_height="25dp"
            android:layout_gravity="center"
            android:layout_marginRight="8dp"
            android:src="@drawable/question"
           />
    </LinearLayout>

</LinearLayout>

    <!--

    <androidx.appcompat.widget.Toolbar xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/toolbar"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_alignParentTop="true"
    android:background="@color/white"
    android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar"
    android:minHeight="?android:attr/actionBarSize"
    android:popupTheme="@style/ThemeOverlay.AppCompat.Light">-->
