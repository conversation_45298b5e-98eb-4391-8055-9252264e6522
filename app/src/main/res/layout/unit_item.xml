<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:id="@+id/linearLayout"
    android:paddingHorizontal="@dimen/dp20"
    android:layout_height="wrap_content">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:gravity="center"
        android:weightSum="4"
        android:paddingVertical="@dimen/dp20"
        android:paddingHorizontal="@dimen/dp5"
        android:orientation="horizontal">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/id_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1.1"
            android:textColor="@color/black"
            android:text="chkolate " />

         <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/short_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1.1"
            android:textColor="@color/black"
            android:text="Categorie 1" />

         <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/id_decimale"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1.1"
            android:textColor="@color/black"
            android:text="13.00 $" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="0.7"
            android:gravity="start">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/btn_delete"
                android:layout_width="@dimen/dp50"
                android:layout_height="50dp"
                android:layout_gravity="start"
                android:src="@drawable/ic_delete_svg"
                android:gravity="center"
                android:padding="@dimen/dp10"
                />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/btn_edit"
                android:layout_width="@dimen/dp50"
                android:layout_height="@dimen/dp50"
                android:layout_marginStart="@dimen/dp10"
                android:tint="@color/skyBlue"
                android:src="@drawable/ic_edit_filled"
                android:gravity="start"
                android:padding="@dimen/dp10"
                />
        </LinearLayout>


    </LinearLayout>

</LinearLayout>