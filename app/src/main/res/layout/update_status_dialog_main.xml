<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/content"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/transparent"
    android:orientation="vertical"

    android:padding="20dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/rounded_white_bg"
        android:orientation="vertical"
        android:padding="@dimen/activity_vertical_margin">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingVertical="@dimen/activity_left_margin"
            android:weightSum="10">

            <androidx.appcompat.widget.AppCompatTextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="9.5"
                android:text="@string/label_update_status"
                android:textColor="@color/black"
                android:textSize="16sp"
                android:textStyle="bold" />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/btn_close"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="0.5"
                android:src="@drawable/ic_close_svg"
                app:tint="@color/blue">

            </androidx.appcompat.widget.AppCompatImageView>
        </LinearLayout>

        <View
            android:layout_width="wrap_content"
            android:layout_height="1dp"
            android:background="@color/blue">

        </View>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingVertical="@dimen/activity_left_margin">

            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/dp10"
                android:layout_marginTop="@dimen/dp20"
                android:layout_weight="1">

                <androidx.appcompat.widget.AppCompatSpinner
                    android:id="@+id/spinner_purchase_status"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/default_spinner_height"
                    android:layout_marginTop="5dp"
                    android:entries="@array/array_purchase_status"
                    android:textSize="12sp"
                    android:textStyle="normal" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/dp_10"
                    android:background="@color/white"
                    android:paddingLeft="4dp"
                    android:paddingRight="4dp"
                    android:text="@string/string_purchase_status"
                    android:textSize="12sp"
                    android:visibility="visible" />
            </FrameLayout>

        </LinearLayout>


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="horizontal">


            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/btn_save"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp40"
                android:background="@drawable/bg_gradient"
                android:layout_marginHorizontal="@dimen/dp10"
                android:layout_marginTop="@dimen/dp5"
                android:text="@string/label_updatee" />

        </LinearLayout>

    </LinearLayout>
</LinearLayout>