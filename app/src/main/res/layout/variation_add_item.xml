<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:weightSum="2"
    android:layout_marginVertical="@dimen/dp2"
    android:orientation="horizontal"
    android:layout_height="wrap_content">
        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="0dp"
            android:layout_weight="1.9"
            style="@style/Widget.MaterialComponents.TextInputLayout.FilledBox"
            android:layout_marginTop="@dimen/dp10"
            android:layout_height="wrap_content">
        <androidx.appcompat.widget.AppCompatEditText
            android:id="@+id/id_name"
            android:layout_width="match_parent"
            android:hint="@string/label_add_value"
            android:layout_height="match_parent"
          />
        </com.google.android.material.textfield.TextInputLayout>

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/btn_mins"
            android:layout_width="30dp"
            android:layout_weight="0.1"
            android:layout_gravity="center"
            android:layout_marginTop="5dp"
            android:layout_marginStart="10dp"
            android:layout_height="@dimen/dp40"
            android:src="@drawable/ic_minus_box" />


</LinearLayout>