<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/content"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/transparent"
    android:orientation="vertical"

    android:padding="20dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/rounded_white_bg"
        android:orientation="vertical"
        android:padding="@dimen/activity_vertical_margin">

        <LinearLayout
            android:layout_width="match_parent"
            android:weightSum="10"
            android:layout_height="wrap_content"
            android:paddingVertical="@dimen/activity_left_margin">

            <androidx.appcompat.widget.AppCompatTextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:text="@string/text_add_variation"
                android:layout_weight="9.5"
                android:textSize="16sp"
                android:textColor="@color/black"
                android:textStyle="bold" />
            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/btn_close"
                android:layout_width="0dp"
                android:layout_weight="0.5"
                app:tint="@color/blue"
                android:src="@drawable/ic_close_svg"
                android:layout_height="match_parent">

            </androidx.appcompat.widget.AppCompatImageView>
        </LinearLayout>
        <View
            android:layout_width="wrap_content"
            android:background="@color/blue"
            android:layout_height="1dp">

        </View>

        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_marginTop="@dimen/dp20"
            android:layout_height="wrap_content">
            <androidx.appcompat.widget.AppCompatEditText
                android:id="@+id/btn_variation_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:hint="@string/image_variatiooon_name" />

        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_marginTop="@dimen/dp10"
            android:layout_height="wrap_content">
            <androidx.appcompat.widget.AppCompatEditText
                android:id="@+id/add_btn"
                android:layout_width="match_parent"
                android:drawableRight="@drawable/ic_plus_btn"
                android:layout_height="wrap_content"
                android:focusable="false"
                android:layout_weight="1"
                android:text="@string/label_variation_values" />

        </com.google.android.material.textfield.TextInputLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:paddingHorizontal="@dimen/nav_header_vertical_spacing">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recycle_add_value"
                android:layout_width="match_parent"
                android:layout_marginTop="@dimen/dp10"
                tools:listitem="@layout/variation_add_item"
                android:layout_height="wrap_content"
                android:layout_weight="1" />


        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="horizontal">
            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/btn_save"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp40"
                android:background="@drawable/bg_gradient"
                android:layout_marginHorizontal="@dimen/dp10"
                android:layout_marginTop="@dimen/dp5"
                android:text="@string/label_save" />

        </LinearLayout>

    </LinearLayout>
</LinearLayout>