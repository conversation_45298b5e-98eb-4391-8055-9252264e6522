<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/linearLayout"
    android:layout_width="match_parent"
    android:paddingHorizontal="@dimen/dp20"
    android:layout_height="wrap_content">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:gravity="center"
        android:orientation="horizontal"
        android:paddingVertical="@dimen/dp20"
        android:paddingHorizontal="@dimen/dp5"
        android:weightSum="4">

        <TextView
            android:id="@+id/id_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1.5"
            android:gravity="start"
            android:text="chkolate "
            android:textColor="@color/black" />

        <TextView
            android:id="@+id/values"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1.5"
            android:gravity="start"
            android:textColor="@color/black" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/btn_delete"
                android:layout_width="@dimen/dp50"
                android:layout_height="50dp"
                android:layout_gravity="center"
                android:src="@drawable/ic_delete_svg"
                android:gravity="center"
                android:padding="@dimen/dp10"
             />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/btn_edit"
                android:layout_width="@dimen/dp50"
                android:layout_height="@dimen/dp50"
                android:layout_marginStart="@dimen/dp10"
                android:tint="@color/skyBlue"
                android:src="@drawable/ic_edit_filled"
                android:gravity="start"
                android:padding="@dimen/dp10"
              />
        </LinearLayout>

    </LinearLayout>

</LinearLayout>