<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/content"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/transparent"
    android:orientation="vertical"

    android:padding="20dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/rounded_white_bg"

        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="@dimen/activity_vertical_margin">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"

                android:layout_weight="1"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingVertical="@dimen/activity_left_margin"
                    android:weightSum="10">

                    <androidx.appcompat.widget.AppCompatTextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="9.5"
                        android:text="@string/label_view_payments"
                        android:textColor="@color/black"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/btn_close"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="0.5"
                        android:src="@drawable/ic_close_svg"
                        app:tint="@color/blue">

                    </androidx.appcompat.widget.AppCompatImageView>
                </LinearLayout>

                <View
                    android:layout_width="wrap_content"
                    android:layout_height="1dp"
                    android:background="@color/blue">

                </View>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">


                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginHorizontal="@dimen/activity_left_margin"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:paddingTop="@dimen/margin_vertical">


                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/label_supplier"
                                android:textColor="@color/black" />

                            <TextView
                                android:id="@+id/supplier_name"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginLeft="5dp" />

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:paddingTop="@dimen/margin_vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/label_mobile"
                                android:textColor="@color/black" />

                            <TextView
                                android:id="@+id/supplier_phone"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginLeft="5dp" />

                        </LinearLayout>

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginHorizontal="@dimen/activity_left_margin"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:paddingTop="@dimen/margin_vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/label_business_location"
                                android:textColor="@color/black" />

                            <TextView
                                android:id="@+id/business_location"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginLeft="5dp" />

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:paddingTop="@dimen/margin_vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/label_mobile"
                                android:textColor="@color/black" />

                            <TextView
                                android:id="@+id/business_phone"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginLeft="5dp" />

                        </LinearLayout>
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginHorizontal="@dimen/activity_left_margin"
                        android:layout_weight="1"
                        android:orientation="vertical">


                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"

                            android:orientation="horizontal"
                            android:paddingTop="@dimen/margin_vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/label_reference_num"

                                android:textColor="@color/black" />

                            <TextView
                                android:id="@+id/label_reference_num"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginLeft="5dp"
                                android:textStyle="bold" />

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:paddingTop="@dimen/margin_vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/label_date"
                                android:textColor="@color/black" />

                            <TextView
                                android:id="@+id/purchase_date"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginLeft="5dp" />

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="horizontal"
                            android:paddingTop="@dimen/margin_vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/string_purchase_status_"
                                android:textColor="@color/black" />

                            <TextView
                                android:id="@+id/purchase_status"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginLeft="5dp" />

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="horizontal"
                            android:paddingTop="@dimen/margin_vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/label_payement_status"
                                android:textColor="@color/black" />

                            <TextView
                                android:id="@+id/payement_status"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginLeft="5dp" />

                        </LinearLayout>

                    </LinearLayout>

                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="2dp"
                    android:layout_marginTop="10dp"
                    android:background="@color/blue_dark" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@drawable/bg_gradient"
                    android:padding="@dimen/dp10"
                    android:weightSum="6">

                    <androidx.appcompat.widget.AppCompatTextView
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:gravity="center|start"
                        android:text="@string/label_date"
                        android:textColor="@color/white" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:gravity="center|start"
                        android:text="@string/label_date"
                        android:textColor="@color/white" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:gravity="center|start"
                        android:text="@string/label_amount_not"
                        android:textColor="@color/white" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:gravity="center|start"
                        android:text="@string/label_payment_method_not"
                        android:textColor="@color/white" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:gravity="center|start"
                        android:text="@string/string_payement_note"
                        android:textColor="@color/white" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:text="@string/label_btn_action"
                        android:textColor="@color/white" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingVertical="@dimen/padding_horizontal"
                    app:layout_constrainedHeight="true"
                    app:layout_constraintHeight_max="10dp">


                    <androidx.cardview.widget.CardView
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:clipChildren="false"
                        android:clipToPadding="false"
                        android:divider="@android:color/transparent"
                        android:dividerHeight="0.0px"
                        android:elevation="8dp"
                        android:visibility="visible"
                        app:cardElevation="2dp"
                        app:cardPreventCornerOverlap="false"
                        app:cardUseCompatPadding="true">

                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/recycle_sub_vente"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content" />

                </androidx.cardview.widget.CardView>


            </LinearLayout>


        </LinearLayout>


    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/cell_shape_border"
        android:padding="@dimen/activity_vertical_margin"
        android:visibility="gone">


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginHorizontal="@dimen/activity_left_margin"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/lbl_alert_exc_tax"
                android:textColor="@color/blue_txt" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center"
                android:orientation="horizontal"
                android:paddingTop="@dimen/margin_vertical">

                <ImageView
                    android:layout_width="30px"
                    android:layout_height="30px"
                    android:layout_weight="1"
                    android:src="@drawable/icons_contacts" />

                <TextView
                    android:id="@+id/id_exc_tax"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="5dp"
                    android:layout_weight="1"
                    android:inputType="numberDecimal" />

            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginHorizontal="@dimen/activity_left_margin"
            android:layout_weight="1"
            android:orientation="vertical"
            android:visibility="visible">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/lbl_alert_inc_tax"
                android:textColor="@color/blue_txt" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center"
                android:orientation="horizontal"
                android:paddingTop="@dimen/margin_vertical">

                <ImageView
                    android:layout_width="30px"
                    android:layout_height="30px"
                    android:layout_weight="1"
                    android:src="@drawable/icons_contacts" />

                <TextView
                    android:id="@+id/id_inc_tax"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="5dp"
                    android:layout_weight="1"

                    android:inputType="numberDecimal" />
            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginHorizontal="@dimen/activity_left_margin"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/label_product_margin"
                android:textColor="@color/blue_txt" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center"
                android:orientation="horizontal"
                android:paddingTop="@dimen/margin_vertical">

                <ImageView
                    android:layout_width="30px"
                    android:layout_height="30px"
                    android:layout_weight="1"
                    android:src="@drawable/icons_contacts" />

                <TextView
                    android:id="@+id/id_margin"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="5dp"
                    android:layout_weight="1"

                    android:inputType="numberDecimal"
                    android:text="25.00" />
            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginHorizontal="@dimen/activity_left_margin"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/string_default_price_vente_exc"
                android:textColor="@color/blue_txt" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="horizontal"
                android:paddingTop="@dimen/margin_vertical">

                <ImageView
                    android:layout_width="30px"
                    android:layout_height="30px"
                    android:src="@drawable/icons8_location" />

                <TextView
                    android:id="@+id/id_default_sel_price_exc_tax"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="5dp"
                    android:layout_weight="1"

                    android:inputType="numberDecimal" />
            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginHorizontal="@dimen/activity_left_margin"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/string_default_price_vente_inc"
                android:textColor="@color/blue_txt" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="horizontal"
                android:paddingTop="@dimen/margin_vertical">

                <ImageView
                    android:layout_width="30px"
                    android:layout_height="30px"
                    android:src="@drawable/icons8_location" />

                <TextView
                    android:id="@+id/default_sell_inc_tax"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="5dp"
                    android:layout_weight="1"

                    android:inputType="numberDecimal" />
            </LinearLayout>

        </LinearLayout>


    </LinearLayout>


</LinearLayout>


    </LinearLayout>