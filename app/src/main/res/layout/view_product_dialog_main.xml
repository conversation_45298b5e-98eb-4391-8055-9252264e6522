<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/content"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/transparent"
    android:orientation="vertical"
    android:padding="20dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/rounded_white_bg"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/dp10"
            android:paddingVertical="@dimen/activity_left_margin">

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/product_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/label_add_opening_stock"
                android:textColor="@color/blue25"
                android:textSize="16sp"
                android:textStyle="bold" />

        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="2dp"
            android:layout_marginStart="@dimen/dp10"
            android:background="@color/blue_dark" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="@dimen/activity_vertical_margin">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="@dimen/nav_header_vertical_spacing">

                    <androidx.appcompat.widget.AppCompatTextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/string_skuut"
                        android:textColor="@color/black" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/id_sku"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        tools:text="TextView" />


                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="@dimen/nav_header_vertical_spacing">

                    <androidx.appcompat.widget.AppCompatTextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/label_category"
                        android:textColor="@color/black" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/catgeroy_name"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        tools:text="TextView" />


                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="@dimen/nav_header_vertical_spacing">

                    <androidx.appcompat.widget.AppCompatTextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/label_unit"
                        android:textColor="@color/black" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/product_unit"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        tools:text="TextView" />


                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="@dimen/nav_header_vertical_spacing">


                    <androidx.appcompat.widget.AppCompatTextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/string_baarc_type"
                        android:textColor="@color/black" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/product_barcode_type"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        tools:text="TextView" />


                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="@dimen/nav_header_vertical_spacing">

                    <androidx.appcompat.widget.AppCompatTextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/string_bprod_type"
                        android:textColor="@color/black" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/product_type"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        tools:text="TextView" />


                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="@dimen/nav_header_vertical_spacing">

                    <androidx.appcompat.widget.AppCompatTextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/label_qty_remaining"
                        android:textColor="@color/black" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/qntity_remaining"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:hint="00.0"
                        android:inputType="number" />


                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="@dimen/nav_header_vertical_spacing">

                    <androidx.appcompat.widget.AppCompatTextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/label_unit_cost"
                        android:textColor="@color/black" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/unit_cost"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:hint="00.0"
                        android:inputType="numberDecimal" />


                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_weight="2"
                android:gravity="center">

                <ImageView
                    android:id="@+id/image_product"
                    android:layout_width="200dp"
                    android:layout_height="200dp"
                    android:src="@drawable/image_notavailable" />

            </LinearLayout>


        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginHorizontal="@dimen/dp10"
            android:background="@drawable/bg_gradient"
            android:padding="@dimen/dp10"
            android:weightSum="5">

            <androidx.appcompat.widget.AppCompatTextView
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center|start"
                android:text="@string/lbl_alert_exc_tax"
                android:textColor="@color/white" />

            <androidx.appcompat.widget.AppCompatTextView
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center|start"
                android:text="@string/lbl_alert_inc_tax"
                android:textColor="@color/white" />

            <androidx.appcompat.widget.AppCompatTextView
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center|start"
                android:text="@string/label_product_margin"
                android:textColor="@color/white" />

            <androidx.appcompat.widget.AppCompatTextView
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center|start"
                android:text="@string/string_default_price_vente_exc"
                android:textColor="@color/white" />

            <androidx.appcompat.widget.AppCompatTextView
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center|start"
                android:text="@string/string_default_price_vente_inc"
                android:textColor="@color/white" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/activity_left_margin"
            android:layout_weight="5"
            android:background="@drawable/border_gray_noradius"
            android:orientation="horizontal"
            android:padding="@dimen/dp10">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:orientation="horizontal"
                android:paddingTop="@dimen/margin_vertical"
                tools:ignore="NestedWeights">

                <ImageView
                    android:layout_width="@dimen/dp40"
                    android:layout_height="@dimen/dp40"
                    android:layout_weight="1"
                    android:src="@drawable/ic_taxes" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/id_exc_tax"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="5dp"
                    android:layout_weight="1"
                    android:inputType="numberDecimal" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:orientation="horizontal"
                android:paddingTop="@dimen/margin_vertical">

                <ImageView
                    android:layout_width="@dimen/dp40"
                    android:layout_height="@dimen/dp40"
                    android:layout_weight="1"
                    android:src="@drawable/ic_taxes" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/id_inc_tax"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="5dp"
                    android:layout_weight="1"

                    android:inputType="numberDecimal" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:orientation="horizontal"
                android:paddingTop="@dimen/margin_vertical">

                <ImageView
                    android:layout_width="@dimen/dp40"
                    android:layout_height="@dimen/dp40"
                    android:layout_weight="1"
                    android:src="@drawable/ic_margin" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/id_margin"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="5dp"
                    android:layout_weight="1"

                    android:inputType="numberDecimal"
                    android:text="25.00" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:orientation="horizontal"
                android:paddingTop="@dimen/margin_vertical">

                <ImageView
                    android:layout_width="@dimen/dp40"
                    android:layout_height="@dimen/dp40"
                    android:layout_weight="1"
                    android:src="@drawable/ic_tag" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/id_default_sel_price_exc_tax"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="5dp"
                    android:layout_weight="1"

                    android:inputType="numberDecimal" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:orientation="horizontal"
                android:paddingTop="@dimen/margin_vertical">

                <ImageView
                    android:layout_width="@dimen/dp40"
                    android:layout_height="@dimen/dp40"
                    android:layout_weight="1"
                    android:src="@drawable/ic_tag" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/default_sell_inc_tax"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="5dp"
                    android:layout_weight="1"

                    android:inputType="numberDecimal" />
            </LinearLayout>
        </LinearLayout>


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="horizontal">

            <Button
                android:id="@+id/btn_save"
                style="@style/my_btn_style"
                android:layout_width="wrap_content"
                android:layout_height="70px"
                android:text="@string/label_save"
                android:visibility="gone" />

<!--            <Button-->
<!--                android:id="@+id/btn_close"-->
<!--                style="@style/my_btn_style"-->
<!--                android:layout_width="wrap_content"-->
<!--                android:layout_height="70px"-->
<!--                android:background="@drawable/rounded_btn_bg_orange"-->
<!--                android:text="@string/label_close" />-->
            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/btn_close"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/dp40"
                android:background="@drawable/bg_gradient"
                android:layout_marginVertical="@dimen/dp3"
                android:text="@string/label_close" />

        </LinearLayout>
    </LinearLayout>


</LinearLayout>