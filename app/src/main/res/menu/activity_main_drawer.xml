<?xml version="1.0" encoding="utf-8"?>
<menu xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:showIn="navigation_view">

    <group android:checkableBehavior="single">

        <item
            android:id="@+id/nav_home"
            android:icon="@drawable/products_smart_ic"
            android:title="@string/menu_home" />

        <item
            android:id="@+id/nav_list_product"
            android:icon="@drawable/product_ic_icon"
            android:title="@string/menu_produit" />

        <item
            android:id="@+id/nav_list_client"
            android:icon="@drawable/smart_contact_ic"
            android:title="@string/label_contact" />
        <item
            android:id="@+id/nav_list_provider"
            android:icon="@drawable/provider_ic"
            android:title="@string/label_cprovider" />

        <item
            android:id="@+id/nav_list_category"
            android:icon="@drawable/category_ic"
            android:title="@string/label_category" />

        <item
            android:id="@+id/nav_list_brand"
            android:icon="@drawable/brand_ic"
            android:title="@string/label_brand" />

        <item
            android:id="@+id/nav_list_unit"
            android:icon="@drawable/unit_ic"
            android:title="@string/label_unit_label" />

        <item
            android:id="@+id/nav_list_return_sell"
            android:icon="@drawable/sell_back_ic"
            android:title="@string/label_unit_list_retourn" />

        <item
            android:id="@+id/label_sell"
            android:icon="@drawable/sales_smart_ic"
            android:title="@string/label_sell" />

        <item
            android:id="@+id/nav_list_expense"
            android:icon="@drawable/stock_expenses_ic"
            android:title="@string/label_stock_expenses" />

        <item
            android:id="@+id/nav_stock_report"
            android:icon="@drawable/stock_reports_ic"
            android:title="@string/label_stock_reports" />

        <item
            android:id="@+id/nav_stock_business_report"
            android:icon="@drawable/ic_stock_business_report"
            android:title="@string/label_stock_report" />

        <item
            android:id="@+id/nav_sync"
            android:icon="@drawable/synch_ic"
            android:title="@string/label_sync" />

    </group>
</menu>