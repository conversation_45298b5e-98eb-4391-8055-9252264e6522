<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Theme.BigUltimateNavDraw" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        <!-- Primary brand color. -->

        <item name="colorPrimary">@color/blue25</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
        <item name="android:windowBackground">@drawable/background_principale</item>

        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/black</item>
        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/blue25</item>
        <item name="colorSecondaryVariant">@color/colorAccent</item>
        <item name="colorOnSecondary">@color/black</item>
        <!-- Status bar color. -->
        <item name="android:statusBarColor" tools:targetApi="l">?attr/colorPrimaryVariant</item>
        <item name="fontFamily">@font/montserrat_regular</item>
        <!-- Customize your theme here. -->
    </style>


    <style name="AppTheme.PopupOverlay" parent="ThemeOverlay.AppCompat.Light">
        <item name="android:paddingTop">20dp</item>
        <item name="android:layout_marginTop">20dp</item>
    </style>

</resources>