<resources>
    <!-- Default screen margins, per the Android Design guidelines. -->
    <dimen name="activity_horizontal_margin">16dp</dimen>
    <dimen name="activity_vertical_margin">16dp</dimen>
    <dimen name="nav_header_vertical_spacing">8dp</dimen>
    <dimen name="nav_header_height">176dp</dimen>
    <dimen name="fab_margin">16dp</dimen>


    <dimen name="activity_left_margin">10dp</dimen>
    <dimen name="activity_padding_horizontal">4dp</dimen>
    <dimen name="padding_top">10dp</dimen>
    <dimen name="margin_vertical">3dp</dimen>
    <dimen name="margin_edit_text">3dp</dimen>
    <dimen name="padding_horizontal">5dp</dimen>
    <dimen name="padding_normal">20dp</dimen>

    <dimen name="fab_width">60dp</dimen>
    <dimen name="fab_height">60dp</dimen>
    <dimen name="booking_ui_component_height">56dp</dimen>
    <dimen name="dp_10">10dp</dimen>
    <dimen name="dp_15">10dp</dimen>
    <dimen name="default_btn_height">53dp</dimen>
    <dimen name="default_edit_text_height">50dp</dimen>
    <dimen name="default_spinner_height">58dp</dimen>
    <dimen name="default_input_field_view_height">53dp</dimen>

    <dimen name="default_elevation">6dp</dimen>
    <dimen name="default_corner_radius">10dp</dimen>
    <dimen name="width_barcode">300dp</dimen>
    <dimen name="height_barcode">65dp</dimen>

    <!-- Live Activity -->
    <!-- Default screen margins, per the Android Design guidelines. -->

    <dimen name="publisher_height">120dp</dimen>
    <dimen name="publisher_width">90dp</dimen>
    <dimen name="publisher_end_margin">16dp</dimen>
    <dimen name="publisher_right_margin">16dp</dimen>
    <dimen name="publisher_bottom_margin">16dp</dimen>
    <dimen name="publisher_padding">2dp</dimen>

    <dimen name="dp0">0dp</dimen>
    <dimen name="dp1">1dp</dimen>
    <dimen name="dp2">2dp</dimen>
    <dimen name="dp3">3dp</dimen>
    <dimen name="dp4">4dp</dimen>

    <dimen name="dp5">5dp</dimen>
    <dimen name="dp10">10dp</dimen>
    <dimen name="dp12">12dp</dimen>
    <dimen name="dp15">15dp</dimen>
    <dimen name="dp20">20dp</dimen>
    <dimen name="dp30">30dp</dimen>
    <dimen name="dp35">35dp</dimen>
    <dimen name="dp40">40dp</dimen>
    <dimen name="dp50">50dp</dimen>
    <dimen name="dp55">55dp</dimen>
    <dimen name="dp60">60dp</dimen>
    <dimen name="dp62">62dp</dimen>
    <dimen name="dp80">80dp</dimen>
    <dimen name="dp90">90dp</dimen>
    <dimen name="dp100">100dp</dimen>

    <!--SIR-->
    <dimen name="extra_small">12sp</dimen>
    <dimen name="small">14sp</dimen>
    <dimen name="normal">16sp</dimen>
    <dimen name="big">18sp</dimen>
    <dimen name="heading">26sp</dimen>
    <dimen name="bigHeading">30sp</dimen>

    <!-- Segment Buttons -->
    <dimen name="default_segment_text_size">16sp</dimen>
    <dimen name="default_segment_height">48dp</dimen>
    <dimen name="default_border_width">1dp</dimen>

</resources>