<resources>
    <string name="app_name">Fbs Mart</string>

    <string name="navigation_drawer_open">Open navigation drawer</string>
    <string name="navigation_drawer_close">Close navigation drawer</string>
    <string name="nav_header_title">Android Studio</string>
    <string name="nav_header_subtitle"><EMAIL></string>
    <string name="nav_header_desc">Navigation header</string>
    <string name="action_settings">Settings</string>
    <string name="label_current_stock">Current Stock</string>
    <string name="lbl_currency">Currency:</string>
    <string name="menu_home">Home</string>
    <string name="menu_gallery">Gallery</string>
    <string name="menu_slideshow">Slideshow</string>
    <string name="lbl_list_product">Liste des produits</string>
    <string name="lbl_add_product">Ajouter un produit</string>
    <string name="lbl_add_client">Ajouter client</string>
    <string name="lbl_list_client">List des client</string>
    <string name="lbl_prix_vente_total">(Total sale price - Total purchase price)</string>
    <string name="lbl_prix_vente_tnett">Gross Profit + (Total Sales Shipping Charges + Total Inventory Recovered + Total Purchase Discount + Total Sales Rounding)
- (Total Adjustment Stock + Total Expense + Total Purchase Shipping Cost + Total Transfer Shipping Cost + Total Sales Discount + Total Customer Reward)</string>
    <string name="lbl_benifice_brut">Gross profit:</string>
    <string name="lbl_cloture_stock">Stock de clôture (Par prix d\'achat)</string>
    <string name="lbl_cloture_stock_par_vente">Stock de clôture (Par prix de vente)</string>
    <string name="lbl_benifice_net">Net profit: </string>
    <string name="lbl_benifice_potentiel">Bénéfice potentiel</string>
    <string name="lbl_stock_opening">Opening Stock  \n (By purchase price):</string>
    <string name="lbl_stock_opening_vente">Closing stock  \n (By purchase price):</string>
    <string name="label_stock_cloture">Closing stock \n (By purchase price):</string>
    <string name="label_stock_venteee">Closing stock \n (Closing stock):</string>
    <string name="label_total_achat">Total des achats:</string>
    <string name="label_total_achat_expedition">Total sales shipping costs:</string>
    <string name="label_total_ventes">Total sales: \n (Exc. tax, discount)</string>
    <string name="label_total_adjustment">Total adjustment stock</string>
    <string name="label_tota_depnese">Total des dépenses:</string>
    <string name="label_tota_recuperere">Total stock recovered:</string>
    <string name="label_totaremse_chat">Total purchase discount:</string>
    <string name="label_retour_achat">Total return of purchase:</string>
    <string name="label_tota_remise_total">Remise totale des ventes:</string>
    <string name="label_total_sell">Total Sell Return:</string>
    <string name="label_card">CARD</string>
    <string name="lbl_filtre">Filtres</string>
    <string name="lbl_all_expenses">All expenses</string>
    <string name="lbl_num_ref">Reference number</string>

    <string name="label_draft">DRAFT</string>
    <string name="label_land_mark">Land Mark</string>
    <string name="label_cutomer">Customer</string>
    <string name="label_cutomer_req">Customer:*</string>
    <string name="label_suspend">SUSPEND</string>


    <string name="label_multi_pay">MULTI PAY \n %1$s %2$s</string>
    <string name="label_pay_cash">Pay \n %1$s %2$s</string>

<!--    <string name="label_multi_pay">MULTI PAY \n %1$s Dh</string>-->
<!--    <string name="label_pay_cash">CASH \n %1$s Dh</string>-->
<!--    <string name="label_total_items">Total Items: \n %1$s</string>-->
<!--    <string name="label_total_payable_items">Total Payable: \n %1$s Dh</string>-->
<!--    <string name="label_total_paying">Total Paying: \n %1$s Dh</string>-->
<!--    <string name="label_return_change">Change Return: \n %1$s Dh</string>-->




    <string name="label_search">Search </string>
    <string name="label_enter_product_name">Enter product name </string>
    <string name="label_enter_phome_number_name">Enter phone number </string>
    <string name="label_txt_product_name">Product name </string>
    <string name="label_our_client">Customers  </string>
    <string name="label_oadd_disc">Add Discount  </string>
    <string name="label_update_disc">Update Discount  </string>
    <string name="label_our_provider">Suppliers  </string>
    <string name="label_our_suppliers">Supplier :</string>
    <string name="label_our_suppliers_required">Supplier:*</string>
    <string name="label_supplier">Supplier</string>
    <string name="label_add_type">Adjustement Type</string>
    <string name="label_expense_category">Expense Category:</string>
    <string name="label_our_provider_label">Fournisseurs </string>
    <string name="label_paiment_status_label">Payment\nStatus</string>
    <string name="label_our_provider_gerer">Gérer vos fournisseurs </string>
    <string name="label_ends_At">Ends at </string>
    <string name="label_ends_At_req">Ends at:* </string>
    <string name="label_product_list">List Products </string>
    <string name="label_choose_product">Choose Product</string>
    <string name="label_choose_seldect_discount_type">Select Discount Type</string>
    <string name="label_discount">Discount(-)</string>
    <string name="label_order_tax">Order Tax(+)</string>
    <string name="label_shipping">Shipping(+)</string>
    <string name="label_total_payeable">Total payable</string>
    <string name="label_pas_avendre">Pas à vendre</string>
    <string name="label_tout_product">All Products</string>
    <string name="label_tout_contact">All contacts</string>
    <string name="label_tout_ventes">All sales</string>
    <string name="label_sales_title">Sales</string>
    <string name="label_list_quotation">List quotations </string>
    <string name="label_add_edit_delete_discount">Add/Edit/Delete Discount </string>
    <string name="label_tout_drafts">Drafts</string>
    <string name="label_add">Add</string>
    <string name="label_list_return_purchase">Purchase Return</string>
    <string name="label_purchase_status">Purchase\nStatus</string>
    <string name="label_recherche">Search</string>
    <string name="label_is_refund">Is refund?</string>
    <string name="label_variation">Variations</string>
    <string name="label_warranty">Warranties</string>
    <string name="label_filter">Search</string>
    <string name="label_received">Received - </string>
    <string name="label_ordered">Ordered - </string>
    <string name="label_partial">Partial - </string>
    <string name="label_paid">Paid - </string>
    <string name="label_pending">Pending - </string>
    <string name="label_due">Due - </string>
    <string name="label_noo">No</string>
    <string name="label_close">Close</string>
    <string name="label_date_range">Date Range:</string>
    <string name="label_nom_product">Nom du produit (Désignation)*</string>
    <string name="image_attache_libelle">Product image : </string>
    <string name="image_quick_sell">Quick Sale</string>
    <string name="txt_typpe">Type</string>
    <string name="lbl_contact_type">Contact type:*</string>
    <string name="image_nom_entrprise">Business Name:</string>
    <string name="image_nom_prefix">Prefix:</string>
    <string name="lbl_role_name">Role Name :*</string>
    <string name="image_nom_entrprise_requiered">*Business Name:</string>
    <string name="image_voir_detail">Voir detail</string>
    <string name="image_mobile">Mobile:*</string>
    <string name="label_mobile">Mobile</string>
    <string name="label_locationnn">Business Location:*</string>
    <string name="label_location_from">Location (From):*</string>
    <string name="label_invoice_scheme">Invoice scheme:</string>
    <string name="label_location_from_txt">Location (From)</string>
    <string name="label_location_to">Location (To):*</string>
    <string name="label_location_to_txt">Location (To)</string>
    <string name="image_note_de_frais">Expense report:</string>
    <string name="image_adresse">Adresse line 1:</string>
    <string name="image_adresse1">Adresse line 2:</string>
    <string name="image_tax_number">Tax number:</string>
    <string name="label_startsss_at">Starts At:</string>
    <string name="label_userrname">Username:</string>
    <string name="label_userrname_req">Username*:</string>
    <string name="image_prenom">First Name:*</string>
    <string name="image_prefix">Prefix:</string>
    <string name="label_to">To:</string>
    <string name="label_acc_summary">Account Summary</string>
    <string name="image_ref_num">Reference number:</string>
    <string name="label_btn_action">Action</string>
    <string name="label_our_client_gerer">Manage your Customers</string>
    <string name="label_name">Name</string>
    <string name="label_role">Role</string>
    <string name="label_insert_lower_price">Please insert lower amount than the total payable </string>

    <string name="label_duration">Duration</string>
    <string name="label_date_naissance">Date of birth:</string>
    <string name="text_dic_amount">Discount Amount:*</string>
    <string name="text_is_active">Is active</string>
    <string name="label_note_paymnet">Payment note:</string>
    <string name="label_date_req">Date:*</string>
    <string name="image_code_postal">Zip Code:</string>
    <string name="image_contact_number">Contact Number:</string>
    <string name="image_code_postal_txt">Zip Code</string>
    <string name="image_ligne_fixe">Landline:</string>
    <string name="image_credit_limit">Credit Limit:</string>
    <string name="image_confirm_password">Confirm Password:*</string>
    <string name="image_ligne_paye">Paid on:*</string>
    <string name="label_expense_note">Expense note</string>
    <string name="label_ligne_paye">Paid on</string>
    <string name="image_priority">Priority :*</string>
    <string name="image_ville">City:</string>
    <string name="image_ville_ob">City*</string>
    <string name="text_zip_code_req">Zip Code:*</string>
    <string name="text_state_req">State:*</string>
    <string name="text_alternance_contact">Alternate contact number</string>
    <string name="text_country_req">Country:*</string>
    <string name="image_ville_txt">City</string>
    <string name="text_website">Website</string>
    <string name="image_statee">State:</string>
    <string name="image_statee_txt">State</string>
    <string name="label_reference_num">Reference No</string>
    <string name="label_heading">heading</string>
    <string name="image_shipping_adress">Shipping Address:</string>
    <string name="image_social_media_link">Social Media Link</string>
    <string name="label_created_at">Created at</string>
    <string name="txt_opening_balance">Opening Balance:</string>
    <string name="label_opening_balance">Opening Balance</string>
    <string name="label_subscreption">Subscriptions</string>
    <string name="text_total_invoice">Total invoice:</string>
    <string name="label_ends_at">Ends At:</string>
    <string name="text_nom_famile">Last Name:</string>
    <string name="label_montant_total_re">Total amount:*</string>
    <string name="image_nom_email">Email</string>
    <string name="image_password">Password:*</string>
    <string name="image_nom_email_req">Email:*</string>
    <string name="image_disc_type">Discount Type:*</string>
    <string name="label_disc_type">Discount Type</string>
    <string name="label_selling_pricee">Selling price</string>
    <string name="label_unit_purchase_pricee">Unit Purchase \nPrice</string>
    <string name="label_apply_in_selling_p"> Apply in selling price groups</string>
    <string name="text_pays">Country:</string>
    <string name="text_pays_txt">Country</string>
    <string name="label_purchase_tax">Purchase Tax</string>
    <string name="label_blood_group">Blood Group:</string>
    <string name="label_payement_status">Payment Status</string>
    <string name="label_reccuring_interval">Recurring interval *</string>
    <string name="label_reccuring_details">Recurring details</string>
    <string name="text_pay_term">Pay term*</string>
    <string name="label_pay_term">Pay term</string>
    <string name="label_par_adress">Permanent Address:</string>
    <string name="label_current_adress">Current Address:</string>
    <string name="label_is_reccuring">is Recurring?</string>
    <string name="label_expense_for_contact">Expense for contact</string>
    <string name="text_mode_paymenet">Mode de payment *</string>
    <string name="text_detail_vente">Sell Details</string>
    <string name="text_roles_and_permission">Roles and Permissions</string>
    <string name="text_permission">Permissions</string>
    <string name="text_add_variation">Add Variation</string>
    <string name="label_add_uniit">Add Unit</string>
    <string name="label_add_braand">Add Brand</string>
    <string name="label_add_opening_stock">Add Opening Stock</string>
    <string name="label_add_delete_payment">Add/Edit/Delete Payments </string>
    <string name="label_view_purchase_price">View Purchase Price</string>
    <string name="label_purchase_detail">Purchase Details</string>
    <string name="label_stock_transfer_detail">Stock Transfer Details</string>
    <string name="label_stock_adjustment_detail">Stock Adjustment Details</string>
    <string name="label_update_status">Update Status</string>
    <string name="label_view_own_purchase">View own purchase only</string>
    <string name="label_add_category">Add Category</string>
    <string name="label_add_category_expensee">Add Expense Category</string>
    <string name="label_add_warranty">Add Warranty</string>
    <string name="label_view_payments">View Payments</string>
    <string name="label_product_type">Product Type:*</string>
    <string name="label_product_typee">Product Type:</string>
    <string name="label_product_margin">x Margin(%)</string>
    <string name="txt_add_are_you_sure">Are you sure ? </string>
    <string name="txt_add_are_you_sure_txt">This item along with all it\'s values will be deleted. </string>
    <string name="label_detail_descreption">Description</string>
    <string name="label_only_avaiable">Only %1$s Pc(s) available</string>
    <string name="image_product_name">Product</string>
    <string name="text_name">Name</string>
    <string name="image_variatiooon_name">Variation name *</string>
    <string name="label_unit_name">Unit name *</string>
    <string name="label_prod_name">Product Name</string>
    <string name="label_brandd_name">Brand name *</string>
    <string name="label_plz_select">Please Select location from</string>
    <string name="label_plz_spriority">Select priority</string>
    <string name="label_plz_select_locations">Select locations</string>
    <string name="label_plz_select_locations_from">Please Select Location From</string>
    <string name="label_plz_select_locations_to">Please Select Location To</string>
    <string name="label_ucatg_nmae">Category name *</string>
    <string name="label_category_name">Category name</string>
    <string name="label_shordt_desc">Short description</string>
    <string name="label_shordt_name">Short name *</string>
    <string name="label_tax_rates_percent">Tax Rate %:* </string>
    <string name="label_qty_remaining">Quantity Remaining</string>
    <string name="label_remarque">Remarks</string>
    <string name="label_unit_cost">Unit Cost </string>
    <string name="label_categiry_code">Category Code</string>
    <string name="label_descreptionn">Description </string>
    <string name="label_allow_decimal">Allow decimal *</string>
    <string name="label_subtotal">Subtotal</string>
    <string name="label_more_info">More Informations</string>
    <string name="label_durationnn">Duration *</string>
    <string name="label_variation_values">Add variation values * </string>
    <string name="label_product_price">Price</string>
    <string name="label_unit_price">Unit Price</string>
    <string name="label_product_qty">Qty</string>
    <string name="labele_product_total">Total</string>
    <string name="labele_grand_total">Grand Total</string>
    <string name="image_payement_du">Payment\ndue</string>
    <string name="image_product_subtotal">Sub-Total</string>
    <string name="label_product_total">Total</string>
    <string name="label_gender">Gender</string>
    <string name="label_material_status">Marital Status:</string>
    <string name="label_pay_cash_pro">Pay Cash</string>
    <string name="label_pay_card">Pay Card</string>
    <string name="label_pay_mobile">Mobile </string>
    <string name="label_priority">Priority</string>
    <string name="label_pay_multi_pay">Multi-Pay</string>
    <string name="label_save">Save</string>
    <string name="label_saved">Saved</string>
    <string name="label_yes">Yes</string>
    <string name="label_add_value">Add Value</string>
    <string name="label_status_payement">Payment status</string>
    <string name="label_status_method">Payment method</string>
    <string name="label_cancel">Cancel</string>
    <string name="label_empty_product">There is not enough stock in this location</string>
    <string name="label_empty_add_product_to">Please add product to location </string>
    <string name="label_open">Open</string>
    <string name="label_location">Location</string>
    <string name="label_location_id">Location id</string>
    <string name="label_contact">Contacts</string>
    <string name="label_achat_parent">Achat du parent</string>
    <string name="label_cprovider">Supplier</string>
    <string name="label_achats">Purchases</string>
    <string name="label_list_purchases">List Purchases</string>
    <string name="label_category">Categories </string>
    <string name="label_garenty">Warranties </string>
    <string name="label_expense_categories">Expense Categories </string>
    <string name="label_brand">Brands </string>
    <string name="label_brands">Brand </string>
    <string name="label_updatee">Update </string>
    <string name="label_deleteddd">Deleted </string>
    <string name="label_desactivatee">Desactivate ? </string>
    <string name="label_activatee">Activate ? </string>
    <string name="label_settup_busines_setting">Setup business Settings </string>
    <string name="label_settup_busines_setting_product">Please Setup Business settings to Add your product </string>
    <string name="label_settup_busines_location">Setup Business Location </string>
    <string name="label_settup_busines_location_plz_add">Please Add Your Business Location to Add your product </string>
    <string name="label_btn_later">Later </string>
    <string name="label_btn_settup">Setup </string>
    <string name="label_select_ssupplier">Select supplier </string>
    <string name="label_select_namme">Select name </string>
    <string name="label_select_type">Select type </string>

    <string name="label_deleted_transaction">Deleted Transaction </string>
    <string name="label_amount_payable_innfer">Amount to be paid is less than the amount payable </string>
    <string name="label_amount_payable_moore">Amount to be paid is more than the amount payable </string>
    <string name="label_cannot_delete_produ">Cannot delete product with associated transactions </string>
    <string name="label_cannot_update_stock_transfer">Can not Update completed stock transfer  </string>
    <string name="label_updated_succesfully">Updated successfully </string>
    <string name="label_maximum_amount">Maximum amount is </string>
    <string name="label_unit_label">Units</string>
    <string name="label_unit_list_retourn">Purchase Return</string>
    <string name="label_pos_title">POS</string>
    <string name="label_all_dsicounts"> All your discounts </string>
    <string name="label_our_category_gerer">  Manage your categories </string>
    <string name="label_our_brand_gerer">  Manage your brands </string>
    <string name="label_our_business_location_gerer">  Manage your business location </string>
    <string name="label_expense_category_gerer">  Manage your expense categories </string>
    <string name="label_our_unit_gerer">  Manage your units </string>
    <string name="label_our_taxes_rates_gerer">  Manage your tax rates </string>
    <string name="label_our_variation_gerer">  Manage your variation </string>
    <string name="label_our_warrnaty_gerer">  Manage your warranties </string>
    <string name="label_edit_purchase">Edit Purchase  </string>
    <string name="label_business_setting">Business Settings  </string>
    <string name="label_business_setting_view">View Business Settings  </string>
    <string name="label_business_location_view">View Business Location  </string>
    <string name="label_purchase_loss_view">View profit/loss report  </string>
    <string name="label_business_setting_setting">Access Business Settings</string>
    <string name="label_access_sell_return">Access Sell Returns</string>
    <string name="no_units_found">No Units Found </string>

    <string name="label_ajout_contact">Add payment</string>
    <string name="label_products">Products  </string>
    <string name="label_purchase_stock_adj">Purchase and Stock Adjustment</string>
    <string name="label_products_gerer">  Manage your products</string>
    <string name="label_nom">Name</string>
    <string name="label_usernom">Username</string>
    <string name="label_none">None</string>
    <string name="label_purchases">Purchases</string>
    <string name="label_added_by">Added By</string>
    <string name="label_added_on">Added On</string>
    <string name="label_id_contact">Contact Id</string>
    <string name="label_starts_at">Starts at</string>
    <string name="label_starts_at_req">Starts at:*</string>
    <string name="label_purchase_Date">Purchase Date:*</string>
    <string name="label_sale_date">Sale Date:*</string>
    <string name="label_sell">Sell</string>
    <string name="label_cash_register">Cash Register</string>
    <string name="label_sync">Synchronization</string>
    <string name="label_settings">Settings</string>
    <string name="label_stock_transfer">Transferts de stock</string>
    <string name="label_stock_expenses">Dépenses</string>
    <string name="label_add_stock_expenses">Add expenses</string>
    <string name="label_list_stock_expenses">List des dépenses</string>
    <string name="label_email">Email</string>
    <string name="label_discount_amount">Discount amount</string>
    <string name="label_discount_type">Discount type</string>
    <string name="label_stock_reports">Rapports</string>
    <string name="label_point_of_sale">Point de vente</string>
    <string name="label_stock_report">Stock Report</string>
    <string name="label_save_transaction">Save Transaction</string>
    <string name="label_nothing">No item selected</string>
    <string name="label_transaction_done">Transaction done</string>
    <string name="label_unit">Units </string>
    <string name="label_tax">Tax </string>
    <string name="label_short_name">Short name </string>
    <string name="label_business_location">Business location </string>
    <string name="label_roles">Roles </string>
    <string name="label_roles_manage">Manage roles </string>
    <string name="label_short_value">Values </string>
    <string name="label_status_paimenent">Payment status</string>
    <string name="label_plage_de_date">Date range:</string>
    <string name="label_station">Location :</string>
    <string name="label_spaymenet_method">Payment Method:*</string>
    <string name="label_station_required">Station :*</string>
    <string name="label_status">Status</string>
    <string name="label_total_purchaee_due">Total Purchase \nDue</string>
    <string name="label_total_sell_due">Total Sell \nDue</string>
    <string name="label_total_purchase_due_return">Total Purchase Return Due</string>
    <string name="label_total_sell_due_return">Total Sell Return Due</string>
    <string name="label_date">Date</string>
    <string name="label_stock">Stock </string>
    <string name="label_select_all">Select all</string>
    <string name="label_selectamuuunt">Sélectionnez le montant</string>
    <string name="label_add_stock_tranfer">Add Stock Transfer</string>
    <string name="label_stock_tranfer">Stock Transfers</string>
    <string name="label_add_stock_adjs">Add Stock Adjustment</string>
    <string name="label_all_stock_adjs">Stock Adjustments</string>
    <string name="label_customer">Customer</string>
    <string name="label_add_new_contact"> Add a new contact</string>
    <string name="label_add_new_user"> Add a new user</string>
    <string name="label_add_new_discount"> Add a new discount</string>
    <string name="label_add_new_provider"> Ajouter un nouveau fournisseur</string>
    <string name="label_add_new_product"> Add new product</string>
    <string name="label_add_new_business_locationt"> Add a new business location</string>
    <string name="label_add_new_purchase"> Add Purchase</string>
    <string name="label_add_new_quotation"> Add Quotation</string>
    <string name="label_add_edit_quotation"> Edit Quotation</string>
    <string name="label_add_new_purchase_return"> Add Purchase Return</string>
    <string name="label_update_new_product">Update product</string>
    <string name="label_update_customer"> Update contact</string>
    <string name="label_updated_at"> Updated at</string>
    <string name="label_add_new_category"> Add new category</string>
    <string name="label_report_profit_loss">Profit / Loss Report</string>
    <string name="label_facture_no">Invoice No. </string>
    <string name="label_decimale_autorize">Allow decimal</string>
    <string name="label_reference_no">Reference No</string>
    <string name="label_quantity">Quantity</string>
    <string name="label_add_expense">Add Expense</string>
    <string name="label_purchase_quantity">Purchase Quantity</string>
    <string name="label_no">No</string>
    <string name="label_oui">Yes</string>
    <string name="label_montant_total">Total amount </string>
    <string name="label_reason">Reason </string>
    <string name="label_montant_total_recovered">Total amount recovered </string>
    <string name="label_montant_total_montant">Amount:*</string>
    <string name="label_search_hint">SKU, Product Name, Product Code, Product Tag </string>
    <string name="string_pcategory">Category</string>
    <string name="string_pcategory_req">Category*</string>
    <string name="string_fiancial_years_start">Financial year start month:</string>
    <string name="string_payement_note">Payment Note</string>
    <string name="string_payement_due">Payment due</string>
    <string name="string_applicable_tax">Applicable Tax:</string>
    <string name="string_sub_pcategory">Sub Category</string>
    <string name="string_stock_accounting">Stock Accounting Method:*</string>
    <string name="string_pick_date_start">Start date</string>
    <string name="string_pick_date_fin">End date</string>
    <string name="string_product_name">Product Name:*</string>
    <string name="string_baroce_type">Barcode Type:*</string>
    <string name="string_purchase_status">Purchase Status:*</string>
    <string name="string_expense_for">Expense for:</string>
    <string name="string_purchase_status_">Purchase Status</string>
    <string name="string_baarc_type">Barcode Type</string>
    <string name="string_status">Status:*</string>
    <string name="string_adj_type">Adjustment type:*</string>
    <string name="string_adj_type_txt">Adjustment type</string>
    <string name="string_bprod_type">Product Type</string>
    <string name="string_name">Name:*</string>
    <string name="string_uniit">Unit:*</string>
    <string name="string_ubranndt">Brand:</string>
    <string name="string_time_zone">Time zone:</string>
    <string name="string_date_format">Date Format:*</string>
    <string name="string_time_format">Time Format:*</string>
    <string name="string_skuut">SKU</string>
    <string name="string_desactivate">Desactivate ?</string>
    <string name="string_activate">Activate ?</string>
    <string name="string_salert_quantity">Alert quantity</string>
    <string name="string_start_date">Start Date:</string>
    <string name="string_anage_stock">Manage Stock?</string>
    <string name="string_default_profit_percent">Default profit percent:*</string>
    <string name="string_not_selling">Not for selling</string>
    <string name="string_total_sell">Total Sale</string>
    <string name="string_total_paid">Total Paid</string>
    <string name="string_attach_doc">Attach Document</string>
    <string name="string_advence_balance">Advance Balance</string>
    <string name="string_view_contact">View Contact</string>
    <string name="string_view_customer">View Customer</string>
    <string name="string_stock_actual">Stock actuel</string>
    <string name="string_view_user">View user</string>
    <string name="string_view_brand">View brand</string>
    <string name="string_view_warranty">View warranty</string>
    <string name="string_view_tax_rate">View Tax Rate</string>
    <string name="string_view_unit">View Unit</string>
    <string name="string_view_category">View Category</string>
    <string name="string_view_variation">View Variation</string>
    <string name="string_add_category">Add Category</string>
    <string name="string_add_variation">Add Variation</string>
    <string name="string_edit_variation">Edit Variation</string>
    <string name="string_delete_variation">Delete Variation</string>
    <string name="string_edit_category">Edit Category</string>
    <string name="string_view_product_stock">View product stock value</string>
    <string name="string_add_business_setting">Add Business Setting</string>
    <string name="string_add_business_location">Add Business Location</string>
    <string name="string_edit_business_setting">Edit Business Setting</string>
    <string name="string_edit_business_location">Edit Business location</string>
    <string name="string_delete_category">Delete Category</string>
    <string name="string_delete_business_setting">Delete Business Setting</string>
    <string name="string_delete_business_location">Delete Business Location</string>
    <string name="string_access_expenses">Access Expenses</string>
    <string name="string_access_reports">Access Reports</string>
    <string name="string_add_unit">Add Unit</string>
    <string name="string_edit_unit">Edit Unit</string>
    <string name="string_delete_unit">Delete Unit</string>
    <string name="string_add_tax_rate">Add Tax Rate</string>
    <string name="string_edit_tax_rate">Edit Tax Rate</string>
    <string name="string_delete_tax_rate">Delete Tax Rate</string>
    <string name="string_add_brand">Add brand</string>
    <string name="string_add_warranty">Add warranty</string>

    <string name="string_view_roles">View roles</string>
    <string name="string_view_supplier">View contact</string>
    <string name="string_view_product">View Product</string>
    <string name="string_view_pourchase_stock">View purchase and Stock Adjustment</string>
    <string name="string_add_pourchase_stock">Add purchase and Stock Adjustment</string>
    <string name="string_add_pos_sell">Add POS sell</string>
    <string name="string_close_cash_register">Close cash register</string>
    <string name="string_close_clear">Clear</string>
    <string name="string_edit_pourchase_stock">Edit purchase and Stock Adjustment</string>
    <string name="string_edit_pos_sell">Edit POS sell</string>
    <string name="string_delete_pourchase_stock">Delete purchase and Stock Adjustment</string>
    <string name="string_delete_pos_sell">Delete POS sell</string>
    <string name="string_veiw_pos_sell">View POS sell</string>
    <string name="string_cash_register">View cash register</string>
    <string name="string_view_add_role">Add Role</string>
    <string name="string_view_add_supplier">Add Contact</string>
    <string name="string_view_add_cutomer">Add Customer</string>
    <string name="string_view_add_product">Add Product</string>
    <string name="string_view_edit_role">Edit Role</string>
    <string name="string_view_edit_supplier">Edit Contact</string>
    <string name="string_view_edit_customer">Edit Customer</string>
    <string name="string_view_edit_product">Edit Product</string>
    <string name="string_delete_edit_role">Delete Role</string>
    <string name="string_delete_edit_supplier">Delete Contact</string>
    <string name="string_delete_edit_customer">Customer Supplier</string>
    <string name="string_delete_edit_product">Customer Product</string>
    <string name="string_delete_product">Delete Product</string>
    <string name="string_is_active"> Is active ? </string>
    <string name="string_view">View</string>
    <string name="string_add_user">Add user</string>
    <string name="string_edit_user">Edit user</string>
    <string name="string_edit_brand">Edit brand</string>
    <string name="string_edit_warranty">Edit warranty</string>
    <string name="string_delete_user">Delete user</string>
    <string name="string_delete_brand">Delete brand</string>
    <string name="string_delete_warranty">Delete warranty</string>
    <string name="string_total_sale_due">Total Sale Due</string>
    <string name="string_customer_name">Customer name</string>
    <string name="string_default_price_vente_inc">Default selling price (Inc.tax)  </string>
    <string name="string_default_price_vente_exc">Default selling price (Exc.tax) </string>
    <string name="lbl_seling_pric_tax">Selling Price Tax Type *</string>
    <string name="text_label_amount">Discount Amount </string>
    <string name="text_no_repetition">No. of Repetitions </string>
    <string name="label_selling_price">Selling Price</string>
    <string name="lbl_app_tax">Taxe Applicable</string>
    <string name="lbl_alert_exc_tax">Exc .Tax* </string>
    <string name="lbl_alert_inc_tax">Inc .Tax* </string>
    <string name="lbl_action_delete">Delete</string>
    <string name="lbl_action_edit">Edit</string>
    <string name="lbl_purchase_tax">Purchase Tax</string>
    <string name="lbl_purchase_tax_plus">(+) Purchase Tax</string>
    <string name="lbl_customer_group_name">Customer Group Name</string>
    <string name="lbl_customer_group_name_req">Customer Group Name:*</string>
    <string name="lbl_discount">Discount:(-) </string>
    <string name="lbl_shipping_detail">Shipping Details </string>
    <string name="lbl_shipping_charges">Shipping Charges </string>
    <string name="lbl_dilevered_to">Delivered To </string>
    <string name="lbl_additional_shipping">(+) Additional Shipping charges:</string>
    <string name="lbl_aaditonal_note">Additional Notes</string>
    <string name="lbl_reason">Reason:</string>
    <string name="lbl_purchase_total">Purchase Total</string>
    <string name="lbl_net_total">Net Total Amount : </string>
    <string name="lbl_total_amount">Total Amount</string>
    <string name="lbl_item_total">Total Items : </string>
    <string name="lbl_total_due">Total Due</string>
    <string name="lbl_total_amount_recovered">Total amount recovered:</string>
    <string name="lbl_please_select_station">Select Location</string>
    <string name="lbl_please_select_station_first">Select Location first</string>
    <string name="lbl_please_select_contact">Select Contact</string>
    <string name="lbl_please_select_customer">Select Customer</string>
    <string name="lbl_current_stock_value">Current Stock Value \n (By purchase price) </string>
    <string name="lbl_current_stock_value_by_sale">Current Stock Value \n (By sale price) </string>
    <string name="lbl_potential_profit">Potential profit</string>
    <string name="lbl_total_unit_sold">Total unit sold</string>
    <string name="lbl_total_unit_transfered">Total Unit Transfered</string>
    <string name="lbl_total_unit_adjusted">Total Unit Adjusted</string>
    <string name="lbl_margin">Margin(%) : </string>
    <string name="lbl_prod_desc">Product Description </string>
    <string name="lbl_prod_weghit">Weight</string>
    <string name="label_descreption">Description </string>
    <string name="label_tax_rates">Tax Rates </string>
    <string name="label_all_users">All users </string>
    <string name="label_users">Users </string>
    <string name="label_role_req">Role:* </string>
    <string name="label_add_role">Add Role</string>
    <string name="label_not_autorized">Not authorized</string>
    <string name="label_not_autorized_purchase">Can not update received purchases</string>
    <string name="label_not_autorized_purchase_edit">Can not edit received purchases</string>
    <string name="label_quotation_details">Quotation details</string>
    <string name="label_cannot_delete_transaction">Can not delete completed transaction</string>
    <string name="label_update_role">Update Role</string>
    <string name="label_tax_rates_perc">Tax Rate % </string>
    <string name="label_product_must_sync">Products must be synchronized first </string>
    <string name="label_contacts_must_sync">Contacts must be synchronized first </string>

    <string name="title_activity_drawer_main">DrawerMainActivity</string>

    <string name="label_all_field_required">Please complete all required fields  </string>
    <string name="label_add_product_dirst_required">No products added ,add some products first  </string>

    <string name="title_activity_drawer_dashboard">DrawerDashboardActivity</string>
    <string name="title_activity_drawer_main2">DrawerMain</string>
    <string name="string_data_up_to_date">Data Is Up To Date </string>
    <string name="string_age_must_be_over">Age must be over than 16 y/o </string>
    <string name="string_select_customer">Select Customer </string>
    <string name="string_please_enter_product_name">Please Enter Product Name </string>
    <string name="string_please_enter_margin">Please Enter Margin % </string>
    <string name="string_please_exclusive_tax">Please Enter Exclusive Tax </string>
    <string name="string_please_einclusive_tax">Please Enter Inclusive Tax </string>


    <string name="label_payment_note">Payment note :*</string>
    <string name="label_payment_not">Note</string>
    <string name="label_payment_additional_not">Additional Note</string>
    <string name="label_amount">Amount:*</string>
    <string name="label_amount_not">Amount</string>
    <string name="label_payment_method">Payment Method:*</string>
    <string name="label_payment_for">Payment For</string>
    <string name="label_payment_method_not">Payment Method</string>
    <string name="label_total_payable">Total Payable</string>


    <!--    <string name="label_return_change">Change Return: \n %1$s %2$s</string>-->
    <string name="label_add_payement_row">Add Payment Row</string>
    <string name="label_finalize_pay">Finalize Payment</string>
    <string name="label_return_change">Change Return</string>

    <string name="label_total_payable_items">Total Payable </string>
    <!--    <string name="label_total_payable_items">Total Payable: \n %1$s %2$s</string>-->
    <string name="label_total_paying">Total Paying</string>
    <!--    <string name="label_total_paying">Total Paying: \n %1$s %2$s</string>-->
    <!--    <string name="label_total_items">Total Items: \n %1 $s</string>-->
    <string name="label_total_items">Total Items </string>

    <string name="action_search">Search</string>
    <string name="drawer_open">Open</string>
    <string name="drawer_close">Close</string>

    <string name="nav_item_home">Home</string>
    <string name="nav_item_friends">Friends</string>
    <string name="nav_item_notifications">Messages</string>

    <string name="title_messages">Messages</string>
    <string name="title_friends">Friends</string>
    <string name="title_home">Home</string>
    <string name="title_activity_main">MainActivity</string>

    <!-- TODO: Remove or change this placeholder text -->
    <string name="hello_blank_fragment">Hello blank fragment</string>
    <string name="title_activity_navi_main">NaviMainActivity</string>
    <string name="title_activity_drawerr_main">DrawerrMainActivity</string>


    <string name="label_draft_lbl">Draft</string>
    <string name="label_all_salles">All Sales</string>
    <string name="label_quotation">Quotation</string>
    <string name="label_lsit_return_sell">List sell return</string>
    <string name="label_sell_note">Sell note</string>
    <string name="label_shippement">Shipement</string>
    <string name="label_discounts">Discounts</string>
    <string name="label_stock_transfers">Stock transfers</string>
    <string name="label_stock_adjustement">Stock adjustement</string>
    <string name="label_expense">Expenses</string>
    <string name="label_lsit_expenses">List Expenses</string>
    <string name="label_reports">Reports</string>
    <string name="label_profit_loss">Profit / Loss Report</string>

    <string name="label_list_expense">List Expense</string>
    <string name="label_expense_categoriess">Expense Categories</string>
    <string name="label_profit_report">Stock report</string>
    <string name="menu_produit">Products</string>
    <string name="lbl_category_code">Category code</string>
    <string name="lbl_added_success">Added successfully</string>
    <string name="label_select_different_station">Please select different location</string>
    <string name="label_select_statuus">Select Status</string>
    <string name="lbl_error_insert">Error insert</string>
    <string name="lbl_unit_cost">Unit Cost</string>

    <string name="label_total">Total</string>
    <string name="label_login">Login</string>
    <string name="label_forgot_password">Forgot Password?</string>
    <string name="logo">Logo</string>
    <string name="reset_here">Reset Here</string>
    <string name="product_category">Product Category</string>
    <string name="scan">Scan</string>
    <string name="no_product_found">No Product Found</string>
    <string name="no_customer_groups_found">No Customer groups Found</string>
    <string name="product_name">Product name</string>
    <string name="business_name">Business Name:*</string>
    <string name="lbl_transaction_edit">Transaction Edit Days:* </string>
    <string name="filters">Filters</string>
    <string name="product_image">Product Image</string>
    <string name="lbl_upload_logo">Upload Logo</string>
    <string name="string_calculation_percentage">Calculation Percentage (%)</string>
    <string name="label_add_customer_groups">Add Customer Group</string>
    <string name="label_calculation_perc">Calculation Percentage (%): </string>
    <string name="label_choose_all">All</string>

    <string name="yes_delete_it">Yes,delete it!</string>
    <string name="delete">Delete</string>
    <string name="lbl_sign_out">Sign Out</string>
    <string name="string_customer_groups">Customer Groups</string>
    <string name="no_contacts_found">No Contacts Found</string>
    <string name="no_sales_found">No Sales Found</string>
    <string name="no_stock_adj_found">No Stock Adjustement Found</string>
    <string name="no_stock_transfer_found">No Stock Transfers Found</string>
    <string name="no_depense_found">No Depense Found</string>
    <string name="no_depense_category_found">No Expense Category Found</string>
    <string name="no_report_stock_found">No Report Stock</string>
    <string name="no_draft_found">No Draft Found</string>
    <string name="no_quotation_found">No Quotation Found</string>
    <string name="no_discounts_found">No Discounts Found</string>
    <string name="no_purchase_return_found">No purchase Return Found</string>
    <string name="no_brands_found">No Brands Found</string>
    <string name="no_warranty_found">No Warranty Found</string>
    <string name="no_variations_found">No Variations Found</string>
    <string name="no_tax_rates_found">No Tax Rates Found</string>
    <string name="no_location_station_found">No Location Station Found</string>
    <string name="no_categories_found">No Categories Found</string>
    <string name="label_manage_customer_groups">  Manage your customer groups </string>
    <string name="no_purchase_found">No Purchase Found</string>
    <string name="success">Success</string>
    <string name="variation_added_success">Variations Added successfully !!</string>
    <string name="units_added_success">Units Added successfully !!</string>
    <string name="categories_added_success">Categories Added successfully !!</string>
    <string name="warranties_added_success">Warranties Added successfully !!</string>
    <string name="brands_added_success">Brands Added successfully !!</string>
    <string name="products_added_success">Products Added successfully !!</string>
    <string name="variation_updated_success">Variations Updated successfully !!</string>
    <string name="units_updated_success">Units Updated successfully !!</string>
    <string name="categories_updated_success">Categories Updated successfully !!</string>
    <string name="warranties_updated_success">Warranties Updated successfully !!</string>
    <string name="brands_updated_success">Brands Updated successfully !!</string>
    <string name="products_updated_success">Products Updated successfully !!</string>
    <string name="contacts_added_success">Contacts Added successfully !!</string>
    <string name="contacts_updated_success">Contacts Updated successfully !!</string>
    <!--  array lists-->
    <string-array name="array_apply_pay">
        <item>Cash</item>
        <item>Card</item>
    </string-array>

    <string-array name="array_apply_tax">
        <item>No</item>
    </string-array>

    <string-array name="array_sub_category">
        <item>No</item>
    </string-array>
    <string-array name="array_accounting_method">
        <item>FIFO (First In First Out)</item>
        <item>LIFO (Lst In First Out)</item>
    </string-array>

    <string-array name="type_product_select">
        <item>Type de produit:</item>
        <item>Tout</item>
        <item>Unique</item>
        <item>Variable</item>
    </string-array>

    <string-array name="array_barcode_type">
        <item>Code 128(C128)</item>
        <item>Code 39(C39)</item>
        <item>EAN-13</item>
        <item>EAN-8</item>
        <item>UPC-A</item>
        <item>UPC-E</item>
    </string-array>

    <string-array name="array_purchase_status">
        <item>Please Select</item>
        <item>received</item>
        <item>pending</item>
        <item>ordered</item>
    </string-array>

    <string-array name="array_quoation_status">
        <item>Please Select</item>
        <item>ordered</item>
        <item>packed</item>
        <item>shipped</item>
        <item>delivered</item>
        <item>canelled</item>
    </string-array>

    <string-array name="array_transfer_stock_status">
        <item>Please Select</item>
        <item>pending</item>
        <item>in transit</item>
        <item>completed</item>
    </string-array>

    <string-array name="array_adj_type">
        <item>Please Select</item>
        <item>normal</item>
        <item>abnormal</item>
    </string-array>

    <string-array name="array_action_product">
        <item>Action</item>
        <item>View</item>
        <item>Edit</item>
        <item>Delete</item>
        <item>Add Or Edit Opening Stock</item>
    </string-array>

    <string-array name="array_stock_transfert">
        <item>Action</item>
        <item>View</item>
        <item>Delete</item>
        <item>Edit</item>
    </string-array>

    <string-array name="array_stock_adjst">
        <item>Action</item>
        <item>View</item>
        <item>Delete</item>
    </string-array>

    <string-array name="array_quotation">
        <item>Action</item>
        <item>View</item>
        <item>Delete</item>
        <item>Edit</item>
    </string-array>

    <string-array name="array_action_purchase">
        <item>Action</item>
        <item>View</item>
        <item>Edit</item>
        <item>Delete</item>
        <item>Add Payement</item>
        <item>View Payements</item>
        <item>Update Status</item>
    </string-array>


    <string-array name="array_action_sell">
        <item>Action</item>
        <item>View</item>
        <item>Delete</item>
        <item>View Payments</item>
        <item>Sell Return</item>
    </string-array>


    <string-array name="array_action_">
        <item>Action</item>
        <item>View</item>
        <item>Delete</item>
        <item>Edit</item>
    </string-array>

    <string-array name="array_action_edit">
        <item>Action</item>
        <item>Edit</item>
        <item>Delete</item>
    </string-array>
    <string-array name="array_invoice_scheme">
        <item>Please Select</item>
        <item>Default</item>
    </string-array>

    <string-array name="array_action_expense">
        <item>Action</item>
        <item>Edit</item>
        <item>Delete</item>
        <item>Add Payement</item>
        <item>View Payements</item>
    </string-array>

    <string-array name="array_action_purchase_return">
        <item>Action</item>
        <item>Edit</item>
<!--        <item>Add Payement</item>-->
        <item>Delete</item>
        <!--        <item>View Payements</item>-->

    </string-array>

    <string-array name="array_action_contact">
        <item>Action</item>
        <item>Pay</item>
        <item>View</item>
        <item>Activate/Desactivate</item>
        <item>Edit</item>
        <item>Delete</item>
    </string-array>

    <string-array name="array_action_contact_supplier">
        <item>Action</item>
        <item>Pay</item>
        <item>View</item>
        <item>Edit</item>
        <item>Delete</item>
        <item>Activate/Desactivate</item>

    </string-array>

    <string-array name="array_action_tax">
        <item>None</item>
    </string-array>


    <string-array name="array_contact_type">
        <item>Both</item>
        <item>customer</item>
        <item>supplier</item>
    </string-array>
    <string-array name="array_contact_typee">
        <item>Select contact</item>
        <item>Both</item>
        <item>customer</item>
        <item>supplier</item>
    </string-array>
    <string-array name="array_prefix">
        <item>Mr.</item>
        <item>Ms.</item>
        <item>Mrs</item>
        <item>Engr.</item>
        <item>Dr.</item>
    </string-array>
    <string-array name="array_gender_type">
        <item>Please Select</item>
        <item>Male</item>
        <item>Female</item>
        <item>Others</item>
    </string-array>

    <string-array name="array_marital_status">
        <item>Marital Status</item>
        <item>Married</item>
        <item>Unmarried</item>
        <item>Divorced</item>
    </string-array>

    <string-array name="array_allow_decimal">
        <item>Please Select</item>
        <item>Yes</item>
        <item>No</item>
    </string-array>

    <string-array name="array_duration">
        <item>Please select</item>
        <item>days</item>
        <item>months</item>
        <item>years</item>
    </string-array>

    <string-array name="array_provider_type">
        <item>Please Select</item>
        <item>Provider</item>
    </string-array>

    <string-array name="payment_method_array">
        <item>cash</item>
        <item>card</item>
        <item>cheque</item>
        <item>bank transfer</item>
        <item>other</item>
    </string-array>

    <string-array name="type_mode_payment">
        <item>Unité:*</item>
        <item>Pieces (Pc(S))</item>
    </string-array>

    <string-array name="type_product_sous_categoriw">
        <item>Sous-catégorie:</item>
    </string-array>

    <string-array name="type_product_site_comercial">
        <item>Sites commerciaux:</item>
    </string-array>

    <string-array name="type_product_marque">
        <item>Brand</item>
    </string-array>

    <string-array name="type_time_zone">
        <item>Time Zone </item>
        <item>Morocco / casablanca </item>
    </string-array>

    <string-array name="type_date_format">
        <item>mm/dd/yyyy</item>
        <item>dd/mm/yyyy</item>
        <item>mm-dd-yyyy</item>
        <item>dd-mm-yyyy</item>
    </string-array>

    <string-array name="type_time_format">
        <item>12 Hour</item>
        <item>24 Hour</item>
    </string-array>
    <string-array name="type_product_categorie">
        <item>Catégorie:</item>
    </string-array>
    <string-array name="type_product_type">
        <item>Single</item>
        <!--        <item>Variable</item>-->
        <!--        <item>Combo</item>-->
    </string-array>

    <string-array name="type_produc_type">
        <item>All</item>
        <item>Single</item>
        <!--        <item>Variable</item>-->
    </string-array>

    <string-array name="array_payement_status">
        <item>All</item>
        <item>Paid</item>
        <item>Due</item>
        <item>Partial</item>
        <item>Overdue</item>
    </string-array>


    <string-array name="array_apply_tax_price">
        <item>Exclusive</item>
        <item>Inclusive</item>
    </string-array>

    <string-array name="type_product_taxe_type">
        <item>Compris</item>
        <item>Exclusif</item>
    </string-array>

    <string-array name="type_select_company">
        <item>Sélectionnez une entreprise</item>
    </string-array>

    <string-array name="type_select_station">
        <item>Sélectionnez une businesslocation</item>
    </string-array>

    <string-array name="type_select_category">
        <item>Catégorie :</item>
        <item>Tout</item>
    </string-array>

    <string-array name="type_select_affaire_lieu">
        <item>Lieu d\'affaires:</item>
        <item>Tout</item>
    </string-array>

    <string-array name="type_select_unite">
        <item>Tout</item>
        <item>Pieces (Pc(s))</item>
        <item>Litre (L)</item>
    </string-array>

    <string-array name="array_currencies">
        <item>Currency</item>
        <item>America - Dollars (USD)</item>
        <item>Angola / Kwanza (AOA)</item>
        <item>Australia / Doolars (AUD)</item>
    </string-array>

    <string-array name="type_payement_status">
        <item>All</item>
        <item>Payed</item>
        <item>Late</item>
    </string-array>

    <string-array name="type_admin_spinner">
        <item>Admin</item>
        <item>Cashier</item>
    </string-array>

    <string-array name="discount_type">
        <item>Please Select</item>
        <item>fixed</item>
        <item>percentage</item>
    </string-array>

    <string-array name="type_select_impot">
        <item>Impôt:</item>
        <item>Tout</item>
    </string-array>

    <string-array name="type_select_active">
        <item>Actif</item>
        <item>Inactive</item>
    </string-array>

    <string-array name="type_select_marque">
        <item>Tout</item>
    </string-array>

    <!-- navigation drawer item labels  -->
    <string-array name="nav_drawer_labels">
        <item>@string/nav_item_home</item>
        <item>@string/nav_item_friends</item>
        <item>@string/nav_item_notifications</item>
    </string-array>

    <string-array name="array_action_return_sell">
        <item>Action</item>
        <item>View</item>
        <item>Edit</item>
        <item>Delete</item>
        <item>Add Payment</item>
    </string-array>
    <string-array name="array_action_return_sell_2">
        <item>Action</item>
        <item>View</item>
        <item>Edit</item>
        <item>Delete</item>
        <item>View Payment</item>
    </string-array>

    <string-array name="elements_home">
        <item>Babha</item>
        <item>Gandhi</item>
        <item>Nehru</item>
        <item>Tagore</item>
    </string-array>

    <string-array name="elements_friends">
        <item>Rachel</item>
        <item>Ross</item>
        <item>Monica</item>
        <item>Phoebe</item>
        <item>Chandler</item>
        <item>Joey</item>
    </string-array>

    <string-array name="elements_notifs">
        <item>Yellow</item>
        <item>Red</item>
        <item>Green</item>
        <item>Blue</item>
        <item>Pink!</item>
    </string-array>
    <string-array name="array_year_list">
        <item>Please Select</item>
        <item>January</item>
        <item>February</item>
        <item>March</item>
        <item>April</item>
        <item>May</item>
        <item>June</item>
        <item>July</item>
        <item>August</item>
        <item>September</item>
        <item>October</item>
        <item>November</item>
        <item>December</item>
    </string-array>
    <string-array name="array_currency_symbol_placement">
        <item>After Amount</item>
        <item>Before Amount</item>
    </string-array>
    <string name="label_voir_action">Action</string>
    <string name="sell_return">Sell Return</string>
    <string name="user">User</string>
    <string name="user_management">User Management</string>
    <string name="parent_sale">Parent Sale</string>
    <string name="no_sell_return">No Report Found</string>
    <string name="imageDescription">imageDescription</string>
    <string name="hash">#</string>
    <string name="sell_quantity">Sell Quantity</string>
    <string name="return_quantity">Return Quantity</string>
    <string name="return_subtotal">Return Subtotal</string>
    <string name="_00_0">00.0</string>
    <string name="select_discount_type">Please Select Discount Type</string>
    <string name="customer_label">Customer :</string>
    <string name="business_location_label">Business Location :</string>
    <string name="advance_balance_amount">Advance Balance Amount *</string>
    <string name="paid_n">Paid on *</string>
    <string name="textview">TextView</string>
    <string name="mobile_label">Mobile:</string>
    <string name="email_label">Email:</string>
    <string name="date_label">Date :</string>
    <string name="payment_status_label">Payment Status:</string>
    <string name="edit_payment_label">Edit Payment</string>
    <string name="edit_payment_success">Payment Updated Successfully</string>
    <string name="currency_symbol_placement">Currency Symbol Placement</string>
    <string name="enter_business_name">Enter Business Name</string>
    <string name="enter_valide_email">Enter a valide email</string>
    <string name="enter_valide_bbusines_name">Enter a valide business name</string>
    <string name="enter_default_per">Enter Default Percentage Profit</string>
    <string name="enter_transaction_edit_days">Please Enter Transaction Edit Days</string>
    <string name="enter_date_format">Enter Date Format</string>
    <string name="enter_time_format">Enter Time Format</string>
    <string name="business_settings_success">Business Settings Updated Successfully</string>
    <string name="stock_adj_success">Stock Adjustement Updated Successfully</string>
    <string name="stock_transfer_success">Stock Transfer Updated Successfully</string>
    <string name="stock_discunt_success">Discount Updated Successfully</string>
    <string name="stock_purchase_success">Purchase Updated Successfully</string>
    <string name="stock_expense_success">Expense Updated Successfully</string>
    <string name="purchase_return_success">Purchase Return Updated Successfully</string>
    <string name="failed_to_update_data">Failed to Update Data</string>
    <string name="variation_value_should_not_be_empty">Variation Value should not be empty</string>
    <string name="variation_name_should_not_be_empty">Variation Name should not be empty</string>
    <string name="select_unit_name">Enter Unit Name</string>
    <string name="select_short_name">Enter Short Name</string>
    <string name="select_allow_decimal">Select Allow Decimal option</string>
    <string name="update_db_error">Failed to update the data</string>
    <string name="add_as_sub_taxonomy">Add as sub taxonomy</string>
    <string name="select_parent_category">Parent Category</string>
    <string name="select_category_name">Select Category Name</string>
    <string name="select_category_spin">Select Category</string>
    <string name="enter_warranty_name">Enter Warranty Name</string>
    <string name="enter_duration">Enter Duration</string>
    <string name="select_duration">Select Duration</string>
    <string name="select_please_sleetc">Select</string>
    <string name="enter_brand_name">Please Enter Brand Name</string>
    <string name="edit_brand">Edit Brand</string>
    <string name="select_barcode_type">Select Barcode Type</string>
    <string name="please_select_unit_name">Select Unit Name</string>
    <string name="please_select_unit_spin">Select Unit</string>
    <string name="please_select_brand_spin">Select Brand</string>
</resources>