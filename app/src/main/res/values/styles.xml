<resources xmlns:tools="http://schemas.android.com/tools">

    <!--~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~-->
    <!--~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ Base application theme.~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~-->
    <!--~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~-->


<!--    <style name="AppTheme1" parent="Theme.MaterialComponents.Light">-->
<!--        <item name="colorPrimary">@color/colorPrimary</item>-->
<!--        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>-->
<!--        <item name="buttonStyle">@style/Widget.App.Button</item>-->
<!--        <item name="android:buttonStyle">@style/Widget.App.Button</item>-->
<!--        <item name="colorAccent">@color/colorPrimary</item>-->
<!--        <item name="textInputStyle">@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox</item>-->
<!--        <item name="android:spinnerStyle">@style/SpinnerStyle</item>-->
<!--        <item name="android:fontFamily" tools:ignore="NewApi">@font/poppins</item>-->


<!--    </style>-->

<!--    <style name="Widget.App.EditText" parent="Widget.AppCompat.EditText">-->
<!--        <item name="android:layout_width">match_parent</item>-->
<!--        <item name="android:gravity">bottom</item>-->
<!--        <item name="android:layout_gravity">bottom</item>-->
<!--        <item name="android:layout_height">@dimen/default_edit_text_height</item>-->
<!--        <item name="android:background">@color/transparent</item>-->
<!--        <item name="android:padding">12dp</item>-->
<!--        <item name="android:drawablePadding">16dp</item>-->
<!--        <item name="android:drawableTint" tools:ignore="NewApi">@color/colorPrimary</item>-->
<!--        <item name="android:lines">1</item>-->
<!--        <item name="android:textSize">@dimen/dp12</item>-->
<!--        <item name="android:maxLines">1</item>-->
<!--        <item name="android:inputType">text</item>-->
<!--        <item name="android:textColor">@color/black</item>-->
<!--    </style>-->
<!--    <style name="SpinnerStyle" parent="Widget.App.Spinner">-->
<!--        <item name="android:divider">@color/gray_500</item>-->
<!--        <item name="android:dividerHeight">0.5dp</item>-->
<!--        <item name="android:background">@drawable/bg_spinner</item>-->
<!--        <item name="android:textSize">@dimen/dp12</item>-->
<!--        <item name="android:padding">@dimen/dp5</item>-->
<!--        <item name="android:layout_height">@dimen/default_edit_text_height</item>-->
<!--    </style>-->

<!--    <style name="Widget.App.Button" parent="Widget.AppCompat.Button">-->
<!--        <item name="android:layout_width">match_parent</item>-->
<!--        <item name="android:layout_height">@dimen/default_btn_height</item>-->
<!--        <item name="android:background">@drawable/bg_button</item>-->
<!--        <item name="android:textColor">@android:color/white</item>-->
<!--        <item name="android:textStyle">bold</item>-->
<!--    </style>-->

<!--    <style name="Widget.App.Button.Gradient">-->
<!--        <item name="android:background">@drawable/bg_button_gradient</item>-->
<!--    </style>-->

<!--    <style name="Widget.App.Spinner" parent="Widget.AppCompat.Spinner">-->
<!--        <item name="android:layout_width">match_parent</item>-->
<!--        <item name="android:layout_height">@dimen/default_input_field_view_height</item>-->
<!--        <item name="overlapAnchor">true</item>-->
<!--        &lt;!&ndash;        <item name="android:background">@drawable/bg_spinner_go_live</item>&ndash;&gt;-->
<!--        <item name="android:paddingStart">8dp</item>-->
<!--        <item name="android:paddingEnd">8dp</item>-->
<!--        <item name="android:paddingBottom">14dp</item>-->
<!--        <item name="android:paddingTop">14dp</item>-->
<!--        <item name="android:ellipsize">end</item>-->
<!--        <item name="android:color">@color/black</item>-->
<!--    </style>-->


<!--    <style name="toolBarTitleText">-->
<!--        <item name="android:textStyle">bold</item>-->
<!--        <item name="android:textSize">@dimen/normal</item>-->
<!--        <item name="android:textColor">@color/white</item>-->
<!--        <item name="android:layout_height">wrap_content</item>-->
<!--        <item name="android:layout_width">match_parent</item>-->
<!--        <item name="android:textAppearance">@style/TextAppearance.AppCompat</item>-->
<!--        <item name="android:gravity">center</item>-->
<!--        <item name="android:maxLines">1</item>-->
<!--        <item name="singleLine">true</item>-->
<!--        <item name="android:ellipsize">end</item>-->

<!--    </style>-->

<!--    <declare-styleable name="RoundedLetterView">-->
<!--        <attr name="rlv_titleText" format="string" />-->
<!--        <attr name="rlv_titleSize" format="dimension" />-->
<!--        <attr name="rlv_titleColor" format="color" />-->
<!--        <attr name="rlv_backgroundColorValue" format="color" />-->
<!--    </declare-styleable>-->

<!--    <style name="BottomNavigationView">-->
<!--        <item name="itemTextAppearanceActive">@style/TextAppearance.BottomNavigationView.Active</item>-->
<!--        <item name="itemTextAppearanceInactive">@style/TextAppearance.BottomNavigationView.Inactive</item>-->
<!--    </style>-->

<!--    &lt;!&ndash; blank styles for better code readability&ndash;&gt;-->
<!--    <style name="TextAppearance"/>-->
<!--    <style name="TextAppearance.BottomNavigationView"/>-->

<!--    &lt;!&ndash; inactive tab icon style &ndash;&gt;-->
<!--    <style name="TextAppearance.BottomNavigationView.Inactive">-->
<!--        <item name="android:textSize">11sp</item>-->
<!--    </style>-->

<!--    &lt;!&ndash; active tab icon style &ndash;&gt;-->
<!--    <style name="TextAppearance.BottomNavigationView.Active">-->
<!--        <item name="android:textSize">11sp</item>-->
<!--    </style>-->
<!--    <style name="Theme.Transparent" parent="android:Theme">-->
<!--        <item name="android:windowIsTranslucent">true</item>-->
<!--        <item name="android:windowBackground">@android:color/transparent</item>-->
<!--        <item name="android:windowContentOverlay">@null</item>-->
<!--        <item name="android:windowNoTitle">true</item>-->
<!--        <item name="android:windowIsFloating">true</item>-->
<!--        <item name="android:backgroundDimEnabled">false</item>-->
<!--    </style>-->

</resources>
