<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <!-- Base application theme. -->
    <style name="AppTheme" parent="Theme.MaterialComponents.Light.NoActionBar">
        <!-- Customize your theme here. -->
        <item name="colorPrimary">@color/blue25</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
        <item name="textInputStyle">@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox</item>
        <item name="android:fontFamily" tools:ignore="NewApi">@font/poppins</item>
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        <item name="android:spinnerStyle">@style/SpinnerStyle</item>
        <item name="buttonStyle">@style/Widget.App.Button</item>
    </style>
    <style name="Widget.App.Button" parent="Widget.AppCompat.Button">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">@dimen/default_btn_height</item>
        <item name="android:background">@drawable/bg_button_gradient</item>
        <item name="android:textColor">@android:color/white</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style  name="MyTheme"   parent="Theme.AppCompat.DayNight.NoActionBar">
        <item name="android:fontFamily">@font/montserrat_regular</item>
        <item name="android:textColor">@color/black</item>
        <item name="android:itemBackground">@color/white</item>

    </style>
    <style name="Widget.App.EditText" parent="Widget.AppCompat.EditText">
            <item name="android:layout_width">match_parent</item>
            <item name="android:gravity">bottom</item>
            <item name="android:layout_gravity">bottom</item>
            <item name="android:layout_height">@dimen/default_edit_text_height</item>
            <item name="android:background">@color/transparent</item>
            <item name="android:padding">12dp</item>
            <item name="android:drawablePadding">16dp</item>
            <item name="android:drawableTint" tools:ignore="NewApi">@color/colorPrimary</item>
            <item name="android:lines">1</item>
            <item name="android:textSize">@dimen/dp12</item>
            <item name="android:maxLines">1</item>
            <item name="android:inputType">text</item>
            <item name="android:textColor">@color/black</item>
        </style>
    <style name="text_btn_theme">
        <item name="android:gravity">center</item>
        <item name="android:textColor">@color/black</item>
        <item name="fontFamily">@font/montserrat_regular</item>
        <item name="android:layout_marginTop">@dimen/nav_header_vertical_spacing</item>
        <item name="android:textSize">15sp</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="my_btn_style">
        <item name="android:textAppearance">@style/Base.DialogWindowTitleBackground.AppCompat</item>
        <item name="android:textSize">12dp</item>
        <item name="android:height">70px</item>
        <item name="android:fontFamily">@font/montserrat_regular</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@color/White</item>
        <item name="android:drawableTint">@color/White</item>
        <item name="android:background">@drawable/rounded_btn_bg</item>
        <item name="android:layout_marginTop">@dimen/padding_top</item>
        <item name="android:layout_marginLeft">@dimen/margin_vertical</item>
        <item name="android:layout_marginRight">@dimen/margin_vertical</item>
        <item name="android:layout_marginBottom">@dimen/padding_top</item>
    </style>


    <style name="my_btn_style_date">
        <item name="android:textAppearance">@style/Base.DialogWindowTitleBackground.AppCompat</item>
        <item name="android:textSize">12dp</item>
        <item name="android:fontFamily">@font/montserrat_regular</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@color/black</item>
        <item name="android:drawableTint">@color/White</item>
        <item name="android:background">@drawable/rounded_btn_bg_date</item>
        <item name="android:layout_marginTop">@dimen/padding_top</item>
        <item name="android:layout_marginLeft">@dimen/margin_vertical</item>
        <item name="android:layout_marginRight">@dimen/margin_vertical</item>
        <item name="android:layout_marginBottom">@dimen/padding_top</item>
    </style>

    <style name="EditTextStyle" parent="@android:style/Theme">
        <item name="android:textSize">18.0sp</item>
        <item name="android:background">@drawable/rounded_edittext</item>
        <item name="android:layout_marginLeft">@dimen/activity_horizontal_margin</item>
        <item name="android:layout_marginRight">@dimen/activity_horizontal_margin</item>
        <item name="android:textColor">#000000</item>
        <item name="android:fontFamily">@font/montserrat_regular</item>
        <item name="android:textColorHint">@color/gray</item>
        <item name="android:paddingTop">12dp</item>
        <item name="android:paddingBottom">12dp</item>
        <item name="android:paddingLeft">5dp</item>
    </style>

    <style name="TextStyle" parent="@android:style/Theme">
        <item name="android:textColor">@color/white</item>

    </style>

    <!-- ToolBar -->
    <style name="ToolBarStyle" parent="Theme.AppCompat">
        <item name="android:textColorPrimary">@android:color/black</item>
        <item name="android:textColorSecondary">@android:color/black</item>
        <item name="actionMenuTextColor">@android:color/black</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="SlidingDialogAnimation">
        <item name="android:windowEnterAnimation">@android:anim/fade_in</item>
        <item name="android:windowExitAnimation">@android:anim/fade_out</item>
    </style>

    <style name="my_spinner_style" parent="Widget.AppCompat.ListView.DropDown">
        <item name="android:background">@drawable/gradient_spinner</item>
        <item name="android:paddingTop">15dp</item>
        <item name="android:paddingBottom">15dp</item>
        <item name="android:layout_marginTop">10dp</item>
        <item name="android:layout_marginBottom">10dp</item>
        <item name="android:textColor">@color/black</item>
        <item name="android:fontFamily">@font/montserrat_regular</item>
    </style>
    <style name="my_spinner1_style" parent="Widget.AppCompat.ListView.DropDown">
        <item name="android:background">@drawable/gradient_spinner</item>
        <item name="android:paddingTop">15dp</item>
        <item name="android:paddingBottom">15dp</item>
        <item name="android:layout_marginTop">10dp</item>
        <item name="android:layout_marginBottom">10dp</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:fontFamily">@font/montserrat_regular</item>
    </style>

    <style name="MyCustomTextAppearance" parent="TextAppearance.Design.Tab">
        <item name="textAllCaps">false</item>
        <item name="android:fontFamily">@font/montserrat_regular</item>
    </style>

    <style name="SpinnerStyle" parent="Widget.App.Spinner">
        <item name="android:divider">@color/gray_500</item>
        <item name="android:dividerHeight">0.5dp</item>
        <item name="android:background">@drawable/bg_spinner</item>
        <item name="android:textSize">14sp</item>
        <item name="android:padding">5dp</item>
        <item name="android:layout_height">@dimen/default_edit_text_height</item>
    </style>
    <style name="Widget.App.Spinner" parent="Widget.AppCompat.Spinner">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">@dimen/default_input_field_view_height</item>
        <item name="overlapAnchor">true</item>
        <!--        <item name="android:background">@drawable/bg_spinner_go_live</item>-->
        <item name="android:paddingStart">8dp</item>
        <item name="android:paddingEnd">8dp</item>
        <item name="android:paddingBottom">14dp</item>
        <item name="android:paddingTop">14dp</item>
        <item name="android:ellipsize">end</item>
        <item name="android:color">@color/black</item>
    </style>
    <style name="Theme.BigUltimateNavDraw.AppBarOverlay" parent="ThemeOverlay.AppCompat.Dark.ActionBar" />

    <style name="Theme.BigUltimateNavDraw.PopupOverlay" parent="ThemeOverlay.AppCompat.Light" />
</resources>